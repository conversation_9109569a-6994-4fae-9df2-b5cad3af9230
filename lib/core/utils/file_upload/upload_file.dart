// ignore_for_file: omit_local_variable_types

import 'package:file_picker/file_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/utils/file_upload/s3_uploader.dart';
import 'package:proc2/features/mandi/data/data_source/remote/service/mandi_service.dart';

enum UploadFileModule {
  smoCharges,
  procurementsInvoice,
  liquidationOrder,
  grading
}

@injectable
class FileUpload {
  FileUpload(this.service);
  final MandiService service;

  Future<List<PlatformFile>> _pickFiles(
    int noOfFiles,
    List<String> extensions,
  ) async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple:
          // ignore: avoid_bool_literals_in_conditional_expressions
          (noOfFiles > 1 ? true : false),
      type: extensions.isEmpty ? FileType.any : FileType.custom,
      allowedExtensions: extensions,
    );
    if (result == null) {
      throw Exception('User didnt pick any file');
    }
    return result.files;
  }

  Future<List<String>> uploadFile(
    UploadFileModule module,
    Map<String, dynamic> body, {
    List<PlatformFile>? files,
    int? noOfFiles,
    List<String>? extensions,
  }) async {
    final platformFiles = <PlatformFile>[];

    if (files == null || files.isEmpty) {
      platformFiles.addAll(
        await _pickFiles(
          noOfFiles ?? 1,
          extensions ?? [],
        ),
      );
    } else {
      platformFiles.addAll(files);
    }

    final awsUrls =
        await S3Uploader().getUploadUrl(module, body, platformFiles, service);

    final fileUrls = <String>[];

    for (var i = 0; i < platformFiles.length; i++) {
      final isUploaded = await S3Uploader()
          .uploadFileToAws(awsUrls[i].presignedUrl, platformFiles[i]);
      if (isUploaded) {
        fileUrls.add(awsUrls[i].key);
      } else {
        throw Exception('File upload failed');
      }
    }

    return fileUrls;
  }
}
