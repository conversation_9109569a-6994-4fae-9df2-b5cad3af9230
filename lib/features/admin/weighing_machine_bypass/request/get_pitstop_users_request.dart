import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/admin/weighing_machine_bypass/model/pitstop_user.dart';

class GetPitstopUsersRequest extends BaseRequest<dynamic, List<PitstopUser>> {
  final int smoId;

  GetPitstopUsersRequest({required this.smoId});

  @override
  String getPath() {
    return 'attendance/aggregated/smo/$smoId';
  }

  @override
  List<PitstopUser> mapper(data) {
    if (data is List) {
      return data.map((e) => PitstopUser.fromMap(e)).toList();
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
