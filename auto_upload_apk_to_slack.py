import requests
import os
import sys
import glob
import getpass
import subprocess

def get_latest_apk_file1(directory):
    apk_files = glob.glob(os.path.join(directory, '**/*.apk'), recursive=True)

    if not apk_files:
        return None

    latest_file = max(apk_files, key=os.path.getmtime)
    return latest_file

def get_git_branch_name():
    try:
        # Use subprocess to run Git commands and capture the output
        result = subprocess.run(['git', 'rev-parse', '--abbrev-ref', 'HEAD'], stdout=subprocess.PIPE, text=True)
        return result.stdout.strip()
    except:
        return "Git branch fetch name failed"

def upload_file_to_slack(api_token, channel_id, file_path, title="Uploaded File"):
    url = "https://slack.com/api/files.upload"
    headers = {"Authorization": f"Bearer {api_token}"}
    filename = os.path.basename(file_path)
    files = {"file": (filename, open(file_path, "rb"))}
    data = {
        "channels": channel_id,
        "initial_comment": title
    }

    response = requests.post(url, headers=headers, data=data, files=files)
    if response.status_code == 200:
        print(response.text)
        print("File uploaded successfully.")
    else:
        print(f"Error uploading file: {response}")

if __name__ == "__main__":
    # Replace these variables with your own values
    API_TOKEN = "*********************************************************"
    CHANNEL_ID = "C058EL15D0Q" # BOT TEST = C05L7508LRY WOW App = C057TEFHNJ0
    RELEASE_TYPE = sys.argv[1]
    APP_Name = sys.argv[2]
    FILE_PATH = get_latest_apk_file1('build/app/outputs/apk')
    BRANCH_NAME = get_git_branch_name()
    print("FILE PATH = ",FILE_PATH)
    TITLE = "<@U058EPRDVL0> <@U057QR6G1PV> <@U0584LJ72G1> " +APP_Name + " " + RELEASE_TYPE + " From Machine " + getpass.getuser() + " Branch: " + BRANCH_NAME  # You can customize the title

    upload_file_to_slack(API_TOKEN, CHANNEL_ID, FILE_PATH, TITLE)