import 'package:flutter/material.dart';

class WTabBar extends StatelessWidget implements PreferredSizeWidget {
  const WTabBar({
    super.key,
    required this.tabs,
    this.isScrollable = false,
    required this.controller,
    this.onTap,
  });
  final List<Widget> tabs;
  final bool isScrollable;
  final ValueChanged<int>? onTap;
  final TabController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[100],
      child: TabBar(
        controller: controller,
        labelStyle: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        labelColor: Colors.black,
        indicatorColor: Colors.black,
        isScrollable: isScrollable,
        tabs: tabs,
        onTap: onTap,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(48);
}
