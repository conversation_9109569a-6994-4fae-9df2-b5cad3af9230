part of 'add_field_charge_bloc.dart';

@freezed
class AddFieldChargeEvent with _$AddFieldChargeEvent {
  const factory AddFieldChargeEvent.fetchChargesType() = _FetchCharges;

  const factory AddFieldChargeEvent.addCharge() = _AddCharge;

  const factory AddFieldChargeEvent.removeCharge(int index) = _RemoveCharge;

  const factory AddFieldChargeEvent.updateChargeType(
    int index,
    String chargeType,
  ) = _UpdateChargeType;

  const factory AddFieldChargeEvent.updateAmount(
    int index,
    String amount,
  ) = _UpdateAmount;

  const factory AddFieldChargeEvent.updateComment(int index, String comment) =
      _UpdateComment;

  const factory AddFieldChargeEvent.submit(int smoId) = _Submit;

  const factory AddFieldChargeEvent.clearMessage() = _ClearMessage;
  const factory AddFieldChargeEvent.addSlips(List<PickedFile> slips) =
      _AddSlips;
}
