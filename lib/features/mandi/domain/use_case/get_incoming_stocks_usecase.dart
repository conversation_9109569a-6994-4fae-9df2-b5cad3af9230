import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetIncomingStocksUseCase {
  GetIncomingStocksUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, List<Consignment>>> call(int mandiId) async {
    return _mandiRepository.getIncomingStockList(mandiId);
  }
}
