import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';

@freezed
class User with _$User {
  const User._();

  const factory User({
    required String id,
    required String name,
    required String email,
    required String jwt,
    required List<String> roles,
    required String? photo,
  }) = _User;

  factory User.fromMap(Map<dynamic, dynamic> map) {
    return User(
      id: map['id'] as String,
      name: map['name'] as String,
      email: map['email'] as String,
      jwt: map['jwt'] as String,
      roles: map['roles'] as List<String>,
      photo: map['photo'] as String?,
    );
  }

  Map<dynamic, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'jwt': jwt,
      'roles': roles,
      'photo': photo,
    };
  }
}
