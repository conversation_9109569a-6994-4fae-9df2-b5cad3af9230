import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_machine_controller.dart';
import 'package:provider/provider.dart';
import 'package:weighing_machine/weighing_machine.dart';

class WeighingMachineScreen extends StatefulWidget {
  const WeighingMachineScreen({super.key});

  @override
  State<WeighingMachineScreen> createState() => _WeighingMachineScreenState();
}

class _WeighingMachineScreenState extends State<WeighingMachineScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      WeighingMachineController controller =
          context.read<WeighingMachineController>();
      controller.startListeningAndDiscovery();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    WeighingMachineController weighingMachineController =
        context.watch<WeighingMachineController>();
    return Scaffold(
      appBar: AppBar(
        title: LangText('weighingMachine', 'Weighing Machine'),
        centerTitle: false,
      ),
      body: WeighingMachineConnectionScreen(
        controller: weighingMachineController,
        getText: (key, defaultValue) => getLangText(key, defaultValue),
        formatDate: (format, date) => DateFormat(format).format(date),
      ),
    );
  }
}
