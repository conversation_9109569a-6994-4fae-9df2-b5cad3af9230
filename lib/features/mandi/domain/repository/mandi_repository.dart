import 'package:either_dart/either.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/alloted_item.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_init.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_v2.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/trip.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward.dart';
import 'package:proc2/features/mandi/domain/entity/charges/charges_type.dart';
import 'package:proc2/features/mandi/domain/entity/charges/field_charges.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';
import 'package:proc2/features/mandi/domain/entity/lang/supported_language.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_types.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/mandi_inventory/mandi_inventory.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/my_proc_summary.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/procurement_data.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/sku_types/sku_types.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_detail_v2.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_history.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order_metadata.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/input_model/inventory_item_input_model.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/input_model/mandi_inventory_input_model.dart';

import '../entity/lotting/lotting_deviation.dart';

abstract class MandiRepository {
  Future<Either<ErrorResult<dynamic>, String>> createSmo(
    String mandiId,
  );
  Future<Either<ErrorResult<dynamic>, List<MandiInfo>>> getAllMandis();
  Future<Either<ErrorResult<dynamic>, List<int>>> getUserMandis();
  Future<Either<ErrorResult<dynamic>, Smo>> getSmo(
    String mandiId,
  );

  Future<Either<ErrorResult<dynamic>, List<Sku>>> getSku(String language) {
    throw UnimplementedError();
  }

  Future<Either<ErrorResult<void>, String>> addProcurement(
    ProcurementData data,
    int vendorLocationId,
    List<ProcFieldCharge> fieldCharges,
    String comments,
    DateTime? supplyDate,
  ) {
    throw UnimplementedError();
  }

  Future<Either<ErrorResult<void>, List<Consignment>>> getIncomingStockList(
    int mandiId,
  );

  Future<Either<ErrorResult<void>, CarryForward>> getCarryForward({
    required int smoId,
  });

  Future<Either<ErrorResult<void>, ChargesType>> getChargesType();

  Future<Either<ErrorResult<void>, FieldCharges>> getFieldCharges(int smoId);

  Future<Either<ErrorResult<void>, LossTypes>> getLossInit();
  Future<Either<ErrorResult<void>, Map<String, List<LossTypeV2>>>> getLossInitV2();

  Future<Either<ErrorResult<void>, SkuTypes>> getSkuTypes();

  Future<Either<ErrorResult<dynamic>, void>> acceptIncomingStocks(
    int smoId,
    InventoryRecievingInputModel inventoryRecievingInputModel,
  );
  Future<Either<ErrorResult<void>, String>> addCharges({
    required int smoId,
    required List<String> files,
    required List<Map<String, dynamic>> charges,
  });

  Future<Either<ErrorResult<dynamic>, MandiInventory>> getMandiInventory(
    int mandiId,
  );

  Future<Either<ErrorResult<dynamic>, void>> updateMandiInventory(
    MandiInventoryInputModel inputModel,
  );

  Future<Either<ErrorResult<dynamic>, String>> acceptCarryForward({
    required CarryForward carryForward,
    required int smoId,
  });

  Future<Either<ErrorResult<dynamic>, Inventory>> getInventory(int mandiId, {String type = 'primary'});

  Future<Either<ErrorResult<dynamic>, void>> closeInventory(
    int smoId,
    int mandiId,
    List<SkuInputWithLoss> data,
  );

  Future<Either<ErrorResult<dynamic>, AllotmentInit>> getAllotmentInit(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, int>> createAllotment({
    required int smoId,
    required int destinationId,
    required String destinationType,
    required int deliveryDate,
    required String deliverySlot,
    required String? customerGroup,
  });

  Future<Either<ErrorResult<dynamic>, List<Allotment>>> getAllotments(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, List<List<AllotmentV2>>>> getAllotmentsV2(
    int smoId,
    AllotmentInit init,
  );

  Future<Either<ErrorResult<dynamic>, List<AllotedItem>>> getAllotedItems(
    int allotmentId,
  );

  Future<Either<ErrorResult<dynamic>, String>> submitAllotment(
    Map<String, dynamic> body,
  );

  Future<Either<ErrorResult<dynamic>, List<SmoHistory>>> getSmoHistory();
  Future<Either<ErrorResult<dynamic>, List<SmoDetail>>> getSmoDetails(
    int smoId,
  );
  Future<Either<ErrorResult<dynamic>, String>> updateOrSubmitEditProcurement(
    Map<String, dynamic> body, {
    bool isSubmit = false,
  });
  Future<Either<ErrorResult<dynamic>, String>> submitConversion({
    required int smoId,
    required List<InventoryItemInputModel> from,
    required List<InventoryItemInputModel> to,
    required List<LossInputModel> losses,
    required bool isLotting,
  });
  Future<Either<ErrorResult<dynamic>, void>> closeSmoOps(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, List<Trip>>> getTripsForAllocations(
    int allocationId,
  );

  Future<Either<ErrorResult<dynamic>, void>> cancelAllocation(
    int allocationId,
  );

  Future<Either<ErrorResult<dynamic>, String>> dispatchTrip({
    required int tripId,
    required String driverName,
    required String driverPhone,
    required String vehicleNumber,
    required int smoId,
  });

  Future<Either<ErrorResult<dynamic>, List<SupportedLanguage>>> getLanguages();
  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getLangFile(
    String languageCode,
  );
  Future<Either<ErrorResult<dynamic>, LottingDeviation>> getLottingDeviation();
  Future<Either<ErrorResult<dynamic>, bool>> regenerateDeliveryMemo(
    int allotmentId,
    String langCode,
  );

  Future<Either<ErrorResult<dynamic>, List<SmoOps>>> getSmoOps(
    String status,
  );

  Future<Either<ErrorResult<dynamic>, List<List<SmoOps>>>> smoHisoty();

  Future<Either<ErrorResult<dynamic>, SmoDetailV2>> getSmoDetailV2(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, List<MyProcSummary>>> getMyProcSummary(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, List<ProcDetail>>> getMyProcDetail(
    int smoId,
  );
  Future<Either<ErrorResult<dynamic>, List<ProcDetail>>> getAllProcDetail(
    int smoId,
    String? status,
    int? limit,
    int? offset,
  );
  Future<Either<ErrorResult<dynamic>, List<List<ProcItem>>>> getProcItem({
    required int procId,
    required int smoId,
  });
  Future<Either<ErrorResult<dynamic>, SupplyOrderMetadata>> getSupplyOrders({
    required DateTime deliveryDate,
    required String deliverySlot,
    required String customerGroup,
    required bool forAdmin,
  });
}
