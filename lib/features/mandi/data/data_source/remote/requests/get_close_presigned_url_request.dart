import 'package:proc2/core/data/network/base_request.dart';

class GetClosePreSignedUrlRequest
    extends BaseRequest<dynamic, Map<String, String>> {
  final int smoId;
  final List<String> fileExtensions;

  GetClosePreSignedUrlRequest({
    required this.smoId,
    required this.fileExtensions,
  });

  @override
  String getPath() => 'closingStocks/images/upload';

  @override
  Map<String, dynamic> getBody() => {
        "smoId": smoId,
        "fileExtensions": fileExtensions,
      };

  @override
  Map<String, String> mapper(data) {
    if (data is Map<String, dynamic>) {
      return data.map((key, value) => MapEntry(key, value.toString()));
    }
    throw Exception('Invalid json for loss image upload.');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
