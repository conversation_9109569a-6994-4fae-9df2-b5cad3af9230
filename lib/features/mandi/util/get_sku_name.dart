import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

Sku getSKU(BuildContext context, {required int skuID}) {
  const falseObject = Sku(id: -1, name: '-', lotSizes: {}, parentSkuId: -1);

  final sku = context.read<SkuBloc>().state.maybeWhen(
    orElse: () {
      return falseObject;
    },
    success: (skus, searchTerm, selectedSku, filteredSku) {
      return skus.firstWhere(
        (sku) => sku.id == skuID,
        orElse: () => falseObject,
      );
    },
  );

  return sku;
}
