import 'package:freezed_annotation/freezed_annotation.dart';

part 'trip_dto.freezed.dart';
part 'trip_dto.g.dart';

@freezed
class TripDto with _$TripDto {
  const factory TripDto({
    required int id,
    required String routeId,
    required String status,
    required bool canDispatch,
    int? tripStartedAt,
    DriverDetailDto? driverDetails,
    VehicleDetailDto? vehicleDetails,
  }) = _TripDto;

  @override
  factory TripDto.fromJson(Map<String, dynamic> json) =>
      _$TripDtoFromJson(json);
}

@freezed
class DriverDetailDto with _$DriverDetailDto {
  const factory DriverDetailDto({
    String? name,
    String? mobile,
  }) = _DriverDetailDto;

  @override
  factory DriverDetailDto.fromJson(Map<String, dynamic> json) =>
      _$DriverDetailDtoFromJson(json);
}

@freezed
class VehicleDetailDto with _$VehicleDetailDto {
  const factory VehicleDetailDto({
    String? vehicleNumber,
  }) = _VehicleDetailDto;

  @override
  factory VehicleDetailDto.fromJson(Map<String, dynamic> json) =>
      _$VehicleDetailDtoFromJson(json);
}
