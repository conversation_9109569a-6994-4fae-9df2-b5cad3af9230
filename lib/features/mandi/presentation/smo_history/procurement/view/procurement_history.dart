import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/charges/charge.dart';
import 'package:proc2/features/mandi/presentation/smo_history/procurement/bloc/procurement_history_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/procurement/cubit/get_field_charges_cubit.dart';
import 'package:url_launcher/url_launcher.dart';

class ProcurementHistory extends StatefulWidget {
  const ProcurementHistory({
    super.key,
    required this.smoId,
    required this.mandiId,
  });
  final int smoId;
  final int mandiId;

  @override
  State<ProcurementHistory> createState() => _ProcurementHistoryState();
}

class _ProcurementHistoryState extends State<ProcurementHistory> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          appBar: AppBar(
            centerTitle: false,
            title: Text(LanguageEnum.smoHistoryProcHistoryTitle.localized()),
            bottom: TabBar(
              tabs: [
                Tab(
                  text: LanguageEnum.smoHistoryProcHistoryTitle.localized(),
                ),
                Tab(
                  text: LanguageEnum.smoHistoryProcHistoryFieldChargesTitle
                      .localized(),
                ),
              ],
            ),
          ),
          body: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            padding: const EdgeInsets.only(
              left: 5,
              right: 5,
              top: 10,
            ),
            child: TabBarView(
              children: [
                _procTab(),
                _chargesTab(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _procTab() {
    return BlocBuilder<ProcurementHistoryBloc, ProcurementHistoryState>(
      builder: (context, smoState) {
        return smoState.when(
          initial: () => const Center(
            child: CircularProgressIndicator(),
          ),
          success: (success) {
            return success.isEmpty
                ? EmptyScreen(
                    message: LanguageEnum.smoHistoryProcHistoryEmptyTitle
                        .localized(),
                    description: LanguageEnum.smoHistoryProcHistoryEmptyMessage
                        .localized(),
                  )
                : Column(
                    children: [
                      Container(
                        color: Colors.grey[100],
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 8.0,
                            horizontal: 16,
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'procurementHistory.procuredBy',
                                  'ProcuredBy',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 3,
                                child: LangText(
                                  'dateAndTime',
                                  'Date & Time',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'noOfItems',
                                  '# of Items',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        child: RefreshIndicator(
                          onRefresh: () async {
                            context.read<ProcurementHistoryBloc>().add(
                                  ProcurementHistoryEvent.started(widget.smoId),
                                );
                          },
                          child: Scrollbar(
                            child: ListView.builder(
                              itemCount: success.length,
                              itemBuilder: (context, index) {
                                final item = success[index];

                                return InkWell(
                                  onTap: () {
                                    context.pushNamed(
                                      'procItem',
                                      extra: item,
                                    );
                                  },
                                  child: Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: 8.0, horizontal: 8),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 16.0, vertical: 8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(0.0),
                                      color: Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.5),
                                          spreadRadius: 1,
                                          blurRadius: 1,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  item.procuredBy,
                                                  style: TextStyle(
                                                    fontSize: 16.0,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 3,
                                                child: Text(
                                                  item.createdAt.toDate(
                                                    'dd MMM - hh:mm a',
                                                  ),
                                                  style: TextStyle(
                                                    color: Colors.black87,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  item.items.length.toString(),
                                                  style: TextStyle(
                                                    color: Colors.black87,
                                                  ),
                                                  textAlign: TextAlign.end,
                                                ),
                                              ),
                                              SizedBox(
                                                width: 8,
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 8,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
          },
          error: (error) {
            return ErrorScreen(
              onPressed: () {
                context.read<ProcurementHistoryBloc>().add(
                      ProcurementHistoryEvent.started(widget.smoId),
                    );
              },
              message: error.message,
            );
          },
        );
      },
    );
  }

  Widget _chargesTab() {
    return BlocBuilder<GetFieldChargesCubit, GetFieldChargesState>(
      builder: (context, state) {
        return state.when(
          initial: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error) {
            return ErrorScreen(
              onPressed: () {
                context.read<GetFieldChargesCubit>().fetch();
              },
              message: error.message,
            );
          },
          success: (success) {
            return Scrollbar(
              child: ListView(
                children: [
                  for (final charge in success.charges) _chargeCard(charge),
                  const SizedBox(
                    height: 16,
                  ),
                  for (int i = 0; i < success.files.length; i++)
                    _chargeFile(i, success.files[i]),
                  const SizedBox(
                    height: 16,
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _chargeCard(Charge charge) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _chargeValue(
              LanguageEnum.dateAndTime.localized(),
              charge.timestamp == 0
                  ? '-'
                  : DateTime.fromMillisecondsSinceEpoch(charge.timestamp)
                      .toFormattedString(),
            ),
            const SizedBox(
              height: 8,
            ),
            _chargeValue(
              LanguageEnum.smoHistoryFieldChargesChargeTypeLabel.localized(),
              charge.type,
            ),
            const SizedBox(
              height: 8,
            ),
            _chargeValue(
              LanguageEnum.smoHistoryFieldChargesAmountLabel.localized(),
              charge.amount.toString(),
            ),
            const SizedBox(
              height: 8,
            ),
            if (charge.createdByUserName != null) ...[
              _chargeValue(
                LanguageEnum.smoHistoryFieldChargesUserNameLabel.localized(),
                charge.createdByUserName!,
              ),
              const SizedBox(
                height: 8,
              )
            ],
            if (charge.comment.isNotEmpty) ...[
              Text(
                LanguageEnum.smoHistoryFieldChargesCommentsLabel.localized(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                height: 4,
              ),
              Text(charge.comment),
            ],
          ],
        ),
      ),
    );
  }

  Widget _chargeValue(String key, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          key,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(value),
      ],
    );
  }

  Widget _chargeFile(int index, String file) {
    return InkWell(
      onTap: () async {
        final downloadUrl = Uri.parse(file);
        await launchUrl(
          downloadUrl,
          mode: LaunchMode.externalApplication,
        );
      },
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.grey,
          ),
          borderRadius: const BorderRadius.all(
            Radius.circular(
              12,
            ),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.only(
            left: 11,
            right: 11,
            top: 8,
            bottom: 8,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LanguageEnum.smoHistoryFieldChargesFileTitle.localized(
                  params: {
                    'index': index + 1,
                  },
                ),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              IconButton.filled(
                onPressed: () async {
                  final downloadUrl = Uri.parse(file);
                  await launchUrl(
                    downloadUrl,
                    mode: LaunchMode.externalApplication,
                  );
                },
                icon: const Icon(Icons.download),
                tooltip: LanguageEnum.smoHistoryFieldChargesDownloadLabel
                    .localized(),
              )
            ],
          ),
        ),
      ),
    );
  }
}
