import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/delivery_date_range_dto.dart';

part 'allotment_init_dto.freezed.dart';
part 'allotment_init_dto.g.dart';

@freezed
class AllotmentInitDto with _$AllotmentInitDto {
  const factory AllotmentInitDto({
    required Map<String, dynamic> destinations,
    required DeliveryDateRangeDto deliveryDateRange,
    required List<String> deliverySlot,
  }) = _AllotmentInitDto;

  @override
  factory AllotmentInitDto.fromJson(Map<String, dynamic> json) =>
      _$AllotmentInitDtoFromJson(json);
}
