import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/sku_types/sku_types.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetSkuTypesUseCase {
  GetSkuTypesUseCase(this._mandiRepository);
  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, SkuTypes>> call() async {
    return _mandiRepository.getSkuTypes();
  }
}
