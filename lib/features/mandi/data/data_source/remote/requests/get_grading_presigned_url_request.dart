import 'package:proc2/core/data/network/base_request.dart';

class GetGradingPresignedUrlRequest
    extends BaseRequest<dynamic, Map<String, dynamic>> {
  final Map<String, dynamic> body;

  GetGradingPresignedUrlRequest({required this.body});

  @override
  Map<String, dynamic> getBody() {
    return body;
  }

  @override
  String getPath() => 'conversions/images/upload';

  @override
  Map<String, dynamic> mapper(data) {
    if (data is Map<String, dynamic>) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
