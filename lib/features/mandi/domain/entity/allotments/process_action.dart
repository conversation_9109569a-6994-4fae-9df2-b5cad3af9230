import 'package:flutter/material.dart';

class ProcessAction {
  final String key;
  final Color color;
  final String value;
  final IconData icon;

  const ProcessAction._(this.key, this.color, this.value, this.icon);

  static const CANCEL = ProcessAction._(
    "CANCEL",
    Colors.red,
    "Cancel",
    Icons.cancel_outlined,
  );
  static const DISPATCH = ProcessAction._(
    "DISPATCH",
    Colors.orange,
    "Dispatch",
    Icons.delivery_dining,
  );
  static const REGENERATE_DELIVERY_MEMOS = ProcessAction._(
      "REGENERATE_DELIVERY_MEMOS",
      Colors.purple,
      "Regenerate Pdf",
      Icons.refresh);
  static const DOWNLOAD_PDF = ProcessAction._(
    "DOWNLOAD_PDF",
    Colors.green,
    "Download Pdf",
    Icons.download,
  );

  static ProcessAction from(String key) {
    switch (key) {
      case "CANCEL":
        return CANCEL;
      case "DISPATCH":
        return DISPATCH;
      case "REGENERATE_DELIVERY_MEMOS":
        return REGENERATE_DELIVERY_MEMOS;
      case "DOWNLOAD_PDF":
        return DOWNLOAD_PDF;
      default:
        return ProcessAction._(
          "unknown",
          Colors.grey,
          "Unknown",
          Icons.device_unknown,
        );
    }
  }
}
