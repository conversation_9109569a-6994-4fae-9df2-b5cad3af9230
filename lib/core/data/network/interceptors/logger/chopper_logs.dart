import 'dart:convert';

import 'package:chopper/chopper.dart';
import 'package:proc2/core/data/network/interceptors/logger/talker_chopper_logger_settings.dart';
import 'package:talker/talker.dart';

const encoder = JsonEncoder.withIndent('  ');

class ChopperRequestLog extends TalkerLog {
  ChopperRequestLog(
    String message, {
    required this.request,
    required this.settings,
  }) : super(message);

  final Request request;
  final TalkerChopperLoggerSettings settings;

  @override
  AnsiPen get pen => settings.requestPen ?? (AnsiPen()..xterm(219));

  @override
  String get key => TalkerLogType.httpRequest.key;

  @override
  String generateTextMessage() {
    var msg = '[$title] [${request.method}] $message';

    final data = request.body;
    final headers = request.headers;

    try {
      if (settings.printRequestData && data != null) {
        final prettyData = encoder.convert(jsonDecode(data));
        msg += '\nData: $prettyData';
      }
      if (settings.printRequestHeaders && headers.isNotEmpty) {
        final prettyHeaders = encoder.convert(headers);
        msg += '\nHeaders: $prettyHeaders';
      }
    } catch (_) {
      // TODO: add handling can`t convert
    }
    return msg;
  }
}

class ChopperResponseLog extends TalkerLog {
  ChopperResponseLog(
    String message, {
    required this.response,
    required this.settings,
  }) : super(message);

  final Response response;
  final TalkerChopperLoggerSettings settings;

  @override
  AnsiPen get pen => settings.responsePen ?? (AnsiPen()..xterm(46));

  @override
  String get key => TalkerLogType.httpResponse.key;

  @override
  String generateTextMessage() {
    var msg = '[$title] [${response.base.request?.method}] $message';

    final responseMessage = response.base.reasonPhrase;
    final data = response.body;
    final headers = response.headers;

    msg += '\nStatus: ${response.statusCode}';

    if (settings.printResponseMessage && responseMessage != null) {
      msg += '\nMessage: $responseMessage';
    }

    try {
      if (settings.printResponseData && data != null) {
        final prettyData = encoder.convert(data);
        msg += '\nData: $prettyData';
      }
      if (settings.printResponseHeaders && headers.isNotEmpty) {
        final prettyHeaders = encoder.convert(headers);
        msg += '\nHeaders: $prettyHeaders';
      }
    } catch (_) {
      // TODO: add handling can`t convert
    }
    return msg;
  }
}

class ChopperErrorLog extends TalkerLog {
  ChopperErrorLog(
    String title, {
    required this.response,
    required this.settings,
  }) : super(title);

  final Response response;
  final TalkerChopperLoggerSettings settings;

  @override
  AnsiPen get pen => settings.errorPen ?? (AnsiPen()..red());

  @override
  String get key => TalkerLogType.httpError.key;

  @override
  String generateTextMessage() {
    var msg = '[$title] [${response.base.request?.method}] $message';
    final body = response.body;
    final headers = response.headers;

    if (body is Map<String, dynamic>) {
      final error = body['error'] as Map<String, dynamic>?;
      final errorCode = error?['code'];
      final errorMessage = error?['message'];

      if (errorCode != null) {
        msg += '\nCode: $errorCode';
      }
      if (errorMessage != null) {
        msg += '\nMessage: $errorMessage';
      }
    } else {
      msg += '\nCONTRACT BREACHED';
    }
    if (settings.printResponseData && body != null) {
      final prettyData = encoder.convert(body);
      msg += '\nData: $prettyData';
    }
    if (headers.isNotEmpty) {
      final prettyHeaders = encoder.convert(headers);
      msg += '\nHeaders: $prettyHeaders';
    }
    return msg;
  }
}
