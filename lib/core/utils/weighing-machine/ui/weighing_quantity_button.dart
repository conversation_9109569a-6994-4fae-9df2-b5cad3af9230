import 'package:flutter/material.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';

class WeighingQuantityButton extends StatelessWidget {
  const WeighingQuantityButton({
    super.key,
    required this.isReadOnly,
    required this.label,
    required this.popupBuilder,
    required this.onChange,
  });

  final bool isReadOnly;
  final String label;
  final Widget Function() popupBuilder;
  final void Function(WeighingQuantity? quantity) onChange;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isReadOnly
          ? null
          : () async {
              final value = await showDialog<WeighingQuantity?>(
                context: context,
                builder: (_) => popupBuilder(),
              );
              onChange(value);
            },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 4,
        ),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey.shade300,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (!isReadOnly)
              Icon(
                Icons.edit,
                size: 18,
              ),
          ],
        ),
      ),
    );
  }
}
