import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class RowItemField extends StatelessWidget {
  const RowItemField({
    super.key,
    required this.label,
    required this.value,
    this.inputFormatters,
    this.onChanged,
    this.errorText,
    this.padding,
    this.keyboardType = TextInputType.number,
    this.isReadOnly = false,
  });
  final String label;
  final String value;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String)? onChanged;
  final String? errorText;
  final EdgeInsets? padding;
  final TextInputType keyboardType;
  final bool isReadOnly;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyles.heading,
          ),
          SizedBox(
            width: 100,
            child: TextForm<PERSON>ield(
              readOnly: isReadOnly,
              initialValue: value,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                isDense: true,
                contentPadding: EdgeInsets.all(8),
                error: errorText == null
                    ? null
                    : Text(errorText!,
                        style: TextStyle(color: Colors.red)
                            .merge(TextStyles.heading)),
              ),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              inputFormatters: inputFormatters,
              onChanged: onChanged,
              textAlign: TextAlign.end,
              keyboardType: keyboardType,
            ),
          ),
        ],
      ),
    );
  }
}
