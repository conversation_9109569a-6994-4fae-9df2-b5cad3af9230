import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/core/presentation/vendor/view/create_vendor_popup.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';

class VendorDropDown extends StatelessWidget {
  const VendorDropDown({
    super.key,
    required this.isEnabled,
    this.selectedVendorLocation,
    this.onChanged,
    this.enableNewCreation = true,
  });
  final bool isEnabled;
  final VendorLocation? selectedVendorLocation;
  final ValueChanged<VendorLocation>? onChanged;
  final bool enableNewCreation;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<VendorCubit>.value(
      value: di.get<VendorCubit>()..fetchVendors(),
      child: _VendorDropDown(
        isEnabled: isEnabled,
        onChanged: onChanged,
        selectedVendorLocation: selectedVendorLocation,
        enableNewCreation: enableNewCreation,
      ),
    );
  }
}

class _VendorDropDown extends StatefulWidget {
  const _VendorDropDown({
    this.selectedVendorLocation,
    required this.isEnabled,
    this.onChanged,
    required this.enableNewCreation,
  });
  final bool isEnabled;
  final VendorLocation? selectedVendorLocation;
  final ValueChanged<VendorLocation>? onChanged;
  final bool enableNewCreation;

  @override
  State<_VendorDropDown> createState() => _VendorDropDownState();
}

class _VendorDropDownState extends State<_VendorDropDown> {
  Vendor? selectedVendor;
  VendorLocation? selectedVendorLocation;
  List<VendorLocation> vendorLocations = [];
  List<Vendor> vendors = [];
  String vendorLocationLastQuery = '';
  String vendorLastQuery = '';

  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      vendors = di.get<VendorCubit>().state.vendors?.values.toList() ?? [];
      setVendorAndLocation(widget.selectedVendorLocation);
    });
    super.initState();
  }

  void setVendorAndLocation(VendorLocation? vendorLocation) {
    if (vendorLocation != null) {
      final vendor = di
          .get<VendorCubit>()
          .getVendorFromLocation(vendorLocation.locationId);
      if (vendor != null) {
        selectedVendor = vendor;
        selectedVendorLocation = vendorLocation;
        setState(() {});
      }
    }
  }

  void didUpdateWidget(covariant _VendorDropDown oldWidget) {
    if (oldWidget.selectedVendorLocation != widget.selectedVendorLocation) {
      setVendorAndLocation(widget.selectedVendorLocation);
    }
    super.didUpdateWidget(oldWidget);
  }

  Future<List<VendorLocation>> _vendorLocationRequest(String query) async {
    vendorLocationLastQuery = query;
    final itemsToSend = vendorLocations
        .where((element) =>
            element.searchKey.toLowerCase().contains(query.toLowerCase()))
        .toList();
    return itemsToSend;
  }

  Future<List<Vendor>> _vendorRequest(String query) async {
    vendorLastQuery = query;
    final itemsToSend = vendors
        .where((element) =>
            element.name.toLowerCase().contains(query.toLowerCase()) ||
            element.mobile.contains(query.toLowerCase()))
        .toList();
    return itemsToSend;
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<VendorCubit, VendorState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          showSnackBar(state.errorMessage!);
          context.read<VendorCubit>().clearMessage();
        }
        if (state.vendors != null) {
          vendors = state.vendors!.values.toList();
          setState(() {});
        }
      },
      builder: (context, state) {
        if (!widget.isEnabled) {
          final procArea = di
                  .get<VendorCubit>()
                  .getProcAreaFromId(selectedVendorLocation?.procurementAreaId)
                  ?.name ??
              'vendorManagement.noAreaMapped'.tr('No Area Mapped');
          return IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          getLangText('vendorName', 'Vendor Name*'),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 16,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      selectedVendor?.name ?? '',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 8,
                              ),
                              Icon(
                                Icons.arrow_drop_down,
                                color: Colors.grey.shade300,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          getLangText('vendorLocation', 'Vendor Location*'),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    procArea,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    selectedVendorLocation?.pocName ?? '-',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 14,
                                    ),
                                  ),
                                  Text(
                                    selectedVendorLocation?.pocPhone ?? '-',
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 13,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 8,
                            ),
                            Icon(
                              Icons.arrow_drop_down,
                              color: Colors.grey.shade300,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        final vendors = state.vendors;
        if (vendors == null) {
          return SizedBox(
            height: 24,
            width: 24,
            child: CircularProgressIndicator(),
          );
        }
        final allVendors = vendors.values.toList();
        return IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (selectedVendor != null)
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          getLangText('vendorName', 'Vendor Name*'),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    Expanded(
                      child: CustomDropdownV2<Vendor>.searchRequest(
                        fillColor: Colors.grey.shade100,
                        selectedItem: selectedVendor,
                        futureRequest: _vendorRequest,
                        items: allVendors,
                        hintText: getLangText('vendorName', 'Vendor Name*'),
                        onItemSelected: (value) {
                          selectedVendor = value;
                          vendorLocations = value.locations.toList();
                          if (vendorLocations.length == 1) {
                            selectedVendorLocation = vendorLocations[0];
                            widget.onChanged?.call(selectedVendorLocation!);
                          } else {
                            selectedVendorLocation = null;
                          }
                          setState(() {});
                        },
                        selectedItemBuilder: (context, item) {
                          return Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        item.name,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 8,
                                ),
                                Icon(Icons.arrow_drop_down),
                              ],
                            ),
                          );
                        },
                        listItemBuilder: (context, item) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item.name,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                item.mobile,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 13,
                                ),
                              ),
                              Divider(
                                height: 4,
                              ),
                            ],
                          );
                        },
                        emptyResultWidget: !widget.enableNewCreation
                            ? null
                            : Container(
                                padding: EdgeInsets.all(12),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add,
                                      size: 12,
                                      color: Colors.green,
                                    ),
                                    SizedBox(
                                      width: 8,
                                    ),
                                    Text(
                                      getLangText(
                                          'vendorManagement.addNewVendor',
                                          'Add New Vendor'),
                                      style: TextStyle(
                                        color: Colors.green,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                        onEmptyResultClick: !widget.enableNewCreation
                            ? null
                            : () async {
                                // Show popup to add new vendor
                                final result =
                                    await showDialog<VendorLocation?>(
                                  context: context,
                                  builder: (_) =>
                                      CreateVendorPopup.createVendor(
                                    vendorName: vendorLastQuery,
                                  ),
                                );
                                if (result != null) {
                                  widget.onChanged?.call(result);
                                }
                              },
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                child: selectedVendor == null
                    ? TextFormField(
                        enabled: false,
                        key: ValueKey(selectedVendorLocation?.vendorName),
                        initialValue: selectedVendorLocation?.vendorName ?? '',
                        decoration: InputDecoration(
                          labelText:
                              getLangText('vendorLocation', 'Vendor Location*'),
                          border: OutlineInputBorder(),
                          isDense: true,
                          contentPadding: EdgeInsets.only(
                            left: 8,
                            right: 4,
                            top: 12,
                            bottom: 12,
                          ),
                        ),
                        keyboardType: TextInputType.text,
                        textInputAction: TextInputAction.next,
                        style: TextStyle(fontSize: 16),
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (selectedVendorLocation != null)
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                getLangText(
                                    'vendorLocation', 'Vendor Location*'),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          CustomDropdownV2<VendorLocation>.searchRequest(
                            fillColor: Colors.grey.shade100,
                            selectedItem: selectedVendorLocation,
                            futureRequest: _vendorLocationRequest,
                            items: vendorLocations,
                            hintText: getLangText(
                                'vendorLocation', 'Vendor Location*'),
                            onItemSelected: (value) {
                              widget.onChanged?.call(value);
                              setState(() {});
                            },
                            selectedItemBuilder: (context, item) {
                              final procArea = di
                                      .get<VendorCubit>()
                                      .getProcAreaFromId(item.procurementAreaId)
                                      ?.name ??
                                  'vendorManagement.noAreaMapped'
                                      .tr('No Area Mapped');
                              return Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 16,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            procArea,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          Text(
                                            item.pocName,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 14,
                                            ),
                                          ),
                                          Text(
                                            item.pocPhone,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      width: 8,
                                    ),
                                    Icon(Icons.arrow_drop_down),
                                  ],
                                ),
                              );
                            },
                            listItemBuilder: (context, item) {
                              final procArea = di
                                  .get<VendorCubit>()
                                  .getProcAreaFromId(item.procurementAreaId);
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (procArea != null)
                                    Text(
                                      procArea.name,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  Text(
                                    item.pocName,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 13,
                                    ),
                                  ),
                                  Text(
                                    item.pocPhone,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 13,
                                    ),
                                  ),
                                  Divider(
                                    height: 4,
                                  ),
                                ],
                              );
                            },
                            emptyResultWidget: !widget.enableNewCreation
                                ? null
                                : Container(
                                    padding: EdgeInsets.all(12),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.add,
                                          size: 12,
                                          color: Colors.green,
                                        ),
                                        SizedBox(
                                          width: 8,
                                        ),
                                        Text(
                                          getLangText(
                                              'vendorManagement.addNewLocation',
                                              'Add New Location'),
                                          style: TextStyle(
                                            color: Colors.green,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                            onEmptyResultClick: !widget.enableNewCreation
                                ? null
                                : () async {
                                    // Show popup to add new vendor
                                    final result =
                                        await showDialog<VendorLocation?>(
                                      context: context,
                                      builder: (_) =>
                                          CreateVendorPopup.createLocation(
                                        vendor: selectedVendor!,
                                        pocName: vendorLocationLastQuery,
                                      ),
                                    );
                                    if (result != null) {
                                      widget.onChanged?.call(result);
                                    }
                                  },
                          ),
                        ],
                      ),
              ),
            ],
          ),
        );
      },
    );
  }
}
