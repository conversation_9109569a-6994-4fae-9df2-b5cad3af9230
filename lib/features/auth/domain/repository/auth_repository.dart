import 'package:either_dart/either.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/auth/domain/entity/user.dart';

abstract class AuthRepository {
  Future<Either<ErrorResult<void>, firebase.User>> signInWithGoogle() {
    throw UnimplementedError();
  }

  Future<Either<ErrorResult<void>, dynamic>> sendOtp({
    required String mobile,
    bool isResend = false,
  }) {
    throw UnimplementedError();
  }

  Future<Either<ErrorResult<void>, User>> verifyOtp({
    required int otp,
    required String mobile,
  }) {
    throw UnimplementedError();
  }

  Future<Either<ErrorResult<void>, String>> getFirebaseCustomToken({
    required String jwt,
  }) {
    throw UnimplementedError();
  }

  Future<Either<ErrorResult<void>, User>> login({
    required String idToken,
  }) {
    throw UnimplementedError();
  }

  Stream<User?> currentUser() {
    throw UnimplementedError();
  }

  Future<Either<ErrorResult<void>, void>> logOut() {
    throw UnimplementedError();
  }
}
