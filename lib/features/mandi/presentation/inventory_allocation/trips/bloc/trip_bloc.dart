import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/trip.dart';
import 'package:proc2/features/mandi/domain/use_case/dispatch_trip_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_trips_usecase.dart';

part 'trip_event.dart';
part 'trip_state.dart';
part 'trip_bloc.freezed.dart';

@injectable
class TripBloc extends Bloc<TripEvent, TripState> {
  TripBloc(
    this._getTripsUseCase,
    this._dispatchTripUseCase,
  ) : super(const TripState.initial()) {
    on<TripEvent>((event, emit) async {
      await event.map(
        started: (started) async {
          emit(const TripState.initial());

          final result = await _getTripsUseCase(started.allotmentId);

          result.fold(
            (error) {
              emit(TripState.error(error));
            },
            (trips) {
              emit(TripState.success(
                smoId: started.smoId,
                allotmentId: started.allotmentId,
                trips: trips,
              ));
            },
          );
        },
        dispatch: (dispatch) async {
          final currentState = state;
          if (currentState is TripSuccess) {
            final trip = currentState.trips[dispatch.index];
            if (!trip.canDispatch) {
              return;
            }
            if (!trip.driverDetails.isValid() ||
                !trip.vehicleDetails.isValid()) {
              return;
            }

            final result = await _dispatchTripUseCase(
              tripId: trip.id,
              driverName: trip.driverDetails.name,
              driverPhone: trip.driverDetails.mobile,
              vehicleNumber: trip.vehicleDetails.vehicleNumber,
              smoId: currentState.smoId,
            );
            result.fold(
              (error) {
                emit(currentState.copyWith(message: error.message));
              },
              (success) {
                emit(
                    currentState.copyWith(message: 'Dispatched Successfully!'));
                add(TripEvent.started(
                    currentState.smoId, currentState.allotmentId));
              },
            );
          }
        },
        updateDetail: (updateDetail) {
          final currentState = state;
          if (currentState is TripSuccess) {
            final trip = currentState.trips[updateDetail.index];
            final updatedTrip = trip.copyWith(
              driverDetails: trip.driverDetails.copyWith(
                name: updateDetail.driverName,
                mobile: updateDetail.driverMobile,
              ),
              vehicleDetails: trip.vehicleDetails.copyWith(
                vehicleNumber: updateDetail.vehicleNumber,
              ),
            );
            final updatedTrips = currentState.trips
                .map((e) => e.id == trip.id ? updatedTrip : e)
                .toList();
            emit(currentState.copyWith(trips: updatedTrips));
          }
        },
      );
    });
  }

  final GetTripsUseCase _getTripsUseCase;
  final DispatchTripUseCase _dispatchTripUseCase;
}
