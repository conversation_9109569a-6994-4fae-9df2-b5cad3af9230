import 'dart:io';

import 'package:file_picker/file_picker.dart';

class BugReportLog {
  final String userId;
  final String? email;
  final String name;
  final List<PlatformFile> attachments;
  final String comment;
  final File logFile;
  final bool hasResolved;

  BugReportLog({
    required this.userId,
    this.email,
    required this.name,
    required this.attachments,
    required this.comment,
    required this.logFile,
    required this.hasResolved,
  });

  // to Json
  Map<String, dynamic> to<PERSON>son() {
    return {
      'userId': userId,
      'email': email,
      'name': name,
      'comment': comment,
      'hasResolved': hasResolved,
    };
  }
}
