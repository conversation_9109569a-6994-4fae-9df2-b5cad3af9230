import 'package:proc2/core/data/network/base_request.dart';
import 'package:weighing_machine/weighing_machine.dart';

class WeighingMachineScaleConfigurationRequest
    extends BaseRequest<dynamic, List<ScaleConfiguration>> {
  @override
  String getPath() => 'config/weighing-machine';

  @override
  List<ScaleConfiguration> mapper(data) {
    if (data is List<dynamic>) {
      return data.map((e) => ScaleConfiguration.fromMap(e)).toList();
    } else {
      throw 'Invalid Json';
    }
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
