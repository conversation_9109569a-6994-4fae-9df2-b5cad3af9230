part of 'add_field_charge_bloc.dart';

@freezed
class AddFieldChargeState with _$AddFieldChargeState {
  const factory AddFieldChargeState.initial() = _Initial;
  const factory AddFieldChargeState.error(ErrorResult<dynamic> error) = _Error;

  const factory AddFieldChargeState.input({
    required List<String> chargeTypes,
    required List<ChargeInputModel> charges,
    required List<PickedFile> files,
    required bool isSubmitLoading,
    String? message,
  }) = InputChargeState;
}
