part of 'close_inventory_bloc.dart';

@freezed
class CloseInventoryState with _$CloseInventoryState {
  // const factory CloseInventoryState.initial() = _Initial;
  // const factory CloseInventoryState.success({
  //   required List<CloseInventorySkuInput> bulk,
  //   required List<CloseInventorySkuInput> lots,
  //   String? submitErrorMessage,
  //   required bool isLoading,
  // }) = CloseInventorySuccessState;
  // const factory CloseInventoryState.error(ErrorResult<dynamic> error) = _Error;
  // const factory CloseInventoryState.submitted(String message) = _Submitted;

  const factory CloseInventoryState.input({
    // required ProcurementData data,
    // required String mandiName,
    required Map<int, List<SkuInputWithLoss>> inputSku,
    required Map<String, int> filterCounts,
    required bool isGetInventoryLoading,
    required bool isSubmitLoading,
    required bool isSubmitCtaActive,
    required String message,
    @Default(false) bool isSubmittedSuccess,
  }) = _Initial;

  static const empty = CloseInventoryState.input(
    inputSku: {},
    filterCounts: {},
    isGetInventoryLoading: false,
    isSubmitLoading: false,
    isSubmitCtaActive: false,
    message: '',
  );
}

@freezed
class SkuInputWithLoss with _$SkuInputWithLoss {
  const SkuInputWithLoss._();
  const factory SkuInputWithLoss({
    required SkuInputData skuInputData,
    required List<LossInputModel> losses,
    @Default([]) List<PickedFile> files,
  }) = _SkuInputWithLoss;

  double totalLoss() {
    return losses.fold(0, (p, e) => p + e.lossValue.toDouble());
  }
}
