import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/create_parent_proc_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/csv_handler.dart';

part 'add_parent_proc_state.dart';
part 'add_parent_proc_cubit.freezed.dart';

class SkuQuantityWithName {
  final String skuName;
  final SkuQuantity skuQuantity;

  SkuQuantityWithName({
    required this.skuName,
    required this.skuQuantity,
  });
}

@injectable
class AddParentProcCubit extends Cubit<AddParentProcState> {
  AddParentProcCubit() : super(AddParentProcState.initial());

  void onVendorLocationChanged(VendorLocation? vendorLocation) {
    emit(state.copyWith(selectedVendorLocation: vendorLocation));
  }

  void onMandiChanged(MandiInfo? mandiInfo) {
    emit(state.copyWith(selectedMandi: mandiInfo));
  }

  void onSupplyDateChanged(DateTime? date) {
    emit(state.copyWith(selectedSupplyDate: date));
  }

  Future<void> downloadCsv({
    required List<MandiInfo> pitstops,
    required List<Sku> skus,
  }) async {
    final headers = [
      '',
      'Price per Unit',
      ...pitstops.map((e) => e.name)
    ];
    final firstRowWithKey = [
      '',
      '',
      ...pitstops.map((e) => e.key)
    ];
    final defaultRowWithoutSku = [
      '',
      ...List.generate(pitstops.length, (index) => '')
    ];
    final rows = <List<String>>[];
    rows.add(firstRowWithKey);
    rows.add([
      'Comments',
      '-',
      ...List.generate(pitstops.length, (index) => '')
    ]);
    rows.add([
      'Sku'
    ]);
    skus.forEach((e) {
      if (e.canProcure) {
        e.bulkUnitTypes.forEach((element) {
          rows.add([
            _generateCompositeKey(skuId: e.id, skuName: e.name, type: 'BULK', unit: element, lotSize: null),
            ...defaultRowWithoutSku
          ]);
        });
        e.lotSizes.entries.forEach((element) {
          final unit = element.key;
          final lotSizes = element.value;
          lotSizes.forEach((lotSize) {
            rows.add([
              _generateCompositeKey(
                skuId: e.id,
                skuName: e.name,
                type: 'LOTS',
                unit: unit,
                lotSize: lotSize.toString().toDouble(),
              ),
              ...defaultRowWithoutSku
            ]);
          });
        });
      }
    });
    await CSVHandler.downloadCSV(fileName: 'add_proc_${DateFormat('yyyy-MM-dd hh:mm a').format(DateTime.now())}.csv', values: rows, headers: headers);
  }

  String _generateCompositeKey({
    required int skuId,
    required String skuName,
    required String type,
    required String unit,
    required double? lotSize,
  }) {
    final baseKey = '${skuId}__${skuName}__${type}__$unit';
    if (lotSize != null) {
      return '${baseKey}__${lotSize}';
    }
    return baseKey;
  }

  SkuQuantityWithName? _getFromCompositeKey(String key) {
    try {
      final splits = key.split('__');
      final skuId = int.parse(splits[0]);
      final skuName = splits[1];
      final type = splits[2];
      final unit = splits[3];
      final lotSize = splits.length > 4 ? double.parse(splits[4]) : null;
      return SkuQuantityWithName(
        skuName: skuName,
        skuQuantity: SkuQuantity(
          skuId: skuId,
          type: type,
          unit: unit,
          lotSize: lotSize,
          quantity: 0,
        ),
      );
    } catch (e) {
      return null;
    }
  }

  Future<void> onCSVUpload({required List<List<dynamic>> data, required List<MandiInfo> allMandis}) {
    final headers1 = data[0].map((e) => e.toString()).toList();
    // data[1] is now keys
    final comments = data[2];
    final nonExistingPitstops = headers1.sublist(2).any((e) => allMandis.where((element) => element.name == e).length == 0);
    if (nonExistingPitstops) {
      emit(state.copyWith(message: 'Invalid Pitstops'));
      return Future.value();
    }

    final skuRows = data.sublist(4);
    final skuValues = <List<dynamic>>[];
    try {
      skuRows.forEach((element) {
        final e = element.sublist(0, headers1.length);
        final key = e[0].toString();
        final skuQuantityWithName = _getFromCompositeKey(key);
        final cost = e[1].toString();

        final hasValue = e.sublist(2).any((e) => e.toString().toDouble() > 0);
        final hasInvalidValues = e.sublist(1).any((e) => e.toString().trim().isNotEmpty && !e.toString().isDouble());
        if (hasInvalidValues) {
          emit(state.copyWith(message: 'Invalid Value: Text can not be converted to double'));
          throw 'Invalid values';
        }
        if (skuQuantityWithName != null && hasValue) {
          if (cost.isNotDouble()) {
            emit(state.copyWith(message: 'Price per unit must be provided for all skus!'));
            throw 'Invalid price per unit';
          }

          final costDouble = cost.toDouble();
          if (costDouble <= 0) {
            emit(state.copyWith(message: 'Price per unit must be greater than 0!'));
            throw 'Invalid price per unit';
          }
          // Calculate total quantity for the SKU
          double totalQuantity = e.sublist(2).map((quantity) => quantity.toString().toDouble()).reduce((a, b) => a + b);
          skuValues.add([
            skuQuantityWithName,
            totalQuantity, // Add total quantity next to SKU
            ...e.sublist(1),
          ]);
        }
      });
      if (skuValues.isNotEmpty) {
        skuValues.add([
          ...comments.sublist(0, 1),
          "-",
          ...comments.sublist(1)
        ]);
        emit(state.copyWith(
          headers: [
            'Sku',
            'Total Quantity',
            ...headers1.sublist(1)
          ], // Update headers to include Total Quantity
          values: skuValues,
        ));
      } else {
        emit(state.copyWith(message: 'No sku found!'));
      }
    } catch (_) {}
    return Future.value();
  }

  void onSubmit(int smoId, List<MandiInfo> allMandis) async {
    emit(state.copyWith(isCtaLoading: true));
    List<List<dynamic>> newArr = [];
    for (List<dynamic> row in state.values) {
      newArr.add([
        ...row.sublist(0, 1),
        ...row.sublist(2)
      ]);
    }
    newArr.removeLast();

    final result = await CreateParentProcOrderRequest(
      smoId: smoId,
      vendorLocationId: state.selectedVendorLocation!.locationId,
      columns: [
        state.headers[0],
        state.headers[2],
        ...state.headers.sublist(3).map((e) => allMandis.where((element) => element.name == e).first.id)
      ],
      rows: newArr,
      comments: [
        ...state.values.last.sublist(0, 1),
        ...state.values.last.sublist(2)
      ],
      selectedSupplyDate: state.selectedSupplyDate!,
    ).execute();
    result.fold(
      (l) {
        emit(state.copyWith(isCtaLoading: false, message: l.message));
      },
      (r) {
        emit(state.copyWith(isCtaLoading: false, message: r, headers: [], values: []));
      },
    );
  }

  void clearMessage() {
    emit(state.copyWith(message: null));
  }
}
