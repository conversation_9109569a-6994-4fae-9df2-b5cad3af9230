import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_supply_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/alloted_item.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/domain/use_case/get_alloted_items_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_inventory_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/submit_allotment_usecase.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocating/input_model/allocate_item_input_model.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/cubit/edit_supply_order_cubit.dart';

part 'allocating_inventory_bloc.freezed.dart';
part 'allocating_inventory_event.dart';
part 'allocating_inventory_state.dart';

@injectable
class AllocatingInventoryBloc
    extends Bloc<AllocatingInventoryEvent, AllocatingInventoryState> {
  AllocatingInventoryBloc(
    this._getInventoryUseCase,
    this._getAllotedItemsUseCase,
    this._submitAllotmentUseCase,
  ) : super(_Initial()) {
    on<AllocatingInventoryEvent>(
      (event, emit) async {
        await event.when(
          started: (
            mandId,
            allotmentId,
            customerGroup,
            deliverySlot,
            deliveryDate,
            skuMap,
          ) async {
            final inventoryFuture = _getInventoryUseCase(mandId);
            final allotedItemsFuture = _getAllotedItemsUseCase(allotmentId);
            final futureToExecute = [inventoryFuture, allotedItemsFuture];
            final isCustomerGroup = customerGroup != null &&
                deliverySlot != null &&
                deliveryDate != null;
            if (isCustomerGroup) {
              final supplyOrderReq = GetSupplyOrderRequest(
                deliveryEpochSeconds: int.parse(deliveryDate),
                deliverySlot: deliverySlot,
                customerGroup: customerGroup,
              ).execute();
              futureToExecute.add(supplyOrderReq);
            }
            final result = await Future.wait(futureToExecute);
            ErrorResult<dynamic>? errorResult;

            final inventory = result[0].fold(
              (l) {
                errorResult = l;
                return null;
              },
              (r) => r as Inventory,
            );
            final alloted = result[1].fold(
              (left) {
                errorResult = left;
                return null;
              },
              (right) => right as List<AllotedItem>,
            );

            final supplyOrder = isCustomerGroup
                ? result[2].fold(
                    (left) {
                      errorResult = left;
                      return null;
                    },
                    (right) => right as List<SupplyOrder>,
                  )
                : null;

            if (inventory != null &&
                alloted != null &&
                (isCustomerGroup ? supplyOrder != null : true)) {
              final allotedItemsMap = <String, AllotedItem>{};
              final terminalResult = supplyOrder != null
                  ? createTerminalListFromSupplyOrder(
                      supplyOrder,
                      skuMap,
                    )
                  : null;
              final supplyOrderQtyMap = <String, double>{};
              if (terminalResult != null &&
                  terminalResult.terminals.isNotEmpty) {
                final rowsLength = terminalResult.terminals[0].skus.length;
                for (int i = 0; i < rowsLength; i++) {
                  final qty = terminalResult.terminals
                      .fold(0.0, (p, e) => p + e.skus[i].skuQuantity.quantity);
                  final compositeKey = terminalResult
                      .terminals[0].skus[i].skuQuantity.compositeKey;
                  supplyOrderQtyMap[compositeKey] = qty;
                }
              }

              final skuIdToCompositeKeyMap =
                  <int, Map<String, AllocateItemInputModel>>{};
              for (final element in alloted) {
                allotedItemsMap[element.getCompositeKey()] = element;
              }
              for (final item in inventory.skus) {
                final skuId = item.skuId;
                final key = item.getCompositeKey();
                if (item.quantity <= 0 &&
                    !allotedItemsMap.containsKey(key) &&
                    !supplyOrderQtyMap.containsKey(key)) continue;

                final allotQty = allotedItemsMap[key]?.quantity ?? 0;
                final allotQtyDouble = allotQty.toDouble();
                if (skuIdToCompositeKeyMap.containsKey(skuId)) {
                  skuIdToCompositeKeyMap[skuId]![key] = AllocateItemInputModel(
                    id: allotedItemsMap[key]?.id ?? null,
                    skuId: item.skuId,
                    type: item.type,
                    unit: item.unit,
                    lotSize: item.lotSize ?? 0,
                    quantity: item.quantity + allotQtyDouble,
                    supplyQuantity: supplyOrderQtyMap[key] ?? null,
                    allotmentQuantity: allotQty > 0 ? allotQty.asString() : '',
                  );
                } else {
                  skuIdToCompositeKeyMap[skuId] = {
                    key: AllocateItemInputModel(
                      id: allotedItemsMap[key]?.id ?? null,
                      skuId: item.skuId,
                      type: item.type,
                      unit: item.unit,
                      lotSize: item.lotSize ?? 0,
                      quantity: item.quantity + allotQtyDouble,
                      supplyQuantity: supplyOrderQtyMap[key] ?? null,
                      allotmentQuantity:
                          allotQty > 0 ? allotQty.asString() : '',
                    )
                  };
                }
              }

              final allList = skuIdToCompositeKeyMap.values
                  .map((e) => e.values.toList())
                  .toList();
              if (isCustomerGroup) {
                final validList = allList
                    .map((e) => e
                        .where((element) =>
                            element.quantity > 0 &&
                            (element.supplyQuantity ?? 0) > 0)
                        .toList())
                    .where((e) => e.isNotEmpty)
                    .toList();
                final onlyMandiList = allList
                    .map((e) => e
                        .where((element) =>
                            element.quantity > 0 &&
                            (element.supplyQuantity ?? 0) <= 0)
                        .toList())
                    .where((e) => e.isNotEmpty)
                    .toList();
                final onlySupplyList = allList
                    .map((e) => e
                        .where((element) =>
                            element.quantity <= 0 &&
                            (element.supplyQuantity ?? 0) > 0)
                        .toList())
                    .where((e) => e.isNotEmpty)
                    .toList();
                emit(
                  _validate(
                    AllocatingInput(
                      items: validList,
                      onlyMandiItems: onlyMandiList,
                      onlySupplyItems: onlySupplyList,
                      allotedItems: alloted,
                    ),
                  ),
                );
              } else {
                emit(
                  _validate(
                    AllocatingInput(
                      items: allList,
                      allotedItems: alloted,
                    ),
                  ),
                );
              }
            } else if (errorResult != null) {
              emit(AllocatingInventoryState.error(errorResult!));
            }
          },
          updateAllotmentQuantity: (
            AllocateListType listType,
            int listIndex,
            int itemIndex,
            String allotmentQuantity,
          ) {
            final currentState = state;
            if (currentState is AllocatingInput) {
              final baseItems = listType == AllocateListType.allList
                  ? currentState.items
                  : listType == AllocateListType.mandiList
                      ? currentState.onlyMandiItems
                      : currentState.onlySupplyItems;
              final item = baseItems[listIndex][itemIndex];
              final updatedItem =
                  item.copyWith(allotmentQuantity: allotmentQuantity);
              final items = List<List<AllocateItemInputModel>>.from(baseItems);
              items[listIndex][itemIndex] = updatedItem;
              final newState = listType == AllocateListType.allList
                  ? currentState.copyWith(items: items)
                  : listType == AllocateListType.mandiList
                      ? currentState.copyWith(onlyMandiItems: items)
                      : currentState.copyWith(onlySupplyItems: items);
              emit(_validate(newState));
            }
          },
          saveAsDraft: (allotmentId) async {
            await _submitOrDraft(
              isSubmit: false,
              allotmentId: allotmentId,
              emit: emit,
            );
          },
          dispatch: (allotmentId) async {
            await _submitOrDraft(
              isSubmit: true,
              allotmentId: allotmentId,
              emit: emit,
            );
          },
          clearMessage: () {
            final currentState = state;
            if (currentState is AllocatingInput) {
              emit(currentState.copyWith(
                message: null,
                shouldPopBack: false,
              ));
            }
          },
          copyFromInventory: (bool isMandi) async {
            final currentState = state;
            if (currentState is AllocatingInput) {
              final updatedItems = currentState.items
                  .map((e) => e
                      .map((e) => e.copyWith(
                          allotmentQuantity: isMandi
                              ? e.quantity.asString()
                              : e.supplyQuantity?.asString() ?? ''))
                      .toList())
                  .toList();
              emit(AllocatingInventoryState.initial());
              await Future.delayed(const Duration(milliseconds: 100));
              emit(
                _validate(currentState.copyWith(
                  items: updatedItems,
                )),
              );
            }
          },
        );
      },
    );
  }

  final GetInventoryUseCase _getInventoryUseCase;
  final GetAllotedItemsUseCase _getAllotedItemsUseCase;
  final SubmitAllotmentUseCase _submitAllotmentUseCase;

  AllocatingInventoryState _validate(AllocatingInput currentState) {
    try {
      int itemCount = 0;
      itemCount += currentState.items
          .expand((element) => element)
          .where((e) =>
              e.allotmentQuantity.isNotEmpty &&
              e.allotmentQuantity.toDouble() > 0)
          .length;
      itemCount += currentState.onlyMandiItems
          .expand((element) => element)
          .where((e) =>
              e.allotmentQuantity.isNotEmpty &&
              e.allotmentQuantity.toDouble() > 0)
          .length;
      itemCount += currentState.onlySupplyItems
          .expand((element) => element)
          .where((e) =>
              e.allotmentQuantity.isNotEmpty &&
              e.allotmentQuantity.toDouble() > 0)
          .length;

      return currentState.copyWith(isValidData: itemCount > 0);
    } catch (_) {}
    return currentState.copyWith(isValidData: false);
  }

  Future<void> _submitOrDraft({
    required bool isSubmit,
    required int allotmentId,
    required Emitter<AllocatingInventoryState> emit,
  }) async {
    final currentState = state;
    if (currentState is AllocatingInput) {
      if (currentState.isSubmitLoading) return;
      if (currentState.isDraftLoading) return;

      emit(
        currentState.copyWith(
          isDraftLoading: !isSubmit,
          isSubmitLoading: isSubmit,
        ),
      );

      final allInputItems = <AllocateItemInputModel>[];
      allInputItems
          .addAll(currentState.items.expand((element) => element).toList());
      allInputItems.addAll(
          currentState.onlyMandiItems.expand((element) => element).toList());
      allInputItems.addAll(
          currentState.onlySupplyItems.expand((element) => element).toList());

      final allotedItemsMap = <String, AllotedItem>{};
      for (final element in currentState.allotedItems) {
        allotedItemsMap[element.getCompositeKey()] = element;
      }
      final newItemsList = <AllocateItemInputModel>[];
      final updatedItemsList = <AllocateItemInputModel>[];

      for (final item in allInputItems) {
        final key = item.getCompositeKey();
        if (allotedItemsMap.containsKey(key)) {
          if (allotedItemsMap[key]!.quantity !=
              item.allotmentQuantity.toDouble()) {
            updatedItemsList.add(item);
          }
          allotedItemsMap.remove(key);
        } else {
          newItemsList.add(item);
        }
      }

      final deletedItemList = allotedItemsMap.values.map((e) => e.id).toList();

      await _submitAllotmentUseCase(
        newItems: newItemsList,
        updatedItems: updatedItemsList,
        deletedItems: deletedItemList,
        isSubmit: isSubmit,
        allotmentId: allotmentId,
      ).then(
        (value) {
          value.fold(
            (l) {
              emit(
                currentState.copyWith(
                  message: l.message,
                  isDraftLoading: false,
                  isSubmitLoading: false,
                ),
              );
            },
            (r) {
              emit(
                currentState.copyWith(
                  message: r,
                  isDraftLoading: false,
                  isSubmitLoading: false,
                  shouldPopBack: true,
                ),
              );
            },
          );
        },
      );
    }
  }
}
