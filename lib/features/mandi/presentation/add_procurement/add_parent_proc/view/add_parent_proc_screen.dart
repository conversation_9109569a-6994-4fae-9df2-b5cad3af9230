import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/vendor/view/vendor_drop_down.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/cubit/add_parent_proc_cubit.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/csv_handler.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/mandi_dropdown.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/pitstops_picker.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:table_sticky_headers/table_sticky_headers.dart';

class AddParentProcScreen extends StatefulWidget {
  const AddParentProcScreen({super.key});
  @override
  State<AddParentProcScreen> createState() => _AddParentProcScreen();
}

class _AddParentProcScreen extends State<AddParentProcScreen> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text('addProc.placeProcOrder'.tr('Place Procurement Order')),
          centerTitle: false,
        ),
        body: BlocBuilder<SkuBloc, SkuState>(
          builder: (context, state) {
            List<Sku> skuList = state.whenOrNull(
                  success: (skus, searchTerm, selectedSku, filteredSku) => skus,
                ) ??
                [];
            return BlocBuilder<MandiBloc, MandiState>(builder: (context, state) {
              final liveOps = state.maybeMap(orElse: () => <SmoOps>[], success: (s) => s.liveOps ?? []);
              final allNonCorporateMandis = state.maybeMap(orElse: () => <MandiInfo>[], success: (s) => s.allMandis);
              return BlocConsumer<AddParentProcCubit, AddParentProcState>(
                listener: (context, state) {
                  final message = state.message;
                  if (message != null) {
                    showSnackBar(message);
                    context.read<AddParentProcCubit>().clearMessage();
                  }
                },
                builder: (context, state) {
                  return Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 8,
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: IntrinsicHeight(
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: MandiDropdown(
                                      selectedMandi: state.selectedMandi,
                                      onChange: (val) {
                                        context.read<AddParentProcCubit>().onMandiChanged(val);
                                      },
                                    ),
                                  ),
                                  SizedBox(
                                    width: 16,
                                  ),
                                  InkWell(
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: DateTime.now(),
                                        firstDate: DateTime.now().subtract(Duration(days: 4)),
                                        lastDate: DateTime.now().add(
                                          Duration(days: 7),
                                        ),
                                      );
                                      context.read<AddParentProcCubit>().onSupplyDateChanged(date);
                                    },
                                    child: SizedBox(
                                      width: 200,
                                      child: TextFormField(
                                        key: Key(state.selectedSupplyDate?.toString() ?? ''),
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.black,
                                        ),
                                        enabled: false,
                                        initialValue: state.selectedSupplyDate == null ? '' : (state.selectedSupplyDate!.millisecondsSinceEpoch ~/ 1000).toDate('dd MMM, yyyy'),
                                        decoration: InputDecoration(
                                          enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: Colors.white,
                                            ),
                                          ),
                                          border: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: Colors.white,
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: Colors.white,
                                            ),
                                          ),
                                          disabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: Colors.white,
                                            ),
                                          ),
                                          prefixIcon: Icon(
                                            Icons.calendar_month,
                                            color: Colors.white,
                                          ),
                                          label: LangText(
                                            'addParentProc.supplyDateMandatory',
                                            'Supply Date*',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.black54,
                                            ),
                                          ),
                                          isDense: false,
                                          contentPadding: EdgeInsets.symmetric(
                                            vertical: 0,
                                            horizontal: 8,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 16,
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: VendorDropDown(
                                      isEnabled: true,
                                      selectedVendorLocation: state.selectedVendorLocation,
                                      onChanged: (value) {
                                        context.read<AddParentProcCubit>().onVendorLocationChanged(value);
                                      },
                                    ),
                                  ),
                                  SizedBox(
                                    width: 16,
                                  ),
                                  Row(
                                    children: [
                                      ElevatedButton.icon(
                                          onPressed: () {
                                            onDownloadCsvClick(skus: skuList);
                                          },
                                          label: Text('Download CSV'),
                                          icon: Icon(
                                            Icons.download,
                                          )),
                                      const SizedBox(width: 8),
                                      ElevatedButton.icon(
                                          onPressed: () {
                                            uploadCSV(allNonCorporateMandis);
                                          },
                                          label: Text('Upload CSV'),
                                          icon: Icon(
                                            Icons.upload,
                                          )),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),
                          Expanded(
                            child: state.values.isEmpty
                                ? Center(
                                    child: Text(
                                      'Please upload CSV to continue!',
                                      style: TextStyle(
                                        color: Colors.red,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  )
                                : Container(
                                    width: MediaQuery.of(context).size.width,
                                    height: MediaQuery.of(context).size.height,
                                    padding: EdgeInsets.symmetric(horizontal: 16),
                                    child: StickyHeadersTable(
                                      columnsLength: state.headers.length,
                                      rowsLength: state.values.length,
                                      legendCell: Container(
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              color: Colors.grey.shade300,
                                              width: 1,
                                            ),
                                            left: BorderSide(
                                              color: Colors.grey.shade300,
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            'S.No.',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                      columnsTitleBuilder: (n) => Container(
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              color: Colors.grey.shade300,
                                              width: 1,
                                            ),
                                            left: BorderSide(
                                              color: Colors.grey.shade300,
                                              width: 1,
                                            ),
                                            right: n == state.headers.length - 1
                                                ? BorderSide(
                                                    color: Colors.grey.shade300,
                                                    width: 1,
                                                  )
                                                : BorderSide.none,
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            state.headers[n],
                                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                      ),
                                      cellDimensions: CellDimensions.fixed(
                                        contentCellWidth: 120,
                                        contentCellHeight: 48,
                                        stickyLegendWidth: 50,
                                        stickyLegendHeight: 48,
                                      ),
                                      showHorizontalScrollbar: true,
                                      showVerticalScrollbar: true,
                                      rowsTitleBuilder: (k) => Container(
                                        decoration: BoxDecoration(
                                          border: Border(
                                            top: BorderSide(
                                              color: Colors.grey.shade300,
                                              width: 1,
                                            ),
                                            left: BorderSide(
                                              color: Colors.grey.shade300,
                                              width: 1,
                                            ),
                                            bottom: k == state.values.length - 1
                                                ? BorderSide(
                                                    color: Colors.grey.shade300,
                                                    width: 1,
                                                  )
                                                : BorderSide.none,
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            k == state.values.length - 1 ? '' : (k + 1).toString(),
                                          ),
                                        ),
                                      ),
                                      contentCellBuilder: (colIndex, rowIndex) {
                                        if (rowIndex < state.values.length && colIndex < state.values[rowIndex].length) {
                                          final cellValue = state.values[rowIndex][colIndex];
                                          var value = cellValue;
                                          if (colIndex == 0) {
                                            if (cellValue is SkuQuantityWithName) {
                                              value = cellValue.skuName + '\n' + cellValue.skuQuantity.unitInfo;
                                            } else {
                                              value = cellValue.toString();
                                            }
                                          } else {
                                            value = cellValue.toString();
                                          }
                                          if (colIndex == 1 && rowIndex != state.values.length - 1) {
                                            value =  value;
                                          }
                                          if (colIndex == 2 && rowIndex != state.values.length - 1) {
                                            value =  '₹' + value;
                                          }
                                          return Container(
                                            decoration: BoxDecoration(
                                              border: Border(
                                                top: BorderSide(
                                                  color: Colors.grey.shade300,
                                                  width: 1,
                                                ),
                                                left: BorderSide(
                                                  color: Colors.grey.shade300,
                                                  width: 1,
                                                ),
                                                right: colIndex == state.headers.length - 1
                                                    ? BorderSide(
                                                        color: Colors.grey.shade300,
                                                        width: 1,
                                                      )
                                                    : BorderSide.none,
                                                bottom: rowIndex == state.values.length - 1
                                                    ? BorderSide(
                                                        color: Colors.grey.shade300,
                                                        width: 1,
                                                      )
                                                    : BorderSide.none,
                                              ),
                                            ),
                                            child: Center(
                                              child: InkWell(
                                                onTap: rowIndex != state.values.length - 1 || colIndex <= 1
                                                    ? null
                                                    : () {
                                                        showDialog(
                                                          context: context,
                                                          builder: (_) => SizedBox(
                                                            width: 200,
                                                            child: Popup(
                                                              height: 0.4,
                                                              title: 'Comment',
                                                              children: [
                                                                Padding(
                                                                  padding: const EdgeInsets.all(8.0),
                                                                  child: Container(
                                                                    width: double.infinity,
                                                                    decoration: BoxDecoration(
                                                                      border: Border.all(
                                                                        color: Colors.grey.shade300,
                                                                        width: 1,
                                                                      ),
                                                                      borderRadius: BorderRadius.circular(8),
                                                                    ),
                                                                    padding: EdgeInsets.all(8),
                                                                    child: Text(
                                                                      cellValue,
                                                                    ),
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                        );
                                                      },
                                                child: AutoSizeText(
                                                  value,
                                                  minFontSize: 12,
                                                  maxFontSize: 16,
                                                  style: TextStyle(fontWeight: colIndex != 0 ? FontWeight.normal : FontWeight.w600, overflow: TextOverflow.ellipsis),
                                                  textAlign: TextAlign.center,
                                                  maxLines: 2,
                                                ),
                                              ),
                                            ),
                                          );
                                        } else {
                                          return Container(
                                            decoration: BoxDecoration(
                                              border: Border(
                                                top: BorderSide(
                                                  color: Colors.grey.shade300,
                                                  width: 1,
                                                ),
                                                left: BorderSide(
                                                  color: Colors.grey.shade300,
                                                  width: 1,
                                                ),
                                                right: colIndex == state.headers.length - 1
                                                    ? BorderSide(
                                                        color: Colors.grey.shade300,
                                                        width: 1,
                                                      )
                                                    : BorderSide.none,
                                                bottom: rowIndex == state.values.length - 1
                                                    ? BorderSide(
                                                        color: Colors.grey.shade300,
                                                        width: 1,
                                                      )
                                                    : BorderSide.none,
                                              ),
                                            ),
                                            child: Center(
                                              child: Text('N/A'), // Default value
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                  ),
                          ),
                          WStickyBottomCta(
                            isLoading: state.isCtaLoading,
                            isEnabled: !(state.headers.isEmpty || state.values.isEmpty || state.isCtaLoading || state.selectedMandi == null || state.selectedVendorLocation == null || state.selectedSupplyDate == null),
                            icon: Icons.check,
                            label: Text('addProc.placeProcOrderSubmit'.tr('Submit')),
                            onPressed: () {
                              final filteredOps = liveOps.where((e) => e.mandiId == state.selectedMandi!.id);
                              final smoId = filteredOps.firstOrNull?.smoId;
                              if (smoId != null) {
                                context.read<AddParentProcCubit>().onSubmit(smoId, allNonCorporateMandis);
                              }
                            },
                          ),
                        ],
                      ));
                },
              );
            });
          },
        ),
      ),
    );
  }

  void onDownloadCsvClick({required List<Sku> skus}) async {
    final pitstops = await showDialog<List<MandiInfo>?>(context: context, builder: (_) => PitStopPicker());
    if (pitstops != null && pitstops.isNotEmpty) {
      await context.read<AddParentProcCubit>().downloadCsv(pitstops: pitstops, skus: skus);
    }
  }

  void uploadCSV(List<MandiInfo> allMandis) async {
    List<List<dynamic>>? result = await CSVHandler.uploadFromFile();
    if (result != null) {
      context.read<AddParentProcCubit>().onCSVUpload(data: result, allMandis: allMandis);
    }
  }
}
