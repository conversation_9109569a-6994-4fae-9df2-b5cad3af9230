import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/assigned_mandi.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/available_mandi.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/user_role.dart';

part 'user_mandi.freezed.dart';
part 'user_mandi.g.dart';

@freezed
class UserMandi with _$UserMandi {
  const UserMandi._();
  const factory UserMandi({
    required List<AvailableMandi> availableMandis,
    required List<AssignedMandi> assignedMandis,
  }) = _UserMandi;

  factory UserMandi.fromJson(Map<String, dynamic> json) =>
      _$UserMandiFromJson(json);

  List<UserRole> _currentRolesForMandi(int mandiId) {
    final List<UserRole> currentRoles = [];

    assignedMandis.forEach((e) {
      if (e.mandis.contains(mandiId)) {
        currentRoles.add(UserRole(roleId: e.roleId, roleName: e.roleName));
      }
    });
    return currentRoles;
  }

  UserMandi assignRole({
    required List<UserRole> newRoles,
    required int mandiId,
  }) {
    final currentRoles = _currentRolesForMandi(mandiId);

    final rolesToAdd =
        newRoles.where((element) => !currentRoles.contains(element));
    final rolesToDelete =
        currentRoles.where((element) => !newRoles.contains(element));

    List<AssignedMandi> mandis = [...assignedMandis];

    assignedMandis.indexed.forEach((el) {
      int index = el.$1;
      final currentMandi = mandis[index];
      final e = el.$2;
      // Do we need to add it?
      if (rolesToAdd.any((element) => element.roleId == e.roleId)) {
        // Add it
        mandis[index] =
            currentMandi.copyWith(mandis: [...currentMandi.mandis, mandiId]);
      }

      // Do we need to delete it?
      if (rolesToDelete.any((element) => element.roleId == e.roleId)) {
        // delete it
        mandis[index] = currentMandi.copyWith(
            mandis: currentMandi.mandis
                .where((element) => element != mandiId)
                .toList());
      }
    });
    return copyWith(assignedMandis: mandis);
  }
}
