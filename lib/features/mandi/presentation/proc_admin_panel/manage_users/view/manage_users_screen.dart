import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/proc_user.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/cubit/manage_users_cubit.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/view/widgets/add_user_popup.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/view/widgets/search_user_dropdown.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/view/widgets/user_role_mandi_list_popup.dart';

class ManageUsersScreen extends StatefulWidget {
  const ManageUsersScreen({super.key});

  @override
  State<ManageUsersScreen> createState() => _ManageUsersScreenState();
}

class _ManageUsersScreenState extends State<ManageUsersScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ManageUsersCubit, ManageUsersState>(
        builder: (context, state) {
      return SafeArea(
          top: true,
          bottom: true,
          child: Scaffold(
            appBar: AppBar(
              centerTitle: false,
              title: Text(
                "Manage Users",
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(50),
                child: Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(0),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Spacer(),
                      Center(
                        child: SizedBox(
                            width: 200,
                            height: 30,
                            child: SearchUserDropdown(
                                selectedUser: state.selectedUser,
                                onChanged: (ProcUser? user) {
                                  context
                                      .read<ManageUsersCubit>()
                                      .setSelectedUser(user);
                                },
                                userList: state.userList)),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                          onPressed: () {
                            context.read<ManageUsersCubit>().getUserMandis();
                          },
                          child: const Text(
                            'Apply',
                            style: TextStyle(
                                fontSize: 14, fontWeight: FontWeight.w500),
                          )),
                      Spacer(),
                      ElevatedButton.icon(
                          onPressed: () async {
                            await showDialog(
                              context: context,
                              builder: (_) => BlocProvider.value(
                                value: context.read<ManageUsersCubit>(),
                                child: CreateUserPopup(),
                              ),
                            );
                          },
                          icon: const Icon(Icons.add, size: 20),
                          label: const Text(
                            'Add User',
                            style: TextStyle(
                                fontSize: 14, fontWeight: FontWeight.w500),
                          )),
                      const SizedBox(width: 20),
                    ],
                  ),
                ),
              ),
            ),
            body: Builder(builder: (context) {
              // return state.map(
              //   initial: (i) => Center(child: CircularProgressIndicator()),
              // );
              return Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.all(8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 100),
                      child: Visibility(
                        visible: state.userMandi != null,
                        child: Card(
                          elevation: 1,
                          color: Colors.grey.shade200,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 10, right: 10, top: 20, bottom: 20),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  state.selectedUser?.firstName ?? "",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500),
                                ),
                                Text(
                                  state.selectedUser?.email ?? "",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500),
                                ),
                                ElevatedButton(
                                    onPressed: () async {
                                      await showDialog(
                                          context: context,
                                          builder: (_) => BlocProvider.value(
                                                value: context
                                                    .read<ManageUsersCubit>(),
                                                child: UserRoleMandiListPopup(),
                                              ));
                                    },
                                    child: const Text(
                                      'Mandi',
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500),
                                    ))
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              );
            }),
          ));
    });
  }
}
