import 'dart:async';
// import 'package:timezone/data/latest.dart' as tz;
// import 'package:timezone/timezone.dart' as tz;

class TrueDateTime {
  static DateTime? _trueTime;
  static Duration _timeOffset = Duration.zero;
  // static tz.Location? _location;

  static Future<void> initializeTrueTime({
    required FutureOr<int> Function() getTrueEpochSeconds,
    // String timeZone = 'Asia/Kolkata', // Default to IST
  }) async {
    // tz.initializeTimeZones();
    // _location = tz.getLocation(timeZone);

    int? backendEpochSeconds;
    Stopwatch stopwatch = Stopwatch();
    stopwatch.start();
    try {
      backendEpochSeconds = await getTrueEpochSeconds();
      print("Backend epoch seconds: $backendEpochSeconds");
    } catch (e, s) {
      print("Error getting true time: $e\n$s");
    }
    stopwatch.stop();
    int halfNetworkLatencyMs = stopwatch.elapsedMilliseconds ~/ 2;

    if (backendEpochSeconds != null) {
      DateTime backendTime = DateTime.fromMillisecondsSinceEpoch(backendEpochSeconds * 1000);

      backendTime = backendTime.add(Duration(milliseconds: halfNetworkLatencyMs));

      DateTime localTimeNow = DateTime.now();

      _timeOffset = backendTime.difference(localTimeNow);

      _trueTime = backendTime;
    }
  }

  static DateTime now() {
    if (_trueTime == null) {
      return DateTime.now();
    }
    return DateTime.now().add(_timeOffset);
  }

  static DateTime getTrueTimeFromDateTime(DateTime inputTime, String placeName) {
    if (_trueTime == null) {
      return inputTime;
    }
    print("Input time --->$inputTime offset --->$_timeOffset correct time --->${inputTime.add(_timeOffset)} place name --->$placeName");
    return inputTime.add(_timeOffset);
  }

  static DateTime? getTrueTime() {
    return _trueTime;
  }

  // static DateTime getTrueTimeInTimeZone(String timeZone) {
  //   if (_trueTime == null) {
  //     return DateTime.now();
  //   }
  //   tz.Location location = tz.getLocation(timeZone);
  //   tz.TZDateTime trueTimeInZone = tz.TZDateTime.from(_trueTime!, location);
  //   return trueTimeInZone;
  // }
}
