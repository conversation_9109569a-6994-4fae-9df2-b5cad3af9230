import 'package:flutter/material.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/supply_order_input.dart';

class SupplyProductCell extends StatefulWidget {
  SupplyProductCell({
    required this.order,
    required this.isChecked,
    required this.onChanged,
    required this.onEdit,
    required this.total,
  });
  final bool isChecked;
  final ValueChanged<bool?> onChanged;
  final GestureTapCallback? onEdit;
  final SupplyOrderSku order;
  final String total;

  @override
  State<SupplyProductCell> createState() => _SupplyOrderCellState();
}

class _SupplyOrderCellState extends State<SupplyProductCell> {
  bool hasMouseOver = false;
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant SupplyProductCell oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (e) async {
        if (hasMouseOver != true) {
          setState(() {
            hasMouseOver = true;
          });
          if (!focusNode.hasFocus) {
            await Future.delayed(Duration(milliseconds: 60));
            focusNode.requestFocus();
          }
        }
      },
      onExit: (e) {
        setState(() {
          hasMouseOver = false;
        });
      },
      child: Container(
        width: 200,
        height: 80,
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          boxShadow: hasMouseOver
              ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ]
              : [],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.order.skuName,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          softWrap: true,
                        ),
                        SizedBox(
                          height: 2,
                        ),
                        if (widget.order.skuQuantity.isBulk)
                          Text(
                            'Bulk - ${widget.order.skuQuantity.unit}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                        if (!widget.order.skuQuantity.isBulk)
                          Text(
                            'Lot Size: ${widget.order.skuQuantity.lotSize} ${widget.order.skuQuantity.unit}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                        Text(
                          'Total: ${widget.total} ${widget.order.skuQuantity.isBulk ? widget.order.skuQuantity.unit : 'Lots'}',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                        SizedBox(
                          height: 2,
                        ),
                      ],
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: SizedBox(
                        height: 64,
                        width: 48,
                        child: Column(
                          children: [
                            if (hasMouseOver || widget.isChecked)
                              Checkbox(
                                value: widget.isChecked,
                                onChanged: widget.onChanged,
                              ),
                            SizedBox(
                              height: 8,
                            ),
                            if (hasMouseOver)
                              InkWell(
                                child: Icon(Icons.edit),
                                onTap: widget.onEdit,
                              )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
