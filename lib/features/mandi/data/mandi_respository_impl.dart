import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:either_dart/either.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/mandi_data_source.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/add_procurement_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/allotment_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/carry_forward_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/charges_type_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/incoming_consignments_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/language_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/mandi_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/sku_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/smo_history_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/smo_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/to_inventory_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_allotment_v2_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_loss_init_v2_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_close_presigned_url_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_loss_presigned_url_request%20copy.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_proc_detail_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_my_proc_summary_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_ops_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_proc_item_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_smo_detail_v2_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_smo_history_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_supply_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/alloted_item.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_init.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_v2.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/trip.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_item.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_loss.dart';
import 'package:proc2/features/mandi/domain/entity/charges/charges_type.dart';
import 'package:proc2/features/mandi/domain/entity/charges/field_charges.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';
import 'package:proc2/features/mandi/domain/entity/lang/supported_language.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_types.dart';
import 'package:proc2/features/mandi/domain/entity/lotting/lotting_deviation.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/mandi_inventory/mandi_inventory.dart';
import 'package:proc2/features/mandi/domain/entity/mandi_inventory/mandi_inventory_sku.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/my_proc_summary.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/procurement_data.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/sku_types/sku_types.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_detail_v2.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_history.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order_metadata.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/ir_sku_edit.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/input_model/inventory_item_input_model.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/input_model/mandi_inventory_input_model.dart';

@prod
@dev
@Injectable(as: MandiRepository)
class MandiRepositoryImpl implements MandiRepository {
  MandiRepositoryImpl(this._mandiDataSource);

  final MandiDataSource _mandiDataSource;

  @override
  Future<Either<ErrorResult<void>, String>> addProcurement(
    ProcurementData data,
    int vendorLocationId,
    List<ProcFieldCharge> fieldCharges,
    String comments,
    DateTime? supplyDate,
  ) {
    final json = procurementDataToJson(
      data,
      vendorLocationId,
      fieldCharges,
      comments,
      supplyDate,
    );
    return _mandiDataSource.addProcurement(json);
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> createSmo(String mandiId) {
    return _mandiDataSource.createSmo(mandiId).then(
          (value) => value.then(
            (right) => Right(right.smoId),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<MandiInfo>>> getAllMandis() {
    return _mandiDataSource.getAllMandis().then(
          (value) => value.then(
            (right) => Right(right.map(toMandiInfo).toList()),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<Sku>>> getSku(String language) {
    return _mandiDataSource.getSku(language).then(
          (value) => value.then(
            (right) => Right(
              right.map(toSku).toList(),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, Smo>> getSmo(String mandiId) {
    return _mandiDataSource.getSmo(mandiId).then(
          (value) => value.then(
            (right) => Right(toSmo(right)),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<int>>> getUserMandis() {
    return _mandiDataSource.getUserMandis().then(
          (value) => value.then(
            (right) => Right(right.mandiIds),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<Consignment>>> getIncomingStockList(
    int mandiId,
  ) {
    return _mandiDataSource.getIncomingStockList(mandiId).then(
          (value) => value.then(
            (right) => Right(
              toIncomingConsignmentsList(right),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<void>, CarryForward>> getCarryForward({
    required int smoId,
  }) {
    return _mandiDataSource.getCarryForward(smoId: smoId).then(
          (value) => value.then(
            (right) => Right(
              toCarryForward(right),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<void>, ChargesType>> getChargesType() {
    return _mandiDataSource.getChargesType().then(
          (value) => value.then(
            (right) => Right(
              toChargeType(right),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<void>, FieldCharges>> getFieldCharges(
    int smoId,
  ) {
    return _mandiDataSource.getFieldCharges(smoId).then(
          (value) => value.then(
            (right) => Right(FieldCharges.fromJson(right)),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<void>, LossTypes>> getLossInit() {
    return _mandiDataSource.getLossInit().then(
          (value) => value.then(
            (right) => Right(
              LossTypes(lossTypes: right.lossType, opsStages: right.opsStage),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<void>, Map<String, List<LossTypeV2>>>> getLossInitV2() {
    return GetLossInitV2Request().execute();
  }

  @override
  Future<Either<ErrorResult<void>, SkuTypes>> getSkuTypes() {
    return _mandiDataSource.getSkuTypes().then(
          (value) => value.then(
            (right) => Right(
              SkuTypes(
                quantityTypes: right.quantityTypes,
                unitTypes: right.unitTypes,
              ),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> acceptIncomingStocks(int smoId, InventoryRecievingInputModel inventoryRecievingInputModel) async {
    final updatedItem = await uploadIncoming(item: inventoryRecievingInputModel, smoId: smoId);
    return _mandiDataSource
        .acceptIncomingStocks(
          smoId,
          updatedItem,
        )
        .then((val) => val.then((right) => const Right(null)));
  }

  Future<InventoryRecievingInputModel> uploadIncoming({required InventoryRecievingInputModel item, required int smoId}) async {
    final bulkFuture = item.bulk.map((e) => updateIRSKU(item: e, smoId: smoId)).toList();
    final lotFuture = item.lots.map((e) => updateIRSKU(item: e, smoId: smoId)).toList();

    final bulk = await Future.wait(bulkFuture);
    final lots = await Future.wait(lotFuture);

    return Future.value(item.copyWith(bulk: bulk, lots: lots));
  }

  Future<IRSKUEditInputModel> updateIRSKU({required IRSKUEditInputModel item, required int smoId}) async {
    final futures = item.losses.map((e) => uploadLossInput(loss: e, smoId: smoId, opsStage: 'INCOMING_STOCK'));
    final result = await Future.wait(futures);
    return Future.value(item.copyWith(
      losses: result,
    ));
  }

  @override
  Future<Either<ErrorResult<void>, String>> addCharges({
    required int smoId,
    required List<String> files,
    required List<Map<String, dynamic>> charges,
  }) {
    final json = <String, dynamic>{
      'smoId': smoId,
      'images': files,
      'charges': charges,
    };
    return _mandiDataSource.addCharges(json);
  }

  @override
  Future<Either<ErrorResult<dynamic>, MandiInventory>> getMandiInventory(
    int mandiId,
  ) {
    return _mandiDataSource.getMandiInventory(mandiId).then(
          (value) => value.then((right) {
            // ignore: prefer_final_locals, omit_local_variable_types
            List<MandiInventorySku> skuList = [];
            for (final sku in right.skuList) {
              skuList.add(
                MandiInventorySku(
                  skuId: sku.skuId,
                  type: sku.type,
                  unit: sku.unit,
                  lotSize: sku.lotSize,
                  quantity: sku.quantity,
                ),
              );
            }
            return Right(MandiInventory(mandiInventoryList: skuList));
          }),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> updateMandiInventory(
    MandiInventoryInputModel inputModel,
  ) {
    return _mandiDataSource.updateMandiInventory(inputModel).then(
          (value) => value.then(
            (right) => const Right(null),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> acceptCarryForward({
    required CarryForward carryForward,
    required int smoId,
  }) async {
    final itemsFuture = [
      ...carryForward.bulkItems,
      ...carryForward.lotItems
    ].where((e) => e.getLoss() > 0.0 || e.getShortOrExcess() > 0.0).map((e) => uploadLoss(item: e, smoId: smoId)).toList();
    try {
      final items = await Future.wait(itemsFuture);

      final json = {
        'carryForwardId': carryForward.id,
        'smoId': smoId,
        'comments': carryForward.comments,
        'itemsWithLoss': carryForwardItemWithLossJson(items).toList(),
      };
      return _mandiDataSource.acceptCarryForward(json);
    } catch (e) {
      return Future.value(Left(ErrorResult(message: e.toString(), code: '', timestamp: DateTime.now().microsecond)));
    }
  }

  Future<CarryForwardItem> uploadLoss({required CarryForwardItem item, required int smoId}) async {
    final futures = item.loss.map((e) => LossInputModel(lossType: e.lossType, lossValue: e.lossValue, unit: e.unit, comment: e.comment, files: e.files)).map((e) => uploadLossInput(loss: e, smoId: smoId, opsStage: 'CARRY_FORWARD'));
    final result = await Future.wait(futures);
    return Future.value(item.copyWith(loss: result.map((e) => CarryForwardLoss(lossType: e.lossType, lossValue: e.lossValue, unit: e.unit, comment: e.comment, files: e.files)).toList()));
  }

  Future<LossInputModel> uploadLossInput({required LossInputModel loss, required int smoId, required String opsStage}) async {
    final nonUploadedFiles = loss.files.where((element) => !element.isUploaded && element.file != null).toList();
    if (nonUploadedFiles.isEmpty) return Future.value(loss);
    final allExtension = nonUploadedFiles.map((e) => e.file!.extension ?? 'jpeg').toList();
    final urls = await GetLossPreSignedUrlRequest(smoId: smoId, fileExtensions: allExtension, lossType: loss.lossType, opsStage: opsStage).execute();
    final keys = urls.right.keys.toList();

    if (urls.isLeft || keys.length != nonUploadedFiles.length) {
      throw 'Error while generating presigned url';
    }
    for (int i = 0; i < nonUploadedFiles.length; i++) {
      nonUploadedFiles[i] = nonUploadedFiles[i].copyWith(uploadKey: keys[i]);
    }
    final uploadResultFuture = nonUploadedFiles.map((e) => uploadFile(e, urls.right[e.uploadKey!]!)).toList();

    final uploadResult = await Future.wait(uploadResultFuture);
    final allFiles = loss.files.map((e) => uploadResult.firstWhere((element) => element.uuid == e.uuid, orElse: () => e));
    return Future.value(loss.copyWith(files: allFiles.toList()));
  }

  Future<PickedFile> uploadFile(PickedFile file, String url) async {
    final fileBytes = kIsWeb ? file.file!.bytes! : File(file.file!.path!).readAsBytesSync();

    final response = await http.put(Uri.parse(url), body: fileBytes);

    // Check the response status code.
    if (response.statusCode == 200) {
      return Future.value(file.copyWith(isUploaded: true));
    } else {
      // The file was not uploaded successfully.
      return Future.value(file);
    }
  }

  @override
  Future<Either<ErrorResult<dynamic>, Inventory>> getInventory(int mandiId, {String type = 'primary'}) {
    return _mandiDataSource.getInventory(mandiId, type).then(
          (value) => value.then(
            (right) => Right(toInventory(right)),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> closeInventory(
    int smoId,
    int mandiId,
    List<SkuInputWithLoss> data,
  ) async {
    final newDataFuture = data.map((e) => uploadClose(item: e, smoId: smoId)).toList();
    final newData = await Future.wait(newDataFuture);

    return _mandiDataSource.closeInventory(smoId, mandiId, newData).then(
          (value) => value.then(
            (right) => const Right(null),
          ),
        );
  }

  Future<SkuInputWithLoss> uploadClose({required SkuInputWithLoss item, required int smoId}) async {
    final futures = item.losses.map((e) => uploadLossInput(loss: e, smoId: smoId, opsStage: 'CLOSING_STOCK'));
    final result = await Future.wait(futures);

    final nonUploadedFiles = item.files.where((element) => !element.isUploaded && element.file != null).toList();
    if (nonUploadedFiles.isEmpty) return Future.value(item.copyWith(losses: result));
    final allExtension = nonUploadedFiles.map((e) => e.file!.extension ?? 'jpeg').toList();
    final urls = await GetClosePreSignedUrlRequest(
      smoId: smoId,
      fileExtensions: allExtension,
    ).execute();
    final keys = urls.right.keys.toList();

    if (urls.isLeft || keys.length != nonUploadedFiles.length) {
      throw 'Error while generating presigned url';
    }
    for (int i = 0; i < nonUploadedFiles.length; i++) {
      nonUploadedFiles[i] = nonUploadedFiles[i].copyWith(uploadKey: keys[i]);
    }
    final uploadResultFuture = nonUploadedFiles.map((e) => uploadFile(e, urls.right[e.uploadKey!]!)).toList();

    final uploadResult = await Future.wait(uploadResultFuture);
    final allFiles = item.files.map((e) => uploadResult.firstWhere((element) => element.uuid == e.uuid, orElse: () => e));

    return Future.value(item.copyWith(losses: result, files: allFiles.toList()));
  }

  @override
  Future<Either<ErrorResult<dynamic>, AllotmentInit>> getAllotmentInit(
    int smoId,
  ) {
    return _mandiDataSource.getAllotmentInit(smoId).then(
          (value) => value.then(
            (right) => Right(fromAllotmentInitDto(right)),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, int>> createAllotment({
    required int smoId,
    required int destinationId,
    required String destinationType,
    required int deliveryDate,
    required String deliverySlot,
    required String? customerGroup,
  }) {
    return _mandiDataSource.createAllotment(
      smoId: smoId,
      destinationId: destinationId,
      destinationType: destinationType,
      deliveryDate: deliveryDate,
      deliverySlot: deliverySlot,
      customerGroup: customerGroup,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<Allotment>>> getAllotments(
    int smoId,
  ) {
    return _mandiDataSource.getAllotments(smoId).then(
          (value) => value.then(
            (right) => Right(
              right.map(toAllotment).toList(),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult, List<List<AllotmentV2>>>> getAllotmentsV2(int smoId, AllotmentInit init) {
    return GetAllotmentV2Request(smoId: smoId, init: init).execute();
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<AllotedItem>>> getAllotedItems(int allotmentId) {
    return _mandiDataSource.getAllotedItems(allotmentId).then(
          (value) => value.then(
            (right) => Right(
              right.map(toAllotedItem).toList(),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> submitAllotment(
    Map<String, dynamic> body,
  ) {
    return _mandiDataSource.submitAllotment(body);
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<SmoHistory>>> getSmoHistory() {
    return _mandiDataSource.getSmoHistory().then(
          (value) => value.then(
            (right) => Right(
              right.map(toSmoHistory).toList(),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<SmoDetail>>> getSmoDetails(
    int smoId,
  ) {
    return _mandiDataSource.getSmoDetails(smoId).then(
          (value) => value.then(
            (right) => Right(
              right.map(toSmoDetail).toList(),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> updateOrSubmitEditProcurement(
    Map<String, dynamic> body, {
    bool isSubmit = false,
  }) {
    return _mandiDataSource.updateOrSubmitEditProcurement(
      body,
      isSubmit: isSubmit,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> submitConversion({
    required int smoId,
    required List<InventoryItemInputModel> from,
    required List<InventoryItemInputModel> to,
    required List<LossInputModel> losses,
    required bool isLotting,
  }) async {
    final futures = losses.where((element) => element.lossValue.toDouble() > 0).map((e) => uploadLossInput(loss: e, smoId: smoId, opsStage: 'CONVERSION'));
    final lossesResult = await Future.wait(futures);

    final body = {
      'smoId': smoId,
      'conversionType': isLotting ? 'LOTTING' : 'DELOTTING',
      'from': from
          .map(
            (e) => isLotting
                ? {
                    'skuId': e.skuId,
                    'type': e.type,
                    'unit': e.unit,
                    'quantity': e.quantityInput.toDouble(),
                  }
                : {
                    'skuId': e.skuId,
                    'type': e.type,
                    'unit': e.unit,
                    'quantity': e.quantityInput.toDouble(),
                    'lotSize': e.lotSize,
                  },
          )
          .toList(),
      'to': to
          .map(
            (e) => isLotting
                ? {
                    'skuId': e.skuId,
                    'type': 'LOTS',
                    'unit': e.unit,
                    'quantity': e.quantityInput.toDouble(),
                    'lotSize': e.lotSizeInput.toDouble(),
                  }
                : {
                    'skuId': e.skuId,
                    'type': 'BULK',
                    'unit': e.unit,
                    'quantity': e.quantityInput.toDouble(),
                  },
          )
          .toList(),
      'losses': [
        if (isLotting)
          {
            'skuId': from[0].skuId,
            'type': from[0].type,
            'unit': from[0].unit,
            'losses': lossesResult
                .map(
                  (e) => {
                    'comment': e.comment,
                    'lossType': e.lossType,
                    'quantity': e.lossValue.toDouble(),
                    'images': e.files
                        .where((e) => e.uploadKey != null)
                        .map(
                          (e) => e.uploadKey,
                        )
                        .toList(),
                  },
                )
                .toList(),
          }
        else
          {
            'skuId': to[0].skuId,
            'type': to[0].type,
            'unit': to[0].unit,
            'lotSize': to[0].lotSize,
            'losses': lossesResult
                .map(
                  (e) => {
                    'comment': e.comment,
                    'lossType': e.lossType,
                    'quantity': e.lossValue.toDouble(),
                    'images': e.files
                        .where((e) => e.uploadKey != null)
                        .map(
                          (e) => e.uploadKey,
                        )
                        .toList(),
                  },
                )
                .toList(),
          },
      ],
    };
    return _mandiDataSource.submitConversion(body);
  }

  Future<Either<ErrorResult<dynamic>, void>> closeSmoOps(
    int smoId,
  ) {
    return _mandiDataSource.closeSmoOps(smoId).then(
          (value) => value.then(
            (r) => const Right(null),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<Trip>>> getTripsForAllocations(
    int allocationId,
  ) {
    return _mandiDataSource.getTripsForAllocation(allocationId).then(
          (value) => value.then(
            (right) => Right(
              right.map(toTrip).toList(),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> dispatchTrip({
    required int tripId,
    required String driverName,
    required String driverPhone,
    required String vehicleNumber,
    required int smoId,
  }) {
    return _mandiDataSource.dispatchTrip(
      tripId: tripId,
      driverName: driverName,
      driverPhone: driverPhone,
      vehicleNumber: vehicleNumber,
      smoId: smoId,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> cancelAllocation(int allocationId) {
    return _mandiDataSource.cancelAllocation(allocationId);
  }

  @override
  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getLangFile(
    String languageCode,
  ) {
    return _mandiDataSource.getLanguageFile(languageCode);
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<SupportedLanguage>>> getLanguages() {
    return _mandiDataSource.getLanguages().then(
          (value) => value.then(
            (value) => Right(
              toSupportedLanguage(
                value,
              ),
            ),
          ),
        );
  }

  @override
  Future<Either<ErrorResult<dynamic>, LottingDeviation>> getLottingDeviation() {
    return _mandiDataSource.getLottingDeviation();
  }

  @override
  Future<Either<ErrorResult<dynamic>, bool>> regenerateDeliveryMemo(
    int allotmentId,
    String langCode,
  ) {
    return _mandiDataSource.regenerateDeliveryMemo(allotmentId, langCode);
  }

  @override
  Future<Either<ErrorResult, List<SmoOps>>> getSmoOps(String status) {
    GetOpsRequest request = GetOpsRequest(status);
    return request.execute();
  }

  @override
  Future<Either<ErrorResult, List<List<SmoOps>>>> smoHisoty() {
    GetSmoHistoryRequest request = GetSmoHistoryRequest();
    return request.execute();
  }

  @override
  Future<Either<ErrorResult, SmoDetailV2>> getSmoDetailV2(int smoId) {
    GetSmoDetailV2Request request = GetSmoDetailV2Request(smoId);
    return request.execute();
  }

  @override
  Future<Either<ErrorResult, List<MyProcSummary>>> getMyProcSummary(int smoId) {
    return GetMyProcSummaryRequest(smoId).execute();
  }

  @override
  Future<Either<ErrorResult, List<ProcDetail>>> getMyProcDetail(int smoId) {
    return GetMyProcDetailRequest(smoId: smoId, isAll: false, status: null, limit: null, offset: null).execute();
  }

  @override
  Future<Either<ErrorResult, List<ProcDetail>>> getAllProcDetail(int smoId, String? status, int? limit, int? offset) {
    return GetMyProcDetailRequest(smoId: smoId, isAll: true, status: status, limit: limit, offset: offset).execute();
  }

  @override
  Future<Either<ErrorResult, List<List<ProcItem>>>> getProcItem({
    required int procId,
    required int smoId,
  }) {
    return GetProcItemRequest(procId, smoId).execute();
  }

  @override
  Future<Either<ErrorResult, SupplyOrderMetadata>> getSupplyOrders({
    required DateTime deliveryDate,
    required String deliverySlot,
    required String customerGroup,
    required bool forAdmin,
  }) {
    return GetSupplyOrderRequest(
      deliveryEpochSeconds: deliveryDate.millisecondsSinceEpoch ~/ 1000,
      deliverySlot: deliverySlot,
      customerGroup: customerGroup,
      forAdmin: forAdmin,
    ).execute();
  }
}
