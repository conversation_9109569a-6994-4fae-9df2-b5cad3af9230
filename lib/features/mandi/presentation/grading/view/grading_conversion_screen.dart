import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/grading/cubit/grading_conversion_cubit.dart';
import 'package:proc2/features/mandi/presentation/grading/view/grading_add_sku_popup.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/inventory_type.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class GradingConversionScreen extends StatefulWidget {
  const GradingConversionScreen({
    super.key,
    required this.mandiId,
    required this.smoId,
    required this.inventoryType,
    required this.title,
    required this.allowGrading,
    required this.config,
  });
  final int mandiId;
  final int smoId;
  final InventoryType inventoryType;
  final String? title;
  final bool allowGrading;
  final ConversionInventoryConfig config;
  @override
  State<GradingConversionScreen> createState() =>
      _GradingConversionScreenState();
}

class _GradingConversionScreenState extends State<GradingConversionScreen> {
  final TextEditingController _skuSearchController = TextEditingController();
  final TextEditingController _qtyController = TextEditingController();
  WeighingQuantity _quantity = WeighingQuantity.noSouce(null);
  final TextEditingController _dumpController = TextEditingController();
  WeighingQuantity _dumpQty = WeighingQuantity.noSouce(null);

  initState() {
    _skuSearchController.addListener(skuChangeListener);

    super.initState();
  }

  @override
  void dispose() {
    _skuSearchController.removeListener(skuChangeListener);
    _skuSearchController.dispose();
    _qtyController.dispose();
    _dumpController.dispose();
    super.dispose();
  }

  void skuChangeListener() async {
    final val = _skuSearchController.text;
    if (val.isEmpty) return;
    final inventory = context.read<GradingConversionCubit>().state.allInventory;
    if (inventory == null) return;
    final success = context.read<SkuBloc>().state.maybeMap(
          orElse: () => null,
          success: (s) => s,
        );
    if (success == null) return;
    final splits = val.split('-');
    if (splits.isNotEmpty) {
      final skuId =
          success.skus.firstWhere((element) => element.name == splits[0]).id;
      final isBulk = splits[1].toLowerCase() == 'bulk';
      InventoryItem? item;
      if (isBulk) {
        final unit = splits[2];
        final inventoryItem = inventory
            .where((e) =>
                e.skuId == skuId &&
                e.type.toLowerCase() == splits[1].toLowerCase() &&
                e.unit.toLowerCase() == unit.toLowerCase())
            .firstOrNull;
        item = inventoryItem;
      } else {
        final lotSize = splits[2];
        final unit = splits[3];
        final inventoryItem = inventory
            .where((e) =>
                e.skuId == skuId &&
                e.type.toLowerCase() == splits[1].toLowerCase() &&
                e.unit.toLowerCase() == unit.toLowerCase() &&
                e.lotSize.toString() == lotSize.toLowerCase())
            .firstOrNull;
        item = inventoryItem;
      }
      if (item != null) {
        await Future.delayed(
          Duration(
            milliseconds: 300,
          ),
        );
        context.read<GradingConversionCubit>().fromSkuSelected(item);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.grey.shade100,
        appBar: WAppBar.getAppBar(
          title: widget.title != null
              ? Text(widget.title!)
              : LangText('gradingConversion.title', 'Grading Conversion'),
          centerTitle: false,
        ),
        body: BlocBuilder<SkuBloc, SkuState>(
          builder: (context, skuState) {
            return skuState.map(
              initial: (initial) => Center(
                child: CircularProgressIndicator(),
              ),
              success: (success) {
                return BlocConsumer<GradingConversionCubit,
                    GradingConversionState>(
                  listener: (context, state) {
                    final shouldPop = state.shouldPop;
                    if (state.message != null) {
                      showSnackBar(state.message!);
                      context.read<GradingConversionCubit>().clearMessage();
                    }
                    if (shouldPop) {
                      context.pop(true);
                    }
                  },
                  builder: (context, state) {
                    final inventory = state.allInventory;
                    final inventoryDeviation = state.gradingInventoryDeviation;
                    final conversionDeviation =
                        state.gradingConversionDeviation;

                    if (inventory == null ||
                        inventoryDeviation == null ||
                        conversionDeviation == null)
                      return Center(
                        child: CircularProgressIndicator(),
                      );
                    if (inventory.isEmpty)
                      return EmptyScreen(
                        message: 'No Inventory Found!',
                      );
                    if (inventoryDeviation == -1 || conversionDeviation == -1)
                      return ErrorScreen(
                        heading: getLangText(
                            'gradingConversion.configLoadError',
                            'Error while loading configuration!'),
                        onPressed: () {
                          context
                              .read<GradingConversionCubit>()
                              .loadDeviation();
                        },
                      );

                    final maxAllowedInventoryQty =
                        (state.fromInventory?.quantity ?? 0) *
                            (1 + inventoryDeviation);
                    final isMoreThanMaxQty =
                        _qtyController.text.toDouble() > maxAllowedInventoryQty;

                    final maxAllowedConversionQty =
                        _qtyController.text.toDouble() *
                            (1 + conversionDeviation);
                    final toInventoryQtySum = state.fromInventory == null
                        ? null
                        : sumOfToSkusQty(
                            state.fromInventory!.unit, state.toInventory);
                    final isMoreThanMaxConversionQty = toInventoryQtySum == null
                        ? false
                        : (toInventoryQtySum +
                                _dumpController.text.toDouble()) >
                            maxAllowedConversionQty;

                    return Column(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                            child: ListView(
                              shrinkWrap: true,
                              children: [
                                Card(
                                  color: Colors.white,
                                  elevation: 4,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade300,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(8),
                                            topRight: Radius.circular(8),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 8,
                                          ),
                                          child: Text(
                                            widget.inventoryType.name +
                                                ' Inventory',
                                            style: TextStyle(
                                              fontSize: 15,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 8,
                                          horizontal: 16,
                                        ),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            state.fromInventory == null
                                                ? CustomDropdown.search(
                                                    items: inventory
                                                        .map((e) =>
                                                            getSKU(context,
                                                                    skuID:
                                                                        e.skuId)
                                                                .name +
                                                            '-' +
                                                            e.unitString())
                                                        .toList(),
                                                    controller:
                                                        _skuSearchController,
                                                    hintText: getLangText(
                                                      'gradingConversion.selectSku',
                                                      'Select Sku',
                                                    ),
                                                    onChanged: (val) async {},
                                                  )
                                                : Container(
                                                    margin: EdgeInsets.only(
                                                      bottom: 8,
                                                      top: 4,
                                                    ),
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                      vertical: 8,
                                                      horizontal: 16,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          Colors.grey.shade100,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    child: InkWell(
                                                      onTap: () async {
                                                        if (state.isCtaLoading)
                                                          return;
                                                        final shouldClear =
                                                            await context
                                                                    .showAlertDialog(
                                                                  title:
                                                                      getLangText(
                                                                    'gradingConversion.clearSkuPopupTitle',
                                                                    'Clear Select Sku?',
                                                                  ),
                                                                  message:
                                                                      getLangText(
                                                                    'gradingConversion.clearSkuPopupMessage',
                                                                    'Are you sure you want to clear selected Sku? All data will be cleared!',
                                                                  ),
                                                                ) ??
                                                                false;
                                                        if (shouldClear) {
                                                          context
                                                              .read<
                                                                  GradingConversionCubit>()
                                                              .fromSkuSelected(
                                                                  null);
                                                          _skuSearchController
                                                              .clear();
                                                          _dumpController
                                                              .clear();
                                                          _qtyController
                                                              .clear();
                                                        }
                                                      },
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text(
                                                            _skuSearchController
                                                                .text,
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.black,
                                                              fontSize: 15,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                            ),
                                                          ),
                                                          Icon(
                                                            Icons.edit,
                                                            size: 18,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                            if (state.fromInventory !=
                                                null) ...[
                                              Divider(),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Expanded(
                                                    child: TextFormField(
                                                      enabled: false,
                                                      initialValue: state
                                                          .fromInventory!
                                                          .quantity
                                                          .asString(),
                                                      decoration:
                                                          InputDecoration(
                                                              label: state
                                                                      .fromInventory!
                                                                      .isBulk()
                                                                  ? LangText(
                                                                      'gradingConversion.availableQty',
                                                                      'Available Qty')
                                                                  : LangText(
                                                                      'gradingConversion.availableLot',
                                                                      'Available Lot'),
                                                              isDense: true,
                                                              enabled: false,
                                                              contentPadding:
                                                                  EdgeInsets
                                                                      .symmetric(
                                                                horizontal: 16,
                                                                vertical: 8,
                                                              ),
                                                              border:
                                                                  OutlineInputBorder()),
                                                      textAlign: TextAlign.end,
                                                      keyboardType:
                                                          TextInputType.number,
                                                      textInputAction:
                                                          TextInputAction.next,
                                                      inputFormatters: Config
                                                          .getNumberFilter(
                                                              isBulk: state
                                                                  .fromInventory!
                                                                  .isBulk(),
                                                              unit: state
                                                                  .fromInventory!
                                                                  .unit),
                                                      style: TextStyle(
                                                        fontSize: 15,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                      onChanged: (val) {},
                                                      // add suffix icon
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 16,
                                                  ),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(state.fromInventory!
                                                                .isBulk()
                                                            ? getLangText(
                                                                'gradingConversion.qtyToGrade',
                                                                'Qty to Grade*')
                                                            : getLangText(
                                                                'gradingConversion.lotsToGrade',
                                                                'No. of Lots to Grade*')),
                                                        WeighingQuantityButton(
                                                          isReadOnly: state
                                                              .isCtaLoading,
                                                          label:
                                                              _quantity.value ??
                                                                  '',
                                                          popupBuilder: () {
                                                            return WeightCapturePopupProcurementReceive(
                                                              isBulkKg: state
                                                                  .fromInventory!
                                                                  .isBulkKg(),
                                                              isManualEditAllowed:
                                                                  !(widget
                                                                      .config
                                                                      .isEditOnlyFromWeighingMachine),
                                                              initialWeight:
                                                                  _qtyController
                                                                      .text
                                                                      .toDouble()
                                                                      .asString()
                                                                      .toDouble(),
                                                              skuName: getSKU(
                                                                      context,
                                                                      skuID: state
                                                                          .fromInventory!
                                                                          .skuId)
                                                                  .name,
                                                              unitInfo: state
                                                                  .fromInventory!
                                                                  .unitString(),
                                                            );
                                                          },
                                                          onChange: (quantity) {
                                                            if (quantity !=
                                                                null) {
                                                              setState(() {
                                                                _qtyController
                                                                        .text =
                                                                    quantity.value ??
                                                                        '';
                                                                _quantity =
                                                                    quantity;
                                                              });
                                                            }
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              if (_qtyController.text
                                                      .toDouble() >
                                                  state.fromInventory!.quantity)
                                                Container(
                                                  margin: EdgeInsets.only(
                                                    top: 16,
                                                  ),
                                                  padding: EdgeInsets.symmetric(
                                                    vertical: 4,
                                                  ),
                                                  width: double.infinity,
                                                  decoration: BoxDecoration(
                                                    color: isMoreThanMaxQty
                                                        ? Colors.red.shade50
                                                        : Colors.blue.shade50,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  child: Text(
                                                    isMoreThanMaxQty
                                                        ? 'Check again, quantity more than mandi inventory not allowed!'
                                                        : 'You have added more qty than inventory!',
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              SizedBox(
                                                height: 8,
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (state.fromInventory != null) ...[
                                  SizedBox(
                                    height: 8,
                                  ),
                                  Align(
                                    child: Icon(
                                      Icons.arrow_downward_rounded,
                                      size: 40,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 8,
                                  ),
                                  Card(
                                    color: Colors.white,
                                    elevation: 4,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Column(
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            color: Colors.grey.shade300,
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(8),
                                              topRight: Radius.circular(8),
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 8,
                                            ),
                                            child: Text(
                                              'Mandi Inventory',
                                              style: TextStyle(
                                                fontSize: 15,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                        if (state.toInventory.isNotEmpty)
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 8,
                                              horizontal: 8,
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8.0),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: LangText(
                                                      'unit',
                                                      'Unit',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 8,
                                                  ),
                                                  Expanded(
                                                    child: LangText(
                                                      'quantity',
                                                      'Quantity',
                                                      textAlign: TextAlign.end,
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 80,
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8,
                                            horizontal: 8,
                                          ),
                                          child: Scrollbar(
                                            child: Column(
                                              // shrinkWrap: true,
                                              children: [
                                                skuCard(
                                                  state,
                                                  success.skus,
                                                ),
                                                Builder(builder: (context) {
                                                  final hasRelatedSku =
                                                      getRemainingToSkus(
                                                    state,
                                                    success.skus,
                                                  );

                                                  if (hasRelatedSku.isEmpty)
                                                    return SizedBox();
                                                  return Align(
                                                    alignment:
                                                        Alignment.centerRight,
                                                    child: ElevatedButton.icon(
                                                      icon: Icon(Icons.add),
                                                      onPressed: () async {
                                                        if (state.isCtaLoading)
                                                          return;
                                                        final result =
                                                            await showDialog<
                                                                GradingInput>(
                                                          context: context,
                                                          builder: (ctx) =>
                                                              GradingAddSkuPopup(
                                                            smoId: widget.smoId,
                                                            skus: hasRelatedSku,
                                                            compositeKey: state
                                                                .toInventory
                                                                .map((e) => e
                                                                    .skuQuantity
                                                                    .compositeKey)
                                                                .toList(),
                                                            skuImages: [],
                                                            config:
                                                                widget.config,
                                                          ),
                                                        );
                                                        if (result != null) {
                                                          context
                                                              .read<
                                                                  GradingConversionCubit>()
                                                              .addToInventory(
                                                                  result);
                                                          setState(() {});
                                                        }
                                                      },
                                                      label: LangText(
                                                        'gradingConversion.addSku',
                                                        'Add Sku',
                                                      ),
                                                    ),
                                                  );
                                                }),
                                                Divider(),
                                                dumpCard(
                                                  files: state.dumpFiles,
                                                  unitInfo: state.fromInventory!
                                                      .unitString(),
                                                  isEnabled:
                                                      !state.isCtaLoading,
                                                  isBulkKg: state.fromInventory!
                                                      .isBulkKg(),
                                                  skuId: state
                                                      .fromInventory!.skuId,
                                                ),
                                                Builder(builder: (ctx) {
                                                  double qty = _qtyController
                                                      .text
                                                      .toDouble();

                                                  double dump = _dumpController
                                                      .text
                                                      .toDouble();
                                                  double total =
                                                      (toInventoryQtySum ?? 0) +
                                                          dump;
                                                  Color c = Colors.white;
                                                  String text = '';
                                                  if (total < qty) {
                                                    c = Colors.yellow.shade50;
                                                    text =
                                                        'There is an untracked qty of ${(qty - total).asString()}';
                                                    // Yellow
                                                  } else if (qty == total) {
                                                    return SizedBox();
                                                  } else if (total <=
                                                      maxAllowedConversionQty) {
                                                    // Blue
                                                    c = Colors.blue.shade50;
                                                    text =
                                                        'You are converting more quantity than added!';
                                                  } else if (total >
                                                      maxAllowedConversionQty
                                                          .asString()
                                                          .toDouble()) {
                                                    // Red
                                                    c = Colors.red.shade50;
                                                    text = 'Check again, total more than input not allowed for ' +
                                                        (widget.allowGrading
                                                            ? 'grading!'
                                                            : 'lotting/delotting!');
                                                  }
                                                  return Container(
                                                    margin: EdgeInsets.only(
                                                      top: 16,
                                                    ),
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                      vertical: 4,
                                                    ),
                                                    width: double.infinity,
                                                    decoration: BoxDecoration(
                                                      color: c,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    child: Text(
                                                      text,
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  );
                                                }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                        WStickyBottomCta(
                          isEnabled: _qtyController.text.isNotEmpty &&
                              !state.isCtaLoading &&
                              !isMoreThanMaxQty &&
                              !isMoreThanMaxConversionQty,
                          isLoading: state.isCtaLoading,
                          icon: Icons.check,
                          label:
                              LangText('gradingConversion.submitCta', 'Submit'),
                          onPressed: () async {
                            final shouldSubmit = await context.showAlertDialog(
                                  title: 'Are you sure?',
                                  message: 'Do you want to submit?',
                                ) ??
                                false;

                            if (!shouldSubmit) {
                              return;
                            }

                            context.read<GradingConversionCubit>().submit(
                                  mandiId: widget.mandiId,
                                  smoId: widget.smoId,
                                  inventoryType: widget.inventoryType,
                                  dumpQty: _dumpQty,
                                  qty: _quantity,
                                );
                          },
                        ),
                      ],
                    );
                  },
                );
              },
              error: (error) {
                return ErrorScreen(
                  onPressed: () {
                    context.read<SkuBloc>().add(SkuEvent.fetch());
                  },
                  message: error.errorr.message,
                );
              },
            );
          },
        ),
      ),
    );
  }

  List<Sku> getRemainingToSkus(
      GradingConversionState state, List<Sku> allSkus) {
    final fromSkuId = state.fromInventory!.skuId;
    final sku = allSkus.where((e) => e.id == fromSkuId).firstOrNull;
    if (sku == null) return [];
    if (!widget.allowGrading) return [sku];
    int parentId = sku.parentSkuId;
    if (parentId == 0) {
      parentId = sku.id;
    }

    final relatedSkus = allSkus
        .where((e) => e.id == parentId || e.parentSkuId == parentId)
        .toList();
    return relatedSkus;
    // final toSkuIds = state.toInventory.map((e) => e.sku.id);
    // return relatedSkus.where((e) => !toSkuIds.contains(e.id)).toList();
  }

  Widget dumpCard({
    required List<PickedFile> files,
    required String unitInfo,
    required bool isEnabled,
    required bool isBulkKg,
    required int skuId,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 8,
      ),
      margin: EdgeInsets.only(
        bottom: 4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              LangText(
                'gradingConversion.dumpQty',
                'Dump Quantity($unitInfo)',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(
                width: 100,
                child: WeighingQuantityButton(
                  isReadOnly: false,
                  label: _dumpQty.value ?? '',
                  popupBuilder: () {
                    return WeightCapturePopupProcurementReceive(
                      isBulkKg: isBulkKg,
                      isManualEditAllowed:
                          !(widget.config.isEditOnlyFromWeighingMachine),
                      initialWeight:
                          _dumpController.text.toDouble().asString().toDouble(),
                      skuName: getSKU(context, skuID: skuId).name,
                      unitInfo: unitInfo,
                    );
                  },
                  onChange: (quantity) {
                    if (quantity != null) {
                      setState(() {
                        _dumpController.text = quantity.value ?? '';
                        _dumpQty = quantity;
                      });
                    }
                  },
                ),
              ),
            ],
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
          ),
          InlineImagePicker(
            isEnabled: isEnabled,
            files: files,
            minFileAllowed: 1,
            maxFileAllowed: 4,
            allowMultiple: true,
            uploadAlso: true,
            module: UploadFileModule.grading,
            smoId: widget.smoId,
            updateFile: (files) {
              context
                  .read<GradingConversionCubit>()
                  .updateDumpSelectedFiles(files);
            },
          ),
        ],
      ),
    );
  }

  Widget skuCard(GradingConversionState state, List<Sku> allSkus) {
    if (state.toInventory.isEmpty) return Container();
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 8,
      ),
      margin: EdgeInsets.only(
        bottom: 4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            getSKU(context, skuID: state.fromInventory!.skuId).name,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
            ),
          ),
          Divider(),
          for (int i = 0; i < state.toInventory.length; i++)
            Padding(
              padding: const EdgeInsets.only(
                bottom: 12,
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      getSKU(context, skuID: state.toInventory[i].sku.id).name +
                          '-' +
                          state.toInventory[i].skuQuantity.getUnitString(),
                    ),
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Text(
                        state.toInventory[i].quantity,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  InkWell(
                    onTap: () async {
                      final remainingSkus = getRemainingToSkus(state, allSkus);
                      final compositeKeys = state.toInventory
                          .map((e) => e.skuQuantity.compositeKey)
                          .toList();
                      compositeKeys.remove(
                          state.toInventory[i].skuQuantity.compositeKey);

                      final result = await showDialog<GradingInput>(
                        context: context,
                        builder: (_) => GradingAddSkuPopup(
                          smoId: widget.smoId,
                          skus: remainingSkus,
                          skuImages: state.toInventory[i].files,
                          procurementType:
                              state.toInventory[i].skuQuantity.isBulk
                                  ? ProcurementType.bulk
                                  : ProcurementType.lots,
                          unit: state.toInventory[i].skuQuantity.unit,
                          lotSize: state.toInventory[i].skuQuantity.lotSize,
                          sku: state.toInventory[i].sku,
                          qty: state.toInventory[i].weighingQuantity,
                          isEditing: true,
                          compositeKey: compositeKeys,
                          onDelete: () {
                            context
                                .read<GradingConversionCubit>()
                                .deleteToInventory(i);
                            context.pop();
                          },
                          config: widget.config,
                        ),
                      );
                      if (result != null) {
                        context
                            .read<GradingConversionCubit>()
                            .updateToInventory(result, i);
                      }
                    },
                    child: SizedBox(
                      width: 72,
                      child: Icon(
                        Icons.edit,
                        size: 18,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  double? sumOfToSkusQty(String unit, List<GradingInput> toInventory) {
    if (toInventory.isEmpty) return null;
    double qty = 0;
    for (final inv in toInventory) {
      if (inv.skuQuantity.unit != unit) return null;
      qty += inv.skuQuantity.isBulk
          ? inv.quantity.toDouble()
          : inv.quantity.toDouble() * (inv.skuQuantity.lotSize ?? 0);
    }
    return qty;
  }
}
