import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/sku_edit/bloc/sku_edit_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku_types/bloc/sku_types_bloc.dart';

class SKUEdit extends StatefulWidget {
  const SKUEdit({super.key, this.onDelete});
  final GestureTapCallback? onDelete;

  // final SkuInputData skuInputData;
  @override
  State<SKUEdit> createState() => _SKUEdit();
}

class _SKUEdit extends State<SKUEdit> {
  // Initial Selected Value

  // final bulkUnits = ['Kg', 'Pieces', 'Bunch'];
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _skuSearchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _skuSearchController.addListener(_skuSearchListener);
  }

  void _skuSearchListener() {
    try {
      final skuName = _skuSearchController.text;
      if (skuName == context.read<SkuEditBloc>().state.sku?.name) return;
      if (skuName.isNotEmpty) {
        final sku = context.read<SkuBloc>().state.mapOrNull(
              success: (s) => s.skus.firstWhere(
                (element) => element.name == skuName,
              ),
            );
        if (sku != null) {
          context.read<SkuEditBloc>().add(
                SkuEditEvent.init(
                  SkuInputData.withSku(sku),
                ),
              );
        }
      }
    } catch (e) {}
  }

  @override
  void dispose() {
    super.dispose();
    _skuSearchController.removeListener(_skuSearchListener);
  }

  @override
  Widget build(BuildContext context) {
    // return BlocProvider(
    //   create: (context) =>
    //       di.get<SkuEditBloc>()..add(SkuEditEvent.init(widget.skuInputData)),
    //   child:
    return BlocBuilder<SkuTypesBloc, SkuTypesState>(
      builder: (context, skuTypesState) {
        return skuTypesState.map(
          initial: (_) => const Center(
            child: CircularProgressIndicator(),
          ),
          loading: (_) => const Center(
            child: CircularProgressIndicator(),
          ),
          failure: (failure) {
            return ErrorScreen(
              onPressed: () {
                context.read<SkuTypesBloc>().add(SkuTypesEvent.fetch());
              },
              message: failure.message,
            );
          },
          success: (success) {
            return BlocConsumer<SkuEditBloc, SkuEditState>(
              listener: (context, state) {
                if (state.sku?.name != null) {
                  _skuSearchController.text = state.sku!.name;
                }
                if (state.quantity != _quantityController.text) {
                  _quantityController.text = state.quantity ?? '';
                }
                if (state.amount != _amountController.text) {
                  _amountController.text = state.amount ?? '';
                }
                if (state.weight != _weightController.text) {
                  _weightController.text = state.weight ?? '';
                }
              },
              builder: (context, state) {
                final skuEditBloc = context.read<SkuEditBloc>();
                return BlocBuilder<SkuBloc, SkuState>(
                  builder: (context, skuState) {
                    return skuState.map(
                      initial: (_) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      success: (skuBlocState) => SafeArea(
                        child: Scaffold(
                          backgroundColor: Colors.transparent,
                          body: InkWell(
                            onTap: () {},
                            child: Center(
                              child: Container(
                                width: MediaQuery.of(context).size.width,
                                height:
                                    MediaQuery.of(context).size.height * 0.6,
                                padding: const EdgeInsets.only(
                                  top: 0,
                                  left: 20,
                                  right: 20,
                                ),
                                child: Card(
                                  margin: const EdgeInsets.only(top: 0),
                                  child: SizedBox(
                                    width: MediaQuery.of(context).size.width,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        header(state.isOpenForEdit, onTap: () {
                                          if (widget.onDelete != null) {
                                            context.pop();
                                            widget.onDelete!();
                                          }
                                        }),
                                        const SizedBox(height: 12),
                                        if (state.isOpenForEdit)
                                          Padding(
                                            padding: const EdgeInsets.only(
                                              left: 16,
                                              right: 16,
                                            ),
                                            child: Text(
                                              state.sku?.name ?? '',
                                              style:
                                                  const TextStyle(fontSize: 18),
                                            ),
                                          ),
                                        if (!state.isOpenForEdit)
                                          CustomDropdown.search(
                                            hintText: LanguageEnum
                                                .addProcSearchBoxHint
                                                .localized(),
                                            items: skuBlocState.skus
                                                .map((e) => e.name)
                                                .toList(),
                                            controller: _skuSearchController,
                                            listItemBuilder: (context, item) =>
                                                Text(
                                              item,
                                              maxLines: 3,
                                            ),
                                          ),
                                        Expanded(
                                          child: Scrollbar(
                                            child: ListView(
                                              shrinkWrap: true,
                                              // crossAxisAlignment: CrossAxisAlignment.start,
                                              // mainAxisSize: MainAxisSize.min,

                                              children: [
                                                const SizedBox(height: 12),

                                                if (state.sku != null) ...[
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                      bottom: 16.0,
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Expanded(
                                                          child:
                                                              procurementTypeField(),
                                                        ),
                                                        const SizedBox(
                                                            height: 12),
                                                        Expanded(
                                                          child: unitField(
                                                            state.sku!
                                                                .bulkUnitTypes,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  if (state.procurementType ==
                                                      ProcurementType.bulk)
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                        bottom: 12.0,
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          Expanded(
                                                            child: quantityField(
                                                                state.procurementType ==
                                                                    ProcurementType
                                                                        .bulk),
                                                          ),
                                                          const SizedBox(
                                                              height: 12),
                                                          Expanded(
                                                            child:
                                                                amountField(),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  if (state.procurementType ==
                                                      ProcurementType.lots) ...[
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                        bottom: 16.0,
                                                      ),
                                                      child: Row(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          if (state
                                                              .showLotSizeField) ...[
                                                            Expanded(
                                                              child:
                                                                  lotSizeField(),
                                                            ),
                                                            const SizedBox(
                                                                height: 12),
                                                          ],
                                                          Expanded(
                                                            child: quantityField(
                                                                state.procurementType ==
                                                                    ProcurementType
                                                                        .bulk),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                        bottom: 16.0,
                                                      ),
                                                      child: Row(
                                                        children: [
                                                          if (state
                                                              .showLotSizeField) ...[
                                                            Expanded(
                                                              child:
                                                                  weightField(),
                                                            ),
                                                            const SizedBox(
                                                                height: 12),
                                                          ],
                                                          Expanded(
                                                            child:
                                                                amountField(),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                  const SizedBox(height: 20),
                                                ],
                                                //       Row(
                                                //         mainAxisAlignment:
                                                //             MainAxisAlignment.center,
                                                //         children: [],
                                                //       )
                                                //     ],
                                                //   ),
                                                // ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Container(
                                          decoration: BoxDecoration(boxShadow: [
                                            BoxShadow(
                                              color:
                                                  Colors.grey.withOpacity(0.1),
                                              spreadRadius: 1,
                                              blurRadius: 1,
                                              offset: Offset(0, 1),
                                            ),
                                          ]),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 12.0),
                                            child: Align(
                                              child: ElevatedButton(
                                                style: ButtonStyle(
                                                  minimumSize:
                                                      MaterialStateProperty.all(
                                                    Size(
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width -
                                                          80,
                                                      50,
                                                    ),
                                                  ),
                                                ),
                                                onPressed: !state.isCtaActive
                                                    ? null
                                                    : () {
                                                        final data = skuEditBloc
                                                            .getSkuInputData();
                                                        if (data != null) {
                                                          context.pop(data);
                                                        }
                                                      },
                                                child: Text(
                                                  LanguageEnum
                                                      .addProcSkuSelectCtaTitle
                                                      .localized(),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      error: (state) => ErrorScreen(
                        onPressed: () {
                          context.read<SkuBloc>().add(const SkuEvent.fetch());
                        },
                        message: state.errorr.message,
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
    //   },
    // ),
    // );
  }

  Widget amountField() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectAmountLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _amountController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.done,
            inputFormatters: Config.numberInputFilters,
            onChanged: (value) {
              context.read<SkuEditBloc>().add(
                    SkuEditEvent.updateAmount(
                      value,
                    ),
                  );
            },
          )
        ],
      ),
    );
  }

  Widget quantityField(bool isBulk) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              isBulk
                  ? LanguageEnum.addProcSkuSelectQuantityLabel.localized()
                  : LanguageEnum.addProcSkuSelectNumberOfLotsLabel.localized(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              )),
          const SizedBox(height: 8),
          TextFormField(
            controller: _quantityController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.numberInputFilters,
            onChanged: (val) {
              context.read<SkuEditBloc>().add(
                    SkuEditEvent.updateQuanity(
                      val,
                      null,
                    ),
                  );
            },
          )
        ],
      ),
    );
  }

  Widget weightField() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectWeightLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _weightController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.numberInputFilters,
            onChanged: (value) {
              context.read<SkuEditBloc>().add(
                    SkuEditEvent.updateWeight(
                      value,
                    ),
                  );
            },
          )
        ],
      ),
    );
  }

  Widget unitField(List<String> bulkUnits) {
    final skuEditBloc = context.read<SkuEditBloc>();
    final state = skuEditBloc.state;
    final items = state.procurementType == ProcurementType.bulk
        ? bulkUnits
        : state.sku?.lotSizes.keys.toList() ?? [];
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectUnitLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          DropdownButton(
            // Initial Value
            value: state.procurementUnit,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: items.map((String item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.capitalize().localized()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (String? newValue) {
              skuEditBloc.add(SkuEditEvent.updateUnit(newValue));
            },
          )
        ],
      ),
    );
  }

  Widget lotSizeField() {
    final skuEditBloc = context.read<SkuEditBloc>();
    final state = skuEditBloc.state;
    final selectedLotSize =
        state.sku?.lotSizes[state.procurementUnit?.toUpperCase() ?? ''] ??
            <String>[];
    final items = <double>[];
    if (selectedLotSize is List) {
      for (final element in selectedLotSize) {
        items.add(double.parse(element.toString()));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectLotSizeLabel.localized(),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          SizedBox(
            height: 16,
          ),
          DropdownButton(
            // Initial Value
            value: state.lotSize,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: items.map((double item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.toString()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (double? newValue) {
              skuEditBloc.add(SkuEditEvent.updateLotSize(newValue));
            },
          )
        ],
      ),
    );
  }

  Widget procurementTypeField() {
    final skuEditBloc = context.read<SkuEditBloc>();
    final state = skuEditBloc.state;
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectProcTypeLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          DropdownButton<ProcurementType>(
            // Initial Value
            value: state.procurementType,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: ProcurementType.values.map((ProcurementType item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.value.capitalize().localized()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (ProcurementType? newValue) {
              skuEditBloc.add(
                SkuEditEvent.updateProcurementType(
                  newValue ?? ProcurementType.bulk,
                ),
              );
            },
          )
        ],
      ),
    );
  }

  Widget header(bool isEditing, {GestureTapCallback? onTap}) {
    // final name = context.watch<SkuEditBloc>().state.sku?.name ?? '';
    return Container(
      decoration: BoxDecoration(
        color: Config.primaryColor,
        boxShadow: const <BoxShadow>[
          BoxShadow(
            color: Colors.black54,
            blurRadius: 10,
            offset: Offset(0, 0.75),
          )
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Expanded(
            child: Row(
              children: [
                Text(
                  isEditing
                      ? LanguageEnum.addProcSkuSelectHeadingEdit.localized()
                      : LanguageEnum.addProcSkuSelectHeadingAdd.localized(),
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              ],
            ),
          ),
          if (widget.onDelete != null)
            InkWell(
              onTap: onTap,
              child: const Icon(Icons.delete, color: Colors.white),
            ),
        ],
      ),
    );
  }
}
