part of 'place_order_cubit.dart';

@freezed
class PlaceOrderState with _$PlaceOrderState {
  const PlaceOrderState._();
  const factory PlaceOrderState.data({
    @Default(-1) int procItemId,
    required Sku? sku,
    required ProcurementType procurementType,
    required String? procurementUnit,
    required double? lotSize,
    required String? orderedQuantity,
    required String? previousReceivedQuantity,
    required String? receivedQuantity,
    required String? weighingSource,
    @Default(false) bool isFinal,
    required String? amount,
    required String? weight,
    required String uuid,
    required bool isOpenForEdit,
    @Default([]) List<String> notAllowedSku,
    @Default(false) bool isDuplicate,
    @Default(false) bool isCtaActive,
    @Default(true) bool newlyAdded,
    @Default(false) bool disableAmountEdit,
    @Default(false) bool disableReceiveQty,
  }) = _InitialData;
  static final empty = PlaceOrderState.data(
    sku: null,
    procurementType: ProcurementType.bulk,
    procurementUnit: null,
    lotSize: null,
    orderedQuantity: null,
    previousReceivedQuantity: null,
    receivedQuantity: null,
    amount: null,
    uuid: di.get<Uuid>().v1(),
    weight: null,
    isOpenForEdit: false,
    weighingSource: 'manual',
  );

  bool get isBulk => procurementType == ProcurementType.bulk;
  bool get isBulkKg => isBulk && procurementUnit?.toLowerCase() == 'kg';
  String get unitInfo => '${procurementType.value} - ${procurementUnit}';
  List<String> get lotsUnit => sku?.lotSizes.keys.toList() ?? [];
  dynamic get lotSizes =>
      sku?.lotSizes[procurementUnit?.toUpperCase() ?? ''] ?? [];

  double get _totalQty =>
      (previousReceivedQuantity?.toDouble() ?? 0) +
      (receivedQuantity?.toDouble() ?? 0);
  double get totalQty => double.parse(_totalQty.toStringAsFixed(3));

  String totalReceive() {
    return totalQty > 0 ? totalQty.toString() : '-';
  }
}
