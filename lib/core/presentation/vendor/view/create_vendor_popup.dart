import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/proc_area.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';

class CreateVendorPopup extends StatefulWidget {
  const CreateVendorPopup({
    super.key,
    required this.initialVendorName,
    required this.initalVendorPhone,
    required this.initialPocName,
    required this.isVendorDisabled,
  });
  final String initialVendorName;
  final String initalVendorPhone;
  final String initialPocName;
  final bool isVendorDisabled;

  factory CreateVendorPopup.createVendor({required String vendorName}) =>
      CreateVendorPopup(
        initialVendorName: vendorName,
        initalVendorPhone: '',
        initialPocName: '',
        isVendorDisabled: false,
      );

  factory CreateVendorPopup.createLocation(
          {required Vendor vendor, required String pocName}) =>
      CreateVendorPopup(
        initialVendorName: vendor.name,
        initalVendorPhone: vendor.mobile,
        initialPocName: pocName,
        isVendorDisabled: true,
      );

  @override
  State<CreateVendorPopup> createState() => _CreateVendorPopupState();
}

class _CreateVendorPopupState extends State<CreateVendorPopup> {
  TextEditingController vendorNameController = TextEditingController();
  TextEditingController pocNameController = TextEditingController();
  TextEditingController pocPhoneController = TextEditingController();
  TextEditingController vendorPhoneController = TextEditingController();
  ProcArea? selectedArea;
  bool isLoading = false;

  @override
  void initState() {
    vendorNameController.text = widget.initialVendorName;
    vendorPhoneController.text = widget.initalVendorPhone;
    pocNameController.text = widget.initialPocName;
    super.initState();
  }

  @override
  void dispose() {
    vendorNameController.dispose();
    pocNameController.dispose();
    vendorPhoneController.dispose();
    pocPhoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final vendorName = vendorNameController.text;
    final pocName = pocNameController.text;
    final vendorPhone = vendorPhoneController.text;
    final pocPhone = pocPhoneController.text;
    final isCtaActive = vendorName.isNotEmpty &&
        vendorPhone.length == 10 &&
        (pocPhone.isEmpty || pocPhone.length == 10) &&
        selectedArea != null;

    final procAreas =
        di.get<VendorCubit>().state.procAreas?.values.toList() ?? <ProcArea>[];

    return Popup(
      title: widget.isVendorDisabled
          ? 'vendorManagement.createLocation'.tr('Create Location')
          : 'vendorManagement.createVendor'.tr('Create Vendor'),
      children: [
        Expanded(
          child: ListView(
            padding: EdgeInsets.symmetric(
              vertical: 8,
              horizontal: 16,
            ),
            children: [
              TextFormField(
                enabled: !widget.isVendorDisabled,
                controller: vendorNameController,
                decoration: InputDecoration(
                  labelText: 'vendorManagement.vendorName'.tr('Vendor Name*'),
                  border: OutlineInputBorder(),
                  isDense: true,
                  contentPadding: EdgeInsets.only(
                    left: 8,
                    right: 8,
                    top: 12,
                    bottom: 12,
                  ),
                ),
                textInputAction: TextInputAction.next,
                onChanged: (_) {
                  setState(() {});
                },
              ),
              const SizedBox(height: 24),
              TextFormField(
                enabled: !widget.isVendorDisabled,
                controller: vendorPhoneController,
                decoration: InputDecoration(
                    labelText:
                        'vendorManagement.vendorPhone'.tr('Vendor Phone*'),
                    border: OutlineInputBorder(),
                    isDense: true,
                    contentPadding: EdgeInsets.only(
                      left: 8,
                      right: 8,
                      top: 12,
                      bottom: 12,
                    ),
                    counter: SizedBox()),
                maxLength: 10,
                inputFormatters: Config.numberInputFiltersInt,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.next,
                onChanged: (_) {
                  setState(() {});
                },
              ),
              Divider(
                height: 16,
                color: Colors.black,
              ),
              SizedBox(
                height: 8,
              ),
              if (procAreas.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 32, top: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (selectedArea != null)
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 8,
                          ),
                          child: Text(
                            'vendorManagement.areaOfOperation'
                                .tr('Area of Operation*'),
                            style: TextStyle(
                                fontSize: 14, fontWeight: FontWeight.w600),
                          ),
                        ),
                      CustomDropdownV2<ProcArea>(
                        items: procAreas,
                        selectedItem: selectedArea,
                        hintText: 'vendorManagement.areaOfOperation'
                            .tr('Area of Operation*'),
                        onItemSelected: (value) {
                          selectedArea = value;
                          setState(() {});
                        },
                        fillColor: Colors.grey.shade100,
                      ),
                    ],
                  ),
                ),
              TextFormField(
                controller: pocNameController,
                decoration: InputDecoration(
                  labelText: 'vendorManagement.pocName'.tr('POC Name'),
                  border: OutlineInputBorder(),
                  isDense: true,
                  contentPadding: EdgeInsets.only(
                    left: 8,
                    right: 8,
                    top: 12,
                    bottom: 12,
                  ),
                ),
                textInputAction: TextInputAction.next,
                onChanged: (_) {
                  setState(() {});
                },
              ),
              const SizedBox(height: 24),
              TextFormField(
                controller: pocPhoneController,
                decoration: InputDecoration(
                  labelText: 'vendorManagement.pocPhone'.tr('POC Phone'),
                  border: OutlineInputBorder(),
                  isDense: true,
                  contentPadding: EdgeInsets.only(
                    left: 8,
                    right: 8,
                    top: 12,
                    bottom: 12,
                  ),
                  counter: SizedBox(),
                ),
                maxLength: 10,
                inputFormatters: Config.numberInputFiltersInt,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,
                onChanged: (_) {
                  setState(() {});
                },
              ),
            ],
          ),
        ),
        WStickyBottomCta(
          isEnabled: isCtaActive && !isLoading,
          isLoading: isLoading,
          icon: Icons.check,
          label: LangText('vendorManagement.create', 'Create'),
          onPressed: () async {
            setState(() {
              isLoading = true;
            });
            final result = await di.get<VendorCubit>().createVendor(
                vendorName: vendorName,
                vendorPhone: vendorPhone,
                pocName: pocName.isEmpty ? vendorName : pocName,
                pocPhone: pocPhone,
                procAreaId: selectedArea!.id);
            context.pop(result);
          },
        )
      ],
    );
  }
}
