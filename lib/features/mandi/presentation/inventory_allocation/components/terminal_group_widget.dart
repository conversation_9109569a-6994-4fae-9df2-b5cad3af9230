import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_terminal.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/add_group_popup.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class TerminalGroup extends StatelessWidget {
  const TerminalGroup({
    super.key,
    this.padding,
    this.margin,
    required this.terminals,
    required this.pendingTerminals,
    required this.groupCount,
    required this.supplyOrderCount,
    required this.onGroupCountChanged,
    required this.onPendingTerminalsUpdate,
  });
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final List<AllocationTerminal> terminals;
  final List<AllocationTerminal> pendingTerminals;
  final int? groupCount;
  final int supplyOrderCount;
  final Function(int groupCount) onGroupCountChanged;
  final Function(List<AllocationTerminal>) onPendingTerminalsUpdate;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 1,
                child: LangText(
                  'allocation.sno',
                  'S.No',
                  textAlign: TextAlign.start,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 2,
                child: LangText(
                  'allocation.terminals',
                  'Terminals',
                  textAlign: TextAlign.start,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                flex: 2,
                child: SizedBox(
                  child: OutlinedButton(
                    onPressed: () async {
                      final count = await showDialog<int?>(
                        context: context,
                        builder: (_) => AddGroupPopup(
                          orders: supplyOrderCount,
                          groupCount: groupCount,
                        ),
                      );
                      if (count != null) {
                        onGroupCountChanged(count);
                      }
                    },
                    child: Text(
                      'allocation.addGroup'.tr('Add Group') +
                          (groupCount == null
                              ? ''
                              : ' (${groupCount.toString()})'),
                      style: TextStyles.body,
                    ),
                  ),
                ),
              ),
            ],
          ),
          Divider(
            height: 0,
          ),
          ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: terminals.length,
            itemBuilder: (context, index) {
              return Container(
                decoration: index == terminals.length - 1
                    ? null
                    : BoxDecoration(
                        border: terminals[index].groupValue !=
                                terminals[index + 1].groupValue
                            ? null
                            : Border(
                                bottom: BorderSide(
                                  color: Colors.grey.shade300,
                                ),
                              ),
                      ),
                padding: index != terminals.length - 1 &&
                        terminals[index].groupValue !=
                            terminals[index + 1].groupValue
                    ? EdgeInsets.only(top: 8)
                    : EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    index == 0 ||
                            terminals[index - 1].groupValueString ==
                                terminals[index].groupValueString
                        ? SizedBox()
                        : Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: Divider(
                              height: 16,
                              color: Colors.black,
                            ),
                          ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Text(
                            terminals[index].serialNumber.toString(),
                            style: TextStyles.body,
                            textAlign: TextAlign.start,
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            terminals[index].id,
                            textAlign: TextAlign.start,
                            style: TextStyles.body,
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            terminals[index].groupValueString,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                            ).merge(TextStyles.heading),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
          if (pendingTerminals.isNotEmpty)
            Divider(
              height: 16,
              color: Colors.black,
            ),
          Visibility(
            visible: pendingTerminals.isNotEmpty,
            child: ListView.builder(
              padding: EdgeInsets.only(top: 8),
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: pendingTerminals.length,
              itemBuilder: (context, index) {
                final item = pendingTerminals[index];
                return Container(
                  decoration: index == pendingTerminals.length - 1
                      ? null
                      : BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey.shade300,
                            ),
                          ),
                        ),
                  padding: EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 1,
                        child: Text(
                          item.serialNumber.toString(),
                          style: TextStyles.body,
                          textAlign: TextAlign.start,
                        ),
                      ),
                      SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          pendingTerminals[index].id,
                          textAlign: TextAlign.start,
                          style: TextStyles.body,
                        ),
                      ),
                      SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        flex: 2,
                        child: groupCount == null
                            ? Text(
                                '-',
                                textAlign: TextAlign.center,
                              )
                            : CustomDropdownV2<GroupItem>(
                                items: List.generate(groupCount!,
                                    (index) => GroupItem(value: index + 1)),
                                onItemSelected: (item) {
                                  final updatedPending = [...pendingTerminals];
                                  updatedPending[index] = updatedPending[index]
                                      .copyWith(groupValue: item.value);
                                  onPendingTerminalsUpdate(updatedPending);
                                },
                                fillColor: Colors.grey.shade100,
                                hintText: 'allocation.selectedGroup'
                                    .tr('Selected Group'),
                                selectedItem: item.groupValue == null
                                    ? null
                                    : GroupItem(value: item.groupValue!),
                              ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
