import 'package:proc2/core/utils/extensions.dart';

class WeightItem {
  final String weight;
  final WeightItemType type;

  WeightItem({required this.weight, required this.type});

  double get weightDouble => weight.toDouble().asString().toDouble();

  WeightItem copyWith({
    String? weight,
    WeightItemType? type,
  }) {
    return WeightItem(
      weight: weight ?? this.weight,
      type: type ?? this.type,
    );
  }
}

enum WeightItemType {
  none,
  added,
  reduce,
}
