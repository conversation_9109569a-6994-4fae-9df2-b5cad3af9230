import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order_metadata.dart';

class GetSupplyOrderRequest extends BaseRequest<dynamic, SupplyOrderMetadata> {
  final int deliveryEpochSeconds;
  final String deliverySlot;
  final String customerGroup;
  final int? allocationId;
  final bool forAdmin;

  GetSupplyOrderRequest({
    required this.deliveryEpochSeconds,
    required this.deliverySlot,
    required this.customerGroup,
    this.allocationId = null,
    this.forAdmin = false,
  });

  @override
  Map<String, dynamic> getBody() {
    return {
      'deliveryDate': deliveryEpochSeconds,
      'deliverySlot': deliverySlot,
      'customerGroup': [customerGroup],
      'allocationId': allocationId,
      'forAdmin': forAdmin,
    };
  }

  @override
  String getPath() => 'supplyOrder/all?langCode=en';

  @override
  SupplyOrderMetadata mapper(data) {
    List<SupplyOrder> supplyOrders = [];
    if (data['supplyOrders'] is List<dynamic>) {
      supplyOrders = (data['supplyOrders'] as List<dynamic>)
          .map((e) => SupplyOrder.fromJson(e as Map<String, dynamic>))
          .map((e) => e.copyWith(
                deliveryDate: deliveryEpochSeconds,
                deliverySlot: deliverySlot,
              ))
          .toList();
    }
    return SupplyOrderMetadata(
      supplyOrders: supplyOrders,
      allowOnlyDispatch: data['allowOnlyDispatch'],
      isActionsBlocked: data['isActionsBlocked'],
    );
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
