import 'package:freezed_annotation/freezed_annotation.dart';

part 'sku_input.freezed.dart';

@freezed
class SkuInput with _$SkuInput {
  const factory SkuInput({
    required int skuId,
    required String type,
    required String unit,
    required double? quantity,
    required double lotSize,
    required double weight,
    required double amount,
    required double orderedQuantity,
    required bool finalQuantity,
    required String? weighingSource,
  }) = _SkuInput;
}
