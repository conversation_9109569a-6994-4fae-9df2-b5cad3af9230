import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';

part 'add_losses_bloc.freezed.dart';
part 'add_losses_event.dart';
part 'add_losses_state.dart';

@injectable
class AddLossesBloc extends Bloc<AddLossesEvent, AddLossesState> {
  AddLossesBloc()
      : super(const AddLossesState.input(
          losses: [],
          isPrefilledData: false,
        )) {
    on<AddLossesEvent>((event, emit) {
      event.when(
        started: (types, unit, shouldHardCodeLosses) {
          if (state.isPrefilledData) return;

          final losses = types.map((type) {
            return LossInputModel(
              lossType: type.lossType,
              lossValue: '',
              unit: unit,
              comment: '',
              maxFileUploadCount: type.maxImageUpload,
              minFileUploadCount: type.minImageUpload,
            );
          }).toList();

          if (shouldHardCodeLosses) {
            losses
              ..add(
                LossInputModel(
                  lossType: 'LIQUIDATION',
                  lossValue: '',
                  unit: unit,
                  comment: '',
                ),
              )
              ..add(
                LossInputModel(
                  lossType: 'HONEYBEE_OR_PENTAGON',
                  lossValue: '',
                  unit: unit,
                  comment: '',
                ),
              );
          }

          emit(state.copyWith(losses: losses));
        },
        prefill: (losses, maxLoss) {
          if (losses == null || losses.isEmpty) {
            emit(state.copyWith(maxLoss: maxLoss));
            return;
          }
          emit(state.copyWith(losses: losses, isPrefilledData: true));
        },
        lossChanged: (index, lossValue, comment, files) {
          final losses = state.losses;

          final updated = losses[index].copyWith(
            lossValue: lossValue,
            comment: comment,
            files: files,
          );
          final updatedLosses = List<LossInputModel>.from(losses)
            ..[index] = updated;

          emit(state.copyWith(losses: updatedLosses));
        },
      );
    });
  }
}
