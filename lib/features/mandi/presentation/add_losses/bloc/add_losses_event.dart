part of 'add_losses_bloc.dart';

@freezed
class AddLossesEvent with _$AddLossesEvent {
  const factory AddLossesEvent.started(
    List<LossTypeV2> losses,
    String unit,
    bool shouldHardcodeLosses,
  ) = _Started;

  const factory AddLossesEvent.prefill(
    List<LossInputModel>? losses,
    double maxLoss,
  ) = _Prefill;

  const factory AddLossesEvent.lossChanged(
    int index,
    String lossValue,
    String comment,
    List<PickedFile> files,
  ) = _LossChanged;
}
