import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

part 'proc_detail.freezed.dart';
part 'proc_detail.g.dart';

@freezed
class ProcDetail with _$ProcDetail {
  const ProcDetail._();
  const factory ProcDetail({
    required int id,
    required int createdAt,
    required String status,
    required int smoId,
    required List<String> images,
    @Default({}) Map<String, dynamic> urls,
    required List<ProcDetailItem> items,
    required String procuredBy,
    @Default(null) String? vendorName,
    @Default(null) int? vendorLocationId,
    @Default([]) List<dynamic> charges,
    @Default('') String comments,
    @Default(null) ParentOrder? parentOrder,
    @Default(null) List<ChildOrderInfo>? pendingChildOrders,
    @Default(null) List<ChildOrderInfo>? closedChildOrders,
    @Default(null) List<int>? orderedSkuIds,
    @Default(null) int? supplyDate,
    @Default(null) String? refId,
    @Default(null) String? refSource,
  }) = _ProcDetail;

  bool get isParentOrder => parentOrder?.isParent == true;
  bool get isBulkPlacement => parentOrder != null;

  List<ChildOrderInfo> get allChildOrders =>
      (pendingChildOrders ?? []) + (closedChildOrders ?? []);

  ProcDetailItem? findByCompositeKey(String key) {
    for (final item in items) {
      if (item.compositeKey == key) {
        return item;
      }
    }
    return null;
  }

  factory ProcDetail.fromJson(Map<String, dynamic> json) =>
      _$ProcDetailFromJson(json);

  double fieldChargesAmount() => charges
      .map((e) => (e as Map<String, dynamic>)['amount'].toString().toDouble())
      .fold(0.0, (p, e) => p + e);
  double totalAmount() =>
      items.fold(0.0, (p, e) => p + (e.receivedAmount)) + fieldChargesAmount();
}

@freezed
class ChildOrderInfo with _$ChildOrderInfo {
  const factory ChildOrderInfo({
    required String status,
    required int mandiId,
  }) = _ChildOrderInfo;

  @override
  factory ChildOrderInfo.fromJson(Map<String, dynamic> json) =>
      _$ChildOrderInfoFromJson(json);
}

@freezed
class ParentOrder with _$ParentOrder {
  const ParentOrder._();
  const factory ParentOrder({
    @Default(null) String? placedBy,
    @Default(false) bool isParent,
  }) = _ParentOrder;

  factory ParentOrder.fromJson(Map<String, dynamic> json) =>
      _$ParentOrderFromJson(json);
}

@freezed
class ProcDetailItem with _$ProcDetailItem {
  const ProcDetailItem._();
  const factory ProcDetailItem({
    required int? id,
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double? quantity,
    required double? weight,
    required double? amount,
    required double? orderedQuantity,
    required bool? finalSubmit,
    @Default([]) List<String> images,
    @Default(null) double? orderedCostPrice,
    @Default(null) double? billedCostPrice,
    @Default(false) bool isAmountUpdated,
  }) = _ProcDetailItem;
  SkuQuantity get skuQuantity {
    return SkuQuantity(
      skuId: skuId,
      type: type,
      unit: unit,
      lotSize: lotSize,
      quantity: quantity,
    );
  }

  bool get isBulk => type.toLowerCase() == 'bulk';
  double get pendingQty =>
      finalSubmit == true ? 0 : (orderedQuantity ?? 0) - (quantity ?? 0);
  String get compositeKey =>
      isBulk ? '$skuId-$type-$unit' : '$skuId-$type-$unit-$lotSize';
  String getUnitString() {
    if (isBulk) {
      return 'Bulk - $unit';
    }
    return 'Lots - $lotSize $unit';
  }

  double? get _billedAmount {
    /// If not received till now, then return null
    if (quantity == null) return null;

    /// This is the fallback for old data, in this case. Send amount
    if (billedCostPrice == null && orderedCostPrice == null) return amount;

    /// If billed cost price is not present, then use ordered cost price
    return (billedCostPrice ?? orderedCostPrice!) * quantity!;
  }

  double get receivedAmount => _billedAmount ?? 0;

  String get receivedAmountString =>
      _billedAmount?.asString(maxDecimalDigits: 2) ?? '-';

  factory ProcDetailItem.fromJson(Map<String, dynamic> json) =>
      _$ProcDetailItemFromJson(json);
}
