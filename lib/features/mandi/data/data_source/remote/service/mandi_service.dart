import 'package:chopper/chopper.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/data/network/util/network_constants.dart';

part 'mandi_service.chopper.dart';

@ChopperApi()
@injectable
abstract class MandiService extends ChopperService {
  @factoryMethod
  static MandiService create([ChopperClient? client]) => _$MandiService(client);

  @Post(path: 'smo/createSmo')
  Future<Response<Map<String, dynamic>>> createSmo(
    @Field('mandiId') String mandiId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'mandis/getMandis')
  Future<Response<Map<String, dynamic>>> getAllMandis({
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'users/getUserMandis')
  Future<Response<Map<String, dynamic>>> getUserMandis({
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'smo/getSmo/mandi/{mandiId}')
  Future<Response<Map<String, dynamic>>> getSmo(
    @Path('mandiId') String mandiId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: 'procurements/addProcurement')
  Future<Response<Map<String, dynamic>>> addProcurement(
    @Body() Map<String, dynamic> body,
  );

  @Get(path: 'skus/{lang}')
  Future<Response<Map<String, dynamic>>> getSku({
    @Path('lang') String lang = 'en',
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'incomingStocks/getIncomingStockList/mandi/{mandiId}')
  Future<Response<Map<String, dynamic>>> getIncomingStocks(
    @Path('mandiId') int mandiId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'carryForwards/smo/{smoId}')
  Future<Response<Map<String, dynamic>>> getCarryForward(
    @Path('smoId') int smoId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'smo/charges/getChargeTypes')
  Future<Response<Map<String, dynamic>>> getChargesType({
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'losses/getLossInit')
  Future<Response<Map<String, dynamic>>> getLossInit({
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'skus/getSkuTypes')
  Future<Response<Map<String, dynamic>>> getSkuTypes({
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: 'incomingStocks/acceptIncomingStocks')
  Future<Response<Map<String, dynamic>>> acceptIncomingStocks(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'smo/charges/addCharges')
  Future<Response<Map<String, dynamic>>> addCharges(
    @Body() Map<String, dynamic> body,
  );

  @Get(path: 'smo/{smoId}/charges')
  Future<Response<Map<String, dynamic>>> getFieldCharges(
    @Path('smoId') int smoId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'mandis/inventory/{mandiId}')
  Future<Response<Map<String, dynamic>>> getMandiInventory(
    @Path('mandiId') int mandiId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: 'mandis/inventories/changeInventory')
  Future<Response<Map<String, dynamic>>> updateMandiInventory(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'carryForwards/acceptCarryForward')
  Future<Response<Map<String, dynamic>>> acceptCarryForward(
    @Body() Map<String, dynamic> body,
  );

  @Get(path: 'mandis/inventory/{mandiId}')
  Future<Response<Map<String, dynamic>>> getInventory(
    @Path('mandiId') int mandiId,
    @Query('type') String type, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'allotments/smoId/{smoId}/init')
  Future<Response<Map<String, dynamic>>> getInitAllotment(
    @Path('smoId') int smoId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: 'allotments/create')
  Future<Response<Map<String, dynamic>>> createAllotment(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'closingStocks/add')
  Future<Response<Map<String, dynamic>>> closeStocks(
    @Body() Map<String, dynamic> body,
  );

  @Get(path: 'allotments/smo/{smoId}')
  Future<Response<Map<String, dynamic>>> getAllotments(
    @Path('smoId') int smoId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'allotments/{allotmentId}/items')
  Future<Response<Map<String, dynamic>>> getAllotedItems(
    @Path('allotmentId') int allotmentId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: 'allotments/submit')
  Future<Response<Map<String, dynamic>>> submitAllotment(
    @Body() Map<String, dynamic> body,
  );

  @Get(path: 'smo/smoHistory/getSmoHistory')
  Future<Response<Map<String, dynamic>>> getSmoHistory({
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'smo/smoHistory/getSmoDetails/{smoId}')
  Future<Response<Map<String, dynamic>>> getSmoDetails(
    @Path('smoId') int smoId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: 'procurements/updateProcurementAmount')
  Future<Response<Map<String, dynamic>>> updateProcurementAmount(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'procurements/submitProcurement')
  Future<Response<Map<String, dynamic>>> submitProcurement(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'smo/smoHistory/closeSmo/{smoId}')
  Future<Response<Map<String, dynamic>>> closeSmo(
    @Path('smoId') int smoId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: 'conversions/addConversion')
  Future<Response<Map<String, dynamic>>> submitConversion(
    @Body() Map<String, dynamic> body,
  );
  @Post(path: 'smo/charges/upload/')
  Future<Response<Map<String, dynamic>>> getSmoChargesPresignedUrl(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'procurements/invoices/upload/')
  Future<Response<Map<String, dynamic>>> getProcurementsInvoicePresignedUrl(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'smo/closeOps/{smoId}')
  Future<Response<Map<String, dynamic>>> closeOps(
    @Path('smoId') int smoId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'trips/{allocationId}')
  Future<Response<Map<String, dynamic>>> getTripsForAllocation(
    @Path('allocationId') int allocationId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Put(path: 'trips/dispatch')
  Future<Response<Map<String, dynamic>>> dispatchTrip(
    @Body() Map<String, dynamic> body,
  );

  @Post(path: 'allotments/{allocationId}/cancel')
  Future<Response<Map<String, dynamic>>> cancelAllocation(
    @Path('allocationId') int allocationId, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'lang/', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> getLanguages({
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: 'lang/{languageCode}', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> getLanguageFile(
    @Path('languageCode') String code, {
    @Body() Map<String, dynamic> body = const {},
  });
  @Get(path: 'conversions/getLottingConfig')
  Future<Response<Map<String, dynamic>>> getLottingDeviations({
    @Body() Map<String, dynamic> body = const {},
  });

  @Put(path: 'allotments/{allotmentId}/delivery-memos/{langCode}')
  Future<Response<Map<String, dynamic>>> regenerateDeliveryMem(
    @Path('allotmentId') int allotmentId,
    @Path('langCode') String langCode, {
    @Body() Map<String, dynamic> body = const {},
  });

  // @Put(path: '{url}', headers: {
  //   NetworkConstants.noAuthKey: '',
  //   contentTypeKey: formEncodedHeaders,
  // })
  // @multipart
  // Future<Response<dynamic>> uploadToAws(
  //   @Path('url') String url,
  //   @PartFile('file') http.MultipartFile file,
  // );
}
