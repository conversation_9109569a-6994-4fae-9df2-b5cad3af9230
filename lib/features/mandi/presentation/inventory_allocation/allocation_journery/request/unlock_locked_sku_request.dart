import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

class UnlockLockedSkuRequest extends BaseRequest<dynamic, String> {
  final int allocationId;
  final SkuQuantity sku;

  UnlockLockedSkuRequest({
    required this.allocationId,
    required this.sku,
  });

  @override
  String getPath() => 'allotments/$allocationId/unlock-item';

  @override
  Map<String, dynamic> getBody() {
    return {'searchSku': sku};
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid data';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
