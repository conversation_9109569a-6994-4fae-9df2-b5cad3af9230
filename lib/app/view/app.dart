import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:proc2/app/route/router.dart';
import 'package:proc2/app/view/restart_widget.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/theme/theme.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_machine_controller.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:proc2/features/mandi/presentation/close_ops/bloc/close_ops_bloc.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/language/cubit/language_cubit.dart';
import 'package:proc2/features/mandi/presentation/loss_types/bloc/loss_types_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku_types/bloc/sku_types_bloc.dart';
import 'package:provider/provider.dart';

class App extends StatelessWidget {
  App({super.key});

  final AppConfig appConfig = di.get();

  @override
  Widget build(BuildContext context) {
    return FlavorBanner(
      child: RestartWidget(
        child: MultiBlocProvider(
          providers: [
            ChangeNotifierProvider(
              create: (_) => WeighingMachineController.instance,
            ),
            BlocProvider<AuthBloc>(
              create: (context) =>
                  di.get<AuthBloc>()..add(const AuthEvent.started()),
            ),
            BlocProvider<MandiBloc>(
              create: (context) => di.get<MandiBloc>(),
            ),
            BlocProvider<SkuBloc>(
              create: (context) => di.get<SkuBloc>(),
            ),
            BlocProvider<LossTypesBloc>(
              create: (context) => di.get<LossTypesBloc>(),
            ),
            BlocProvider<SkuTypesBloc>(
              create: (context) => di.get<SkuTypesBloc>(),
            ),
            BlocProvider<CloseOpsBloc>(
              create: (context) => di.get<CloseOpsBloc>(),
            ),
            BlocProvider(create: (_) => di.get<LanguageCubit>()),
          ],
          child: RestorationScope(
            restorationId: 'root',
            child: MaterialApp.router(
              scaffoldMessengerKey: rootScaffoldMessengerKey,
              routerConfig: router,
              theme: appTheme,
              title: appConfig.appTitle,
              debugShowCheckedModeBanner: appConfig.isDev,
            ),
          ),
        ),
      ),
    );
  }
}
