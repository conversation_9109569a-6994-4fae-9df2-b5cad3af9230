import 'package:permission_handler/permission_handler.dart';
import 'package:synchronized/synchronized.dart';

class PermissionManager {
  static final _instance = PermissionManager._();
  PermissionManager._();
  static final _lock = Lock();

  Future<Map<Permission, PermissionStatus>?> _requestPermissionSerially(
    List<Permission> permissions,
  ) async {
    return await _lock.synchronized(() async {
      try {
        return await permissions.request();
      } catch (e) {
        print(e.toString());
        return null;
      }
    });
  }

  Future<PermissionStatus?> _requestPermissionSeriallySingle(
    Permission permission,
  ) async {
    return await _lock.synchronized(() async {
      try {
        return await permission.request();
      } catch (e) {
        print(e.toString());
        return null;
      }
    });
  }
}

extension SerialPermissionListActions on List<Permission> {
  Future<Map<Permission, PermissionStatus>?> requestSerial() async {
    return PermissionManager._instance._requestPermissionSerially(this);
  }
}

extension SerialPermissionActions on Permission {
  Future<PermissionStatus?> requestSerial() async {
    return PermissionManager._instance._requestPermissionSeriallySingle(this);
  }
}
