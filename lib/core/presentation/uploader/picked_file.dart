import 'dart:io';
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/di/di.dart';
import 'package:uuid/uuid.dart';

part 'picked_file.freezed.dart';

@freezed
class PickedFile with _$PickedFile {
  const PickedFile._();
  const factory PickedFile({
    @Default(null) PlatformFile? file,
    @Default(false) bool isUploaded,
    @Default(null) String? uploadKey,
    @Default(null) String? uploadUrl,
    required String uuid,
  }) = _PickedFile;

  factory PickedFile.fromData(Uint8List file) => PickedFile(
        uuid: di.get<Uuid>().v1(),
        file: PlatformFile(
          name: 'example.jpeg',
          size: file.length,
          bytes: file,
        ),
      );

  factory PickedFile.fromFile(PlatformFile file) =>
      PickedFile(file: file, uuid: di.get<Uuid>().v1());

  factory PickedFile.fromUploadPath(String uploadKey, {String? uploadUrl}) =>
      PickedFile(
        uuid: di.get<Uuid>().v1(),
        isUploaded: true,
        uploadKey: uploadKey,
        uploadUrl: uploadUrl,
      );

  ImageProvider<Object> get image => file == null
      ? (uploadUrl == null
          ? Image.asset('assets/images/no_preview.png').image
          : Image.network(uploadUrl!).image)
      : file!.bytes != null
          ? Image.memory(file!.bytes!).image
          : FileImage(File(file!.path ?? ''));
}
