import 'package:freezed_annotation/freezed_annotation.dart';

part 'sku_quantity.freezed.dart';

@freezed
class SKUQuantity with _$SKUQuantity {
  const SKUQuantity._();
  const factory SKUQuantity({
    required int skuId,
    required String type,
    required String unit,
    required double lotSize,
    required double quantity,
  }) = _SKUQuantity;

  bool isBulk() {
    return type.toLowerCase() == 'bulk';
  }

  String get unitInfo => isBulk() ? 'BULK - $unit' : 'LOTS - $lotSize $unit';
  bool get isBulkKg => isBulk() && unit.toLowerCase() == 'kg';
}
