import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_mandi_status_card.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/smo_history/home/<USER>/smo_history_bloc.dart';
import 'package:proc2/features/mandi/util/get_mandi_name.dart';

class SmoHistoryHome extends StatefulWidget {
  const SmoHistoryHome({super.key});

  @override
  State<SmoHistoryHome> createState() => _SmoHistoryHomeState();
}

class _SmoHistoryHomeState extends State<SmoHistoryHome> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          centerTitle: false,
          title: Text(LanguageEnum.smoHistory.localized()),
        ),
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          padding: const EdgeInsets.only(
            left: 0,
            right: 0,
            top: 10,
          ),
          child: BlocBuilder<SmoHistoryBloc, SmoHistoryState>(
            builder: (context, smoState) {
              return smoState.when(
                initial: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                success: (success) {
                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<SmoHistoryBloc>().add(
                            const SmoHistoryEvent.started(),
                          );
                    },
                    child: Scrollbar(
                      child: ListView.builder(
                        itemCount: success.length,
                        itemBuilder: (context, index) {
                          final date = success[index][0]
                              .smoEndTime!
                              .toDate('dd MMM, yyyy');
                          final smoOpsList = success[index];
                          return Stack(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        top: 16, bottom: 16, left: 48),
                                    child: Text(date),
                                  ),
                                  for (final op in smoOpsList)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          left: 48, bottom: 8),
                                      child: WMandiStatusCard(
                                        status: op.currentStatus.localized(),
                                        mandiName: getMandiName(context,
                                            mandiId: op.mandiId),
                                        startTime: op.smoStartTime,
                                        closedAt: op.smoEndTime,
                                        onTap: () {
                                          context.pushNamed(
                                            'opsSummary',
                                            pathParameters: {
                                              'smoId': op.smoId.toString(),
                                              'mandiId': op.mandiId.toString(),
                                              'status': op.currentStatus,
                                              'startTime':
                                                  op.smoStartTime.toString(),
                                              'endTime':
                                                  op.smoEndTime.toString(),
                                            },
                                          );
                                        },
                                        statusBackgroundColor:
                                            Color(0x89f2efd3),
                                        cardBackgroundColor: Color(0xFFfefdfa),
                                      ),
                                    ),
                                ],
                              ),
                              Positioned(
                                top: 42,
                                bottom: 0.0,
                                left: 26.0,
                                child: new Container(
                                  height: double.infinity,
                                  width: 1.1,
                                  color: Colors.black,
                                ),
                              ),
                              Positioned(
                                top: 16.0,
                                left: 16.0,
                                child: new Container(
                                  height: 20.0,
                                  width: 20.0,
                                  decoration: new BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.green,
                                  ),
                                  child: new Container(
                                    margin: new EdgeInsets.all(5.0),
                                    height: 10.0,
                                    width: 10.0,
                                    decoration: new BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.white),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  );
                },
                error: (error) {
                  return ErrorScreen(
                    onPressed: () {
                      context.read<SmoHistoryBloc>().add(
                            const SmoHistoryEvent.started(),
                          );
                    },
                    message: error.message,
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
