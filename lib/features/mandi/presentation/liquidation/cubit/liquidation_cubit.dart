import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_liquidation_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';

part 'liquidation_state.dart';
part 'liquidation_cubit.freezed.dart';

@injectable
class LiquidationCubit extends Cubit<LiquidationState> {
  LiquidationCubit() : super(LiquidationState.initial());

  Future<void> getLiquidationOrders(
    int smoId,
    LiquidationOrderStatus status,
  ) async {
    final updatedState = status == LiquidationOrderStatus.pending
        ? state.copyWith(
            activeOrdersError: null,
            activeOrdersLoading: true,
          )
        : state.copyWith(
            pastOrdersError: null,
            pastOrdersLoading: true,
          );
    emit(updatedState);
    final result =
        await GetLiquidationOrderRequest(smoId: smoId, status: status)
            .execute();
    final newState = result.fold(
      (left) => status == LiquidationOrderStatus.pending
          ? state.copyWith(
              activeOrdersError: left.message,
              activeOrdersLoading: false,
            )
          : state.copyWith(
              pastOrdersError: left.message,
              pastOrdersLoading: false,
            ),
      (right) => status == LiquidationOrderStatus.pending
          ? state.copyWith(
              activeOrders: right,
              activeOrdersLoading: false,
            )
          : state.copyWith(
              pastOrders: right,
              pastOrdersLoading: false,
            ),
    );
    emit(newState);
  }

  void getAllLiquidationOrders(int smoId) async {
    await getLiquidationOrders(smoId, LiquidationOrderStatus.pending);
    await getLiquidationOrders(smoId, LiquidationOrderStatus.completed);
  }
}
