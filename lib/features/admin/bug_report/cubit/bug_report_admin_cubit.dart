import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/presentation/report_bug/api/bug_reporter_interface.dart';
import 'package:proc2/core/presentation/report_bug/model/bug_report.dart';

part 'bug_report_admin_state.dart';
part 'bug_report_admin_cubit.freezed.dart';

@injectable
class BugReportAdminCubit extends Cubit<BugReportAdminState> {
  BugReportAdminCubit(this.bugReporter) : super(BugReportAdminState.initial());
  final BugReporter bugReporter;

  void loadBugReports({required bool isResolved}) async {
    final bugReports = await bugReporter.getBugReports(isResolved: isResolved);
    emit(state.copyWith(bugReports: bugReports));
  }

  Future<void> markResolved(BugReport bugReport) async {
    final isResolved = await bugReporter.markResolved(bugReport);
    if (isResolved) {
      final reports = List<BugReport>.from(state.bugReports ?? <BugReport>[]);
      final bugReports = state.bugReports
          ?.where((element) => element.id != bugReport.id)
          .toList();
      emit(state.copyWith(bugReports: bugReports, message: 'Bug resolved!'));
    } else {
      emit(state.copyWith(message: 'Failed to resolve bug!'));
    }
  }

  void clearMessage() {
    emit(state.copyWith(message: null));
  }
}
