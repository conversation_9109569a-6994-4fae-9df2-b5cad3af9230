import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_detail_v2.dart';

class GetSmoDetailV2Request extends BaseRequest<dynamic, SmoDetailV2> {
  final int smoId;

  GetSmoDetailV2Request(this.smoId);

  @override
  String getPath() => 'smo/smoHistory/v2/getSmoDetails/$smoId';

  @override
  SmoDetailV2 mapper(data) {
    if (data is Map<String, dynamic>) {
      return SmoDetailV2.fromJson(data);
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
