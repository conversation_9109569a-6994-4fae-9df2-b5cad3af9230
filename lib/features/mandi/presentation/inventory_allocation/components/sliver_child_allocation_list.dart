import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_configuration.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/sku_allocation_card.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/total_allocation_popup.dart';

class SliverChildAllocationList extends StatelessWidget {
  const SliverChildAllocationList({
    super.key,
    required this.items,
    this.heading,
    this.headingColor,
    this.showContextMenu = false,
    required this.onQtyChanged,
    required this.isUpdateUi,
    required this.isCustomerOrder,
    required this.filterData,
    required this.isSelectionActive,
    required this.selectedItems,
    required this.onSelectedItemChange,
    required this.isEditingAllowed,
    required this.allowExpectation,
    this.forceShow = false,
    this.onAutoAllocateClick,
    required this.configuration,
  }) : showOrderColumn = isCustomerOrder;
  final List<AllocationSkuItem> items;
  final String? heading;
  final Color? headingColor;
  final bool showContextMenu;
  final Function(int index, AllocationQty qty) onQtyChanged;
  final bool showOrderColumn;
  final bool isUpdateUi;
  final bool isCustomerOrder;
  final FilterData filterData;
  final bool isSelectionActive;
  final List<String> selectedItems;
  final Function(String) onSelectedItemChange;
  final bool isEditingAllowed;
  final bool allowExpectation;
  final bool forceShow;
  final VoidCallback? onAutoAllocateClick;
  final AllocationConfiguration? configuration;

  @override
  Widget build(BuildContext context) {
    final filteredItems = forceShow
        ? items
        : isUpdateUi
            ? items
            : items.where((e) => e.isValidId).toList();
    if (filteredItems.isEmpty) return SizedBox();
    return SliverStickyHeader(
      header: heading != null
          ? Container(
              padding: EdgeInsets.only(bottom: 8, top: 8),
              color: Colors.white,
              child: onAutoAllocateClick != null
                  ? Row(
                      children: [
                        Expanded(
                          child: Center(
                            child: Text(
                              heading!,
                              style: TextStyle(
                                fontSize: 10,
                                color: headingColor ?? Colors.blue,
                                fontWeight: FontWeight.bold,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        SizedBox(
                          width: 60,
                          child: InkWell(
                            onTap: () async {
                              final shouldProceed =
                                  await context.showAlertDialog(
                                        title:
                                            'allocation.autoAllocateRemainingTitle'
                                                .tr('Auto Allocate Remaining?'),
                                        message:
                                            'allocation.autoAllocateRemainingMessage'
                                                .tr('Are you sure you want to auto allocate remaining sku?'),
                                      ) ??
                                      false;
                              if (shouldProceed) {
                                onAutoAllocateClick!();
                              }
                            },
                            child: Container(
                              padding: EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.grey.shade600,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [Text('Auto')],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                      ],
                    )
                  : Center(
                      child: Text(
                        heading!,
                        style: TextStyle(
                          fontSize: 10,
                          color: headingColor ?? Colors.blue,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
            )
          : SizedBox(
              height: 16,
            ),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final item = items[index];
            if (!isUpdateUi && !item.isValidId && !forceShow) return SizedBox();
            if (!filterData.shouldShow(item)) return SizedBox();
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: SkuAllocationCard(
                item: item,
                skuImage: item.skuImage,
                isBulkKg: item.skuQuantity.isBulkKg,
                skuName: item.skuName,
                unitString: item.skuQuantity.unitInfo,
                orderQty: item.orderedQty,
                mandiQty: item.mandiQty,
                config: configuration,
                qty: isUpdateUi
                    ? item.updateAllocationQty
                    : item.backendAllocationQty,
                showContextMenu: showContextMenu,
                onQtyChanged: (value) {
                  if (item.isValidId && value.totalQty.isEmpty) {
                    onQtyChanged(
                      index,
                      value.updateErrorMessage(
                        errorMessage: 'allocationQtyBlankError'
                            .tr('Allocation Qty can not be blank!'),
                      ),
                    );
                  } else if (item.isValidId &&
                      (value.mandiQty.toDouble() <
                          (item.distributionQuantity ?? 0))) {
                    onQtyChanged(
                      index,
                      value.updateErrorMessage(
                        errorMessage: 'allocationQtyLessThanDistribution'.tr(
                          'Allocation Qty can not be less than ##qty##. To change please update distribution first',
                          params: {
                            'qty': item.distributionQuantity?.asString()
                          },
                        ),
                      ),
                    );
                  } else {
                    onQtyChanged(index, value);
                  }
                },
                padding: EdgeInsets.only(
                  left: 8,
                  top: 8,
                  bottom: 8,
                ),
                showOrderColumn: showOrderColumn,
                isUpdateUi: isUpdateUi,
                status: item.distributionStatus,
                isCustomerOrder: isCustomerOrder,
                openManualDistribution: () {
                  context.read<AllocationJourneyCubit>().openManualDistribution(
                        item,
                      );
                },
                onSelected: () {
                  onSelectedItemChange(item.skuQuantity.compositeKey);
                },
                isSelected:
                    selectedItems.contains(item.skuQuantity.compositeKey),
                isSelectionActive: isSelectionActive,
                isManuallyEdited: item.isManuallyEdited,
                isReadOnly: !isEditingAllowed,
                allowExpectation: allowExpectation,
              ),
            );
          },
          childCount: items.length,
        ),
      ),
    );
  }
}
