import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';

@singleton
class Preference {
  Preference(@Named('HiveDirPath') this._hiveDirectoryPath);

  final String _hiveDirectoryPath;

  static const _preferenceBoxName = '_preferences';
  static const authTokenKey = '_authToken';

  @PostConstruct(preResolve: true)
  Future<void> init() async {
    await Hive.openBox(
      _preferenceBoxName,
      path: _hiveDirectoryPath,
    );
  }

  @disposeMethod
  void dispose() {
    Hive.box(_preferenceBoxName).close();
  }

  T? get<T>(dynamic key) {
    return Hive.box(_preferenceBoxName).get(key) as T?;
  }

  void put(dynamic key, dynamic value) {
    Hive.box(_preferenceBoxName).put(key, value);
  }

  void remove(dynamic key) {
    Hive.box(_preferenceBoxName).delete(key);
  }
}
