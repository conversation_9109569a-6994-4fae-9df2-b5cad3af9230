part of 'weighing_machine_bypass_cubit.dart';

@freezed
class WeighingMachineBypassState with _$WeighingMachineBypassState {
  const factory WeighingMachineBypassState.initial({
    @Default(null) MandiInfo? selectedMandi,
    @Default(null) int? smoId,
    @Default(null) List<PitstopUser>? assignedUsers,
    @Default(false) bool isLoading,
    @Default(false) bool hasAnyUpdateActionDone,
    @Default(null) String? message,
    @Default(false) bool isCtaLoading,
    @Default(false) bool isSearchLoading,
  }) = _Initial;
}
