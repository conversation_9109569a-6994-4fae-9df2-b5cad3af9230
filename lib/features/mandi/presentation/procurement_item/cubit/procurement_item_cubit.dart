import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';
import 'package:proc2/features/mandi/presentation/procurement_item/cubit/proc_item_state.dart';

@injectable
class ProcItemCubit extends Cubit<ProcItemState> {
  final MandiRepository _mandiRepository;

  ProcItemCubit({required MandiRepository mandiRepository})
      : _mandiRepository = mandiRepository,
        super(ProcItemInitial());

  // void load({required int procId, required int smoId}) async {
  //   emit(ProcItemLoading());
  //   final myProcDetailList =
  //       await _mandiRepository.getProcItem(procId: procId, smoId: smoId);
  //   final newState = myProcDetailList.fold(
  //       (left) => ProcItemError(left.message),
  //       (right) => ProcItemLoaded(right));
  //   emit(newState);
  // }

  void loadWithData(ProcDetail procDetail) {
    final procItems = procDetail.items.toList();
    emit(
      ProcItemLoaded(
        _groupProcItemsBySkuId(procItems),
        procDetail.charges
            .map((e) => ProcFieldCharge.fromJson(e))
            .map((e) => e.copyWith(
                images: e.images
                    .map((i) =>
                        i.copyWith(uploadUrl: procDetail.urls[i.uploadKey]))
                    .toList()))
            .toList(),
        di.get<VendorCubit>().getVendorLocation(
            procDetail.vendorLocationId, procDetail.vendorName),
        procDetail.urls,
        procDetail.comments,
        procDetail.parentOrder,
        procDetail.refId,
        procDetail.refSource,
      ),
    );
  }

  List<List<ProcDetailItem>> _groupProcItemsBySkuId(
      List<ProcDetailItem> procItems) {
    final skuIdMap = <int, List<ProcDetailItem>>{};

    for (final procItem in procItems) {
      final skuId = procItem.skuId;
      if (!skuIdMap.containsKey(skuId)) {
        skuIdMap[skuId] = [];
      }
      skuIdMap[skuId]!.add(procItem);
    }

    return skuIdMap.values.toList();
  }
}
