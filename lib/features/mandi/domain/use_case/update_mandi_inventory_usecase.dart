import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/input_model/mandi_inventory_input_model.dart';

@injectable
class UpdateMandiInventoryUseCase {
  UpdateMandiInventoryUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, void>> call(
    MandiInventoryInputModel inputModel,
  ) async {
    return _mandiRepository.updateMandiInventory(inputModel);
  }
}
