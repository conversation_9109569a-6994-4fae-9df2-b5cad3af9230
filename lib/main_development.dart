import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:proc2/app/app.dart';
import 'package:proc2/bootstrap.dart';

void main() {
  HttpOverrides.global = MyHttpOverrides();
  bootstrap(environment: dev, builder: () => App());
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}
