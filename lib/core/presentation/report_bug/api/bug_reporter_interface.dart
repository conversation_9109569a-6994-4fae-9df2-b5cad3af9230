import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:proc2/core/presentation/report_bug/model/bug_report.dart';
import 'package:proc2/core/presentation/report_bug/model/bug_report_log.dart';

abstract class BugReporter {
  Stream<String> reportBug(BugReportLog bugReportLog);
  Future<List<BugReport>> getBugReports({bool isResolved = false});
  Future<String?> uploadFile(File file);
  Future<String?> uploadPlatformFile(PlatformFile file);
  Future<bool> markResolved(BugReport bugReport);
}
