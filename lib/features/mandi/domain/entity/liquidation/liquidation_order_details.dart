import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';

part 'liquidation_order_details.freezed.dart';
part 'liquidation_order_details.g.dart';

@freezed
class LiquidationOrderDetails with _$LiquidationOrderDetails {
  factory LiquidationOrderDetails({
    required List<LiquidationOrder> orders,
    required List<DumpLog> dumpLogs,
    required List<AmountLog> amountLogs,
  }) = _LiquidationOrderDetails;

  factory LiquidationOrderDetails.fromJson(Map<String, dynamic> json) =>
      _$LiquidationOrderDetailsFromJson(json);
}

@freezed
class DumpLog with _$DumpLog {
  factory DumpLog({
    required double dumpedQuantity,
    required LiquidationItem liquidationItem,
    required String createdBy,
    required int createdAt,
    required VendorDetails vendor,
  }) = _DumpLog;

  factory DumpLog.fromJson(Map<String, dynamic> json) =>
      _$DumpLogFromJson(json);
}

@freezed
class AmountLog with _$AmountLog {
  factory AmountLog({
    required double receivedAmount,
    required double liquidatedQuantity,
    required LiquidationItem liquidationItem,
    required String createdBy,
    required int createdAt,
    required VendorDetails vendor,
  }) = _AmountLog;

  factory AmountLog.fromJson(Map<String, dynamic> json) =>
      _$AmountLogFromJson(json);
}

@freezed
class LiquidationOrderSummary with _$LiquidationOrderSummary {
  const LiquidationOrderSummary._();
  const factory LiquidationOrderSummary({
    required double amount,
    required LiquidationItem item,
    required String skuName,
    required VendorDetails vendor,
    required int createdAt,
  }) = _LiquidationOrderSummary;
}
