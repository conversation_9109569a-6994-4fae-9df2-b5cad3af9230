import 'package:either_dart/either.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/db/db.dart';
// import 'package:isar/isar.dart';
import 'package:proc2/core/data/network/util/network_status_code.dart';
import 'package:proc2/core/data/preference/preference.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/features/auth/data/data_source/auth_datasource.dart';
import 'package:proc2/features/auth/domain/entity/user.dart';
import 'package:proc2/features/auth/domain/repository/auth_repository.dart';

@prod
@dev
@Injectable(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl(
    this._authDataSource,
    this._firebaseAuth,
    // this._isar,
    this._preference,
    this._db,
  );
  final AuthDataSource _authDataSource;
  final firebase.FirebaseAuth _firebaseAuth;
  // final Isar _isar;
  final Preference _preference;
  final Db _db;

  GoogleSignIn get _googleSignIn => di.get();

  @override
  Future<Either<ErrorResult<void>, firebase.User>> signInWithGoogle() async {
    GoogleSignInAccount? googleUser;
    googleUser = await _googleSignIn.signIn();

    if (googleUser == null) {
      return Future.value(
        Left(
          ErrorResult(
            message: LanguageEnum.signInAborted.localized(),
            code: NetworkStatusCode.signInAborted,
            timestamp: DateTime.now().millisecondsSinceEpoch,
          ),
        ),
      );
    }

    final googleAuth = await googleUser.authentication;

    final credential = firebase.GoogleAuthProvider.credential(
      accessToken: googleAuth.accessToken,
      idToken: googleAuth.idToken,
    );

    try {
      final result = await _firebaseAuth.signInWithCredential(credential);
      final user = result.user;
      if (user == null) {
        return Future.value(
          Left(
            ErrorResult(
              message: LanguageEnum.firebaseUserNotFound.localized(),
              code: NetworkStatusCode.genricException,
              timestamp: DateTime.now().millisecondsSinceEpoch,
            ),
          ),
        );
      }
      return Future.value(Right(user));
    } on firebase.FirebaseAuthException catch (e, _) {
      return Future.value(
        Left(
          ErrorResult(
            message: e.message ?? '',
            code: NetworkStatusCode.genricException,
            timestamp: DateTime.now().millisecondsSinceEpoch,
          ),
        ),
      );
    }
  }

  @override
  Future<Either<ErrorResult<void>, User>> login({
    required String idToken,
  }) async {
    final firebaseUser = _firebaseAuth.currentUser;
    if (firebaseUser == null) {
      return Future.value(
        Left(
          ErrorResult(
            message: LanguageEnum.firebaseSignInRequired.localized(),
            code: NetworkStatusCode.genricException,
            timestamp: DateTime.now().millisecondsSinceEpoch,
          ),
        ),
      );
    }

    final loginResult = await _authDataSource.login(idToken: idToken);
    final result = loginResult.map(
      (loginDto) => User(
        id: firebaseUser.uid,
        name: loginDto.fullName,
        email: firebaseUser.email ?? '',
        jwt: loginDto.jwt,
        roles: loginDto.roles ?? [],
        photo: firebaseUser.photoURL,
      ),
    );
    if (result.isRight) {
      _preference.put(Preference.authTokenKey, result.right.jwt);
      _db.updateUser(result.right);
    }
    return Future.value(result);
  }

  @override
  Stream<User?> currentUser() {
    return _db.getCurrentUser();
  }

  @override
  Future<Either<ErrorResult<void>, void>> logOut() async {
    try {
      _preference.remove(Preference.authTokenKey);

      await _googleSignIn.disconnect();
      await _googleSignIn.signOut();
      await _firebaseAuth.signOut();
      await _db.deleteUser();

      return Future.value(const Right(null));
    } catch (e, s) {
      talker.handle(e, s);
      return Future.value(
        Left(
          ErrorResult(
            message: LanguageEnum.logoutError.localized(),
            code: 'code',
            timestamp: DateTime.now().millisecondsSinceEpoch,
          ),
        ),
      );
    }
  }

  @override
  Future<Either<ErrorResult<void>, dynamic>> sendOtp({
    required String mobile,
    bool isResend = false,
  }) async {
    final result =
        await _authDataSource.sendOtp(mobile: mobile, isResend: isResend);
    return result.fold(
      (error) => Left(ErrorResult<void>(
        message: error.message,
        code: error.code,
        timestamp: DateTime.now().millisecondsSinceEpoch,
      )),
      (data) => Right(data),
    );
  }

  @override
  Future<Either<ErrorResult<void>, User>> verifyOtp({
    required int otp,
    required String mobile,
  }) async {
    final result = await _authDataSource.verifyOtp(otp: otp, mobile: mobile);
    return result.fold(
      (error) => Left(ErrorResult<void>(
        message: error.message,
        code: error.code,
        timestamp: DateTime.now().millisecondsSinceEpoch,
      )),
      (loginDto) {
        final user = User(
          id: mobile,
          name: loginDto.fullName,
          email: '',
          jwt: loginDto.jwt,
          roles: loginDto.roles ?? [],
          photo: null,
        );

        _preference.put(Preference.authTokenKey, user.jwt);
        _db.updateUser(user);
        return Right(user);
      },
    );
  }

  @override
  Future<Either<ErrorResult<void>, String>> getFirebaseCustomToken(
      {required String jwt}) async {
    final result = await _authDataSource.getFirebaseCustomToken(jwt: jwt);
    return result.fold(
      (error) => Left(ErrorResult<void>(
        message: error.message,
        code: error.code,
        timestamp: DateTime.now().millisecondsSinceEpoch,
      )),
      (customToken) {
        return Right(customToken);
      },
    );
  }
}
