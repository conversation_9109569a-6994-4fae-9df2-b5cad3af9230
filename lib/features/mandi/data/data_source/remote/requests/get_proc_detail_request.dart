import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';

class GetMyProcDetailRequest extends BaseRequest<dynamic, List<ProcDetail>> {
  final int smoId;
  final bool isAll;
  final String? status;
  final int limit;
  final int offset;

  GetMyProcDetailRequest({
    required this.smoId,
    required this.isAll,
    required this.status,
    int? limit,
    int? offset,
  })  : limit = limit ?? 10,
        offset = offset ?? 0;

  @override
  String getPath() =>
      'procurements/${isAll ? 'allProcurements' : 'myProcurements'}/smo/$smoId${isAll ? '?status=${status ?? 'PLACED'}&limit=$limit&offset=$offset' : ''}';

  @override
  List<ProcDetail> mapper(data) {
    if (data is List<dynamic>) {
      return data
          .map((e) => e as Map<String, dynamic>)
          .map((e) => ProcDetail.from<PERSON>son(e))
          .toList();
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
