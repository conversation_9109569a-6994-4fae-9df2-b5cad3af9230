import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/user_mandi.dart';

class GetUserMandisRequest extends BaseRequest<dynamic, UserMandi> {
  final String email;

  GetUserMandisRequest({required this.email});

  @override
  String getPath() => 'users/getUserRoleMandis/$email';

  @override
  UserMandi mapper(data) {
    if (data != null) {
      return UserMandi.fromJson(data);
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
