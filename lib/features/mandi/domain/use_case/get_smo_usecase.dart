import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetSmoUseCase {
  GetSmoUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;
  Future<Either<ErrorResult<void>, Smo>> call(String mandiId) async {
    return _mandiRepository.getSmo(mandiId);
  }
}
