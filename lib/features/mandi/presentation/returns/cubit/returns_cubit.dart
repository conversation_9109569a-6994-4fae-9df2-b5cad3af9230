import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_returns_history_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_returns_list_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/reject_returns_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

part 'returns_state.dart';
part 'returns_cubit.freezed.dart';

@injectable
class ReturnsCubit extends Cubit<ReturnsState> {
  ReturnsCubit() : super(ReturnsState.data());
  bool _isWastageReturns = false;

  void load(int smoId,
      {required bool isWastageReturns, bool isHistory = false}) async {
    this._isWastageReturns = isWastageReturns;
    if (!isHistory) {
      final result = await GetReturnsListRequest(
              smoId: smoId, isWastageReturns: isWastageReturns)
          .execute();
      final newState = result.fold(
        (left) => state.copyWith(message: left.message),
        (right) {
          return state.copyWith(
            returns: right,
          );
        },
      );
      if (!isClosed) emit(newState);
    } else {
      final result = await GetReturnsHistoryRequest(
        smoId: smoId,
        isWastageReturns: isWastageReturns,
      ).execute();
      final newState = result.fold(
        (left) => state.copyWith(message: left.message),
        (right) {
          return state.copyWith(
            returnsHistory: right,
          );
        },
      );
      if (!isClosed) emit(newState);
    }
  }

  void clearMessage() {
    if (!isClosed) emit(state.copyWith(message: null));
  }

  void reject(
    int index,
    int smoId,
    String returnOrderId,
    DriversDetails? driverDetails,
    String returnType,
    int createdAt,
  ) async {
    final currentReturns = state.returns;
    final allReturns = List<Returns>.from(state.returns ?? []);
    allReturns.removeAt(index);
    emit(state.copyWith(returns: allReturns));
    final result = await RejectReturnsRequest(
      returnOrderId: returnOrderId,
      smoId: smoId,
      driversDetails: driverDetails,
      returnType: returnType,
      isWastageReturns: _isWastageReturns,
      createdAt: createdAt,
    ).execute();
    if (result.isRight) {
      emit(state.copyWith(message: result.right));
      load(
        smoId,
        isWastageReturns: _isWastageReturns,
      );
    } else {
      if (!isClosed)
        emit(state.copyWith(
          message: result.left.message,
          returns: currentReturns,
        ));
    }
  }
}
