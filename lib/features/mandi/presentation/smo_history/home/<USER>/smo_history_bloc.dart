import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

part 'smo_history_event.dart';
part 'smo_history_state.dart';
part 'smo_history_bloc.freezed.dart';

@injectable
class SmoHistoryBloc extends Bloc<SmoHistoryEvent, SmoHistoryState> {
  SmoHistoryBloc(this._mandiRepository)
      : super(const SmoHistoryState.initial()) {
    on<SmoHistoryEvent>((event, emit) async {
      await event.map(
        started: (started) async {
          final result = await _mandiRepository.smoHisoty();
          result.fold(
            (error) => emit(SmoHistoryState.error(error)),
            (data) => emit(SmoHistoryState.success(data)),
          );
        },
      );
    });
  }

  final MandiRepository _mandiRepository;
}
