import 'package:chopper/chopper.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/data/network/util/network_constants.dart';

part 'network_service.chopper.dart';

@ChopperApi()
@injectable
abstract class NetworkService extends ChopperService {
  @factoryMethod
  static NetworkService create([ChopperClient? client]) =>
      _$NetworkService(client);

  @Get(path: '{path}')
  Future<Response<Map<String, dynamic>>> get(
    @Path('path') String path,
    @QueryMap() Map<String, dynamic> query, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Get(path: '{path}', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> getNoAuth(
    @Path('path') String path,
    @QueryMap() Map<String, dynamic> query, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Post(path: '{path}')
  Future<Response<Map<String, dynamic>>> post(
    @Path('path') String path,
    @Body() Map<String, dynamic> body,
    @QueryMap() Map<String, dynamic> query,
  );

  @Post(path: '{path}', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> postNoAuth(
    @Path('path') String path,
    @Body() Map<String, dynamic> body,
    @QueryMap() Map<String, dynamic> query,
  );

  @Put(path: '{path}')
  Future<Response<Map<String, dynamic>>> put(
    @Path('path') String path,
    @Body() Map<String, dynamic> body,
    @QueryMap() Map<String, dynamic> query,
  );

  @Put(path: '{path}', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> putNoAuth(
    @Path('path') String path,
    @Body() Map<String, dynamic> body,
    @QueryMap() Map<String, dynamic> query,
  );

  @Delete(path: '{path}')
  Future<Response<Map<String, dynamic>>> delete(
    @Path('path') String path,
    @QueryMap() Map<String, dynamic> query, {
    @Body() Map<String, dynamic> body = const {},
  });

  @Delete(path: '{path}', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> deleteNoAuth(
    @Path('path') String path,
    @QueryMap() Map<String, dynamic> query, {
    @Body() Map<String, dynamic> body = const {},
  });
}
