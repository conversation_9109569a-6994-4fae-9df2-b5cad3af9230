import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/sku_history_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/smo_detail_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/smo_history_dto.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/sku_history.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_history.dart';

SmoDetail toSmoDetail(SmoDetailDto from) {
  return SmoDetail(
    id: from.id,
    createdAt: from.createdAt * 1000,
    status: from.status,
    smoId: from.smoId,
    images: from.images ?? [],
    items: from.items.map(toProcurementHistory).toList(),
    vendorName: from.vendorName,
  );
}

SkuHistory toProcurementHistory(SkuHistoryDto from) {
  return SkuHistory(
    id: from.id,
    skuId: from.skuId,
    type: from.type,
    unit: from.unit,
    lotSize: from.lotSize,
    quantity: from.quantity,
    weight: from.weight,
    amount: from.amount.toString(),
    amountDouble: from.amount,
    orderedQuantity: from.orderedQuantity,
    finalSubmit: from.finalSubmit,
    orderedCostPrice: from.orderedCostPrice,
    billedCostPrice: from.billedCostPrice,
  );
}

SmoHistory toSmoHistory(SmoHistoryDto from) {
  return SmoHistory(
    id: from.id,
    date: from.date * 1000,
    status: from.status,
    mandiId: from.mandiId,
  );
}
