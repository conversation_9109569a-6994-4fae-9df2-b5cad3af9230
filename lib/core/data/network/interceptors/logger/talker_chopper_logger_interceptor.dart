// ignore_for_file: must_be_immutable

import 'package:chopper/chopper.dart';
import 'package:proc2/core/data/network/interceptors/logger/chopper_logs.dart';
import 'package:proc2/core/data/network/interceptors/logger/talker_chopper_logger_settings.dart';
import 'package:talker/talker.dart';

/// [Chopper] http client logger on [Talker] base
///
/// [talker] filed is current [Talker] instance.
/// Provide your instance if your application used [Talker] as default logger
/// Common Talker instance will be used by default
class TalkerChopperLogger implements RequestInterceptor, ResponseInterceptor {
  TalkerChopperLogger({
    Talker? talker,
    this.settings = const TalkerChopperLoggerSettings(),
    this.addonId,
  }) {
    _talker = talker ?? Talker();
  }

  late Talker _talker;

  /// [TalkerChopperLogger] settings and customization
  TalkerChopperLoggerSettings settings;

  /// Talker addon functionality
  /// addon id for create a lot of addons
  final String? addonId;

  /// Method to update [settings] of [TalkerChopperLogger]
  void configure({
    bool? printResponseData,
    bool? printResponseHeaders,
    bool? printResponseMessage,
    bool? printRequestData,
    bool? printRequestHeaders,
    AnsiPen? requestPen,
    AnsiPen? responsePen,
    AnsiPen? errorPen,
  }) {
    settings = settings.copyWith(
      printRequestData: printRequestData,
      printRequestHeaders: printRequestHeaders,
      printResponseData: printResponseData,
      printResponseHeaders: printResponseHeaders,
      printResponseMessage: printResponseMessage,
      requestPen: requestPen,
      responsePen: responsePen,
      errorPen: errorPen,
    );
  }

  @override
  Future<Request> onRequest(Request request) async {
    final accepted = settings.requestFilter?.call(request) ?? true;
    if (!accepted) {
      return request;
    }
    try {
      final message = '${request.url}';
      final httpLog = ChopperRequestLog(
        message,
        request: request,
        settings: settings,
      );
      _talker.logTyped(httpLog);
    } catch (_) {
      //pass
    }
    return request;
  }

  @override
  Future<Response> onResponse(Response response) async {
    final accepted = settings.responseFilter?.call(response) ?? true;
    final message = '${response.base.request!.url}';
    if (!accepted) {
      final httpLog = ChopperResponseLog(
        '[Response filtered] $message',
        response: response,
        settings: settings.copyWith(
          printResponseData: false,
          printResponseHeaders: false,
          printResponseMessage: false,
        ),
      );
      _talker.logTyped(httpLog);
      return response;
    }
    try {
      bool status = true;
      if (response.body is Map<String, dynamic>) {
        status = response.body['status'] as bool? ?? true;
      }

      final httpLog = status
          ? ChopperResponseLog(
              message,
              settings: settings,
              response: response,
            )
          : ChopperErrorLog(message, response: response, settings: settings);
      _talker.logTyped(httpLog);
    } catch (_) {
      //pass
    }
    return response;
  }
}
