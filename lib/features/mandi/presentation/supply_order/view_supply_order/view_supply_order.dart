import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/supply_order/view_supply_order/cubit/view_supply_order_cubit.dart';
import 'package:proc2/features/mandi/presentation/supply_order/view_supply_order/view_supply_order_detail.dart';

class ViewSupplyOrder extends StatefulWidget {
  const ViewSupplyOrder(
      {super.key, required this.mandiId, required this.smoId});
  final int mandiId;
  final int smoId;

  @override
  State<ViewSupplyOrder> createState() => _ViewSupplyOrderState();
}

class _ViewSupplyOrderState extends State<ViewSupplyOrder> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<ViewSupplyOrderCubit>().init(widget.smoId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BlocConsumer<ViewSupplyOrderCubit, ViewSupplyOrderState>(
        listener: (context, state) {
          if (state.message != null) {
            showSnackBar(state.message!);
            context.read<ViewSupplyOrderCubit>().clearMessage();
          }
        },
        builder: (context, input) {
          return Scaffold(
            appBar: WAppBar.getAppBar(
              title: LangText('viewSupplyOrder.title', 'View Supply Order'),
              centerTitle: false,
            ),
            body: BlocBuilder<SkuBloc, SkuState>(
              builder: (context, state) {
                if (input.customerGroups == null)
                  return Center(
                    child: CircularProgressIndicator(),
                  );
                return state.map(
                  initial: (initial) => Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error) => ErrorScreen(
                    onPressed: () {},
                    message: error.errorr.message,
                  ),
                  success: (_) {
                    return Container(
                      width: MediaQuery.of(context).size.width,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          mainSearch(
                            context,
                            deliveryDate: input.deliveryDate,
                            deliverySlot: input.deliverySlot,
                            isSearchLoading: input.isSearchLoading,
                            showEdit: input.showEdit,
                            customerGroups: input.customerGroups!,
                            selectedCustomerGroup: input.selectedCustomerGroup,
                            deliverySlots: input.deliverySlots,
                          ),
                          if (input.supplyOrders?.isNotEmpty == true)
                            headings(
                              context,
                              allSelected: input.allSelected,
                            ),
                          Expanded(
                            child: input.supplyOrders == null
                                ? Center(
                                    child: Text(
                                      input.isSearchLoading
                                          ? 'Searching supply orders...'
                                          : 'Please search the supply orders from above!',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        color: input.isSearchLoading
                                            ? Colors.green
                                            : Colors.red,
                                      ),
                                    ),
                                  )
                                : input.supplyOrders!.isEmpty
                                    ? EmptyScreen(
                                        message: 'No Supply Orders Found!',
                                        description:
                                            'There are no supply orders for the given delivery date and delivery slot!',
                                      )
                                    : Scrollbar(
                                        child: ListView.builder(
                                          shrinkWrap: true,
                                          itemCount: input.supplyOrders!.length,
                                          itemBuilder: (context, index) {
                                            return supplyOrderItem(
                                              context,
                                              index: index,
                                              supplyOrder:
                                                  input.supplyOrders![index],
                                            );
                                          },
                                        ),
                                      ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget mainSearch(
    BuildContext context, {
    required DateTime? deliveryDate,
    required String? deliverySlot,
    required bool isSearchLoading,
    required bool showEdit,
    required List<String> customerGroups,
    required String? selectedCustomerGroup,
    required List<String> deliverySlots,
  }) {
    return Container(
      width: MediaQuery.of(context).size.width,
      color: Colors.grey[100],
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(8)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 1,
                blurRadius: 10,
                offset: Offset(2, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  // mainAxisSize: MainAxisSize.max,
                  children: [
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now().subtract(Duration(days: 4)),
                          lastDate: DateTime.now().add(
                            Duration(days: 4),
                          ),
                        );
                        context
                            .read<ViewSupplyOrderCubit>()
                            .updateDeliveryDate(date);
                      },
                      child: SizedBox(
                        width: 200,
                        child: TextFormField(
                          key: Key(deliveryDate?.toString() ?? ''),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                          enabled: false,
                          initialValue: deliveryDate == null
                              ? ''
                              : (deliveryDate.millisecondsSinceEpoch ~/ 1000)
                                  .toDate('dd MMM, yyyy'),
                          decoration: InputDecoration(
                            prefixIcon: Icon(Icons.calendar_month),
                            border: OutlineInputBorder(),
                            label: LangText(
                              'viewSupplyOrder.deliveryDateLabel',
                              'Delivery Date',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.black54,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 16,
                    ),
                    DropdownMenu<String>(
                      width: 200,
                      leadingIcon: Icon(Icons.wb_sunny_rounded),
                      label: LangText(
                        'viewSupplyOrder.deliverySlotLabel',
                        'Delivery Slot',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black54,
                        ),
                      ),
                      initialSelection: deliverySlot,
                      enableSearch: false,
                      enableFilter: false,
                      onSelected: (value) {
                        context
                            .read<ViewSupplyOrderCubit>()
                            .updateDeliverySlot(value);
                      },
                      dropdownMenuEntries: deliverySlots
                          .map((e) => DropdownMenuEntry(
                              value: e, label: e.capitalize()))
                          .toList(),
                    ),
                    SizedBox(
                      width: 16,
                    ),
                    DropdownMenu<String>(
                      width: 250,
                      leadingIcon: Icon(Icons.group),
                      label: LangText(
                        'viewSupplyOrder.customerGroupsLabel',
                        'Customers Group',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black54,
                        ),
                      ),
                      initialSelection: selectedCustomerGroup,
                      enableSearch: false,
                      enableFilter: false,
                      onSelected: (value) {
                        context
                            .read<ViewSupplyOrderCubit>()
                            .updateCustomerGroup(value);
                      },
                      dropdownMenuEntries: customerGroups
                          .map((e) => DropdownMenuEntry(value: e, label: e))
                          .toList(),
                    ),
                    SizedBox(
                      width: 16,
                    ),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(30),
                      child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(200, 60),
                        ),
                        onPressed: deliveryDate == null ||
                                deliverySlot == null ||
                                selectedCustomerGroup == null ||
                                isSearchLoading
                            ? null
                            : () {
                                context.read<ViewSupplyOrderCubit>().load(
                                      deliveryDate: deliveryDate,
                                      deliverySlot: deliverySlot,
                                      customerGroup: selectedCustomerGroup,
                                    );
                              },
                        icon: isSearchLoading ? SizedBox() : Icon(Icons.search),
                        label: isSearchLoading
                            ? SizedBox(
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                                width: 20,
                                height: 20,
                              )
                            : LangText(
                                'viewSupplyOrder.searchCta',
                                'Search',
                              ),
                      ),
                    ),
                    Spacer(),
                    if (showEdit)
                      ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          visualDensity: VisualDensity(),
                        ),
                        onPressed: () async {
                          final supplyOrderToEdit = context
                              .read<ViewSupplyOrderCubit>()
                              .state
                              .supplyOrders
                              ?.where((element) => element.isChecked)
                              .toList();
                          await context.push(
                            context.namedLocation('editSupplyOrder',
                                pathParameters: {
                                  'mandiId': '${widget.mandiId}'
                                }),
                            extra: supplyOrderToEdit,
                          );
                          if (deliveryDate != null &&
                              deliverySlot != null &&
                              selectedCustomerGroup != null) {
                            context.read<ViewSupplyOrderCubit>().load(
                                  deliveryDate: deliveryDate,
                                  deliverySlot: deliverySlot,
                                  customerGroup: selectedCustomerGroup,
                                );
                          }
                        },
                        icon: Icon(Icons.edit),
                        label: LangText(
                          'viewSupplyOrder.editCta',
                          'Edit Supply Order',
                        ),
                      ),
                  ],
                ),
                // Divider(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  int hoverIndex = -1;

  Widget supplyOrderItem(
    BuildContext context, {
    required SupplyOrder supplyOrder,
    required int index,
  }) {
    return MouseRegion(
      onEnter: (event) {
        setState(() {
          hoverIndex = index;
        });
      },
      onExit: (event) {
        setState(() {
          hoverIndex = -1;
        });
      },
      child: Card(
        elevation: hoverIndex == index || supplyOrder.isChecked ? 8 : 0,
        child: Container(
          decoration: BoxDecoration(
            color: index == hoverIndex || supplyOrder.isChecked
                ? Colors.grey[100]
                : Colors.white,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(
                  width: 48,
                  child: Checkbox(
                    value: supplyOrder.isChecked,
                    onChanged: supplyOrder.isPreOrder
                        ? null
                        : (value) {
                            context.read<ViewSupplyOrderCubit>().toggleSelect(
                                  index: index,
                                  isSelected: value ?? true,
                                );
                          },
                  ),
                ),
                SizedBox(
                  width: 16,
                ),
                ...supplyOrderItemValue(text: supplyOrder.customer.key),
                ...supplyOrderItemValue(
                  text: supplyOrder.orderDateString,
                  flex: 2,
                ),
                ...supplyOrderItemValue(
                  text: supplyOrder.id,
                  flex: 2,
                ),
                ...supplyOrderItemValue(
                  text: supplyOrder.customer.mobile,
                  flex: 1,
                ),
                ...supplyOrderItemValue(
                  text: supplyOrder.deliveryDateString,
                  flex: 1,
                ),
                ...supplyOrderItemValue(
                  text: supplyOrder.deliverySlot,
                  flex: 1,
                ),
                ...supplyOrderItemValue(
                    text: supplyOrder.customer.customerGroup, flex: 1),
                SizedBox(
                  width: 100,
                  child: ElevatedButton(
                    child: Text('View'),
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => BlocProvider.value(
                            value: context.read<ViewSupplyOrderCubit>(),
                            child: ViewSupplyOrderDetail(index: index),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> supplyOrderItemValue({
    required String text,
    int flex = 1,
    TextAlign textAlign = TextAlign.start,
  }) {
    return [
      Expanded(
        flex: flex,
        child: Align(
          alignment: Alignment.centerLeft,
          child: SelectionArea(
            child: Text(
              text,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
              maxLines: 2,
              textAlign: textAlign,
            ),
          ),
        ),
      ),
      SizedBox(
        width: 16,
      )
    ];
  }

  Widget headings(BuildContext context, {required bool allSelected}) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 16,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          SizedBox(
            width: 48,
            child: Checkbox(
              value: allSelected,
              onChanged: (value) {
                context
                    .read<ViewSupplyOrderCubit>()
                    .toggleSelectAll(value ?? true);
              },
            ),
          ),
          SizedBox(
            width: 16,
          ),
          ...heading(text: getLangText('viewSupplyOrder.terminal', 'Terminal')),
          ...heading(
              text: getLangText('viewSupplyOrder.orderDate', 'Order Date'),
              flex: 2),
          ...heading(
            text: getLangText('viewSupplyOrder.orderNumber', 'Order Number'),
            flex: 2,
          ),
          ...heading(
            text: getLangText('viewSupplyOrder.user', 'User'),
            flex: 1,
          ),
          ...heading(
            text: getLangText('viewSupplyOrder.deliveryDate', 'Delivery Date'),
            flex: 1,
          ),
          ...heading(
            text: getLangText('viewSupplyOrder.deliverySlot', 'Delivery Slot'),
            flex: 1,
          ),
          ...heading(
            text:
                getLangText('viewSupplyOrder.customerGroup', 'Customer Group'),
            flex: 1,
          ),
          SizedBox(
            width: 100,
          ),
        ],
      ),
    );
  }

  List<Widget> heading({
    required String text,
    int flex = 1,
    TextAlign textAlign = TextAlign.start,
  }) {
    return [
      Expanded(
        flex: flex,
        child: AutoSizeText(
          text,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
          maxLines: 1,
          textAlign: textAlign,
        ),
      ),
      SizedBox(
        width: 16,
      )
    ];
  }
}
