import 'package:proc2/features/mandi/data/data_source/remote/dto/smo/menu_config_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo/smo_dto.dart';
import 'package:proc2/features/mandi/domain/entity/smo/mandi_menu_config.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo.dart';

Smo toSmo(SmoDto from) {
  return Smo(
    smoId: from.smoDetails?.id ?? -1,
    slot: from.smoDetails?.slot ?? '',
    menuConfig: toMandiMenuConfig(from.menuConfig),
    role: from.roles ?? [],
  );
}

MandiMenuConfig toMandiMenuConfig(MenuConfigDto? from) {
  return MandiMenuConfig(
    proc: from?.proc ?? false,
    addCarryForward: from?.addCarryForward ?? false,
    addFieldCharges: from?.addFieldCharges ?? false,
    recieveInventory: from?.receiveInventory ?? false,
    mandiInventory: from?.mandiInventory ?? false,
    skuAllocation: from?.skuAllocation ?? false,
    mandiProcSummary: from?.mandiProcSummary ?? false,
    viewLoadingPlan: from?.viewLoadingPlan ?? false,
    closingStockPlan: from?.closingStockPlan ?? false,
    closeOpsBtn: from?.closeSmoOps ?? false,
    lotting: from?.lotting ?? false,
    supplyOrder: from?.supplyOrder ?? false,
    liquidation: from?.liquidation ?? false,
    grading: from?.grading ?? false,
    returns: from?.returns ?? false,
    procAdmin: from?.procAdmin ?? false,
    attendance: from?.attendance ?? false,
  );
}
