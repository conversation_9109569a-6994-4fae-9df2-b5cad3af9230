import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/ui/vendor_cache.dart';

class VendorTextField extends StatefulWidget {
  const VendorTextField({
    super.key,
    required this.controller,
    required this.isEnabled,
    this.onChanged,
    this.onMobileChanged,
  }) : showMobile = onMobileChanged != null;
  final TextEditingController controller;
  final bool isEnabled;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onMobileChanged;
  final bool showMobile;

  @override
  State<VendorTextField> createState() => _VendorTextFieldState();
}

class _VendorTextFieldState extends State<VendorTextField> {
  String lastVendorName = '';
  void initState() {
    WidgetsBinding.instance.addPersistentFrameCallback((timeStamp) async {
      final vendors = await di.get<VendorCache>().getVendors();
      vendorEntries = vendors;
      lastVendorName = widget.controller.text;
    });
    widget.controller.addListener(_listener);
    super.initState();
  }

  void _listener() {
    final currentText = widget.controller.text;
    final splits = currentText.split('-');
    if (splits.length == 2) {
      // widget.controller.text = splits[0].trim();
      if (lastVendorName != splits[0].trim()) {
        setState(() {
          lastVendorName = splits[0].trim();
        });
        widget.onChanged?.call(splits[0].trim());
      }

      if (widget.showMobile) {
        widget.onMobileChanged?.call(splits[1].trim());
      }
    } else {
      if (lastVendorName != splits[0].trim()) {
        setState(() {
          lastVendorName = splits[0].trim();
        });
        widget.onChanged?.call(splits[0].trim());
      }
    }
  }

  void dispose() {
    widget.controller.removeListener(_listener);
    super.dispose();
  }

  Map<String, String> vendorEntries = <String, String>{};
  Future<List<String>> _futureRequest(String query) async {
    widget.onChanged?.call(query);
    final itemsToSend = vendorEntries.entries
        .where((element) =>
            element.key.toLowerCase().contains(query.toLowerCase()))
        .map((e) => widget.showMobile ? '${e.key}-${e.value}' : '${e.key}')
        .toList();
    return itemsToSend.toSet().toList();
  }

  @override
  Widget build(BuildContext context) {
    return widget.isEnabled
        ? CustomDropdown.searchRequest(
            hintText: getLangText('vendorName', 'Vendor Name*'),
            controller: widget.controller,
            futureRequest: _futureRequest,
          )
        : TextFormField(
            enabled: false,
            controller: widget.controller,
            decoration: InputDecoration(
              labelText: getLangText('vendorName', 'Vendor Name*'),
              border: OutlineInputBorder(),
              isDense: true,
              contentPadding: EdgeInsets.only(
                left: 8,
                right: 4,
                top: 12,
                bottom: 12,
              ),
            ),
            onChanged: (val) {},
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            style: TextStyle(fontSize: 16),
          );
  }
}
