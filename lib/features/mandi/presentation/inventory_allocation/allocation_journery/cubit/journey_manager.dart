import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_error_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_journey_stage.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_allocation_data_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_allowed_sku_quantity_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_mandi_inventory_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';

abstract class JourneyManager {
  final StateStreamable<AllocationJourneyState> stream;
  final Emittable<AllocationJourneyState> emmiter;

  AllocationJourneyState get state => stream.state;
  void Function(AllocationJourneyState) get emit => emmiter.emit;

  JourneyManager({required this.stream, required this.emmiter});

  Future<void> initialize();
  Future<void> loadSupplyOrders() {
    return Future.value();
  }

  Future<void> retry() async {
    if (state.errorData?.functionToRetry == 'getInventory') {
      await getInventory();
    } else if (state.errorData?.functionToRetry == 'getAllocationData') {
      await getAllocationData();
    }
  }

  void addGroupCount(int count) {
    return;
  }

  void createUpdateGroup() {
    return;
  }

  Future<Inventory?> getInventory({
    String? functionToRetry,
    bool showLoadingScreen = false,
  }) async {
    if (showLoadingScreen) {
      emit(state.copyWith(
        loadingMessage: 'Fetching mandi inventory...',
        stage: AllocationJourneyStage.loading,
        previousStage: null,
      ));
    }

    final result = state.isCustomerGroup
        ? await GetAllowedSkuQuantityRequest(state.allocationId).execute()
        : (await GetMandiInventoryRequest(
                    mandiId: state.mandiId, type: 'primary')
                .execute())
            .map((right) => right.skus);
    final newState = result.fold(
      (left) => state.copyWith(
        loadingMessage: null,
        stage: AllocationJourneyStage.error,
        previousStage: null,
        errorData: AllocationErrorData(
          heading: 'Oops',
          message: left.message.isEmpty
              ? 'Error fetching mandi inventory!'
              : left.message,
          functionToRetry: functionToRetry ?? 'getInventory',
        ),
      ),
      (right) {
        final inventoryMap = <String, SkuQuantity>{};
        right.forEach((element) {
          if (element.quantity > 0) {
            inventoryMap[element.getCompositeKey()] = SkuQuantity(
              skuId: element.skuId,
              quantity: element.quantity,
              type: element.type,
              unit: element.unit,
              lotSize: element.lotSize,
            );
          }
        });
        return state.copyWith(
          mandiInventoryMap: inventoryMap,
          loadingMessage: null,
          errorData: null,
        );
      },
    );
    emit(newState);
    if (result.isLeft) return null;
    return Inventory(skus: result.right);
  }

  Future<bool> getAllocationData({
    String? functionToRetry,
    Map<String, dynamic>? retryParams,
    bool showLoadingScreen = false,
    AllocationJourneyStage? stage,
  }) async {
    // Get allocation api
    if (showLoadingScreen) {
      emit(state.copyWith(
        loadingMessage: 'Fetching allocation data...',
        stage: AllocationJourneyStage.loading,
        previousStage: null,
      ));
    }
    final result =
        await GetAllocationDataRequest(allocationId: state.allocationId)
            .execute();

    if (result.isLeft) {
      emit(
        state.copyWith(
          loadingMessage: null,
          stage: AllocationJourneyStage.error,
          previousStage: null,
          errorData: AllocationErrorData(
            heading: 'Oops!',
            message: 'Error while fetching allocation data!',
            functionToRetry: functionToRetry ?? 'getAllocationData',
            params: retryParams,
          ),
        ),
      );
      return false;
    }

    processAllocationData(
      result.right,
      stage: stage,
    );

    return true;
  }

  void processAllocationData(List<AllocationSkuItem> items,
      {AllocationJourneyStage? stage});

  Future<void> reloadAfterAllocationUpdate(
      {AllocationJourneyStage? stage, bool skipInventory = false});

  void autoAllocateRemainingSku() {}
}

abstract class StateManager
    implements
        StateStreamable<AllocationJourneyState>,
        Emittable<AllocationJourneyState> {}
