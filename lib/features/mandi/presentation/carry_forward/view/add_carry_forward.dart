import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_loss.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/add_losses/view/add_losses.dart';
import 'package:proc2/features/mandi/presentation/carry_forward/bloc/add_carry_forward_bloc.dart';
import 'package:proc2/features/mandi/presentation/carry_forward/view/excess_shortage_dialog.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

class AddCarryForward extends StatefulWidget {
  const AddCarryForward({super.key, required this.mandiName});

  final String mandiName;
  @override
  State<AddCarryForward> createState() => _AddCarryForward();
}

class _AddCarryForward extends State<AddCarryForward> {
  bool isBulkExpanded = true;
  bool isLotExpanded = true;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: BlocBuilder<SkuBloc, SkuState>(
            builder: (context, skuState) {
              return BlocConsumer<AddCarryForwardBloc, AddCarryForwardState>(
                listener: (context, state) {
                  state.maybeMap(
                    error: (error) {
                      showSnackBar(error.error.message);
                    },
                    submitted: (s) {
                      showSnackBar(s.message);
                      context.pop();
                    },
                    success: (s) async {
                      if (s.message != null) {
                        showSnackBar(s.message!);
                        context
                            .read<AddCarryForwardBloc>()
                            .add(const AddCarryForwardEvent.clearError());
                      }
                    },
                    orElse: () {},
                  );
                },
                builder: (context, state) {
                  return state.map(
                    initial: (initial) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    },
                    success: (success) {
                      final inventoryClosedBy =
                          success.carryForward.invenotryClosedBy;
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          appBar(),
                          if (inventoryClosedBy != null)
                            Card(
                              child: SizedBox(
                                width: MediaQuery.of(context).size.width,
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    left: 16,
                                    right: 16,
                                    top: 8,
                                    bottom: 8,
                                  ),
                                  child: Text(
                                    LanguageEnum
                                        .addCarryForwardInventoryClosedByMessage
                                        .localized(
                                      params: {'name': inventoryClosedBy},
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ),
                          const SizedBox(height: 10),
                          // successList(success, skuState),
                          successList(success, skuState),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 16,
                              right: 16,
                              top: 8,
                              bottom: 8,
                            ),
                            child: FilledButton(
                              onPressed:
                                  success.isCtaLoading ? null : showSubmitAlert,
                              style: FilledButton.styleFrom(
                                minimumSize: const Size(double.infinity, 50),
                              ),
                              child: success.isCtaLoading
                                  ? SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    )
                                  : Text(LanguageEnum.cfSubmitBtn.localized()),
                            ),
                          ),
                        ],
                      );
                    },
                    error: (error) {
                      return ErrorScreen(
                        onPressed: () {
                          context.read<AddCarryForwardBloc>().add(
                                AddCarryForwardEvent.started(
                                  smoId: error.smoId,
                                ),
                              );
                        },
                        message: error.error.message,
                      );
                    },
                    submitted: (submitted) {
                      return Container();
                    },
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget successList(SuccessCarryForward success, SkuState skuState) {
    if (success.carryForward.isEmpty()) {
      return Expanded(
        child: EmptyScreen(
          message: LanguageEnum.cfEmptyMessage.localized(),
          description: LanguageEnum.cfEmptyDescription.localized(),
        ),
      );
    }
    return Expanded(
      child: Scrollbar(
        child: ListView(
          children: [
            ExpansionPanelList(
              expansionCallback: (int index, bool isExpanded) {
                setState(() {
                  if (index == 0) {
                    isBulkExpanded = !isExpanded;
                  } else {
                    isLotExpanded = !isExpanded;
                  }
                });
              },
              children: [
                if (success.carryForward.bulkItems.isNotEmpty)
                  ExpansionPanel(
                    headerBuilder: (BuildContext context, bool isExpanded) {
                      return ListTile(
                        title: Text(
                          LanguageEnum.bulk.localized(),
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    },
                    body: Padding(
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      child: Column(
                        // shrinkWrap: true,
                        // Add 16 px padding to left and right of column
                        children: [
                          skuHeadingWidget(
                            heading1: LanguageEnum.sku.localized(),
                            heading2: LanguageEnum.unit.localized(),
                            heading3: LanguageEnum.qty.localized(),
                            heading4: LanguageEnum.shortageOrExcess.localized(),
                            heading5: LanguageEnum.damage.localized(),
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          for (int i = 0;
                              i < success.carryForward.bulkItems.length;
                              i++)
                            skuWidget(
                              index: i,
                              sku: skuState.getSkuName(
                                success.carryForward.bulkItems[i].skuId,
                              ),
                              value2: success.carryForward.bulkItems[i].unit,
                              quantity: success
                                  .carryForward.bulkItems[i].quantity
                                  .toString(),
                              unit: success.carryForward.bulkItems[i].unit,
                              losses: success.carryForward.bulkItems[i].loss,
                              shortageOrLosses: success
                                  .carryForward.bulkItems[i].shortOrExcess,
                              lossValue: success.carryForward.bulkItems[i]
                                  .getLoss()
                                  .toString(),
                              shortOrExcessValue: success
                                  .carryForward.bulkItems[i]
                                  .getShortOrExcessString(),
                              isBulk: true,
                            ),
                        ],
                      ),
                    ),
                    isExpanded: isBulkExpanded,
                    canTapOnHeader: true,
                  ),
                if (success.carryForward.lotItems.isNotEmpty)
                  ExpansionPanel(
                    headerBuilder: (BuildContext context, bool isExpanded) {
                      return ListTile(
                        title: Text(
                          LanguageEnum.lots.localized(),
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    },
                    body: Padding(
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      child: Column(
                        // Add 16 px padding to left and right of column
                        children: [
                          skuHeadingWidget(
                            heading1: LanguageEnum.sku.localized(),
                            heading2: LanguageEnum.lotSize.localized(),
                            heading3: LanguageEnum.qty.localized(),
                            heading4: LanguageEnum.shortageOrExcess.localized(),
                            heading5: LanguageEnum.damage.localized(),
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          for (int i = 0;
                              i < success.carryForward.lotItems.length;
                              i++)
                            skuWidget(
                              index: i,
                              sku: skuState.getSkuName(
                                success.carryForward.lotItems[i].skuId,
                              ),
                              value2: success.carryForward.lotItems[i].lotSize
                                  .toString(),
                              quantity: success
                                  .carryForward.lotItems[i].quantity
                                  .toString(),
                              lossValue: success.carryForward.lotItems[i]
                                  .getLoss()
                                  .toString(),
                              unit: success.carryForward.lotItems[i].unit,
                              shortageOrLosses: success
                                  .carryForward.lotItems[i].shortOrExcess,
                              losses: success.carryForward.lotItems[i].loss,
                              shortOrExcessValue: success
                                  .carryForward.lotItems[i]
                                  .getShortOrExcessString(),
                              isBulk: false,
                            ),
                        ],
                      ),
                    ),
                    isExpanded: isLotExpanded,
                    canTapOnHeader: true,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget skuWidget({
    required String sku,
    required String value2,
    required String quantity,
    required String lossValue,
    required String shortOrExcessValue,
    required List<CarryForwardLoss> losses,
    required List<CarryForwardLoss> shortageOrLosses,
    required String unit,
    required int index,
    required bool isBulk,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              sku,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value2,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              quantity,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 14,
              ),
            ),
          ),
          SizedBox(
            width: 8,
          ),
          Expanded(
            flex: 2,
            child: OutlinedButton(
              onPressed: () async {
                final result = await showDialog<List<CarryForwardLoss>>(
                  context: context,
                  builder: (context) => ExcessShortageDialog(
                    unit: unit,
                    losses: shortageOrLosses,
                  ),
                );
                if (result != null) {
                  // ignore: use_build_context_synchronously
                  context.read<AddCarryForwardBloc>().add(
                        AddCarryForwardEvent.updateExcessOrShortage(
                          index: index,
                          isBulk: isBulk,
                          loss: result,
                        ),
                      );
                }
              },
              child: Text(
                shortOrExcessValue,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 8,
          ),
          Expanded(
            flex: 2,
            child: OutlinedButton.icon(
              icon: const Icon(
                Icons.edit,
                color: Colors.black,
                size: 16,
              ),
              onPressed: () async {
                final result = await showDialog<List<LossInputModel>>(
                  context: context,
                  builder: (context) => AddLosses(
                    moduleKey: 'CARRY_FORWARD',
                    unit: unit,
                    losses: toLossInputModel(losses),
                    title: LanguageEnum.damage.localized(),
                  ),
                );
                if (result != null) {
                  // ignore: use_build_context_synchronously
                  context.read<AddCarryForwardBloc>().add(
                        AddCarryForwardEvent.updateLoss(
                          index: index,
                          isBulk: isBulk,
                          loss: result,
                        ),
                      );
                }
              },
              label: Text(
                lossValue,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget skuHeadingWidget({
    required String heading1,
    required String heading2,
    required String heading3,
    required String heading4,
    required String heading5,
  }) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            heading1,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            heading2,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            heading3,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(
          width: 8,
        ),
        Expanded(
          flex: 2,
          child: Text(
            heading4,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(
          width: 8,
        ),
        Expanded(
          flex: 2,
          child: Text(
            heading5,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  void showSubmitAlert() {
    Alert(
      context: context,
      type: AlertType.warning,
      title: LanguageEnum.cfConfirmationAlertTitle.localized(),
      desc: LanguageEnum.cfConfirmationAlertMsg.localized(),
      buttons: [
        DialogButton(
          onPressed: () => Navigator.pop(context),
          width: 120,
          color: Colors.red,
          child: Text(
            LanguageEnum.cancelButton.localized(),
            style: const TextStyle(color: Colors.white, fontSize: 20),
          ),
        ),
        DialogButton(
          onPressed: () {
            final smoId = context
                    .read<SmoBloc>()
                    .state
                    .mapOrNull(success: (s) => s.smo.smoId) ??
                -1;
            Navigator.pop(context);

            context
                .read<AddCarryForwardBloc>()
                .add(AddCarryForwardEvent.submitCarryForward(smoId: smoId));
          },
          width: 120,
          child: Text(
            LanguageEnum.confirmButton.localized(),
            style: const TextStyle(color: Colors.white, fontSize: 20),
          ),
        ),
      ],
    ).show();
  }

  Widget appBar() {
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: Config.primaryColor,
        boxShadow: const <BoxShadow>[
          BoxShadow(
              color: Colors.black54,
              blurRadius: 10.0,
              offset: Offset(0.0, 0.75))
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.arrow_back, color: Colors.white)),
              Expanded(
                child: Column(
                  children: [
                    const SizedBox(height: 10),
                    Text(
                      LanguageEnum.addCarryForwardTitle.localized(),
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      widget.mandiName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 10),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}

List<LossInputModel> toLossInputModel(List<CarryForwardLoss> losses) {
  return losses
      .map(
        (e) => LossInputModel(
          lossType: e.lossType,
          unit: e.unit,
          lossValue: e.lossValue,
          comment: e.comment,
          files: e.files,
          minFileUploadCount: e.minFileAllowed,
          maxFileUploadCount: e.maxFileAllowed,
        ),
      )
      .toList();
}
