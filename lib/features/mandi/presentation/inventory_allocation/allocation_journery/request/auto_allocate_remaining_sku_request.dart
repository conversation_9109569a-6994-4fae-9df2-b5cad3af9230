import 'package:proc2/core/data/network/base_request.dart';

class AutoAllocateRemainingSkuRequest extends BaseRequest<dynamic, String> {
  final int allocationId;

  AutoAllocateRemainingSkuRequest({required this.allocationId});
  @override
  String getPath() => 'allotments/$allocationId/trigger-auto-allocation';

  @override
  String mapper(data) {
    if (data is String) return data;
    throw Exception('Invalid response');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
