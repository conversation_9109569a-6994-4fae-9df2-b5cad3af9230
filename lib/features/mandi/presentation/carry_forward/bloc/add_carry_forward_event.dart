part of 'add_carry_forward_bloc.dart';

@freezed
class AddCarryForwardEvent with _$AddCarryForwardEvent {
  const factory AddCarryForwardEvent.started({required int smoId}) = _Started;
  const factory AddCarryForwardEvent.updateLoss({
    required int index,
    required bool isBulk,
    required List<LossInputModel> loss,
  }) = _UpdateLoss;
  const factory AddCarryForwardEvent.updateExcessOrShortage({
    required int index,
    required bool isBulk,
    required List<CarryForwardLoss> loss,
  }) = _UpdateExcessOrShortage;
  const factory AddCarryForwardEvent.submitCarryForward({required int smoId}) =
      _SubmitCarryForward;
  const factory AddCarryForwardEvent.clearError() = _ClearError;
}
