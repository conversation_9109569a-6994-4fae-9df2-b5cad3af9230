import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/admin/procurement_update/model/proc_child_order.dart';

class GetChildOrdersForParentRequest
    extends BaseRequest<dynamic, List<ProcChildOrder>> {
  final int parentId;

  GetChildOrdersForParentRequest({required this.parentId});

  @override
  String getPath() => 'procurements/parent/$parentId/children';

  @override
  List<ProcChildOrder> mapper(data) {
    if (data is List<dynamic>) {
      return data.map((e) => ProcChildOrder.fromJson(e)).toList();
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
