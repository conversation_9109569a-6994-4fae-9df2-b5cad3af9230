import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/allocation_filter_widget.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class AllocationListColumnHeading extends StatelessWidget {
  const AllocationListColumnHeading({
    super.key,
    required this.state,
    required this.isUpdateUi,
  });
  final AllocationJourneyState state;
  final bool isUpdateUi;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isUpdateUi)
          AllocationFilterWidget(
            filterData:
                isUpdateUi ? state.allocationFilterData : state.viewFilterData,
            onFilterDataChanged: (data) {
              context
                  .read<AllocationJourneyCubit>()
                  .updateFilterData(data: data, isAllocationFilter: isUpdateUi);
            },
            filters: state.isCustomerGroup
                ? (state.isEditingAllowed
                    ? [
                        AllocationFilter.expected,
                        AllocationFilter.pending,
                        AllocationFilter.ready,
                      ]
                    : [])
                : [AllocationFilter.expected],
            groupAllocationFilters: state.groupAllocationFilters,
            padding: EdgeInsets.symmetric(
              horizontal: 4,
              vertical: 4,
            ),
            margin: EdgeInsets.only(bottom: 8),
          ),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(12),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: 8.0,
            vertical: 8,
          ),
          alignment: Alignment.centerLeft,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  'sku'.tr('SKU'),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ).merge(TextStyles.heading),
                ),
              ),
              if (state.isCustomerGroup) ...[
                SizedBox(
                  width: 4,
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'ordered'.tr('Ordered'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ).merge(TextStyles.heading),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
              if (isUpdateUi) ...[
                SizedBox(
                  width: 4,
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'mandi'.tr('Mandi'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ).merge(TextStyles.heading),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
              SizedBox(
                width: 4,
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'allocation'.tr('Allocation'),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ).merge(TextStyles.heading),
                  textAlign: isUpdateUi ? TextAlign.end : TextAlign.center,
                ),
              ),
              if (!isUpdateUi && state.isCustomerGroup) ...[
                SizedBox(
                  width: 4,
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'status'.tr('Status'),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ).merge(TextStyles.heading),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
