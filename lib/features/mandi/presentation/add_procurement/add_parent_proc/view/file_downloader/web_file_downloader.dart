import 'dart:convert';
import 'dart:html' as html;

import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/file_downloader/file_downloader.dart';

class FileDownloaderImpl implements FileDownloader {
  @override
  Future<void> downloadText(String fileName, String data) async {
    final bytes = utf8.encode(data);
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.document.createElement('a') as html.AnchorElement
      ..href = url
      ..style.display = 'none'
      ..download = fileName;
    html.document.body?.children.add(anchor);

    anchor.click();

    html.document.body?.children.remove(anchor);
    html.Url.revokeObjectUrl(url);
  }

  @override
  Future<void> downloadBlob(String fileName, List<int>? bytes) async {
    if (bytes == null) {
      return;
    }

    // Trigger download in web
    final blob = html.Blob([bytes]);
    final url = html.Url.createObjectUrlFromBlob(blob);
    html.AnchorElement(href: url)
      ..setAttribute('download', '$fileName.xlsx')
      ..click();
    html.Url.revokeObjectUrl(url);
  }
}
