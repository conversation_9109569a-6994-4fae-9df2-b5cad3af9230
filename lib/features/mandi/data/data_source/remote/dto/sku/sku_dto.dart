import 'package:freezed_annotation/freezed_annotation.dart';

part 'sku_dto.freezed.dart';
part 'sku_dto.g.dart';

@freezed
class SkuDto with _$SkuDto {
  const factory SkuDto({
    required String name,
    required int id,
    // required String unit,
    // required double supplyLotSize,
    required Map<String, dynamic> lotSizes,
    required int parentSkuId,
    @Default('') String image,
    @Default([]) List<String> bulkUnitTypes,
    @Default([]) List<String> alternateNames,
    @Default(true) bool canProcure,
    @Default(null) String? grade,
    @Default(null) String? shelfLifeType,
    @Default('') String pickingGroupKey,
    @Default(null) String? topLevelCategory,
  }) = _SkuDto;

  @override
  factory SkuDto.fromJson(Map<String, dynamic> json) => _$SkuDtoFromJson(json);
}
