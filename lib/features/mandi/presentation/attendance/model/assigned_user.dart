class AssignedUser {
  final int attendanceRoleId;
  final int smoId;
  final String userName;
  final String? phoneNumber;
  final String? email;
  final String role;
  final int? checkedInAt;
  final int? checkedOutAt;
  final bool isWeighingMachineBypassed;

  AssignedUser({
    required this.attendanceRoleId,
    required this.smoId,
    required this.userName,
    required this.phoneNumber,
    required this.role,
    this.checkedInAt,
    this.checkedOutAt,
    this.email,
    this.isWeighingMachineBypassed = false,
  });

  AssignedUser copyWith({
    int? attendanceRoleId,
    int? smoId,
    String? userName,
    String? mobile,
    String? role,
    int? checkedInAt,
    int? checkedOutAt,
    String? email,
    bool? isWeighingMachineBypassed,
  }) {
    return AssignedUser(
      attendanceRoleId: attendanceRoleId ?? this.attendanceRoleId,
      smoId: smoId ?? this.smoId,
      userName: userName ?? this.userName,
      phoneNumber: mobile ?? this.phoneNumber,
      role: role ?? this.role,
      checkedInAt: checkedInAt ?? this.checkedInAt,
      checkedOutAt: checkedOutAt ?? this.checkedOutAt,
      email: email ?? this.email,
      isWeighingMachineBypassed:
          isWeighingMachineBypassed ?? this.isWeighingMachineBypassed,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'attendanceRoleId': attendanceRoleId,
      'smoId': smoId,
      'userName': userName,
      'phoneNumber': phoneNumber,
      'role': role,
      'checkedInAt': checkedInAt,
      'checkedOutAt': checkedOutAt,
      'email': email,
      'isWeighingMachineBypassed': isWeighingMachineBypassed,
    };
  }

  factory AssignedUser.fromMap(Map<String, dynamic> map) {
    return AssignedUser(
      attendanceRoleId: map['attendanceRoleId'],
      smoId: map['smoId'],
      userName: (map['firstName']?.toString() ?? '') +
          ' ' +
          (map['lastName']?.toString() ?? '').trim(),
      phoneNumber: map['phoneNumber'],
      role: map['role'],
      checkedInAt: map['checkedInAt'],
      checkedOutAt: map['checkedOutAt'],
      email: map['email'] ?? map['userDto']?['email'],
      isWeighingMachineBypassed: map['isWeighingMachineBypassed'] ?? false,
    );
  }
}
