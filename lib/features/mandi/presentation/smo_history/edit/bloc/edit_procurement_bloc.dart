import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/sku_history.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';
import 'package:proc2/features/mandi/domain/use_case/update_or_submit_edit_procurement_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';

part 'edit_procurement_event.dart';
part 'edit_procurement_state.dart';
part 'edit_procurement_bloc.freezed.dart';

@injectable
class EditProcurementBloc
    extends Bloc<EditProcurementEvent, EditProcurementState> {
  EditProcurementBloc(this._updateOrSubmitEditProcurementUseCase)
      : super(_Initial()) {
    on<EditProcurementEvent>((event, emit) async {
      await event.map(
        started: (started) {
          final bulkItems = started.smoDetail.items
              .where((element) => element.isBulk())
              .toList();
          final lotItems = started.smoDetail.items
              .where((element) => !element.isBulk())
              .toList();
          final fieldCharges = started.smoDetail.charges.map(
            (e) {
              final charge =
                  ProcFieldCharge.fromJson(e as Map<String, dynamic>);
              return charge.copyWith(
                images: charge.images
                    .map(
                      (e) => PickedFile.fromUploadPath(
                        e.uploadKey ?? '',
                        uploadUrl:
                            started.smoDetail.urls[e.uploadKey].toString(),
                      ),
                    )
                    .toList(),
              );
            },
          ).toList();
          final vendorLocation = di.get<VendorCubit>().getVendorLocation(
                started.smoDetail.vendorLocationId,
                started.smoDetail.vendorName,
              );
          emit(
            EditProcurementState.edit(
              bulkItems: bulkItems,
              lotItems: lotItems,
              procId: started.smoDetail.id,
              files: started.smoDetail.images
                  .map(
                    (e) => PickedFile.fromUploadPath(
                      e,
                      uploadUrl: started.smoDetail.urls[e]?.toString(),
                    ),
                  )
                  .toList(),
              procFieldCharges: fieldCharges,
              vendorLocation: vendorLocation,
              refId: started.smoDetail.refId,
              refSource: started.smoDetail.refSource,
            ),
          );
        },
        updateSlips: (e) {
          final currentState = state;
          if (currentState is EditPriceState) {
            final bulkItems = List<SkuHistory>.from(currentState.bulkItems);
            final lotItems = List<SkuHistory>.from(currentState.lotItems);
            if (e.isBulk) {
              bulkItems[e.index] = bulkItems[e.index].copyWith(
                files: e.files,
              );
            } else {
              lotItems[e.index] = lotItems[e.index].copyWith(
                files: e.files,
              );
            }
            emit(
              currentState.copyWith(
                bulkItems: bulkItems,
                lotItems: lotItems,
                files: e.allFiles,
              ),
            );
          }
        },
        updateProcCharge: (e) {
          final currentState = state;
          if (currentState is EditPriceState) {
            final newCharges =
                List<ProcFieldCharge>.from(currentState.procFieldCharges);
            newCharges[e.index] = e.charge;
            emit(currentState.copyWith(procFieldCharges: newCharges));
          }
        },
        addProcCharge: (e) {
          final currentState = state;
          if (currentState is EditPriceState) {
            final newCharges =
                List<ProcFieldCharge>.from(currentState.procFieldCharges);
            newCharges.add(e.charge);
            emit(currentState.copyWith(procFieldCharges: newCharges));
          }
        },
        deleteProcCharge: (e) {
          final currentState = state;
          if (currentState is EditPriceState) {
            final newCharges =
                List<ProcFieldCharge>.from(currentState.procFieldCharges);
            newCharges.removeAt(e.index);
            emit(currentState.copyWith(procFieldCharges: newCharges));
          }
        },
        updatePrice: (_UpdatePrice value) {
          final currentState = state;
          if (currentState is EditPriceState) {
            final bulkItems = List<SkuHistory>.from(currentState.bulkItems);
            final lotItems = List<SkuHistory>.from(currentState.lotItems);
            if (value.isBulk) {
              bulkItems[value.index] = bulkItems[value.index].copyWith(
                amount: value.price,
                isAmountUpdated: true,
              );
            } else {
              lotItems[value.index] = lotItems[value.index].copyWith(
                amount: value.price,
                isAmountUpdated: true,
              );
            }
            emit(
              currentState.copyWith(
                bulkItems: bulkItems,
                lotItems: lotItems,
              ),
            );
          }
        },
        update: (_Update value) async {
          final currentState = state;
          if (currentState is EditPriceState) {
            if (currentState.isSubmitLoading || currentState.isUpdateLoading)
              return;
            emit(currentState.copyWith(
              isSubmitLoading: value.isSubmit,
              isUpdateLoading: !value.isSubmit,
            ));
            final items = [...currentState.bulkItems, ...currentState.lotItems];
            final result = await _updateOrSubmitEditProcurementUseCase(
              smoId: value.smoId,
              procId: currentState.procId,
              items: items,
              images: currentState.files.map((e) => e.uploadKey ?? '').toList(),
              isSubmit: value.isSubmit,
              charges: currentState.procFieldCharges,
            );

            result.fold((l) {
              emit(currentState.copyWith(
                message: l.message,
                isSubmitLoading: false,
                isUpdateLoading: false,
              ));
            }, (r) {
              emit(currentState.copyWith(
                message: value.shouldPop ? r : null,
                shouldGoBack: value.shouldPop,
                isUpdateLoading: false,
                isSubmitLoading: false,
              ));
            });
          }
        },
        clearMessage: (_) {
          final currentState = state;
          if (currentState is EditPriceState) {
            emit(currentState.copyWith(
              message: null,
              shouldGoBack: false,
            ));
          }
        },
        addSlips: (_AddSlips value) async {
          final currentState = state;
          if (currentState is EditPriceState) {
            final slipsSet = Set<String>.from(value.slips
                .where((e) => e.uploadKey != null)
                .map((e) => e.uploadKey!));
            final bulkItems = List<SkuHistory>.from(currentState.bulkItems)
                .map((e) => e.copyWith(
                      files: e.files
                          .where(
                              (element) => slipsSet.contains(element.uploadKey))
                          .toList(),
                    ))
                .toList();
            final lotItems = List<SkuHistory>.from(currentState.lotItems)
                .map((e) => e.copyWith(
                      files: e.files
                          .where(
                              (element) => slipsSet.contains(element.uploadKey))
                          .toList(),
                    ))
                .toList();
            ;

            emit(
              currentState.copyWith(
                files: value.slips,
                bulkItems: bulkItems,
                lotItems: lotItems,
              ),
            );
          }

          await Future.delayed(Duration(milliseconds: 60));
          add(EditProcurementEvent.update(
            value.smoId,
            isSubmit: false,
            shouldPop: false,
          ));
        },
        clearSlips: (_ClearSlips value) {
          final currentState = state;
          if (currentState is EditPriceState) {
            emit(
              currentState.copyWith(
                files: [],
              ),
            );
          }
        },
      );
    });
  }

  final UpdateOrSubmitEditProcurementUseCase
      _updateOrSubmitEditProcurementUseCase;
}
