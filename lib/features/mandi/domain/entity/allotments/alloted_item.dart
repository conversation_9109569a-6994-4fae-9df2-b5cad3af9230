import 'package:freezed_annotation/freezed_annotation.dart';

part 'alloted_item.freezed.dart';

@freezed
class AllotedItem with _$AllotedItem {
  const AllotedItem._();

  const factory AllotedItem({
    required int id,
    required int allotmentId,
    required double availableInventory,
    required int skuId,
    required String type,
    required String unit,
    required double lotSize,
    required double quantity,
  }) = _AllotedItem;

  String getCompositeKey() {
    if (type.toLowerCase() == 'bulk') {
      return '$skuId-$type-$unit';
    } else {
      return '$skuId-$type-$unit-$lotSize';
    }
  }
}
