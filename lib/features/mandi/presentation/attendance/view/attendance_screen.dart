import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/attendance/cubit/attendance_cubit.dart';
import 'package:proc2/features/mandi/presentation/attendance/view/assign_user_popup.dart';
import 'package:proc2/features/mandi/presentation/attendance/view/attendance_loading_widget.dart';

class AttendanceScreen extends StatelessWidget {
  const AttendanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          title: Text('attendance'.tr('Attendance')),
          centerTitle: false,
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () async {
            // Open assign user popup.
            await showDialog(
              context: context,
              builder: (_) {
                return AssignUserPopup(
                  suggestionsCallback: (query) async {
                    return context
                        .read<AttendanceCubit>()
                        .getPhoneNumberSuggestion(query: query);
                  },
                  onAssignUser: (String phoneNumber, String? name) {
                    return context.read<AttendanceCubit>().assignUser(
                          phoneNumber: phoneNumber,
                          name: name,
                        );
                  },
                );
              },
            );
          },
          child: Icon(Icons.add),
        ),
        body: BlocBuilder<AttendanceCubit, AttendanceState>(
          builder: ((context, state) {
            return Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 8,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                    ),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              'name'.tr('Name'),
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 16,
                          ),
                          Expanded(
                            child: Text(
                              'phoneOrEmail'.tr('Phone/Email'),
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 16,
                          ),
                          SizedBox(
                            width: 48,
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (state.isLoading && state.assignedUsers.isEmpty)
                    AttendanceLoadingWidget(),
                  if (state.isLoading && state.assignedUsers.isNotEmpty)
                    LinearProgressIndicator(),
                  if (state.assignedUsers.isNotEmpty || !state.isLoading)
                    Expanded(
                      child: state.assignedUsers.isEmpty
                          ? ErrorScreen(
                              heading: 'attendance.noUserAssigned'.tr(
                                'No User Assigned',
                              ),
                              message: 'Please add user to give access!',
                              ctaLabel: 'refresh'.tr('Refresh'),
                              onPressed: () {
                                context
                                    .read<AttendanceCubit>()
                                    .getAssignedeUsers();
                              },
                            )
                          : RefreshIndicator(
                              onRefresh: () async {
                                context
                                    .read<AttendanceCubit>()
                                    .getAssignedeUsers();
                              },
                              child: ListView(
                                shrinkWrap: true,
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 8,
                                ),
                                children: state.assignedUsers.map((user) {
                                  return Container(
                                    padding: EdgeInsets.symmetric(
                                      vertical: 4,
                                      horizontal: 12,
                                    ),
                                    margin: EdgeInsets.only(bottom: 16),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Expanded(
                                            child: Text(
                                                user.userName.capitalize())),
                                        SizedBox(
                                          width: 16,
                                        ),
                                        Expanded(
                                            child: Text(user.email ??
                                                user.phoneNumber ??
                                                '-')),
                                        SizedBox(
                                          width: 12,
                                        ),
                                        IconButton(
                                          onPressed: () async {
                                            final shouldDelete =
                                                await context.showAlertDialog(
                                                      title: 'attendanceRemove.title'
                                                          .tr('Are you sure?'),
                                                      message:
                                                          'attendanceRemove.message'
                                                              .tr('You want to remove this user from attendance?'),
                                                    ) ??
                                                    false;
                                            if (shouldDelete) {
                                              context
                                                  .read<AttendanceCubit>()
                                                  .removeAssignedUser(
                                                    user: user,
                                                  );
                                            }
                                          },
                                          icon: Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList()
                                  ..add(Container(
                                    height: 60,
                                  )),
                              ),
                            ),
                    )
                ],
              ),
            );
          }),
        ),
      ),
    );
  }
}
