part of 'language_cubit.dart';

@freezed
class LanguageState with _$LanguageState {
  const factory LanguageState.initial() = _Initial;
  const factory LanguageState.error(ErrorResult<dynamic> error) = _Error;
  const factory LanguageState.success({
    required List<SupportedLanguage> supportedLanguages,
    @Default(null) String? message,
    @Default(null) String? selectedLanguage,
    @Default(false) bool isLoading,
  }) = LanguageSuccess;
}
