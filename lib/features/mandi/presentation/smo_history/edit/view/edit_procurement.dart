import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/uploader/image_picker_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/vendor/view/vendor_drop_down.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/sku_history.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/view/proc_field_charges_widget.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/edit/bloc/edit_procurement_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/edit/view/edit_price_dialog.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

class EditProcurement extends StatefulWidget {
  const EditProcurement({
    super.key,
    required this.smoId,
    required this.mandiId,
    this.isAdmin = false,
  });
  final int smoId;
  final int mandiId;
  final bool isAdmin;

  @override
  State<EditProcurement> createState() => _EditProcurementState();
}

class _EditProcurementState extends State<EditProcurement> {
  bool isBulkExpanded = true;
  bool isLotExpanded = true;
  GlobalKey _fieldChargeKey = GlobalKey();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BlocConsumer<EditProcurementBloc, EditProcurementState>(
        listener: (context, state) {
          state.maybeMap(
            edit: (editState) {
              if (editState.shouldGoBack) {
                context.pop();
              }
              if (editState.message != null) {
                showSnackBar(editState.message!);
                context.read<EditProcurementBloc>().add(
                      const EditProcurementEvent.clearMessage(),
                    );
              }
            },
            orElse: () {},
          );
        },
        builder: (context, smoState) {
          return smoState.map(
            initial: (i) => const Center(
              child: CircularProgressIndicator(),
            ),
            edit: (editState) {
              final listNotEmpty = editState.bulkItems.isNotEmpty ||
                  editState.lotItems.isNotEmpty;

              return Scaffold(
                backgroundColor: Colors.grey.shade300,
                appBar: AppBar(
                  centerTitle: false,
                  title: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(LanguageEnum.smoHistoryUpdatePriceTitle.localized()),
                      if (editState.refId != null)
                        Text(
                          editState.refId! + ' (${editState.refSource})',
                          style: TextStyle(fontSize: 14),
                        ),
                    ],
                  ),
                  actions: [
                    FilledButton.icon(
                      onPressed: () async {
                        final result = await Navigator.push<PickerResult?>(
                          context,
                          // ignore: inference_failure_on_instance_creation
                          MaterialPageRoute(
                            builder: (context) => ImagePickerScreen(
                              pageTitle: LanguageEnum.addProcUploadSplitsBtn
                                  .localized(),
                              reqBody: {'smoId': widget.smoId},
                              module: UploadFileModule.procurementsInvoice,
                              allowMultiple: true,
                              initialImages: editState.files,
                              maxSelection: 20,
                              deleteMessage: getLangText(
                                'editProc.deleteImagePoolMessage',
                                'Are you sure you want to delete it? This will also delete from any sku if you have selected it.',
                              ),
                            ),
                          ),
                        );
                        if (result != null) {
                          if (result.message.isNotEmpty) {
                            showSnackBar(result.message);
                          }
                          // ignore: use_build_context_synchronously
                          context.read<EditProcurementBloc>().add(
                              EditProcurementEvent.addSlips(
                                  widget.smoId, result.files));
                        }
                      },
                      icon: const Icon(
                        Icons.upload_file_outlined,
                      ),
                      label: Text(
                        LanguageEnum.addProcUploadSplitsBtn.localized() +
                            (editState.files.isEmpty
                                ? ''
                                : '(${editState.files.length})'),
                      ),
                    ),
                  ],
                ),
                body: Center(
                  child: Container(
                    width: context.maxScreenWidth,
                    height: MediaQuery.of(context).size.height,
                    padding: const EdgeInsets.only(
                      left: 5,
                      right: 5,
                      top: 10,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (editState.vendorLocation.vendorName.isNotEmpty)
                          Container(
                            margin: EdgeInsets.only(
                              bottom: 16,
                            ),
                            color: Colors.white,
                            width: double.infinity,
                            child: Center(
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  vertical: 16,
                                  horizontal: 16,
                                ),
                                child: VendorDropDown(
                                  isEnabled: false,
                                  onChanged: (location) {},
                                  selectedVendorLocation:
                                      editState.vendorLocation,
                                ),
                              ),
                            ),
                          ),
                        if (listNotEmpty) onSuccessWidget(editState),
                        Card(
                          elevation: 24,
                          shadowColor: Colors.green,
                          margin: EdgeInsets.only(bottom: 0),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                          child: Column(
                            children: [
                              AmountSummary(
                                editState,
                                onFieldChargeClick: () {
                                  Scrollable.ensureVisible(
                                    _fieldChargeKey.currentContext!,
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeOut,
                                  );
                                },
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                child: Row(
                                  children: [
                                    Expanded(child: updateButton(editState)),
                                    if (!widget.isAdmin)
                                      SizedBox(
                                        width: 16,
                                      ),
                                    if (!widget.isAdmin)
                                      Expanded(child: submitButton(editState)),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // WStickyBottomCta(
                        //   icon: Icons.send,
                        //   label: LangText('updatePrice.ctaTitle', 'Update'),
                        //   isEnabled: !(editState.isUpdateLoading ||
                        //       editState.isSubmitLoading),
                        //   isLoading: (editState.isSubmitLoading ||
                        //       editState.isUpdateLoading),
                        //   onPressed: () async {
                        //     final yes = await _showSubmitDialog();
                        //     if (yes == true) {
                        //       context.read<EditProcurementBloc>().add(
                        //             EditProcurementEvent.update(
                        //               widget.smoId,
                        //               isSubmit: _isCheckedForSubmit,
                        //             ),
                        //           );
                        //     }
                        //   },
                        // ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget updateButton(EditPriceState inputState) {
    return ElevatedButton.icon(
      style: ButtonStyle(
        padding: MaterialStateProperty.all(
          const EdgeInsets.all(
            4,
          ),
        ),
        backgroundColor: MaterialStateProperty.all(
          Colors.white,
        ),
        shape: MaterialStateProperty.all(
          RoundedRectangleBorder(
            side: BorderSide(
              color: Config.primaryColor,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        fixedSize: MaterialStateProperty.all(
          Size(MediaQuery.of(context).size.width, 40),
        ),
      ),
      onPressed: inputState.isUpdateLoading || inputState.isSubmitLoading
          ? null
          : () {
              context.read<EditProcurementBloc>().add(
                    EditProcurementEvent.update(
                      widget.smoId,
                      isSubmit: widget
                          .isAdmin, // If admin then update works as submit otherwise submit will be false
                    ),
                  );
            },
      icon: (inputState.isSubmitLoading || inputState.isUpdateLoading)
          ? SizedBox()
          : Icon(
              Icons.save,
              color: Colors.green,
            ),
      label: inputState.isUpdateLoading
          ? SizedBox(
              height: 18,
              width: 18,
              child: CircularProgressIndicator(
                color: Config.primaryColor,
              ),
            )
          : Text(
              'updatePrice.update'.tr('Update'),
              style: TextStyle(color: Config.primaryColor),
            ),
    );
  }

  Widget submitButton(EditPriceState inputState) {
    final anySkuHasNoAmountButWeight = inputState.bulkItems.any((e) =>
            e.quantity != null &&
            e.quantity! > 0 &&
            (e.amount == null || e.amount!.toDouble() <= 0)) ||
        inputState.lotItems.any((e) =>
            e.quantity != null &&
            e.quantity! > 0 &&
            (e.amount == null || e.amount!.toDouble() <= 0));
    return ElevatedButton.icon(
      style: ButtonStyle(
        padding: MaterialStateProperty.all(
          const EdgeInsets.all(4),
        ),
        shape: MaterialStateProperty.all(
          RoundedRectangleBorder(
            side: BorderSide(color: Config.primaryColor, width: 2),
            borderRadius: BorderRadius.circular(
              4,
            ),
          ),
        ),
        fixedSize: MaterialStateProperty.all(
          Size(
            MediaQuery.of(context).size.width,
            40,
          ),
        ),
      ),
      onPressed: inputState.isUpdateLoading ||
              inputState.isSubmitLoading ||
              anySkuHasNoAmountButWeight
          ? null
          : () async {
              final yes = await _showSubmitDialog();
              if (yes == true) {
                context.read<EditProcurementBloc>().add(
                      EditProcurementEvent.update(
                        widget.smoId,
                        isSubmit: true,
                      ),
                    );
              }
            },
      icon: inputState.isSubmitLoading
          ? SizedBox()
          : Icon(
              Icons.send,
              color: Colors.white,
            ),
      label: inputState.isSubmitLoading
          ? const SizedBox(
              height: 18,
              width: 18,
              child: CircularProgressIndicator(
                color: Colors.white,
              ),
            )
          : Text(
              'updatePrice.submit'.tr('Submit'),
              style: TextStyle(
                color: Colors.white,
              ),
            ),
    );
  }

  Widget AmountSummary(EditPriceState state,
      {required VoidCallback onFieldChargeClick}) {
    final productCost =
        state.bulkItems.fold(0.0, (p, e) => p + (e.amount?.toDouble() ?? 0)) +
            state.lotItems.fold(0.0, (p, e) => p + (e.amount?.toDouble() ?? 0));

    final fieldChargesCost =
        state.procFieldCharges.fold(0.0, (p, e) => p + (e.amount.toDouble()));

    final totalCost = productCost + fieldChargesCost;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'addProc.productCost'.tr('Product Cost'),
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  productCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: onFieldChargeClick,
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 4,
                    bottom: 4,
                  ),
                  child: Text(
                    'addProc.fieldCharges'.tr('Field Charges'),
                    style: TextStyle(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  fieldChargesCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Divider(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  totalCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<bool?> _showSubmitDialog() async {
    return showDialog<bool?>(
      context: context,
      barrierDismissible: false, // User must tap on a button to close dialog
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Update/Submit Prices'),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Divider(
                    thickness: 2,
                  ),
                  Text(
                    'You will not be able to update price after submitting.',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              );
            },
          ),
          actions: <Widget>[
            TextButton(
              child: Text(LanguageEnum.falseButton.localized()),
              onPressed: () {
                context.pop(false);
              },
            ),
            TextButton(
              child: Text(LanguageEnum.trueButton.localized()),
              onPressed: () {
                context.pop(true);
              },
            ),
          ],
        );
      },
    );
  }

  Widget onSuccessWidget(EditPriceState state) {
    return Expanded(
      child: Scrollbar(
        child: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            children: [
              ExpansionPanelList(
                materialGapSize: 16,
                expandedHeaderPadding: EdgeInsets.zero,
                expansionCallback: (int index, bool isExpanded) {
                  setState(() {
                    if (index == 0 && state.bulkItems.isNotEmpty) {
                      isBulkExpanded = isExpanded;
                    } else {
                      isLotExpanded = isExpanded;
                    }
                  });
                },
                children: [
                  if (state.bulkItems.isNotEmpty)
                    ExpansionPanel(
                      headerBuilder: (BuildContext context, bool isExpanded) {
                        return ListTile(
                          title: Text(
                            LanguageEnum.bulk.localized(),
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      },
                      body: Padding(
                        padding: const EdgeInsets.only(left: 0, right: 0),
                        child: Column(
                          // Add 16 px padding to left and right of column
                          children: [
                            inputHeadingWidget(true),
                            for (int i = 0; i < state.bulkItems.length; i++)
                              inputWidget(
                                state.bulkItems[i],
                                i,
                                state.files,
                                context.read(),
                              ),
                          ],
                        ),
                      ),
                      isExpanded: isBulkExpanded,
                      canTapOnHeader: true,
                    ),
                  if (state.lotItems.isNotEmpty)
                    ExpansionPanel(
                      headerBuilder: (BuildContext context, bool isExpanded) {
                        return ListTile(
                          title: Text(
                            LanguageEnum.lots.localized(),
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      },
                      body: Padding(
                        padding: const EdgeInsets.only(left: 0, right: 0),
                        child: Column(
                          // Add 16 px padding to left and right of column
                          children: [
                            inputHeadingWidget(false),
                            for (int i = 0; i < state.lotItems.length; i++)
                              inputWidget(
                                state.lotItems[i],
                                i,
                                state.files,
                                context.read(),
                              ),
                          ],
                        ),
                      ),
                      isExpanded: isLotExpanded,
                      canTapOnHeader: true,
                    ),
                ],
              ),
              Divider(
                height: 16,
              ),
              ProcFieldChargesWidget(
                key: _fieldChargeKey,
                smoId: widget.smoId,
                padding: EdgeInsets.all(8),
                fieldCharges: state.procFieldCharges,
                onChanged: (value, index) {
                  context.read<EditProcurementBloc>()
                    ..add(
                      EditProcurementEvent.updateProcCharge(
                        value,
                        index,
                      ),
                    );
                },
                enableEditing: !widget.isAdmin,
                onDelete: (index) {
                  context.read<EditProcurementBloc>()
                    ..add(
                      EditProcurementEvent.deleteProcCharge(
                        index,
                      ),
                    );
                },
                onAdd: (charge) async {
                  context.read<EditProcurementBloc>()
                    ..add(
                      EditProcurementEvent.addProcCharge(charge),
                    );
                  await Future<void>.delayed(
                    const Duration(
                      milliseconds: 200,
                    ),
                  );
                  await _scrollController.animateTo(
                    _scrollController.position.maxScrollExtent,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget inputHeadingWidget(bool isBulk) {
    return Container(
      color: Colors.grey[100],
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 8.0,
          horizontal: 16,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              flex: 2,
              child: LangText(
                'unit',
                'Unit',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              width: 4,
            ),
            Expanded(
              flex: 3,
              child: Text(
                isBulk
                    ? getLangText('orderRecQty', 'Ord/Rec Qty')
                    : getLangText('editPrice.orderedRecLots', 'Ord/Rec Lots'),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              width: 4,
            ),
            Expanded(
              flex: 2,
              child: LangText(
                'expectedAmount',
                'Expt. Amount',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              width: 4,
            ),
            Expanded(
              flex: 3,
              child: LangText(
                'actualAmount',
                'Actual Amount',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.end,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget inputWidget(
    SkuHistory inputModel,
    int index,
    List<PickedFile> allFiles,
    EditProcurementBloc bloc,
  ) {
    final hasNoAmountButWeight = inputModel.quantity != null &&
        inputModel.quantity! > 0 &&
        (inputModel.amount == null || inputModel.amount!.toDouble() <= 0);

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 0.0),
      padding: EdgeInsets.symmetric(vertical: 0.0, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                  vertical: 4,
                  horizontal: 8,
                ),
                color: Colors.grey.shade100,
                child: Text(
                  getSKU(context, skuID: inputModel.skuId).name,
                  style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.bold),
                ),
              ),
              Divider(
                height: 4,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      inputModel.getUnitString(),
                      style: TextStyle(
                        fontSize: 14,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      (inputModel.orderedQuantity?.toString() ?? '-') +
                          '/' +
                          (inputModel.quantity.toString()),
                      style: TextStyle(
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      inputModel.actualAmount,
                      style: TextStyle(
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(width: 4),
                  Expanded(
                    flex: 3,
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  final result =
                                      await showDialog<Map<String, dynamic>?>(
                                          context: context,
                                          builder: (context) =>
                                              BlocProvider<EditProcurementBloc>(
                                                create: (_) => bloc,
                                                child: EditPriceDialog(
                                                  inputModel: inputModel,
                                                  index: index,
                                                  allSlips: allFiles,
                                                  smoId: widget.smoId,
                                                ),
                                              ));
                                  if (result != null) {
                                    context.read<EditProcurementBloc>().add(
                                          EditProcurementEvent.updatePrice(
                                            index: index,
                                            price: result['amount'],
                                            isBulk: inputModel.isBulk(),
                                          ),
                                        );
                                    context
                                        .read<EditProcurementBloc>()
                                        .add(EditProcurementEvent.updateSlips(
                                          index: index,
                                          allFiles: result['allSlips'],
                                          files: result['selectedFiles'],
                                          isBulk: inputModel.isBulk(),
                                        ));
                                  }
                                },
                                child: Column(
                                  children: [
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade50,
                                        border: Border(
                                          bottom: BorderSide(
                                            color: hasNoAmountButWeight
                                                ? Colors.red
                                                : Colors.blue,
                                          ),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          inputModel.amount == null
                                              ? '-'
                                              : inputModel.amount!
                                                  .toDouble()
                                                  .asString(),
                                          style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                    if (hasNoAmountButWeight)
                                      Text(
                                        'Amount can not be empty!',
                                        style: TextStyle(
                                          color: Colors.red,
                                          fontSize: 12,
                                        ),
                                      )
                                  ],
                                ),
                              ),
                            ),
                            if (inputModel.actualAmount != '-') ...[
                              SizedBox(width: 8),
                              InkWell(
                                onTap: () {
                                  context.read<EditProcurementBloc>().add(
                                        EditProcurementEvent.updatePrice(
                                          index: index,
                                          price: inputModel.actualAmount,
                                          isBulk: inputModel.isBulk(),
                                        ),
                                      );
                                },
                                child: Icon(
                                  Icons.copy,
                                  color: Colors.blue,
                                  size: 24,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.0),
            ],
          ),
        ],
      ),
    );
  }

  Widget bulkInputWidget(
    SkuHistory inputModel,
    int index,
  ) {
    final skuBloc = context.watch<SkuBloc>();
    return Padding(
      padding: const EdgeInsets.only(
        top: 8,
        bottom: 8,
      ),
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 4,
              child: Text(
                skuBloc.state.getSkuName(inputModel.skuId) ?? '-',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Text(
                inputModel.orderedQuantity == null
                    ? '-'
                    : '${inputModel.orderedQuantity}  ${inputModel.unit.localized()}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Text(
                inputModel.quantity == null
                    ? '-'
                    : '${inputModel.quantity}  ${inputModel.unit.localized()}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.only(left: 16),
                child: TextFormField(
                  initialValue: inputModel.amount,
                  onChanged: (value) {
                    context.read<EditProcurementBloc>().add(
                          EditProcurementEvent.updatePrice(
                            index: index,
                            price: value,
                            isBulk: true,
                          ),
                        );
                  },
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    hintText: '0',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.only(
                      left: 8,
                      right: 8,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget lotHeadingWidget() {
    return Row(
      children: [
        Expanded(
          flex: 4,
          child: Text(
            LanguageEnum.sku.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 4,
          child: Text(
            LanguageEnum.lotSize.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            LanguageEnum.qty.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            LanguageEnum.price.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget lotInputWidget(
    SkuHistory inputModel,
    int index,
  ) {
    final skuBloc = context.watch<SkuBloc>();
    return Padding(
      padding: const EdgeInsets.only(
        top: 8,
        bottom: 8,
      ),
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 4,
              child: Text(
                skuBloc.state.getSkuName(inputModel.skuId) ?? '-',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Text(
                '${inputModel.lotSize}  ${inputModel.unit.localized()}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(
                  '${inputModel.quantity}',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: TextFormField(
                    initialValue: inputModel.amount,
                    onChanged: (value) {
                      context.read<EditProcurementBloc>().add(
                            EditProcurementEvent.updatePrice(
                              index: index,
                              price: value,
                              isBulk: false,
                            ),
                          );
                    },
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      hintText: '0',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.only(
                        left: 8,
                        right: 8,
                      ),
                    ),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  void showSubmitAlert() {
    Alert(
      context: context,
      type: AlertType.warning,
      title: LanguageEnum.smoHistoryUpdatePriceAlertTitle.localized(),
      desc: LanguageEnum.smoHistoryUpdatePriceAlertMessage.localized(),
      buttons: [
        DialogButton(
            onPressed: () => Navigator.pop(context),
            width: 120,
            color: Colors.red,
            child: Text(
              LanguageEnum.cancelButton.localized(),
              style: const TextStyle(color: Colors.white, fontSize: 20),
            )),
        DialogButton(
          onPressed: () {
            context.read<EditProcurementBloc>().add(
                  EditProcurementEvent.update(
                    widget.smoId,
                    isSubmit: true,
                  ),
                );
            context.pop();
          },
          width: 120,
          child: Text(
            LanguageEnum.confirmButton.localized(),
            style: const TextStyle(color: Colors.white, fontSize: 20),
          ),
        ),
      ],
    ).show();
  }
}
