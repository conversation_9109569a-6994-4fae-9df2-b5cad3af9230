import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'inventory_item.freezed.dart';
part 'inventory_item.g.dart';

@freezed
class InventoryItem with _$InventoryItem implements DropDownItem {
  const InventoryItem._();

  const factory InventoryItem({
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double quantity,
  }) = _InventoryItem;

  factory InventoryItem.fromJson(Map<String, dynamic> json) =>
      _$InventoryItemFromJson(json);

  bool isBulk() {
    return type.toLowerCase() == 'bulk';
  }

  bool isBulkKg() {
    return isBulk() && unit.toLowerCase() == 'kg';
  }

  String getCompositeKey() {
    if (isBulk()) {
      return '$skuId-$type-$unit';
    } else {
      return '$skuId-$type-$unit-$lotSize';
    }
  }

  String unitString() {
    if (isBulk()) {
      return 'Bulk-$unit';
    } else {
      return 'Lots-$lotSize-$unit';
    }
  }

  @override
  String get displayText => '';
}
