import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class CloseSmoOpsUseCase {
  CloseSmoOpsUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, void>> call({
    required int smoId,
  }) async {
    return _mandiRepository.closeSmoOps(
      smoId,
    );
  }
}
