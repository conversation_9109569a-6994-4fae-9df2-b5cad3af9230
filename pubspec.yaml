name: proc2
version: 1.9.2+78
publish_to: none
description: Wheelocity Procurement

environment:
  sdk: ^3.5.2

dependencies:
  animated_custom_dropdown: ^1.5.0
  auto_size_text: ^3.0.0
  bloc: ^8.1.3
  provider: 6.1.2
  get: ^4.6.6
  chopper: ^7.1.1+1
  device_info_plus: ^9.1.2
  either_dart: ^1.0.0
  file_picker: ^6.1.1
  firebase_analytics: ^10.8.7
  firebase_auth: ^4.17.6
  firebase_core: ^2.25.5
  firebase_crashlytics: ^3.4.16
  permission_handler: ^11.3.0
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.4
  flutter_launcher_icons: ^0.13.1
  font_awesome_flutter: ^10.7.0
  formz: ^0.7.0
  # camera: ^0.10.5+3
  freezed_annotation: ^2.4.1
  get_it: ^7.6.7
  go_router: ^13.2.0
  google_sign_in: ^6.2.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http_status_code: ^0.0.2
  injectable: ^2.3.2
  intl: ^0.19.0
  # isar: ^3.1.0+1
  # isar_flutter_libs: ^3.1.0+1
  json_annotation: ^4.8.1
  package_info_plus: ^6.0.0
  path: ^1.9.0
  path_provider: ^2.1.2
  rflutter_alert: ^2.0.7
  styled_text: ^8.1.0
  flutter_sticky_header: ^0.6.5
  sliver_tools: ^0.2.12
  table_sticky_headers: ^2.0.5
  url_launcher: ^6.2.5
  uuid: ^4.3.3
  image_picker: ^1.0.7
  csv: ^6.0.0
  connectivity_plus: ^5.0.2
  syncfusion_flutter_pdfviewer: ^24.2.8
  animated_custom_dropdown_v2: ^1.5.2
  weighing_machine:
    path: ../wow-firebase-apps/weighing_machine
  flutter_flavor: ^3.1.3
  shimmer: ^3.0.0
  flutter_typeahead: ^5.2.0
  cloud_firestore: ^4.15.6
  firebase_database: ^10.4.8
  firebase_storage: ^11.6.8
  talker: ^4.0.2
  talker_bloc_logger: ^4.0.2
  talker_flutter: ^4.0.2
  talker_logger: ^4.0.2
  shake: ^2.2.0
  screenshot_callback: ^3.0.1
  share_plus: ^7.2.2
  pretty_qr_code: ^3.3.0
  synchronized: ^3.0.0
  shorebird_code_push: ^1.1.3
  geolocator: ^12.0.0
  in_app_update: ^4.2.3
  excel: ^4.0.6

dev_dependencies:
  bloc_test: ^9.1.6
  build_runner: ^2.4.8
  chopper_generator: ^7.1.1
  flutter_test:
    sdk: flutter
  freezed: ^2.4.7
  hive_generator: ^2.0.1
  injectable_generator: ^2.4.1
  # isar_generator: ^3.1.0+1
  json_serializable: ^6.7.1
  mocktail: ^1.0.3
  # pubspec_dependency_sorter: ^1.0.3
  # very_good_analysis: ^3.1.0
  # custom_lint_builder: ^0.6.2
  # string_lang_linter:
  #   path: ../string_lang_linter

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/logo.jpg
    - assets/images/empty_box.webp
    - assets/images/empty_image_gallery.webp
    - assets/images/no_preview.png
    - shorebird.yaml
    - assets/images/mandatory_permissions/location_permission_granted.png
    - assets/images/mandatory_permissions/location_permission.png
    - assets/images/mandatory_permissions/file_permission_granted.png
    - assets/images/mandatory_permissions/file_permission.png
    - assets/images/mandatory_permissions/location_background_permission.png
    - assets/images/mandatory_permissions/location_background_permission_granted.png
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/poppins/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/poppins/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/poppins/Poppins-ExtraBold.ttf
          weight: 800
