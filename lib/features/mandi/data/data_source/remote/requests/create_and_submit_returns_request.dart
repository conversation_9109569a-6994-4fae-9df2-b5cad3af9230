import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

class CreateAndSubmitReturnsRequest extends BaseRequest<dynamic, String> {
  final String terminalId;
  final int smoId;
  final List<ReturnItem> returnItems;

  CreateAndSubmitReturnsRequest({
    required this.terminalId,
    required this.returnItems,
    required this.smoId,
  });

  @override
  String getPath() => '/api/returns/create';

  @override
  Map<String, dynamic> getBody() {
    return {
      'terminalID': terminalId,
      'returnType': 'SELFDROP',
      'itemsReturned': returnItems
          .map(
            (e) => {
              "skuID": e.skuQuantity.skuId,
              "qty": e.skuQuantity.quantity ?? 0,
              "unit": e.skuQuantity.unit,
              "type": e.skuQuantity.type,
              "lotSize": e.skuQuantity.lotSize,
            },
          )
          .toList(),
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
