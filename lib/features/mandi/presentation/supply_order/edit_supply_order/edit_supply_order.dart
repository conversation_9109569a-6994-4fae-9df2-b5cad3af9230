import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/add_sku_dialog.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/cubit/edit_supply_order_cubit.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/supply_order_cell.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/supply_order_input.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/supply_product_cell.dart';
import 'package:table_sticky_headers/table_sticky_headers.dart';

class EditSupplyOrder extends StatefulWidget {
  const EditSupplyOrder({super.key});

  @override
  State<EditSupplyOrder> createState() => _EditSupplyOrderState();
}

class _EditSupplyOrderState extends State<EditSupplyOrder> {
  int hoveredColumn = -1;
  int hoveredRow = -1;
  bool isScrolling = false;
  final ScrollControllers scrollController = ScrollControllers();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // scro.addListener(() {
      //   print('scrolling');
      // });
      scrollController.verticalBodyController.position.isScrollingNotifier
          .addListener(() {
        if (!scrollController
            .verticalBodyController.position.isScrollingNotifier.value) {
          isScrolling = false;
        } else {
          isScrolling = true;
        }
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BlocConsumer<EditSupplyOrderCubit, EditSupplyOrderState>(
        listener: (context, state) {
          if (state.shouldPop) {
            context.pop();
          }
          if (state.message != null) {
            showSnackBar(state.message!);
            context.read<EditSupplyOrderCubit>().clearMessage();
          }
        },
        builder: (context, state) {
          final terminal = state.terminals;
          if (terminal.isEmpty)
            return Center(
              child: CircularProgressIndicator(),
            );
          return Scaffold(
            appBar: WAppBar.getAppBar(
              centerTitle: false,
              title: Row(
                children: [
                  LangText('editSupplyOrder.title', 'Edit Supply Order'),
                  SizedBox(
                    width: 64,
                  ),
                  TextButton.icon(
                    style: TextButton.styleFrom(),
                    onPressed: state.isUpdating
                        ? null
                        : () {
                            context.read<EditSupplyOrderCubit>().update();
                          },
                    icon: state.isUpdating
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          )
                        : Icon(
                            Icons.save,
                            color: Colors.white,
                          ),
                    label: Text(
                      'Save',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  TextButton.icon(
                    onPressed: state.isUpdating
                        ? null
                        : () async {
                            final newSkuEntry =
                                await showDialog<List<SupplyOrderSku>?>(
                              context: context,
                              builder: (_) => AddSkuDialog(
                                terminalCount: terminal.length,
                                addedSkus: state.allAddedSkusCompositeKeys,
                              ),
                            );
                            if (newSkuEntry != null) {
                              context
                                  .read<EditSupplyOrderCubit>()
                                  .addSkuEntry(newSkuEntry);
                            }
                          },
                    icon: Icon(
                      Icons.add,
                      color: Colors.white,
                    ),
                    label: Text(
                      'Add Sku',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            body: Container(
              color: Colors.grey.shade300,
              width: MediaQuery.of(context).size.width,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                ),
                child: StickyHeadersTable(
                  scrollControllers: scrollController,
                  columnsLength: terminal.length,
                  rowsLength: terminal[0].skus.length,
                  legendCell: Container(
                      height: 80,
                      width: 200,
                      color: Colors.grey.shade300,
                      child: Center(
                        child: LangText(
                          'editSupplyOrder.productLabel',
                          'Product',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )),
                  cellDimensions: CellDimensions.fixed(
                    contentCellWidth: 100,
                    contentCellHeight: 80,
                    stickyLegendWidth: 200,
                    stickyLegendHeight: 80,
                  ),
                  columnsTitleBuilder: (index) => Container(
                    height: 80,
                    width: 100,
                    color: Colors.grey.shade300,
                    child: Center(
                      child: Text(
                        terminal[index].terminalName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  rowsTitleBuilder: (index) => SupplyProductCell(
                      total: terminal
                          .fold(0.0,
                              (p, e) => p + e.skus[index].quantity.toDouble())
                          .toString(),
                      order: terminal[0].skus[index],
                      isChecked: terminal[0].skus[index].isChecked,
                      onChanged: (value) async {
                        // Update the isChecked
                        bool shouldUpdate = true;
                        if (value == true) {
                          final qty = terminal[0].skus[index].quantity;

                          final msg = qty.isEmpty
                              ? 'Entire row values will be cleared.'
                              : 'Entire row values will be updated to $qty';
                          shouldUpdate = await context.showAlertDialog(
                                title: getLangText(
                                    'editSupplyOrder.copyAllDialogTitle',
                                    'Are you sure?'),
                                message: msg,
                              ) ??
                              false;
                        }
                        if (shouldUpdate) {
                          context
                              .read<EditSupplyOrderCubit>()
                              .onCheckboxValueChanged(
                                  value: value, index: index);
                        }
                      },
                      onEdit: () async {
                        final allSkuOrder =
                            terminal.map((e) => e.skus[index]).toList();
                        final updatedSkuEntry =
                            await showDialog<List<SupplyOrderSku>?>(
                          context: context,
                          builder: (_) => AddSkuDialog(
                            previousSku: allSkuOrder,
                            addedSkus: state.allAddedSkusCompositeKeys,
                            terminalCount: terminal.length,
                          ),
                        );
                        if (updatedSkuEntry != null) {
                          context.read<EditSupplyOrderCubit>().updateSkuEntry(
                                entry: updatedSkuEntry,
                                index: index,
                              );
                        }
                      }),
                  contentCellBuilder: (columnIndex, rowIndex) {
                    final order = terminal[columnIndex].skus[rowIndex];
                    return SupplyOrderCell(
                      isInputDisabled: !terminal[columnIndex].isEditAllowed
                          ? true
                          : columnIndex == 0
                              ? state.isUpdating
                              : terminal[0].skus[rowIndex].isChecked ||
                                  state.isUpdating,
                      suffix: order.skuQuantity.isBulk
                          ? order.skuQuantity.unit
                          : 'Lots',
                      lotSize: order.skuQuantity.isBulk
                          ? ''
                          : '${order.skuQuantity.lotSize} ${order.skuQuantity.unit} each',
                      hasValueChanged: order.hasChanged,
                      text: order.quantity,
                      showBottomBorder: rowIndex == terminal[0].skus.length - 1,
                      showRightBorder: columnIndex == terminal.length - 1,
                      onChanged: (value) {
                        context.read<EditSupplyOrderCubit>().onQuantityChanged(
                              value: value,
                              columnIndex: columnIndex,
                              rowIndex: rowIndex,
                            );
                      },
                    );
                  },
                  showHorizontalScrollbar: true,
                  showVerticalScrollbar: true,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
