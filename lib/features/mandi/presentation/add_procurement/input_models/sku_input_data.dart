import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:uuid/uuid.dart';

part 'sku_input_data.freezed.dart';

@freezed
class SkuInputData with _$SkuInputData {
  const SkuInputData._();
  const factory SkuInputData({
    @Default(-1) int procItemId,
    required Sku sku,
    required ProcurementType procurementType,
    required String? procurementUnit,
    required double? lotSize,
    required String? quantity, // ReceiveQty
    required String? amount,
    required String? weight,
    required String uuid,
    @Default(false) bool isReceived,
    @Default([]) List<LossInputModel> losses,
    // @Default(true) bool canEdit,
    @Default(null) String? orderedQuantity,
    @Default(false) bool isFinal,
    @Default(null) String? newReceivedQty,
    @Default(null) String? weighingSource,
    @Default(true) bool newlyAdded,
    @Default(false) bool hasChanged,
    @Default(null) double? orderedCostPrice,
    @Default(null) double? billedCostPrice,
  }) = _SkuInputData;

  bool isBulk() {
    return procurementType == ProcurementType.bulk;
  }

  double get _totalQty =>
      (quantity?.toDouble() ?? 0) + (newReceivedQty?.toDouble() ?? 0);
  double get totalQty => double.parse(_totalQty.toStringAsFixed(3));

  String totalReceived() {
    return totalQty > 0 ? totalQty.toString() : '-';
  }

  String getCompositeKey() {
    if (isBulk()) {
      return '${sku.id}-${procurementType.value}-$procurementUnit';
    } else {
      return '${sku.id}-${procurementType.value}-$procurementUnit-$lotSize';
    }
  }

  factory SkuInputData.from(ProcDetailItem item, List<Sku> skus) {
    final amount = item.billedCostPrice == null && item.orderedCostPrice == null
        ? item.amount
        : (item.quantity ?? 0) == 0
            ? ((item.orderedQuantity ?? 0) * (item.orderedCostPrice ?? 0))
            : (item.billedCostPrice ?? item.orderedCostPrice)! *
                (item.quantity ?? 0);
    return SkuInputData(
      sku: skus.firstWhere((element) => element.id == item.skuId,
          orElse: () => Sku(id: -1, name: '-', lotSizes: {}, parentSkuId: -1)),
      procurementType: item.type.toLowerCase() == 'bulk'
          ? ProcurementType.bulk
          : ProcurementType.lots,
      procurementUnit: item.unit,
      lotSize: item.lotSize,
      amount: amount?.asString(maxDecimalDigits: 3) ?? '',
      weight: item.weight.toString(),
      uuid: di.get<Uuid>().v1(),
      quantity: item.quantity?.toString(),
      isReceived: item.finalSubmit ?? false,
      newlyAdded: false,
      orderedQuantity: item.orderedQuantity?.toString() ?? '',
      procItemId: item.id ?? -1,
      orderedCostPrice: item.orderedCostPrice,
      billedCostPrice: item.billedCostPrice,
    );
  }

  String getUnitString() {
    if (procurementType == ProcurementType.bulk) {
      return 'Bulk - $procurementUnit';
    }
    return 'Lots - $lotSize $procurementUnit';
  }

  double getTotalLoss() {
    return losses.fold(0, (previousValue, element) {
      return previousValue + (double.tryParse(element.lossValue) ?? 0.0);
    });
  }

  factory SkuInputData.withSku(Sku sku) => SkuInputData(
        sku: sku,
        procurementType: ProcurementType.bulk,
        procurementUnit: null,
        lotSize: null,
        quantity: null,
        amount: null,
        uuid: di.get<Uuid>().v1(),
        weight: null,
      );

  factory SkuInputData.fromJson(Map<dynamic, dynamic> json) {
    return SkuInputData(
      sku: Sku.fromJson(json['sku'] as Map<dynamic, dynamic>),
      procurementType: ProcurementType.values.firstWhere(
        (element) => element.value == json['procurementType'] as String,
      ),
      procurementUnit: json['procurementUnit'] as String?,
      lotSize: json['lotSize'] as double?,
      quantity: json['quantity'] as String?,
      amount: json['amount'] as String?,
      weight: json['weight'] as String?,
      uuid: json['uuid'] as String,
      orderedCostPrice: json['orderedCostPrice'] as double?,
      billedCostPrice: json['billedCostPrice'] as double?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sku': sku.toJson(),
      'procurementType': procurementType.value,
      'procurementUnit': procurementUnit,
      'lotSize': lotSize,
      'quantity': quantity,
      'amount': amount,
      'weight': weight,
      'uuid': uuid,
      'orderedCostPrice': orderedCostPrice,
      'billedCostPrice': billedCostPrice,
    };
  }
}
