import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/domain/use_case/get_charges_type_usecase.dart';

part 'field_charge_cubit.freezed.dart';
part 'field_charge_state.dart';

@singleton
class FieldChargeCubit extends Cubit<FieldChargeState> {
  FieldChargeCubit(this._chargesTypeUseCase)
      : super(FieldChargeState.initial());

  final GetChargesTypeUseCase _chargesTypeUseCase;
  Future<void> getFieldCharges({bool hardRefresh = false}) async {
    if (!isClosed) {
      emit(state.copyWith(error: null));
    }
    if (!hardRefresh &&
        state.procFieldCharges != null &&
        state.procFieldCharges!.isNotEmpty) return;
    final newState = await _chargesTypeUseCase().then(
      (value) => value.fold(
        (left) => state.copyWith(error: left.message),
        (right) => state.copyWith(
            procFieldCharges: right.proc.map((e) => ChargeType(e)).toList()),
      ),
    );
    if (!isClosed) emit(newState);
  }
}
