import yaml
import requests
import csv
import re

# Load pubspec.yaml
with open('pubspec.yaml', 'r') as file:
    pubspec = yaml.safe_load(file)

# Create a new CSV file and a writer
with open('dependencies.csv', 'w', newline='') as file:
    writer = csv.writer(file)

    # Write the headers to the CSV file
    writer.writerow(['Package Name', 'Current Version', 'Latest Version', 'Library Link'])

    # Get the dependencies and dev_dependencies
    dependencies = pubspec.get('dependencies', {})
    dev_dependencies = pubspec.get('dev_dependencies', {})

    # Combine dependencies and dev_dependencies
    all_dependencies = {**dependencies, **dev_dependencies}

    # Remove any Flutter SDK dependencies
    all_dependencies.pop('flutter', None)
    all_dependencies.pop('flutter_test', None)

    # Check each dependency
    for name, version in all_dependencies.items():
        # Skip if the dependency is a local path or depends on the Flutter SDK
        if isinstance(version, dict) or 'flutter' in version:
            continue

        # Remove any leading characters like ^
        version = re.sub(r'^[^0-9]*', '', version)

        try:
            # Get the latest version from pub.dev
            response = requests.get(f'https://pub.dev/api/packages/{name}')
            data = response.json()
            latest_version = data['latest']['version'] if 'latest' in data else 'Not found'
        except requests.exceptions.RequestException as e:
            print(f"Error fetching data for {name}: {e}")
            latest_version = 'Error'

        # Skip if the current version is the same as the latest version
        if version == latest_version:
            continue

        # Create the library link
        link = f'https://pub.dev/packages/{name}'

        # Write the data to the CSV file
        writer.writerow([name, version, latest_version, link])