import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/charges/field_charges.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetFieldChargesUseCase {
  GetFieldChargesUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, FieldCharges>> call(int smoId) async {
    return _mandiRepository.getFieldCharges(smoId);
  }
}
