import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';

part 'carry_forward_loss.freezed.dart';

@freezed
class CarryForwardLoss with _$CarryForwardLoss {
  const CarryForwardLoss._();
  const factory CarryForwardLoss({
    required String lossType,
    required String lossValue,
    required String unit,
    required String comment,
    @Default([]) List<PickedFile> files,
    @Default(0) int maxFileAllowed,
    @Default(1) int minFileAllowed,
  }) = _CarryForwardLoss;

  static const empty = CarryForwardLoss(
    lossType: '',
    lossValue: '',
    unit: '',
    comment: '',
  );
}
