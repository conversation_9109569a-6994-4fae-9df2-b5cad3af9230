import 'package:freezed_annotation/freezed_annotation.dart';

part 'assigned_mandi.freezed.dart';
part 'assigned_mandi.g.dart';

@freezed
class AssignedMandi with _$AssignedMandi {
  const factory AssignedMandi({
    required int roleId,
    required String roleName,
    required List<int> mandis,
  }) = _AssignedMandi;

  factory AssignedMandi.fromJson(Map<String, dynamic> json) =>
      _$AssignedMandiFromJson(json);
}
