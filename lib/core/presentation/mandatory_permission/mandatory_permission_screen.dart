import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/mandatory_permission/mandatory_permission_manager.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:synchronized/synchronized.dart';

class MandatoryPermissionScreen extends StatefulWidget {
  const MandatoryPermissionScreen({super.key});

  @override
  State<MandatoryPermissionScreen> createState() =>
      _MandatoryPermissionScreenState();
}

class _MandatoryPermissionScreenState extends State<MandatoryPermissionScreen>
    with WidgetsBindingObserver {
  bool canPop = false;
  List<PermissionStatus> statuses = [];
  bool shouldOpenSettings = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await updateState();
    });
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      updateState();
    }
    super.didChangeAppLifecycleState(state);
  }

  Future<void> updateState(
      {bool shouldPopIfAllPermissionGranted = true}) async {
    statuses = await MandatoryPermissionManager.getAllPermissionStatus();
    shouldOpenSettings = await MandatoryPermissionManager.shouldOpenSettings();
    final allGranted =
        await MandatoryPermissionManager.isAllPermissionGranted();
    if (context.mounted) {
      if (allGranted && shouldPopIfAllPermissionGranted) {
        await pop();
      } else {
        setState(() {});
      }
    }
  }

  final _lock = Lock();
  Future<void> pop() async {
    if (_lock.locked) return;
    return await _lock.synchronized(() async {
      await Future.delayed(const Duration(milliseconds: 500));
      canPop = true;
      if (context.mounted) Navigator.of(context).pop(true);
    });
  }

  Future<void> onCtaClick() async {
    if (shouldOpenSettings) {
      final openSettings = await showDialog(
              context: context,
              builder: (_) => AlertDialog(
                    title: Text('openSettings'.tr('Open Settings')),
                    content: Text('openSettingsMessage'
                        .tr('Please open settings and grant all permissions')),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: Text('cancel'.tr('Cancel')),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: Text('openSettings'.tr('Open Settings')),
                      ),
                    ],
                  )) ??
          false;
      if (!openSettings) {
        return;
      }
      final didOpenSettings = await openAppSettings();
      if (didOpenSettings) {
        return;
      }
    } else {
      await MandatoryPermissionManager.startGrantingProcess(context, () async {
        await updateState(shouldPopIfAllPermissionGranted: false);
      });
      updateState();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: PopScope(
      canPop: canPop,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        final isAllPermissionGranted =
            await MandatoryPermissionManager.isAllPermissionGranted();
        if (isAllPermissionGranted && context.mounted) {
          await pop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text('mandatoryPermissionsTitle'.tr('Mandatory Permissions')),
          centerTitle: false,
        ),
        body: Column(
          children: [
            statuses.isEmpty
                ? const Center(
                    child: SizedBox(
                        width: 48,
                        height: 48,
                        child: CircularProgressIndicator()),
                  )
                : Expanded(
                    child: Center(
                      child: Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 8,
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListView.separated(
                          padding: const EdgeInsets.symmetric(
                            vertical: 0,
                          ),
                          separatorBuilder: (context, index) => const Divider(),
                          shrinkWrap: true,
                          itemCount: MandatoryPermissionManager
                              .mandatoryPermissions.length,
                          itemBuilder: (context, index) {
                            final mandatoryPermission =
                                MandatoryPermissionManager
                                    .mandatoryPermissions[index];
                            final status = statuses[index];
                            return ListTile(
                              title: Text(
                                mandatoryPermission.overviewText(),
                                style: const TextStyle(
                                  fontSize: 14,
                                ),
                              ),
                              subtitle: mandatoryPermission.subText == null
                                  ? null
                                  : Text(
                                      mandatoryPermission.subText!(),
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                      ),
                                    ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              leading: Opacity(
                                opacity: status == PermissionStatus.granted
                                    ? 1
                                    : 0.3,
                                child: Image(
                                  width: 48,
                                  height: 48,
                                  image: AssetImage(
                                    mandatoryPermission.imagePath
                                        .getPath(status),
                                  ),
                                ),
                              ),
                              trailing: status == PermissionStatus.granted
                                  ? const Icon(
                                      Icons.check_circle,
                                      color: Colors.green,
                                    )
                                  : status == PermissionStatus.permanentlyDenied
                                      ? const Icon(
                                          Icons.close_rounded,
                                          color: Colors.red,
                                        )
                                      : SizedBox(
                                          width: 80,
                                          child: Text(
                                            'pending'.tr('Pending'),
                                            style: TextStyle(
                                              color: Colors.amber.shade800,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
            WStickyBottomCta(
              icon: Icons.security,
              label: Text(shouldOpenSettings
                  ? 'openSettings'.tr('Open Settings')
                  : 'grantPermissions'.tr('Grant Permissions')),
              onPressed: onCtaClick,
            ),
          ],
        ),
      ),
    ));
  }
}
