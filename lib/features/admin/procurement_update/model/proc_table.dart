import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/admin/procurement_update/cubit/procurement_update_edit_cubit.dart';
import 'package:proc2/features/admin/procurement_update/model/proc_child_order.dart';
import 'package:proc2/features/admin/procurement_update/model/proc_table_item.dart';
import 'package:proc2/features/admin/procurement_update/model/proc_table_row.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';

class ProcTable {
  final Map<int, Sku> skuMap;
  final List<SkuQuantity> skus;
  final List<ProcTableRow> pitstops;
  final List<double> totalOrdered;
  final List<double> totalReceived;
  final bool hasChanges;

  ProcTable({
    required this.skuMap,
    required this.skus,
    required this.pitstops,
    required this.totalOrdered,
    required this.totalReceived,
    this.hasChanges = false,
  });

  factory ProcTable.fromResponse({
    required Map<int, Sku> skuMap,
    required List<ProcChildOrder> childOrders,
    required Map<int, MandiInfo> mandis,
  }) {
    final allOrderedSkuIds = <String, SkuQuantity>{};
    final mandiIdToSkuQuantityMap = <int, Map<String, ProcDetailItem>>{};
    final mandiIdToProcIdMap = <int, int>{};

    for (final order in childOrders) {
      final mandiId = order.mandiId;
      final procId = order.id;
      mandiIdToProcIdMap[mandiId] = procId;
      final items = order.items;
      final skuQuantityMap =
          mandiIdToSkuQuantityMap.putIfAbsent(mandiId, () => {});
      for (final item in items) {
        skuQuantityMap.putIfAbsent(item.skuQuantity.compositeKey, () => item);
        allOrderedSkuIds.putIfAbsent(
            item.skuQuantity.compositeKey, () => item.skuQuantity);
      }
      mandiIdToSkuQuantityMap[mandiId] = skuQuantityMap;
    }

    final skus = allOrderedSkuIds.values.toList();

    final pitstops = <ProcTableRow>[];
    for (final entry in mandiIdToSkuQuantityMap.entries) {
      final mandiId = entry.key;
      final procOrderId = mandiIdToProcIdMap[mandiId]!;
      final skuQuantityMap = entry.value;
      final items = <ProcTableItem>[];
      for (final sku in skus) {
        final item = skuQuantityMap[sku.compositeKey];
        items.add(ProcTableItem(
          itemId: item?.id ?? -1,
          orderedQuantity: item?.orderedQuantity?.asString() ?? '',
          lastOrderedQuantity: item?.orderedQuantity?.asString() ?? '',
          receivedQuantity: item?.skuQuantity.quantity,
        ));
      }
      pitstops.add(ProcTableRow(
        mandi: mandis[mandiId]!,
        items: items,
        deletedItems: [],
        procurementId: procOrderId,
      ));
    }

    final total = List<double>.filled(skus.length, 0);
    final totalReceived = List<double>.filled(skus.length, 0);
    for (var i = 0; i < skus.length; i++) {
      total[i] = pitstops
          .map((e) => e.items[i].orderedQuantity)
          .map((e) => e.toDouble())
          .fold(0, (p, e) => p + e);
      totalReceived[i] = pitstops
          .map((e) => e.items[i].receivedQuantity)
          .map((e) => e ?? 0)
          .fold(0, (p, e) => p + e);
    }
    return ProcTable(
      skuMap: skuMap,
      skus: skus,
      pitstops: pitstops,
      totalOrdered: total,
      totalReceived: totalReceived,
    );
  }

  ProcTable copyWith({
    Map<int, Sku>? skuMap,
    List<SkuQuantity>? skus,
    List<ProcTableRow>? pitstops,
    List<double>? total,
    List<double>? totalReceived,
    bool? hasChanges,
  }) {
    return ProcTable(
      skuMap: skuMap ?? this.skuMap,
      skus: skus ?? this.skus,
      pitstops: pitstops ?? this.pitstops,
      totalOrdered: total ?? this.totalOrdered,
      totalReceived: total ?? this.totalReceived,
      hasChanges: hasChanges ?? this.hasChanges,
    );
  }

  int getColumnLength() {
    return skus.length;
  }

  int getRowLength() {
    return pitstops.length + 1;
  }

  String getColumnName(int index) {
    return skuMap[skus[index].skuId]!.name;
  }

  String getRowName(int index) {
    if (index == pitstops.length) {
      return 'total'.tr('Total');
    }
    return pitstops[index].mandi.name;
  }

  Widget getCellWidget({
    required int columnIndex,
    required int rowIndex,
    required int? focusedItemId,
    required ValueChanged<int?> onFoucsedItemIdChanged,
    required bool showBottomBorder,
    required bool showRightBorder,
    required ValueChanged<String> onChanged,
  }) {
    if (rowIndex == pitstops.length) {
      return Text(
        totalOrdered[columnIndex].asString() +
            ' / ' +
            totalReceived[columnIndex].asString(),
        style: TextStyle(fontWeight: FontWeight.bold),
      );
    }

    final item = pitstops[rowIndex].items[columnIndex];

    if (item.itemId == -1) {
      return Text(
        '-',
      );
    }

    final hasMouseOver = focusedItemId == item.itemId;

    return MouseRegion(
      onEnter: (e) async {
        if (focusedItemId != item.itemId) {
          onFoucsedItemIdChanged(item.itemId);
        }
      },
      onExit: (e) {
        onFoucsedItemIdChanged(null);
      },
      child: InkWell(
        onTap: () {
          if (focusedItemId != item.itemId) {
            onFoucsedItemIdChanged(item.itemId);
          }
        },
        child: Container(
          width: 200,
          height: 80,
          decoration: BoxDecoration(
            color: item.hasChanged ? Colors.blue.shade50 : Colors.white,
            boxShadow: hasMouseOver
                ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ]
                : [],
          ),
          child: Center(
            child: hasMouseOver
                ? Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          autofocus: true,
                          initialValue: item.orderedQuantity,
                          style: TextStyle(fontSize: 14),
                          decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 8,
                              ),
                              border: InputBorder.none,
                              suffix: Text(
                                ' / ' +
                                    (item.receivedQuantity?.asString() ?? '0'),
                                style: TextStyle(fontSize: 14),
                              )),
                          textAlign: TextAlign.center,
                          onChanged: (value) {
                            onChanged(value);
                          },
                          inputFormatters: Config.numberInputFilters,
                        ),
                      ),
                    ],
                  )
                : Text(
                    item.orderedQuantity +
                        ' / ' +
                        (item.receivedQuantity?.asString() ?? '0'),
                    style: TextStyle(fontSize: 14),
                  ),
          ),
        ),
      ),
    );
  }

  ProcTable deleteSku({required int skuIndex}) {
    final newSkus = List<SkuQuantity>.from(skus);
    newSkus.removeAt(skuIndex);
    final newPitstops =
        pitstops.map((e) => e.deleteItem(index: skuIndex)).toList();
    final newTotal = List<double>.from(totalOrdered);
    newTotal.removeAt(skuIndex);
    return copyWith(
      skus: newSkus,
      pitstops: newPitstops,
      total: newTotal,
      hasChanges: true,
    );
  }

  ProcTable updateSku({
    required int rowIndex,
    required int skuIndex,
    required String orderedQuantity,
  }) {
    final updatedPitstops = List<ProcTableRow>.from(pitstops);
    updatedPitstops[rowIndex] = updatedPitstops[rowIndex].updateItem(
      index: skuIndex,
      item: updatedPitstops[rowIndex]
          .items[skuIndex]
          .copyWith(orderedQuantity: orderedQuantity),
    );
    final newTotal = List<double>.from(totalOrdered);
    newTotal[skuIndex] = updatedPitstops
        .map((e) => e.items[skuIndex].orderedQuantity)
        .map((e) => e.toDouble())
        .fold(0, (p, e) => p + e);

    return copyWith(
      pitstops: updatedPitstops.toList(),
      total: newTotal,
      hasChanges: true,
    );
  }

  Widget getColumnWidget(BuildContext context, int i) {
    final sku = skus[i];
    final skuName = skuMap[sku.skuId]!.name;
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(skuName,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              )),
          Text(sku.unitInfo, style: TextStyle(fontSize: 14)),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'O/R',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(
                  width: 16,
                ),
                InkWell(
                    onTap: () async {
                      final shouldDelete = await context.showAlertDialog(
                            title: 'procurementUpdate.deleteSkuTitle'
                                .tr('Delete SKU'),
                            message: 'procurementUpdate.deleteSkuMessage'.tr(
                                'Are you sure you want to delete this SKU? It can not be reverted!'),
                          ) ??
                          false;
                      if (shouldDelete) {
                        context
                            .read<ProcurementUpdateEditCubit>()
                            .deleteSku(columnIndex: i);
                      }
                    },
                    child: Icon(
                      Icons.delete,
                      size: 24,
                      color: Colors.red.shade400,
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> getRequestBody() {
    final list = <Map<String, dynamic>>[];
    for (final row in pitstops) {
      final changes = row.getUpdatedItems();
      if (changes.isNotEmpty) {
        list.add({
          'procurementId': row.procurementId,
          'items': changes
              .map((e) => {
                    'itemId': e.itemId,
                    'editedOrderQuantity': e.orderedQuantity.toDouble(),
                  })
              .toList(),
        });
      }
    }
    return list;
  }
}
