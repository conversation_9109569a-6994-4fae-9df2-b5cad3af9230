part of 'field_charge_cubit.dart';

@freezed
class FieldChargeState with _$FieldChargeState {
  const factory FieldChargeState.initial({
    @Default(null) List<ChargeType>? procFieldCharges,
    @Default(null) String? error,
  }) = _Initial;
}

class ChargeType implements DropDownItem {
  const ChargeType(this.value);
  final String value;

  @override
  String get displayText => value;

  @override
  int get hashCode => this.value.hashCode;
}
