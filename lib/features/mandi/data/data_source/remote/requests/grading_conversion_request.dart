import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/presentation/grading/cubit/grading_conversion_cubit.dart';

class GradingConversionRequest extends BaseRequest<dynamic, String> {
  final String inventoryType;
  final List<
      (
        InventoryItem,
        WeighingQuantity
      )> fromInventory;
  final WeighingQuantity dumpQty;
  final List<GradingInput> toInventory;
  final int smoId;
  final List<PickedFile> dumpFiles;
  final double gradingInventoryDeviation;
  final double gradingConversionDeviation;

  GradingConversionRequest({
    required this.inventoryType,
    required this.fromInventory,
    required this.dumpQty,
    required this.toInventory,
    required this.smoId,
    required this.dumpFiles,
    required this.gradingInventoryDeviation,
    required this.gradingConversionDeviation,
  });

  @override
  String getPath() => 'conversions/addConversion';

  @override
  Map<String, dynamic>? getQuery() {
    return {
      'type': inventoryType
    };
  }

  @override
  Map<String, dynamic> getBody() {
    final listOfLosses = <Map<String, dynamic>>[];
    for (final (
          fromInventory,
          inventoryQty
        ) in fromInventory) {
      if (fromInventory.quantity < (inventoryQty.quantity ?? 0)) {
        listOfLosses.add({
          "skuId": fromInventory.skuId,
          "type": fromInventory.type,
          "unit": fromInventory.unit,
          "lotSize": fromInventory.lotSize,
          "losses": [
            {
              "comment": "User added more quantity than available",
              "lossType": "UNTRACKED",
              "quantity": fromInventory.quantity - (inventoryQty.quantity ?? 0),
            }
          ]
        });
      }
    }

    final dumpDouble = dumpQty.quantity ?? 0;
    if (dumpDouble > 0) {
      final inventory = fromInventory.first.$1;
      listOfLosses.add({
        "skuId": inventory.skuId,
        "type": inventory.type,
        "unit": inventory.unit,
        "lotSize": inventory.lotSize,
        "weighingSource": dumpQty.source,
        "losses": [
          {
            "comment": "Dump quantity added by user",
            "lossType": "DUMP",
            "quantity": dumpDouble,
            "images": dumpFiles.map((e) => e.uploadKey ?? '').toList(),
          }
        ]
      });
    }
    if (fromInventory.length == 1) {
      final inventory = fromInventory.first.$1;
      final inventoryQty = fromInventory.first.$2;
      final toQtySum = toInventory.isEmpty ? 0.0 : _sumOfToSkusQty(inventory.unit, toInventory);
      if (toQtySum != null) {
        if (toQtySum.asString().toDouble() + dumpDouble.asString().toDouble() != (inventoryQty.quantity ?? 0).asString().toDouble()) {
          final l = (inventoryQty.quantity ?? 0) - toQtySum - dumpDouble;
          listOfLosses.add({
            "skuId": inventory.skuId,
            "type": inventory.type,
            "unit": inventory.unit,
            "lotSize": inventory.lotSize,
            "losses": [
              {
                "comment": l > 0 ? "User added less quantity than available" : "User added more quantity than available",
                "lossType": "UNTRACKED",
                "quantity": l,
              }
            ]
          });
        }
      }
    }
    return {
      "smoId": smoId,
      "conversionType": "GRADING",
      "from": fromInventory
          .map((e) => {
                "skuId": e.$1.skuId,
                "type": e.$1.type,
                "unit": e.$1.unit,
                "quantity": e.$2.quantity ?? 0,
                "lotSize": e.$1.lotSize,
                "weighingSource": e.$2.source,
              })
          .toList(),
      "to": toInventory
          .map(
            (e) => {
              'skuId': e.sku.id,
              'type': e.skuQuantity.type,
              'unit': e.skuQuantity.unit,
              'quantity': e.quantity.toDouble(),
              'weighingSource': e.weighingQuantity?.source,
              'lotSize': e.skuQuantity.lotSize,
              'images': e.files.map((e) => e.uploadKey ?? '').toList(),
            },
          )
          .toList(),
      "losses": listOfLosses,
    };
  }

  double? _sumOfToSkusQty(String unit, List<GradingInput> toInventory) {
    if (toInventory.isEmpty) return null;
    double qty = 0;
    for (final inv in toInventory) {
      if (inv.skuQuantity.unit != unit) return null;
      qty += inv.skuQuantity.isBulk ? inv.quantity.toDouble() : inv.quantity.toDouble() * (inv.skuQuantity.lotSize ?? 0);
    }
    return qty;
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw Exception('Invalid Response');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
