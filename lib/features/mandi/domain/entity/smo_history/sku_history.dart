import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_detail_v2.dart';
import 'package:proc2/core/utils/extensions.dart';

part 'sku_history.freezed.dart';

@freezed
class SkuHistory with _$SkuHistory {
  SkuHistory._();
  factory SkuHistory({
    required int id,
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double? quantity,
    required double? weight,
    required String? amount,
    required double? amountDouble,
    required double? orderedQuantity,
    required bool? finalSubmit,
    @Default([]) List<PickedFile> files,
    required double? billedCostPrice,
    required double? orderedCostPrice,
    @Default(false) bool isAmountUpdated,
  }) = _SkuHistory;

  String get fileCountText => files.length == 0 ? '' : ' (${files.length})';

  factory SkuHistory.fromItem(
    Item item,
    Map<String, dynamic> urls,
  ) {
    final amount = item.billedCostPrice == null && item.orderedCostPrice == null
        ? item.amount
        : (item.billedCostPrice ?? item.orderedCostPrice)! *
            (item.quantity ?? 0);
    return SkuHistory(
      id: item.id,
      skuId: item.skuId,
      type: item.type,
      unit: item.unit,
      lotSize: item.lotSize,
      quantity: item.quantity,
      weight: item.weight,
      amount: amount?.toString(),
      amountDouble: amount,
      orderedQuantity: item.orderedQuantity,
      orderedCostPrice: item.orderedCostPrice,
      billedCostPrice: item.billedCostPrice,
      finalSubmit: item.finalSubmit,
      files: item.images
          .map(
            (e) => PickedFile.fromUploadPath(
              e,
              uploadUrl: urls[e]?.toString(),
            ),
          )
          .toList(),
    );
  }
  factory SkuHistory.fromProcDetailItem(
    ProcDetailItem item,
    Map<String, dynamic> urls,
  ) {
    final amount = item.billedCostPrice == null && item.orderedCostPrice == null
        ? item.amount
        : (item.billedCostPrice ?? item.orderedCostPrice)! *
            (item.quantity ?? 0);
    return SkuHistory(
      id: item.id ?? -1,
      skuId: item.skuId,
      type: item.type,
      unit: item.unit,
      lotSize: item.lotSize,
      quantity: item.quantity,
      weight: item.weight,
      amount: item.isAmountUpdated ? item.amount?.toString() : null,
      amountDouble: amount,
      orderedQuantity: item.orderedQuantity,
      finalSubmit: item.finalSubmit,
      billedCostPrice: item.billedCostPrice,
      orderedCostPrice: item.orderedCostPrice,
      files: item.images
          .map(
            (e) => PickedFile.fromUploadPath(
              e,
              uploadUrl: urls[e]?.toString(),
            ),
          )
          .toList(),
      isAmountUpdated: item.isAmountUpdated,
    );
  }

  bool isBulk() {
    return type.toLowerCase() == 'bulk';
  }

  String getUnitString() {
    if (isBulk()) {
      return '$unit';
    }
    return '$lotSize $unit';
  }

  String getCompositeKey() {
    if (type.toLowerCase() == 'bulk') {
      return '$skuId-$type-$unit';
    } else {
      return '$skuId-$type-$unit-$lotSize';
    }
  }

  String get actualAmount {
    if (orderedCostPrice == null) return '-';
    if (quantity == null) return '-';
    return (orderedCostPrice! * quantity!).asString();
  }
}
