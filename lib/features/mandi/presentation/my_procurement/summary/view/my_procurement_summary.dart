import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/search_suggestion.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/summary/cubit/my_proc_summary_cubit.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/summary/cubit/my_proc_summary_state.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/summary/view/my_procurement_order_popup.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

class MyProcSummary extends StatefulWidget {
  final int smoId;
  final int mandiId;
  final bool isProcManager;
  final bool isFieldOps;

  const MyProcSummary(
      {Key? key,
      required this.smoId,
      required this.mandiId,
      required this.isProcManager,
      required this.isFieldOps})
      : super(key: key);

  @override
  State<MyProcSummary> createState() => _MyProcSummaryState();
}

class _MyProcSummaryState extends State<MyProcSummary> {
  Sku? _skuToFilter;
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _loadData();
    });
    super.initState();
  }

  List<Sku> get skus => context
      .read<SkuBloc>()
      .state
      .maybeMap(orElse: () => <Sku>[], success: (s) => s.skus);

  void _loadData() {
    final cubit = context.read<MyProcSummaryCubit>();
    cubit.loadSummary(
      smoId: widget.smoId,
      skus: skus,
      isAll: cubit.state.isMandiSummaryOpen,
    );
  }

  Widget mandiSwitcherWidget(bool isMandiSummaryOpen, bool isMandiSummaryNull) {
    if (!widget.isProcManager) return SizedBox();
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      margin: EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 4,
      ),
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'My Procurement',
            style: TextStyle(
              color: Colors.black,
              fontWeight: !isMandiSummaryOpen ? FontWeight.bold : null,
            ),
          ),
          Switch(
            value: isMandiSummaryOpen,
            onChanged: (value) async {
              context
                  .read<MyProcSummaryCubit>()
                  .changeMandiSummaryOpen(isMandiSummaryOpen: value);
              if (value && isMandiSummaryNull) {
                await Future.delayed(Duration(milliseconds: 50));
                _loadData();
              }
            },
          ),
          Text(
            'Mandi Procurement',
            style: TextStyle(
              color: Colors.black,
              fontWeight: isMandiSummaryOpen ? FontWeight.bold : null,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final shouldShowAddProc = false || di.get<AppConfig>().isDev;

    // context.read<SmoBloc>().state.maybeMap(
    //         orElse: () => false, success: (s) => s.smo.menuConfig.proc) &&
    //     widget.isProcManager;
    return BlocListener<SkuBloc, SkuState>(
      listener: (context, state) {
        state.maybeMap(
          orElse: () {},
          success: (s) {
            _loadData();
          },
        );
      },
      child: BlocConsumer<MyProcSummaryCubit, MyProcSummaryState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            showSnackBar(state.errorMessage!);
            context.read<MyProcSummaryCubit>().clearMessage();
          }
        },
        builder: (context, state) {
          if ((state.isMandiSummaryOpen && state.mandiSummary == null) ||
              (!state.isMandiSummaryOpen && state.mySummary == null)) {
            return Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }
          final openedSummary =
              state.isMandiSummaryOpen ? state.mandiSummary! : state.mySummary!;
          return Scaffold(
            appBar: WAppBar.getAppBar(
              title: Text(
                state.isMandiSummaryOpen
                    ? 'Mandi Procurement Summary'
                    : 'My Procurement Summary',
                style: TextStyle(fontSize: 16),
              ),
              centerTitle: false,
              actions: widget.isProcManager
                  ? [
                      TextButton.icon(
                        onPressed: () async {
                          await context.push(
                            context.namedLocation(
                              'myProcDetail',
                              pathParameters: {'mandiId': '${widget.mandiId}'},
                            ),
                            extra: context,
                          );
                          _loadData();
                        },
                        icon: Icon(
                          Icons.info,
                          color: Colors.white,
                          size: 20,
                        ),
                        label: LangText(
                          'detail',
                          'Details',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ]
                  : null,
              bottom: openedSummary.result.isEmpty
                  ? null
                  : SearchSkuWidget(
                      initialValue: _skuToFilter,
                      skus: skus,
                      onSelected: (item) {
                        setState(() {
                          _skuToFilter = item;
                        });
                      },
                      hintText: 'myProcSummary.selectSkuFilter'
                          .tr('Select SKU to filter'),
                    ),
            ),
            body: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.grey[200],
              child: Builder(
                builder: (context) {
                  final results = _skuToFilter == null
                      ? openedSummary.result
                      : openedSummary.result
                          .where((element) =>
                              element.first.skuId == _skuToFilter!.id)
                          .toList();
                  if (results.isEmpty)
                    return Column(
                      children: [
                        mandiSwitcherWidget(
                          state.isMandiSummaryOpen,
                          state.mandiSummary == null,
                        ),
                        Expanded(
                          child: EmptyScreen(
                            message: _skuToFilter == null
                                ? getLangText('myProcSummary.noData',
                                    'No Procurements Found!')
                                : getLangText('myProcSummary.noDataFilter',
                                    'No procurements for this search!'),
                          ),
                        ),
                        if (shouldShowAddProc) _addProcButton(context),
                      ],
                    );
                  return Column(
                    children: [
                      mandiSwitcherWidget(
                        state.isMandiSummaryOpen,
                        state.mandiSummary == null,
                      ),
                      Container(
                        color: Colors.grey[100],
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8.0,
                            vertical: 4.0,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Expanded(
                                flex: 3,
                                child: LangText(
                                  'unit',
                                  'Unit',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'received',
                                  'Received',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'pendingQty',
                                  'Pending',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'amount',
                                  'Amount',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          itemCount: results.length,
                          itemBuilder: (context, index) {
                            final myProcSummary = results[index];
                            return Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: 8.0, horizontal: 0.0),
                              padding: EdgeInsets.symmetric(
                                  vertical: 8.0, horizontal: 16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    myProcSummary.first.skuName,
                                    style: TextStyle(
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  SizedBox(height: 8.0),
                                  Divider(
                                    height: 1,
                                    color: Colors.grey[300],
                                  ),
                                  SizedBox(height: 8.0),
                                  SizedBox(height: 8.0),
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount: myProcSummary.length,
                                    itemBuilder: (context, index) {
                                      final procItem = myProcSummary[index];

                                      return Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(height: 8.0),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                flex: 3,
                                                child: Text(
                                                  procItem.unitText,
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 4,
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  procItem.totalReceived
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),
                                              SizedBox(
                                                width: 4,
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: InkWell(
                                                  onTap: procItem.hasPending
                                                      ? () async {
                                                          await showDialog(
                                                            context: context,
                                                            builder: (_) =>
                                                                BlocProvider(
                                                              create: (_) =>
                                                                  context.read<
                                                                      SmoBloc>(),
                                                              child:
                                                                  MyProcurementOrderPopup(
                                                                smoId: widget
                                                                    .smoId,
                                                                mandiId: widget
                                                                    .mandiId,
                                                                skuName: procItem
                                                                    .skuName,
                                                                compositeKey:
                                                                    procItem
                                                                        .compositeKey,
                                                                unitText: procItem
                                                                    .unitText,
                                                                procDetail: openedSummary
                                                                    .getProcDetailsById(
                                                                        procItem
                                                                            .procDetailIds),
                                                              ),
                                                            ),
                                                          );
                                                          _loadBothData();
                                                        }
                                                      : null,
                                                  child: Container(
                                                    decoration:
                                                        procItem.hasPending
                                                            ? BoxDecoration(
                                                                color: Colors
                                                                    .green
                                                                    .shade50,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            4.0),
                                                              )
                                                            : null,
                                                    child: Text(
                                                      procItem.pendingText,
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 4),
                                              Expanded(
                                                flex: 2,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 8.0),
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 8.0),
                                                    child: Text(
                                                      procItem.totalAmount
                                                          .toString(),
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                      ),
                                                      textAlign: TextAlign.end,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 8.0),
                                        ],
                                      );
                                    },
                                  ),
                                  Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Total',
                                        style: TextStyle(
                                          fontSize: 16.0,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '₹${myProcSummary.fold(0.0, (p, e) => p + e.totalAmount)}',
                                        style: TextStyle(
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                      if (shouldShowAddProc) _addProcButton(context),
                    ],
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _addProcButton(BuildContext context) {
    return WStickyBottomCta(
      icon: Icons.add_box_rounded,
      label: LangText('mandiMenu.addProcurement', 'Add Procurement'),
      onPressed: () async {
        await context.push(
          context.namedLocation(
            'addProcurement',
            pathParameters: {'mandiId': '${widget.mandiId}'},
          ),
          extra: context,
        );
        // context.read<MyProcSummaryCubit>().loadSummary(widget.smoId);
        _loadBothData();
      },
    );
  }

  void _loadBothData() {
    final cubit = context.read<MyProcSummaryCubit>();
    cubit.loadSummary(
      smoId: widget.smoId,
      skus: skus,
      isAll: true,
    );
    if (widget.isProcManager) {
      cubit.loadSummary(
        smoId: widget.smoId,
        skus: skus,
        isAll: false,
      );
    }
  }
}
