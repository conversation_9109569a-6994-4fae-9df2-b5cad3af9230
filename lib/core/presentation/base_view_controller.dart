import 'package:flutter/material.dart';

class BaseViewController extends ChangeNotifier {
  bool _isLoading = false;
  String? _errMessage;

  bool get isLoading => _isLoading;
  String? get errMessage => _errMessage;

  void setLoading(bool val) {
    _isLoading = val;
    notifyListeners();
  }

  void setErrorMessage(String? msg) {
    _errMessage = msg;
    notifyListeners();
  }

  void setStatus({bool loading = false, String? err}) {
    _isLoading = loading;
    _errMessage = err;
    notifyListeners();
  }
}
