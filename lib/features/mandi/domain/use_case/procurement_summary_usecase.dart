import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/execution_time.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_proc_detail_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/aggregated_proc_summary.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';

@injectable
class ProcurementSummaryUseCase {
  Future<Either<ErrorResult<dynamic>, AgreegatedProcSummaryResult>> call({
    required int smoId,
    required bool isAll,
    required List<Sku> sku,
  }) async {
    final result = await GetMyProcDetailRequest(smoId: smoId, isAll: isAll, status: null, limit: null, offset: null).execute();
    return result.either(
      (left) => left,
      (right) => executionTime(
        () => _processResult(right, sku),
        label: 'Procurement Summary Aggregation Processing Time',
      ),
    );
  }

  AgreegatedProcSummaryResult _processResult(List<ProcDetail> procDetails, List<Sku> sku) {
    final Map<int, Sku> skuMap = Map.fromEntries(sku.map((e) => MapEntry(e.id, e))); // skuId -> Sku
    final Map<int, ProcDetail> procDetailMap = {}; // procDetailId -> ProcDetail
    final Map<int, Map<String, AgreegatedProcSummary>> result = {}; // skuId -> Map<compositeKey, AgreegatedProcSummary>
    for (final procDetail in procDetails) {
      procDetailMap[procDetail.id] = procDetail;
      for (final item in procDetail.items) {
        final compositeKey = item.compositeKey;
        final skuId = item.skuId;
        final skuName = skuMap[item.skuId]?.name ?? '-';
        final type = item.type;
        final unit = item.unit;
        final lotSize = item.lotSize;
        final totalOrdered = item.orderedQuantity ?? 0;

        final totalReceived = item.quantity ?? 0;

        final totalPending = (item.finalSubmit ?? false) ? 0.0 : totalOrdered - totalReceived;
        final totalAmount = item.amount ?? 0;
        final hasPending = !(item.finalSubmit ?? false);

        final procDetailIds = hasPending
            ? [
                procDetail.id
              ]
            : <int>[];
        if (result.containsKey(skuId)) {
          final skuMap = result[skuId]!;
          if (skuMap.containsKey(compositeKey)) {
            final summary = skuMap[compositeKey]!;
            skuMap[compositeKey] = summary.copyWith(
              totalOrdered: summary.totalOrdered + totalOrdered,
              totalReceived: summary.totalReceived + totalReceived,
              totalPending: summary.totalPending + totalPending,
              totalAmount: summary.totalAmount + totalAmount,
              procDetailIds: [
                ...summary.procDetailIds,
                ...procDetailIds
              ],
              hasPending: summary.hasPending || hasPending,
            );
          } else {
            skuMap[compositeKey] = AgreegatedProcSummary(
              skuId: skuId,
              skuName: skuName,
              type: type,
              unit: unit,
              lotSize: lotSize,
              totalOrdered: totalOrdered,
              totalReceived: totalReceived,
              totalPending: totalPending,
              totalAmount: totalAmount,
              procDetailIds: procDetailIds,
              hasPending: hasPending,
            );
          }
        } else {
          result[skuId] = {
            compositeKey: AgreegatedProcSummary(
              skuId: skuId,
              skuName: skuName,
              type: type,
              unit: unit,
              lotSize: lotSize,
              totalOrdered: totalOrdered,
              totalReceived: totalReceived,
              totalPending: totalPending,
              totalAmount: totalAmount,
              procDetailIds: procDetailIds,
              hasPending: hasPending,
            ),
          };
        }
      }
    }
    final convertedResult = result.map((key, value) => MapEntry(key, value.values.toList()));
    return AgreegatedProcSummaryResult(
      procDetails: procDetailMap,
      result: convertedResult.values.toList(),
    );
  }
}
