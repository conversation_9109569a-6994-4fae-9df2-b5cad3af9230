import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/locked_sku.dart';

class GetLockedSkuRequest extends BaseRequest<dynamic, List<LockedSku>> {
  final int allocationId;

  GetLockedSkuRequest({required this.allocationId});

  @override
  String getPath() => 'allotments/$allocationId/locked-items';

  @override
  List<LockedSku> mapper(data) {
    if (data is List<dynamic>) {
      final lockedSkus = <LockedSku>[];
      for (final item in data) {
        try {
          lockedSkus.add(LockedSku.fromJson(item));
        } catch (e, s) {
          talker.handle(e, s);
        }
      }
      return lockedSkus;
    }
    throw 'Invalid data';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
