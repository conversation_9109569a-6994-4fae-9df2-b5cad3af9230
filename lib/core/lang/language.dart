import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/extensions.dart';

abstract class Language {
  String getString({
    String? key,
    String? defaultValue,
    LanguageEnum? languageEnum,
    String? language,
    Map<String, dynamic>? params,
    bool skipParsing = false,
  });

  Future<void> updateLangDb({
    required String language,
    required Map<String, dynamic> json,
  });

  Future<bool> updateCurrentLanguage({required String language});

  Future<String?> currentLanguage();

  Future<void> init(String defaultLanguage);
}

extension LanguageExtension on LanguageEnum {
  Language get _language => di.get();

  String localized({String? language, Map<String, dynamic>? params}) {
    return _language.getString(
      languageEnum: this,
      defaultValue: defaultValue,
      params: params,
    );
  }
}

extension LanguageStringExtension on String {
  Language get _language => di.get();

  String localized({
    String? language,
    Map<String, dynamic>? params,
    String? defaultValue,
    bool convertKey = false,
  }) {
    return _language.getString(
      key: convertKey ? camelCase() : this,
      defaultValue: defaultValue ?? this,
      params: params,
    );
  }

  String camelCase() {
    if (length == 0) return this;
    final str = split('_').map((e) => e.capitalize()).join('');
    return str[0].toLowerCase() + substring(1);
  }

  String tr(String defaultValue, {Map<String, dynamic>? params}) {
    return getLangText(this, defaultValue, params: params);
  }
}

String getLangText(
  String key,
  String defaultValue, {
  Map<String, dynamic>? params,
}) {
  return di.get<Language>().getString(
        key: key,
        defaultValue: defaultValue,
        params: params,
        skipParsing: params == null,
      );
}
