import 'package:flutter/foundation.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/journey_manager.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/process_background.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_error_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_journey_stage.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_allocation_configuration_request.dart';

class MandiJourneyManager extends JourneyManager {
  MandiJourneyManager({required super.stream, required super.emmiter});

  @override
  Future<void> initialize() async {
    final allocationConfigRequest = await GetAllocationConfigurationRequest(
      allocationId: state.allocationId,
    ).execute();
    if (allocationConfigRequest.isRight) {
      emit(state.copyWith(allocationConfig: allocationConfigRequest.right));
    } else {
      emit(state.copyWith(
        stage: AllocationJourneyStage.error,
        errorData: AllocationErrorData(
            heading: 'Error while loading allocation configuration',
            message: allocationConfigRequest.left.message ?? '',
            functionToRetry: 'initialize'),
        loadingMessage: null,
      ));
      return;
    }
    await getInventoryAndAllocationData();
    await Future.delayed(Duration(milliseconds: 400));
    final hasAnyAlloted = state.items.any((element) => element.id != -1);
    emit(state.copyWith(
      stage: hasAnyAlloted || !state.isEditingAllowed
          ? AllocationJourneyStage.view
          : AllocationJourneyStage.allocation,
      previousStage: null,
      loadingMessage: null,
      errorData: null,
    ));
  }

  Future<void> getInventoryAndAllocationData({
    bool skipInventory = false,
    String? functionToRetry,
  }) async {
    await getInventory(
        functionToRetry: functionToRetry ?? 'getInventoryAndAllocationData',
        showLoadingScreen: true);
    await getAllocationData(
      functionToRetry: functionToRetry ?? 'getInventoryAndAllocationData',
      showLoadingScreen: true,
      retryParams: {
        'skipInventory': true,
      },
    );
  }

  @override
  void processAllocationData(
    List<AllocationSkuItem> items, {
    AllocationJourneyStage? stage,
  }) async {
    final result = await compute<Map<String, dynamic>, Map<String, dynamic>>(
      processAllocationBackground,
      {
        'items': items,
        'skuMap': state.skuMap,
        'mandiInventory': state.mandiInventoryMap,
        'supplyOrders': <SupplyOrder>[],
      },
    );
    final itemsInMandi = result['itemsInMandi'] as List<AllocationSkuItem>;
    emit(state.copyWith(
      items: itemsInMandi,
    ));
  }

  @override
  Future<void> reloadAfterAllocationUpdate(
      {AllocationJourneyStage? stage, bool skipInventory = false}) async {
    await getInventoryAndAllocationData(
      functionToRetry: 'reloadAfterAllocationUpdate',
      skipInventory: skipInventory,
    );
    await Future.delayed(Duration(milliseconds: 200));
    // Move to different screen now
    emit(state.copyWith(
      stage: stage ?? AllocationJourneyStage.view,
      loadingMessage: null,
      errorData: null,
    ));
  }

  @override
  Future<void> retry() async {
    if (state.errorData?.functionToRetry == 'getInventoryAndAllocationData') {
      final skipInventory = state.errorData?.params?['skipInventory'] ?? false;
      await getInventoryAndAllocationData(skipInventory: skipInventory);
    } else if (state.errorData?.functionToRetry ==
        'reloadAfterAllocationUpdate') {
      final skipInventory = state.errorData?.params?['skipInventory'] ?? false;
      await reloadAfterAllocationUpdate(skipInventory: skipInventory);
    } else if (state.errorData?.functionToRetry == 'initialize') {
      await initialize();
    } else {
      await super.retry();
    }
  }
}
