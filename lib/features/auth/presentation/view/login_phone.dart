import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:proc2/features/auth/presentation/bloc/otp_login_controller.dart';

class LoginPhoneScreen extends StatefulWidget {
  const LoginPhoneScreen({super.key});

  @override
  State<LoginPhoneScreen> createState() => _LoginPhoneScreenState();
}

class _LoginPhoneScreenState extends State<LoginPhoneScreen> {
  void otpAction() async {
    OTPLoginController otpLoginController = context.read<OTPLoginController>();
    print("OTP Action function. Code Sent = ${otpLoginController.codeSent}");
    if (otpLoginController.codeSent) {
      print("OTP Was sent verifying OTP");
      bool success = await otpLoginController.verifyOTP();
      print(
          "OTP Login status = $success. Err message = ${otpLoginController.errMessage}");
    } else {
      print("OTP Was not sent sending otp");
      await otpLoginController.sendOtp();
    }
    // User? user = FirebaseAuth.instance.currentUser;

    // if (user != null) {
    //   widget.onLoginSuccess();
    // }
  }

  @override
  Widget build(BuildContext context) {
    OTPLoginController otpLoginController = context.watch<OTPLoginController>();

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            appBar: AppBar(
              title: Text('loginViaPhone'.tr('Login with Phone')),
              centerTitle: false,
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  const SizedBox(
                    height: 12,
                  ),
                  Visibility(
                    visible: !otpLoginController.codeSent,
                    child: TextFormField(
                      onChanged: (txt) {
                        otpLoginController.setPhone(txt);
                      },
                      maxLength: 10,
                      initialValue: otpLoginController.phone,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      decoration: InputDecoration(
                          enabled: !otpLoginController.isLoading,
                          counterText: "",
                          border: InputBorder.none,
                          fillColor: Colors.grey.shade200,
                          filled: true,
                          prefixIcon: Icon(
                            Icons.phone_android,
                            color: Colors.grey.shade500,
                          ),
                          hintText: "phoneNumber".tr("Phone number"),
                          hintStyle: TextStyle(color: Colors.grey.shade500)),
                    ),
                  ),
                  Visibility(
                    visible: otpLoginController.codeSent,
                    child: TextFormField(
                      onChanged: (txt) {
                        otpLoginController.setOTP(txt);
                      },
                      maxLength: 4,
                      initialValue: (otpLoginController.otp ?? "").toString(),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                          enabled: !otpLoginController.isLoading,
                          counterText: "",
                          border: InputBorder.none,
                          fillColor: Colors.grey.shade200,
                          filled: true,
                          prefixIcon: Icon(
                            Icons.lock,
                            color: Colors.grey.shade500,
                          ),
                          hintText: "otp".tr("OTP"),
                          hintStyle: TextStyle(color: Colors.grey.shade500)),
                    ),
                  ),
                  Visibility(
                    visible: otpLoginController.codeSent,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            otpLoginController.changePhoneNumber();
                          },
                          child: Text(
                            'changePhoneNumber'.tr('Change Number'),
                            style: const TextStyle(
                                color: Colors.blue,
                                fontSize: 12,
                                decoration: TextDecoration.underline),
                          ),
                        ),
                        const ResendOTPButton()
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Visibility(
                    visible: otpLoginController.errMessage != null,
                    child: SizedBox(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              otpLoginController.errMessage?.substring(
                                      0,
                                      (otpLoginController.errMessage?.length ??
                                                  0) >
                                              90
                                          ? 90
                                          : (otpLoginController
                                                  .errMessage?.length ??
                                              0)) ??
                                  "",
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  loginButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget loginButton() {
    OTPLoginController otpLoginController = context.watch<OTPLoginController>();
    return otpLoginController.isLoading
        ? const CircularProgressIndicator()
        : ElevatedButton(
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Config.primaryColor),
              shape: WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
              fixedSize: WidgetStateProperty.all(
                Size(
                  MediaQuery.of(context).size.width - 50,
                  50,
                ),
              ),
            ),
            onPressed: otpAction,
            child: Text(
              otpLoginController.codeSent
                  ? 'verifyOTP'.tr('Verify OTP')
                  : "sendOTP".tr("Get OTP"),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
  }
}

class ResendOTPButton extends StatefulWidget {
  const ResendOTPButton({super.key});
  @override
  State<ResendOTPButton> createState() => _ResendOTPButton();
}

class _ResendOTPButton extends State<ResendOTPButton> {
  Timer? _timer;
  int secondsElapsed = 0;

  void startTimer() {
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        setState(() {
          secondsElapsed++;
        });
      },
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      startTimer();
    });
  }

  @override
  Widget build(BuildContext context) {
    OTPLoginController otpLoginController = context.watch<OTPLoginController>();
    return TextButton(
      onPressed: () {
        otpLoginController.resendOTP();
      },
      child: Text(
        'Resend OTP ' +
            (otpLoginController.resendInSeconds > 0
                ? "(${otpLoginController.resendInSeconds} sec)"
                : ""),
        style: const TextStyle(
            fontSize: 12,
            color: Colors.blue,
            decoration: TextDecoration.underline),
      ),
    );
  }
}
