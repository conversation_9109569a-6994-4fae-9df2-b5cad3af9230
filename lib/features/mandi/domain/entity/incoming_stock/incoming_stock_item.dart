import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/sku_quantity.dart';

part 'incoming_stock_item.freezed.dart';

@freezed
class IncomingStockItem with _$IncomingStockItem {
  const factory IncomingStockItem({
    required int id,
    required double expectedQuantity,
    required SKUQuantity skuQuantity,
  }) = _IncomingStockItem;
}
