import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/use_case/procurement_summary_usecase.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/summary/cubit/my_proc_summary_state.dart';

@injectable
class MyProcSummaryCubit extends Cubit<MyProcSummaryState> {
  final ProcurementSummaryUseCase _procurementSummaryUseCase;

  MyProcSummaryCubit(
    this._procurementSummaryUseCase,
  ) : super(MyProcSummaryState.data());

  void changeMandiSummaryOpen({required bool isMandiSummaryOpen}) {
    emit(state.copyWith(isMandiSummaryOpen: isMandiSummaryOpen));
  }

  void loadSummary({
    required int smoId,
    required List<Sku> skus,
    required bool isAll,
  }) async {
    emit(state.copyWith(isLoading: true));
    final result = await _procurementSummaryUseCase.call(
      smoId: smoId,
      isAll: isAll,
      sku: skus,
    );
    final newState = result.fold(
      (left) => state.copyWith(errorMessage: left.message),
      (right) => isAll
          ? state.copyWith(mandiSummary: right)
          : state.copyWith(mySummary: right),
    );
    emit(newState);
  }

  void clearMessage() {
    emit(state.copyWith(errorMessage: null));
  }
}
