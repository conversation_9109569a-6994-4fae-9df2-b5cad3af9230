import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/combo/cubit/combo_conversion_cubit.dart';
import 'package:proc2/features/mandi/presentation/combo/view/combo_add_sku_popup.dart';
import 'package:proc2/features/mandi/presentation/grading/cubit/grading_conversion_cubit.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/inventory_type.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class ComboConversionScreen extends StatefulWidget {
  const ComboConversionScreen({
    super.key,
    required this.mandiId,
    required this.smoId,
    required this.inventoryType,
    required this.title,
    required this.allowGrading,
    required this.config,
  });
  final int mandiId;
  final int smoId;
  final InventoryType inventoryType;
  final String? title;
  final bool allowGrading;
  final ConversionInventoryConfig config;
  @override
  State<ComboConversionScreen> createState() => _ComboConversionScreenState();
}

class _ComboConversionScreenState extends State<ComboConversionScreen> {
  List<WeighingQuantity> _fromQuantities = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.grey.shade100,
        appBar: WAppBar.getAppBar(
          title: widget.title != null
              ? Text(widget.title!)
              : LangText('gradingConversion.title', 'Grading Conversion'),
          centerTitle: false,
        ),
        body: BlocBuilder<SkuBloc, SkuState>(
          builder: (context, skuState) {
            return skuState.map(
              initial: (initial) => Center(
                child: CircularProgressIndicator(),
              ),
              success: (success) {
                return BlocConsumer<ComboConversionCubit, ComboConversionState>(
                  listener: (context, state) {
                    final shouldPop = state.shouldPop;
                    if (state.message != null) {
                      showSnackBar(state.message!);
                      context.read<ComboConversionCubit>().clearMessage();
                    }
                    if (shouldPop) {
                      context.pop(true);
                    }
                  },
                  builder: (context, state) {
                    final inventory = state.allInventory;
                    final inventoryDeviation = state.gradingInventoryDeviation;
                    final conversionDeviation =
                        state.gradingConversionDeviation;

                    if (inventory == null ||
                        inventoryDeviation == null ||
                        conversionDeviation == null)
                      return Center(
                        child: CircularProgressIndicator(),
                      );
                    if (inventory.isEmpty)
                      return EmptyScreen(
                        message: 'No Inventory Found!',
                      );
                    if (inventoryDeviation == -1 || conversionDeviation == -1)
                      return ErrorScreen(
                        heading: getLangText(
                            'gradingConversion.configLoadError',
                            'Error while loading configuration!'),
                        onPressed: () {
                          context.read<ComboConversionCubit>().loadDeviation();
                        },
                      );

                    bool isAllSkuWeighted = state.fromInventory != null &&
                        state.fromInventory!.length == _fromQuantities.length &&
                        _fromQuantities.every(
                            (e) => e.quantity != null && e.quantity! > 0) &&
                        state.toInventory.isNotEmpty &&
                        state.toInventory
                            .every((e) => e.weighingQuantity != null);

                    return Column(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                            child: ListView(
                              shrinkWrap: true,
                              children: [
                                Card(
                                  color: Colors.white,
                                  elevation: 4,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: Colors.grey.shade300,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(8),
                                            topRight: Radius.circular(8),
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 8,
                                          ),
                                          child: Text(
                                            widget.inventoryType.name +
                                                ' Inventory',
                                            style: TextStyle(
                                              fontSize: 15,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 8,
                                          horizontal: 16,
                                        ),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            for (var i = 0;
                                                i < state.fromInventory!.length;
                                                i++)
                                              ...fromInventoryCards(
                                                  state.fromInventory![i],
                                                  state,
                                                  i),
                                            selectSkuFromInventory(inventory,
                                                state.fromInventory!),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (state.fromInventory != null &&
                                    state.fromInventory!.isNotEmpty) ...[
                                  SizedBox(
                                    height: 8,
                                  ),
                                  Align(
                                    child: Icon(
                                      Icons.arrow_downward_rounded,
                                      size: 40,
                                    ),
                                  ),
                                  SizedBox(
                                    height: 8,
                                  ),
                                  Card(
                                    color: Colors.white,
                                    elevation: 4,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Column(
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            color: Colors.grey.shade300,
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(8),
                                              topRight: Radius.circular(8),
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 8,
                                            ),
                                            child: Text(
                                              'Mandi Inventory',
                                              style: TextStyle(
                                                fontSize: 15,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                        if (state.toInventory.isNotEmpty)
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 8,
                                              horizontal: 8,
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8.0),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: LangText(
                                                      'unit',
                                                      'Unit',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 8,
                                                  ),
                                                  Expanded(
                                                    child: LangText(
                                                      'quantity',
                                                      'Quantity',
                                                      textAlign: TextAlign.end,
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 80,
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8,
                                            horizontal: 8,
                                          ),
                                          child: Scrollbar(
                                            child: Column(
                                              // shrinkWrap: true,
                                              children: [
                                                skuCard(
                                                  state,
                                                  success.skus,
                                                ),
                                                Builder(builder: (context) {
                                                  if (state
                                                      .toInventory.isNotEmpty)
                                                    return SizedBox();
                                                  final hasRelatedSku =
                                                      getRemainingToSkus(
                                                    state,
                                                    success.skus,
                                                  );

                                                  if (hasRelatedSku.isEmpty)
                                                    return SizedBox();
                                                  return Align(
                                                    alignment:
                                                        Alignment.centerRight,
                                                    child: ElevatedButton.icon(
                                                      icon: Icon(Icons.add),
                                                      onPressed: () async {
                                                        if (state.isCtaLoading)
                                                          return;
                                                        final result =
                                                            await showDialog<
                                                                GradingInput>(
                                                          context: context,
                                                          builder: (ctx) =>
                                                              ComboAddSkuPopup(
                                                            smoId: widget.smoId,
                                                            skus: hasRelatedSku,
                                                            compositeKey: state
                                                                .toInventory
                                                                .map((e) => e
                                                                    .skuQuantity
                                                                    .compositeKey)
                                                                .toList(),
                                                            skuImages: [],
                                                            config:
                                                                widget.config,
                                                          ),
                                                        );
                                                        if (result != null) {
                                                          context
                                                              .read<
                                                                  ComboConversionCubit>()
                                                              .addToInventory(
                                                                  result);
                                                          setState(() {});
                                                        }
                                                      },
                                                      label: LangText(
                                                        'gradingConversion.addSku',
                                                        'Add Sku',
                                                      ),
                                                    ),
                                                  );
                                                }),
                                                Divider(),
                                                // dumpCard(
                                                //   files: state.dumpFiles,
                                                //   unitInfo: state.fromInventory!
                                                //       .unitString(),
                                                //   isEnabled:
                                                //       !state.isCtaLoading,
                                                //   isBulkKg: state.fromInventory!
                                                //       .isBulkKg(),
                                                //   skuId: state
                                                //       .fromInventory!.skuId,
                                                // ),
                                                // Builder(builder: (ctx) {
                                                //   double qty = _qtyController
                                                //       .text
                                                //       .toDouble();

                                                //   double dump = _dumpController
                                                //       .text
                                                //       .toDouble();
                                                //   double total =
                                                //       (toInventoryQtySum ?? 0) +
                                                //           dump;
                                                //   Color c = Colors.white;
                                                //   String text = '';
                                                //   if (total < qty) {
                                                //     c = Colors.yellow.shade50;
                                                //     text =
                                                //         'There is an untracked qty of ${(qty - total).asString()}';
                                                //     // Yellow
                                                //   } else if (qty == total) {
                                                //     return SizedBox();
                                                //   } else if (total <=
                                                //       maxAllowedConversionQty) {
                                                //     // Blue
                                                //     c = Colors.blue.shade50;
                                                //     text =
                                                //         'You are converting more quantity than added!';
                                                //   } else if (total >
                                                //       maxAllowedConversionQty
                                                //           .asString()
                                                //           .toDouble()) {
                                                //     // Red
                                                //     c = Colors.red.shade50;
                                                //     text = 'Check again, total more than input not allowed for ' +
                                                //         (widget.allowGrading
                                                //             ? 'grading!'
                                                //             : 'lotting/delotting!');
                                                //   }
                                                //   return Container(
                                                //     margin: EdgeInsets.only(
                                                //       top: 16,
                                                //     ),
                                                //     padding:
                                                //         EdgeInsets.symmetric(
                                                //       vertical: 4,
                                                //     ),
                                                //     width: double.infinity,
                                                //     decoration: BoxDecoration(
                                                //       color: c,
                                                //       borderRadius:
                                                //           BorderRadius.circular(
                                                //               8),
                                                //     ),
                                                //     child: Text(
                                                //       text,
                                                //       textAlign:
                                                //           TextAlign.center,
                                                //     ),
                                                //   );
                                                // }),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                        WStickyBottomCta(
                          isEnabled: isAllSkuWeighted,
                          isLoading: state.isCtaLoading,
                          icon: Icons.check,
                          label:
                              LangText('gradingConversion.submitCta', 'Submit'),
                          onPressed: () async {
                            final shouldSubmit = await context.showAlertDialog(
                                  title: 'Are you sure?',
                                  message: 'Do you want to submit?',
                                ) ??
                                false;

                            if (!shouldSubmit) {
                              return;
                            }

                            context.read<ComboConversionCubit>().submit(
                                  mandiId: widget.mandiId,
                                  smoId: widget.smoId,
                                  inventoryType: widget.inventoryType,
                                  fromQuantities: _fromQuantities,
                                );
                          },
                        ),
                      ],
                    );
                  },
                );
              },
              error: (error) {
                return ErrorScreen(
                  onPressed: () {
                    context.read<SkuBloc>().add(SkuEvent.fetch());
                  },
                  message: error.errorr.message,
                );
              },
            );
          },
        ),
      ),
    );
  }

  List<Sku> getRemainingToSkus(ComboConversionState state, List<Sku> allSkus) {
    final toSkuIds = state.toInventory.map((e) => e.sku.id);
    return allSkus.where((e) => !toSkuIds.contains(e.id)).toList();
  }

  List<Widget> fromInventoryCards(
    InventoryItem inventoryItem,
    ComboConversionState state,
    int index,
  ) {
    return [
      Container(
        margin: EdgeInsets.only(
          bottom: 8,
          top: 4,
        ),
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 16,
        ),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: InkWell(
          onTap: () async {
            if (state.isCtaLoading) return;
            final shouldClear = await context.showAlertDialog(
                  title: getLangText(
                    'gradingConversion.clearSkuPopupTitle',
                    'Clear Select Sku?',
                  ),
                  message: getLangText(
                    'gradingConversion.clearSkuPopupMessage',
                    'Are you sure you want to clear selected Sku? All data will be cleared!',
                  ),
                ) ??
                false;
            if (shouldClear) {
              _fromQuantities.removeAt(index);
              context.read<ComboConversionCubit>().deleteFromInventory(index);
              // TODO: Check here
              // context
              //     .read<
              //         ComboConversionCubit>()
              //     .fromSkuSelected(
              //         null);
              // _skuSearchController
              //     .clear();
              // _dumpController.clear();
              // _qtyController.clear();
            }
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                getSKU(context, skuID: inventoryItem.skuId).name +
                    '-' +
                    inventoryItem.unitString(),
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Icon(
                Icons.delete,
                size: 18,
              ),
            ],
          ),
        ),
      ),
      Divider(),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  getLangText(
                      'gradingConversion.availableQty', 'Available Qty'),
                ),
                SizedBox(
                  height: 2,
                ),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.shade300,
                    ),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                  child: Text(inventoryItem.quantity.asString()),
                ),
              ],
            ),
          ),
          SizedBox(
            width: 16,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(inventoryItem.isBulk()
                    ? getLangText('gradingConversion.qty', 'Qty*')
                    : getLangText('gradingConversion.qtyLots', 'No. of Lots*')),
                SizedBox(
                  height: 2,
                ),
                WeighingQuantityButton(
                  isReadOnly: state.isCtaLoading,
                  label: _fromQuantities[index].value ?? '',
                  popupBuilder: () {
                    return WeightCapturePopupProcurementReceive(
                      isBulkKg: inventoryItem.isBulkKg(),
                      isManualEditAllowed:
                          !(widget.config.isEditOnlyFromWeighingMachine),
                      initialWeight: _fromQuantities[index].quantity ?? 0.0,
                      skuName: getSKU(
                        context,
                        skuID: inventoryItem.skuId,
                      ).name,
                      unitInfo: inventoryItem.unitString(),
                    );
                  },
                  onChange: (quantity) {
                    if (quantity != null) {
                      setState(() {
                        _fromQuantities[index] = quantity;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),

      // if ((_fromQuantities[index].quantity ?? 0.0) > inventoryItem.quantity)
      //   Container(
      //     margin: EdgeInsets.only(
      //       top: 16,
      //     ),
      //     padding: EdgeInsets.symmetric(
      //       vertical: 4,
      //     ),
      //     width: double.infinity,
      //     decoration: BoxDecoration(
      //       color: isMoreThanMaxQty
      //           ? Colors.red.shade50
      //           : Colors.blue.shade50,
      //       borderRadius:
      //           BorderRadius.circular(
      //               8),
      //     ),
      //     child: Text(
      //       isMoreThanMaxQty
      //           ? 'Check again, quantity more than mandi inventory not allowed!'
      //           : 'You have added more qty than inventory!',
      //       textAlign: TextAlign.center,
      //     ),
      //   ),
      Divider(height: 32, color: Colors.black54),
    ];
  }

  Widget selectSkuFromInventory(
    List<InventoryItem> inventory,
    List<InventoryItem> fromInventory,
  ) {
    final remainingInventory =
        inventory.where((e) => !fromInventory.contains(e)).toList();
    if (remainingInventory.isEmpty) return SizedBox();
    return CustomDropdownV2.searchRequest(
      items: remainingInventory,
      onItemSelected: (inventoryItem) {
        _fromQuantities.add(WeighingQuantity.noSouce(null));
        setState(() {});
        context.read<ComboConversionCubit>().addFromInventory(inventoryItem);
      },
      listItemBuilder: (context, inventoryItem) {
        final text = getSKU(context, skuID: inventoryItem.skuId).name +
            '-' +
            inventoryItem.unitString();
        return Text(
          text,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontSize: 16,
          ),
        );
      },
      fillColor: Colors.grey.shade100,
      hintText: getLangText(
        'gradingConversion.selectSku',
        'Select Sku',
      ),
      futureRequest: (String text) async {
        final filteredInventory = inventory
            .where((e) =>
                !fromInventory.contains(e) &&
                getSKU(context, skuID: e.skuId)
                    .name
                    .toLowerCase()
                    .contains(text.toLowerCase()))
            .toList();
        return filteredInventory;
      },
    );
  }

  Widget skuCard(ComboConversionState state, List<Sku> allSkus) {
    if (state.toInventory.isEmpty) return Container();
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 8,
      ),
      margin: EdgeInsets.only(
        bottom: 4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 12,
          ),
          for (int i = 0; i < state.toInventory.length; i++)
            Padding(
              padding: const EdgeInsets.only(
                bottom: 12,
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      getSKU(context, skuID: state.toInventory[i].sku.id).name +
                          '-' +
                          state.toInventory[i].skuQuantity.getUnitString(),
                    ),
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Text(
                        state.toInventory[i].quantity,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  InkWell(
                    onTap: () async {
                      final remainingSkus = getRemainingToSkus(state, allSkus);
                      final compositeKeys = state.toInventory
                          .map((e) => e.skuQuantity.compositeKey)
                          .toList();
                      compositeKeys.remove(
                          state.toInventory[i].skuQuantity.compositeKey);

                      final result = await showDialog<GradingInput>(
                        context: context,
                        builder: (_) => ComboAddSkuPopup(
                          smoId: widget.smoId,
                          skus: remainingSkus,
                          skuImages: state.toInventory[i].files,
                          procurementType:
                              state.toInventory[i].skuQuantity.isBulk
                                  ? ProcurementType.bulk
                                  : ProcurementType.lots,
                          unit: state.toInventory[i].skuQuantity.unit,
                          lotSize: state.toInventory[i].skuQuantity.lotSize,
                          sku: state.toInventory[i].sku,
                          qty: state.toInventory[i].weighingQuantity,
                          isEditing: true,
                          compositeKey: compositeKeys,
                          onDelete: () {
                            context
                                .read<ComboConversionCubit>()
                                .deleteToInventory(i);
                            context.pop();
                          },
                          config: widget.config,
                        ),
                      );
                      if (result != null) {
                        context
                            .read<ComboConversionCubit>()
                            .updateToInventory(result, i);
                      }
                    },
                    child: SizedBox(
                      width: 72,
                      child: Icon(
                        Icons.edit,
                        size: 18,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  double? sumOfToSkusQty(String unit, List<GradingInput> toInventory) {
    if (toInventory.isEmpty) return null;
    double qty = 0;
    for (final inv in toInventory) {
      if (inv.skuQuantity.unit != unit) return null;
      qty += inv.skuQuantity.isBulk
          ? inv.quantity.toDouble()
          : inv.quantity.toDouble() * (inv.skuQuantity.lotSize ?? 0);
    }
    return qty;
  }
}
