import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/create_vendor_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_all_vendors_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_procurement_area_request.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/proc_area.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';

part 'vendor_state.dart';
part 'vendor_cubit.freezed.dart';

@singleton
class VendorCubit extends Cubit<VendorState> {
  VendorCubit() : super(VendorState.data());

  Future<void> fetchVendors({bool hardRefresh = false}) async {
    await _fetchAreaOfOperations();
    if (!hardRefresh && state.vendors != null && state.vendors!.isNotEmpty)
      return;
    if (!isClosed) emit(state.copyWith(isLoading: true, errorMessage: null));
    final result = await GetAllVendorsRequest().execute();
    result.fold(
      (l) => emit(state.copyWith(isLoading: false, errorMessage: l.message)),
      (r) => emit(
        state.copyWith(
          isLoading: false,
          vendors: Map.fromEntries(r.map((e) => MapEntry(e.vendorId, e))),
        ),
      ),
    );
  }

  Future<void> _fetchAreaOfOperations() async {
    if (state.procAreas != null && state.procAreas!.isNotEmpty) return;
    final result = await GetProcurementAreaRequest().execute();
    if (result.isRight) {
      final map = Map.fromEntries(result.right.map((e) => MapEntry(e.id, e)));
      if (!isClosed) {
        emit(state.copyWith(procAreas: map));
      }
    }
  }

  Future<VendorLocation?> createVendor({
    required String vendorName,
    required String vendorPhone,
    required String pocName,
    required String pocPhone,
    required int procAreaId,
  }) async {
    final result = await CreateVendorRequest(
      vendorName: vendorName,
      pocName: pocName,
      vendorPhone: vendorPhone,
      pocPhone: pocPhone,
      procAreaId: procAreaId,
    ).execute();

    if (result.isLeft) {
      if (!isClosed) {
        emit(state.copyWith(errorMessage: result.left.message));
      }
      return null;
    } else {
      await fetchVendors(hardRefresh: true);
      return result.right;
    }
  }

  void clearMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  VendorLocation getVendorLocation(int? locationId, String? vendorName) {
    if (locationId == null || state.vendors == null)
      return VendorLocation(
        locationId: -1,
        address: 'address',
        vendorName: vendorName ?? '',
        vendorPhone: '',
        pocName: '',
        pocPhone: '',
        searchKey: '',
      );
    final allLocations =
        state.vendors!.values.expand((e) => e.locations).toList();
    return allLocations.firstWhere(
      (e) => e.locationId == locationId,
      orElse: () => VendorLocation(
        locationId: -1,
        address: 'address',
        vendorName: vendorName ?? '',
        vendorPhone: '',
        pocName: '',
        pocPhone: '',
        searchKey: '',
      ),
    );
  }

  Vendor? getVendorFromLocation(int locationId) {
    return state.vendors?.values
        .where((e) =>
            e.locations.any((element) => element.locationId == locationId))
        .firstOrNull;
  }

  ProcArea? getProcAreaFromId(int? procAreaId) {
    if (procAreaId == null) return null;
    return state.procAreas?[procAreaId];
  }
}
