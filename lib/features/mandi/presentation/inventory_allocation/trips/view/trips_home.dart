import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/trip.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/trips/bloc/trip_bloc.dart';

class TripsHome extends StatefulWidget {
  const TripsHome({
    super.key,
    required this.smoId,
    required this.mandiId,
    required this.allotmentId,
  });

  final int smoId;
  final int mandiId;
  final int allotmentId;
  @override
  State<TripsHome> createState() => _TripsHome();
}

class _TripsHome extends State<TripsHome> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: appBar(),
        body: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: BlocConsumer<TripBloc, TripState>(
            listener: (context, state) {
              state.maybeMap(
                orElse: () {},
                error: (e) {
                  showSnackBar(e.error.message);
                },
                success: (success) async {
                  if (success.message != null) {
                    showSnackBar(success.message!);
                  }
                },
              );
            },
            builder: (context, state) {
              return state.map(
                initial: (_) =>
                    const Center(child: CircularProgressIndicator()),
                error: (error) => ErrorScreen(
                  onPressed: () {
                    context.read<TripBloc>().add(
                        TripEvent.started(widget.smoId, widget.allotmentId));
                  },
                  message: error.error.message,
                ),
                success: (success) {
                  if (success.trips.isEmpty) {
                    return EmptyScreen(
                      message: LanguageEnum.allocateInventoryTripsEmptyTitle
                          .localized(),
                      description: LanguageEnum
                          .allocateInventoryTripsEmptyMessage
                          .localized(),
                    );
                  }
                  return Padding(
                    padding: const EdgeInsets.only(
                      left: 16,
                      right: 16,
                      top: 8,
                      bottom: 8,
                    ),
                    child: Scrollbar(
                      child: ListView.builder(
                        itemCount: success.trips.length,
                        itemBuilder: (context, index) {
                          final trip = success.trips[index];
                          return tripCard(index, trip);
                        },
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget tripCard(int index, Trip trip) {
    final isCtaActive = trip.canDispatch &&
        trip.driverDetails.isValid() &&
        trip.vehicleDetails.isValid();
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 16,
      ),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LanguageEnum.allocateInventoryTripsRoutIdLabel.localized(
                  params: {'routeId': trip.routeId},
                ),
              ),
              const SizedBox(height: 8),
              Text(
                LanguageEnum.allocateInventoryTripsStatus.localized(
                  params: {'status': trip.status},
                ),
              ),
              const SizedBox(height: 8),
              if (trip.canDispatch) ...[
                TextFormField(
                  initialValue: trip.driverDetails.name,
                  onChanged: (value) {
                    context.read<TripBloc>().add(
                          TripEvent.updateDetail(
                            index: index,
                            driverName: value,
                            driverMobile: trip.driverDetails.mobile,
                            vehicleNumber: trip.vehicleDetails.vehicleNumber,
                          ),
                        );
                  },
                  decoration: InputDecoration(
                    hintText: LanguageEnum.allocateInventoryTripsDriverNameLabel
                        .localized(),
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.only(
                      left: 8,
                      right: 8,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextFormField(
                  initialValue: trip.driverDetails.mobile,
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.only(
                      left: 8,
                      right: 8,
                      top: 5,
                      bottom: 5,
                    ),
                    border: OutlineInputBorder(),
                    hintText: LanguageEnum
                        .allocateInventoryTripsMobilePhoneLabel
                        .localized(),
                    counterStyle: TextStyle(
                      height: double.minPositive,
                    ),
                    counterText: '',
                  ),
                  keyboardType: TextInputType.number,
                  maxLength: 10,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^(\d+)?')),
                  ],
                  onChanged: (String value) {
                    context.read<TripBloc>().add(
                          TripEvent.updateDetail(
                            index: index,
                            driverName: trip.driverDetails.name,
                            driverMobile: value,
                            vehicleNumber: trip.vehicleDetails.vehicleNumber,
                          ),
                        );
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  initialValue: trip.vehicleDetails.vehicleNumber,
                  onChanged: (value) {
                    context.read<TripBloc>().add(
                          TripEvent.updateDetail(
                            index: index,
                            driverName: trip.driverDetails.name,
                            driverMobile: trip.driverDetails.mobile,
                            vehicleNumber: value,
                          ),
                        );
                  },
                  decoration: InputDecoration(
                    hintText: LanguageEnum
                        .allocateInventoryTripsVehicleNumberLabel
                        .localized(),
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.only(
                      left: 8,
                      right: 8,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                ElevatedButton(
                  onPressed: !isCtaActive
                      ? null
                      : () {
                          context
                              .read<TripBloc>()
                              .add(TripEvent.dispatch(index));
                        },
                  style: ElevatedButton.styleFrom(
                    minimumSize: Size(
                      MediaQuery.of(context).size.width,
                      48,
                    ),
                  ),
                  child: Text(
                    LanguageEnum.allocateInventoryTripsDispatchLabel
                        .localized(),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
              ],
              if (!trip.canDispatch) ...[
                Text(
                    '${LanguageEnum.allocateInventoryTripsDriverNameLabel.localized()}: ${trip.driverDetails.name}'),
                const SizedBox(height: 8),
                Text(
                    '${LanguageEnum.allocateInventoryTripsMobilePhoneLabel.localized()}: ${trip.driverDetails.mobile}'),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                        '${LanguageEnum.allocateInventoryTripsVehicleNumberLabel.localized()}: ${trip.vehicleDetails.vehicleNumber}'),
                  ],
                ),
                const SizedBox(height: 8),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   children: [
                //     Text('Trip Started At: ${trip.tripStartedAt?.toDate().format(Config.dateTimeFormat)}'),
                //   ],
                // ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  AppBar appBar() {
    return WAppBar.getAppBar(
      title: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            LanguageEnum.allocateInventoryTripsTitle.localized(),
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
      centerTitle: false,
    );
  }
}
