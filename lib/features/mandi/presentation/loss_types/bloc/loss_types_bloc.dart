import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';
import 'package:proc2/features/mandi/domain/use_case/get_loss_init_usecase.dart';

part 'loss_types_bloc.freezed.dart';
part 'loss_types_event.dart';
part 'loss_types_state.dart';

@injectable
class LossTypesBloc extends Bloc<LossTypesEvent, LossTypesState> {
  LossTypesBloc(this._getLossInitUseCase) : super(const _Initial()) {
    on<LossTypesEvent>((event, emit) async {
      await event.when(
        fetch: () async {
          emit(const LossTypesState.loading());
          final getLossInitFuture = _getLossInitUseCase().then(
            (result) => result.fold(
              (left) {
                emit(const LossTypesState.failed());
                // showRetrySnackBar(
                //     'Failed to fetch Loss Types. ERROR : ${left.message}', () {
                //   add(const LossTypesEvent.fetch());
                // });
              },
              (right) => emit(
                LossTypesState.success(right),
              ),
            ),
          );

          await Future.wait([getLossInitFuture]);
        },
      );
    });
  }

  final GetLossInitUseCase _getLossInitUseCase;
}
