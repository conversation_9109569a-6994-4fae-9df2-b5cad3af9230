// ignore_for_file: lines_longer_than_80_chars

import 'dart:async';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/network/interceptors/network_info.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/mandatory_permission/mandatory_permission_manager.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/login_bypass_config_interface.dart';
import 'package:proc2/firebase_options_development.dart' as devOptions;
import 'package:proc2/firebase_options_production.dart' as prodOptions;
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:talker/talker.dart';
import 'package:talker_bloc_logger/talker_bloc_logger.dart';
import 'package:weighing_machine/weighing_machine.dart';

class CrashlyticsTalkerObserver extends TalkerObserver {
  CrashlyticsTalkerObserver();

  @override
  void onError(err) {
    FirebaseCrashlytics.instance.recordError(
      err.error,
      err.stackTrace,
      reason: err.message,
    );
  }

  @override
  void onException(err) {
    FirebaseCrashlytics.instance.recordError(
      err.exception,
      err.stackTrace,
      reason: err.message,
    );
  }
}

class AppBlocObserver extends BlocObserver {
  const AppBlocObserver();

  @override
  void onChange(BlocBase<dynamic> bloc, Change<dynamic> change) {
    super.onChange(bloc, change);
    logger.shout('onChange(${bloc.runtimeType}, $change)');
  }

  @override
  void onEvent(Bloc bloc, Object? event) {
    super.onEvent(bloc, event);
    logger.shout('onEvent(${bloc.runtimeType}, $event)');
  }

  @override
  void onError(BlocBase<dynamic> bloc, Object error, StackTrace stackTrace) {
    logger.shout('onError(${bloc.runtimeType}, $error, $stackTrace)');
    super.onError(bloc, error, stackTrace);
  }
}

Future<void> bootstrap({
  required Environment environment,
  required FutureOr<Widget> Function() builder,
}) async {
  await runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();
      await Firebase.initializeApp(
        options: environment == dev
            ? devOptions.DefaultFirebaseOptions.currentPlatform
            : prodOptions.DefaultFirebaseOptions.currentPlatform,
      );
      await configureDependencies(environment);
      if (!kIsWeb) {
        await MandatoryPermissionManager.init();
      }

      FlutterError.onError = (details) {
        log(details.exceptionAsString(), stackTrace: details.stack);
      };

      Bloc.observer = TalkerBlocObserver(
          talker: talker,
          settings: TalkerBlocLoggerSettings(
            printStateFullData: false,
          ));

      final language = di.get<Language>();
      await language.init('en');
      await NetworkInfo.instance.init();
      final version = NetworkInfo.instance.packageInfo?.version ?? '';
      String patchVersion = '';
      try {
        final patchVersionInt = await ShorebirdCodePush().currentPatchNumber();
        patchVersion = patchVersionInt != null ? '.$patchVersionInt' : '';
        talker.info('Shorebird patch version: $patchVersionInt');
      } catch (e) {
        talker.error('Error getting patch version: $e');
      }
      final loginConfigInterface = di.get<LoginBypassConfigInterface>();
      await loginConfigInterface.getConfig(overrideLocal: true);
      FlutterBluetooth.addLogRequestHandler((type, message) {
        if (type == LogType.error) {
          talker.error("[FlutterBluetooth] $message");
        } else {
          talker.info("[FlutterBluetooth] $message");
        }
      });
      FlavorConfig(
        name: version + patchVersion,
        color: environment == dev ? Colors.red : Colors.blue,
        location: BannerLocation.bottomStart,
      );
      runApp(await builder());
    },
    (error, stackTrace) =>
        talker.handle(error, stackTrace, 'Uncaught app exception'),
  );
}
