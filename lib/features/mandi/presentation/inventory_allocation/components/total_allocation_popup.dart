import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/row_item_field.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/row_item_value.dart';

class AllocationQty {
  final String mandiQty;
  final String expectedQty;
  final String totalQty;
  final String? weighingSource;
  // final bool hasError;
  final String? errorMessage;
  bool get hasError => errorMessage != null;

  String get mandiQtyText => mandiQty.isEmpty ? '0' : mandiQty;
  String get expectedQtyText =>
      (expectedQty.isEmpty ? '0' : expectedQty) + ' (E)';
  String get mandiAndExpectedText => mandiQtyText + ' + ' + expectedQtyText;

  AllocationQty._({
    required this.mandiQty,
    required this.expectedQty,
    required this.totalQty,
    required this.errorMessage,
    this.weighingSource = null,
  });

  factory AllocationQty.fromTotalQty({
    required String qty,
    required double mandiQty,
    required bool allowExpectation,
    String? weighingSource,
  }) {
    final doubleQty = qty.toDouble();
    if (doubleQty <= mandiQty) {
      return AllocationQty._(
        mandiQty: qty,
        expectedQty: '',
        totalQty: qty,
        errorMessage: null,
        weighingSource: weighingSource,
      );
    } else {
      return AllocationQty._(
        mandiQty: mandiQty.asString(),
        expectedQty: (doubleQty - mandiQty).asString(),
        totalQty: qty,
        weighingSource: weighingSource,
        errorMessage: allowExpectation
            ? null
            : 'You cannot allocate more than mandi inventory, please contact pitstop head!',
      );
    }
  }

  factory AllocationQty.fromMandiAndExpected({
    required String mandiQty,
    required String expectedQty,
    required double actualMandiQty,
    String? weighingSource,
  }) {
    final doubleMandiQty = mandiQty.toDouble();
    final doubleExpectedQty = expectedQty.toDouble();
    final totalQty = doubleMandiQty + doubleExpectedQty;
    return AllocationQty._(
      mandiQty: mandiQty,
      expectedQty: expectedQty,
      totalQty: totalQty > 0 ? totalQty.asString() : '',
      weighingSource: weighingSource,
      errorMessage: mandiQty.toDouble() > actualMandiQty
          ? 'You cannot allocate more than mandi inventory, please contact pitstop head!'
          : null,
    );
  }

  // copyWith
  AllocationQty updateErrorMessage({
    String? errorMessage,
  }) {
    return AllocationQty._(
      mandiQty: this.mandiQty,
      expectedQty: this.expectedQty,
      totalQty: this.totalQty,
      errorMessage: errorMessage,
    );
  }
}

class TotalAllocationPopup extends StatefulWidget {
  final AllocationQty qty;
  final double mandiQty;
  final String skuName;
  final bool allowExpectation;
  const TotalAllocationPopup({
    super.key,
    required this.qty,
    required this.mandiQty,
    required this.skuName,
    required this.allowExpectation,
  });

  @override
  State<TotalAllocationPopup> createState() => _TotalAllocationPopupState();
}

class _TotalAllocationPopupState extends State<TotalAllocationPopup> {
  String _mandiQty = '';
  String _expectedQty = '';

  @override
  void initState() {
    _mandiQty = widget.qty.mandiQty;
    _expectedQty = widget.qty.expectedQty;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool isMandiQtyGreaterThanMandiQty = _mandiQty.toDouble() > widget.mandiQty;
    String totalQty =
        (_mandiQty.toDouble() + _expectedQty.toDouble()).asString();
    return Popup(
      title: widget.skuName + ' Allocation',
      children: [
        RowItemValue(
          label: 'Mandi Inventory',
          value: widget.mandiQty.asString(),
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            bottom: 8,
          ),
        ),
        Divider(),
        RowItemField(
          label: 'mandi'.tr('Mandi'),
          value: _mandiQty,
          inputFormatters: Config.numberInputFilters,
          onChanged: (value) {
            setState(() {
              _mandiQty = value;
            });
          },
          padding: EdgeInsets.symmetric(
            horizontal: 16,
          ),
          keyboardType: TextInputType.number,
        ),
        SizedBox(
          height: 16,
        ),
        RowItemField(
          label: 'expected'.tr('Expected'),
          value: _expectedQty,
          inputFormatters: Config.numberInputFilters,
          onChanged: (value) {
            setState(() {
              _expectedQty = value;
            });
          },
          padding: EdgeInsets.symmetric(
            horizontal: 16,
          ),
          keyboardType: TextInputType.number,
          isReadOnly: !widget.allowExpectation,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Divider(
            height: 32,
          ),
        ),
        RowItemValue(
          label: 'Allocated'.tr('Total'),
          value: totalQty,
          padding: EdgeInsets.only(
            left: 16,
            right: 25,
          ),
        ),
        Spacer(),
        if (isMandiQtyGreaterThanMandiQty)
          Padding(
            padding: const EdgeInsets.only(
              bottom: 16,
            ),
            child: Center(
                child: Text(
              'mandiQtyError'.tr('Qty can not be greater than mandi qty'),
              style: TextStyle(
                  color: Colors.red, fontSize: 12, fontWeight: FontWeight.w600),
            )),
          ),
        WStickyBottomCta(
          isEnabled: !isMandiQtyGreaterThanMandiQty,
          icon: Icons.check,
          label: Text('update'.tr('Update')),
          onPressed: () {
            Navigator.of(context).pop(AllocationQty.fromMandiAndExpected(
              mandiQty: _mandiQty,
              expectedQty: _expectedQty,
              actualMandiQty: widget.mandiQty,
            ));
          },
        )
      ],
    );
  }
}
