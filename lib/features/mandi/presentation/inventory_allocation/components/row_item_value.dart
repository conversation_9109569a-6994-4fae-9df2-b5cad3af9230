import 'package:flutter/material.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class RowItemValue extends StatelessWidget {
  const RowItemValue(
      {super.key, required this.label, required this.value, this.padding});
  final String label;
  final String value;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 14).merge(TextStyles.heading),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ).merge(TextStyles.heading),
          ),
        ],
      ),
    );
  }
}
