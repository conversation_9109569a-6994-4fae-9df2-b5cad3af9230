import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/ir_sku_edit.dart';

part 'inventory_recieving_input_model.freezed.dart';

@freezed
class InventoryRecievingInputModel with _$InventoryRecievingInputModel {
  const InventoryRecievingInputModel._();
  const factory InventoryRecievingInputModel({
    required Consignment consignment,
    required List<IRSKUEditInputModel> bulk,
    required List<IRSKUEditInputModel> lots,
    String? comments,
  }) = _InventoryRecievingInputModel;

  bool isEmpty() {
    return bulk.isEmpty && lots.isEmpty;
  }
}
