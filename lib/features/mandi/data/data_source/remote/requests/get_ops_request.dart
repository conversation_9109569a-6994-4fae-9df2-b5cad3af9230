import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';

class GetOpsRequest extends BaseRequest<dynamic, List<SmoOps>> {
  final String status;

  GetOpsRequest(this.status);

  @override
  String getPath() => 'smo/status/$status';

  @override
  RequestMethod get method => RequestMethod.GET;

  @override
  List<SmoOps> mapper(dynamic data) {
    if (data is List<dynamic>) {
      return data
          .map((e) => e as Map<String, dynamic>)
          .map((e) => SmoOps.fromJson(e))
          .toList();
    }
    throw Exception();
  }
}
