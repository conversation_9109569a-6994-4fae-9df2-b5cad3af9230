import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/group_info.dart';

class CreateGroupRequest extends BaseRequest<dynamic, String> {
  final int allocationId;
  final List<GroupInfo> groupInfo;
  final bool isUpdate;

  CreateGroupRequest({
    required this.allocationId,
    required this.groupInfo,
    required this.isUpdate,
  });

  @override
  Map<String, dynamic> getBody() {
    return {
      'allocationId': allocationId,
      'groups': groupInfo,
    };
  }

  @override
  String getPath() =>
      isUpdate ? 'allotments/edit-groups' : 'allotments/create-groups';

  @override
  String mapper(data) {
    if (data is String) return data;

    return 'Group created successfully!';
  }

  @override
  RequestMethod get method => isUpdate ? RequestMethod.PUT : RequestMethod.POST;
}
