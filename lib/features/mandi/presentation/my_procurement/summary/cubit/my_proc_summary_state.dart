import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/aggregated_proc_summary.dart';

part 'my_proc_summary_state.freezed.dart';

@freezed
abstract class MyProcSummaryState with _$MyProcSummaryState {
  const factory MyProcSummaryState.data({
    @Default(true) bool isLoading,
    @Default(null) AgreegatedProcSummaryResult? mySummary,
    @Default(null) AgreegatedProcSummaryResult? mandiSummary,
    @Default(null) String? errorMessage,
    @Default(false) bool isMandiSummaryOpen,
  }) = MyProcSummaryData;
}
