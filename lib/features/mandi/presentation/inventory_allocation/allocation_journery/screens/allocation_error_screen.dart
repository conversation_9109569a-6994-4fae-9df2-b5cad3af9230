import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';

class AllocationErrorScreen extends StatelessWidget {
  const AllocationErrorScreen({super.key, required this.state});
  final AllocationJourneyState state;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          centerTitle: false,
          title: Text('allocationError'.tr('Allocation Error')),
        ),
        body: Column(
          children: [
            Expanded(
              child: ErrorScreen(
                heading: state.errorData?.heading ?? 'Oops!',
                message: state.errorData?.message ?? 'Unknown error!',
                onPressed: () {
                  context.read<AllocationJourneyCubit>().retry();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
