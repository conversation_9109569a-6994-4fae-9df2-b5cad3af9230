import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/supply_order/view_supply_order/cubit/view_supply_order_cubit.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class ViewSupplyOrderDetail extends StatefulWidget {
  const ViewSupplyOrderDetail({super.key, required this.index});
  final int index;

  @override
  State<ViewSupplyOrderDetail> createState() => _ViewSupplyOrderDetailState();
}

class _ViewSupplyOrderDetailState extends State<ViewSupplyOrderDetail> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BlocBuilder<ViewSupplyOrderCubit, ViewSupplyOrderState>(
        builder: (context, input) {
          return Scaffold(
            appBar: WAppBar.getAppBar(
              title:
                  LangText('ViewSupplyOrderDetail.title', 'View Supply Order'),
              centerTitle: false,
            ),
            body: BlocBuilder<SkuBloc, SkuState>(
              builder: (context, state) {
                return state.map(
                  initial: (initial) => Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error) => ErrorScreen(
                    onPressed: () {},
                    message: error.errorr.message,
                  ),
                  success: (_) {
                    final item = input.supplyOrders![widget.index];
                    return Container(
                      width: MediaQuery.of(context).size.width,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 16,
                        ),
                        child: ListView(
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                headingValue('Order Number', item.id),
                                headingValue(
                                    'Order Placed', item.orderDateString),
                                headingValue(
                                    'Delivery Date', item.deliveryDateString),
                                headingValue(
                                    'Delivery Slot', item.deliverySlot),
                              ],
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                headingValue('Terminal', item.customer.key),
                                headingValue(
                                    item.createdBy.isEmpty ? '' : 'Ordered By',
                                    item.createdBy),
                                headingValue(
                                  item.updatedBy == null ? '' : 'Updated By',
                                  item.updatedBy ?? '',
                                ),
                                headingValue(
                                  item.updatedAtDateString.isEmpty
                                      ? ''
                                      : 'Last Updated At',
                                  item.updatedAtDateString,
                                ),
                              ],
                            ),
                            Divider(),
                            SizedBox(
                              height: 16,
                            ),
                            Row(
                              children: [
                                heading('Product', flex: 1),
                                heading('Unit Type'),
                                heading('Unit'),
                                heading('Lot Size'),
                                heading('No. of Lots'),
                                heading('Quantity'),
                              ],
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            for (int index = 0;
                                index < item.items.length;
                                index++)
                              Builder(
                                builder: (context) {
                                  final order = item.items[index];
                                  return Column(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 8,
                                          top: 8,
                                        ),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            value(
                                              getSKU(context,
                                                      skuID: order
                                                          .skuQuantity.skuId)
                                                  .name,
                                              flex: 1,
                                            ),
                                            value(
                                              order.skuQuantity.isBulk
                                                  ? 'Bulk'
                                                  : 'Lots',
                                            ),
                                            value(
                                              order.skuQuantity.unit,
                                            ),
                                            value(
                                              order.skuQuantity.isBulk
                                                  ? '-'
                                                  : order.skuQuantity.lotSize
                                                          .toString() +
                                                      ' ' +
                                                      order.skuQuantity.unit,
                                            ),
                                            value(
                                              order.skuQuantity.isBulk
                                                  ? '-'
                                                  : order.skuQuantity.quantity
                                                      .toString(),
                                            ),
                                            value(
                                              (!order.skuQuantity.isBulk
                                                      ? (order.skuQuantity
                                                                  .lotSize! *
                                                              order.skuQuantity
                                                                  .quantity)
                                                          .toString()
                                                      : order
                                                          .skuQuantity.quantity
                                                          .toString()) +
                                                  ' ${order.skuQuantity.unit}',
                                            ),
                                          ],
                                        ),
                                      ),
                                      Divider(),
                                    ],
                                  );
                                },
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget value(String value, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Text(
        value,
        style: TextStyle(
          fontSize: 14,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget heading(String heading, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Text(
        heading,
        style: TextStyle(
          fontSize: 16,
          color: Colors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget headingValue(String heading, String value) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            heading,
            style: TextStyle(
                fontSize: 14, color: Colors.grey, fontWeight: FontWeight.w700),
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: SelectionArea(
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
