import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'menu_config_dto.freezed.dart';
part 'menu_config_dto.g.dart';

@freezed
class MenuConfigDto with _$MenuConfigDto {
  const factory MenuConfigDto({
    bool? proc,
    bool? addCarryForward,
    bool? addFieldCharges,
    bool? receiveInventory,
    bool? mandiInventory,
    bool? skuAllocation,
    bool? mandiProcSummary,
    bool? viewLoadingPlan,
    bool? closingStockPlan,
    bool? closeSmoOps,
    bool? lotting,
    bool? supplyOrder,
    bool? liquidation,
    bool? grading,
    bool? returns,
    bool? procAdmin,
    bool? attendance,
  }) = _MenuConfigDto;

  @override
  factory MenuConfigDto.fromJson(Map<String, dynamic> json) =>
      _$MenuConfigDtoFromJson(json);
}
