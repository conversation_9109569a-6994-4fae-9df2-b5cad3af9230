import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/allocation_dispatch_poup.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/allocation_list_ui.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/locked_sku_popup.dart';

class AllocationViewScreen extends StatefulWidget {
  const AllocationViewScreen({
    super.key,
    required this.state,
  });
  final AllocationJourneyState state;

  @override
  State<AllocationViewScreen> createState() => _AllocationViewScreenState();
}

class _AllocationViewScreenState extends State<AllocationViewScreen> {
  final selectedItems = <String>[];
  bool isSelectionActive = false;
  bool hasAnyExpectation = true;
  bool hasAnyPending = true;
  bool hasAnyNotOrderedAndDistributed = false;
  bool allEmpty = false;
  @override
  void initState() {
    _updateAnyExpectationAndPending();
    super.initState();
  }

  List<AllocationSkuItem> filterItems(List<AllocationSkuItem> items) {
    return items.where((e) => e.isValidId).toList();
  }

  bool filterHasAnyExpected(List<AllocationSkuItem> items) {
    return items.any((e) => e.backendAllocationQty.expectedQty.toDouble() > 0);
  }

  bool filterHasAnyPending(List<AllocationSkuItem> items) {
    return items.any((e) => e.isPending);
  }

  void _updateAnyExpectationAndPending() {
    final items = filterItems(widget.state.items);
    final itemsPartial = filterItems(widget.state.itemsPartial);
    final itemsInMandi = filterItems(widget.state.itemsInMandi);
    final itemsInSupplyOrder = filterItems(widget.state.itemsInSupplyOrder);

    hasAnyExpectation = filterHasAnyExpected(items) ||
        filterHasAnyExpected(itemsPartial) ||
        filterHasAnyExpected(itemsInMandi) ||
        filterHasAnyExpected(itemsInSupplyOrder);

    hasAnyPending = !widget.state.isCustomerGroup
        ? false
        : filterHasAnyPending(items) ||
            filterHasAnyPending(itemsPartial) ||
            filterHasAnyPending(itemsInMandi) ||
            filterHasAnyPending(itemsInSupplyOrder);

    // hasAnyNotOrderedAndDistributed = !widget.state.isCustomerGroup
    //     ? false
    //     : !widget.state.isEveningOrder
    //         ? itemsInMandi.isNotEmpty
    //         : itemsInMandi.where((e) => e.isPending).isNotEmpty;
    allEmpty = items.isEmpty &&
        itemsPartial.isEmpty &&
        itemsInMandi.isEmpty &&
        itemsInSupplyOrder.isEmpty;
  }

  @override
  void didUpdateWidget(covariant AllocationViewScreen oldWidget) {
    if (widget.state.items == oldWidget.state.items ||
        widget.state.itemsPartial == oldWidget.state.itemsPartial ||
        widget.state.itemsInMandi == oldWidget.state.itemsInMandi ||
        widget.state.itemsInSupplyOrder == oldWidget.state.itemsInSupplyOrder) {
      _updateAnyExpectationAndPending();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final enableUpdateAllocation = widget.state.allocationConfig?.allocation
            .updateAllocation.allowExpectation ??
        false;
    final isDispatchTimeOver =
        widget.state.allocationConfig?.canDispatchAllocation ?? true;
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: WAppBar.getAppBar(
          title: Text(
            widget.state.isEditingAllowed
                ? 'viewAllocation'.tr('View Allocation')
                : 'allocationSummary'.tr('Allocation Summary'),
          ),
          actions: [
            if (widget.state.allocationConfig?.allocation.canUnlockLockedSku ==
                    true &&
                widget.state.isCustomerGroup)
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: IconButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (_) => LockedSkuPopup(
                        allocationId: widget.state.allocationId,
                        skuMap: widget.state.skuMap,
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.lock,
                    color: Colors.white,
                  ),
                ),
              ),
            if (widget.state.canManageOrders)
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: IconButton(
                  onPressed: () {
                    context
                        .read<AllocationJourneyCubit>()
                        .openAllocationManagementScreen();
                  },
                  icon: Icon(
                    Icons.manage_history,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
          centerTitle: false,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AllocationListUi(
              state: widget.state,
              isUpdateUi: false,
              selectedItems: widget.state.isEditingAllowed &&
                      !widget.state.allowOnlyDispatch
                  ? selectedItems
                  : [],
              onSelectedItemChange: !widget.state.isEditingAllowed ||
                      widget.state.allowOnlyDispatch
                  ? (_) {}
                  : (value) {
                      if (selectedItems.contains(value)) {
                        selectedItems.remove(value);
                      } else {
                        selectedItems.add(value);
                      }
                      isSelectionActive = selectedItems.isNotEmpty;
                      setState(() {});
                    },
              isEditingAllowed: widget.state.isEditingAllowed &&
                  !widget.state.allowOnlyDispatch,
              isSelectionActive: isSelectionActive,
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Visibility(
                visible: !isDispatchTimeOver && widget.state.isEditingAllowed,
                child: Text(
                  'allocation.dispatchTimeMsg'.tr(
                      'You can not dispatch the allocation before ##time##.',
                      params: {
                        'time': widget.state.allocationConfig?.dispatchTime,
                      }),
                  style: TextStyle(
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            Visibility(
              visible: widget.state.isEditingAllowed,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.shade300,
                      blurRadius: 4,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: enableUpdateAllocation &&
                          !widget.state.allowOnlyDispatch,
                      child: Container(
                        margin: EdgeInsets.only(
                          right: 8,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.blue,
                        ),
                        child: IconButton(
                          onPressed: () {
                            context
                                .read<AllocationJourneyCubit>()
                                .openAllocationScreenWithSelectedItems(
                                    selectedItems);
                          },
                          icon: Icon(
                            Icons.edit,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: widget.state.isCustomerGroup &&
                          !widget.state.allowOnlyDispatch,
                      child: Container(
                        margin: EdgeInsets.only(
                          right: 8,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.blue,
                        ),
                        child: IconButton(
                          onPressed: () {
                            context
                                .read<AllocationJourneyCubit>()
                                .showPdf(selectedItems);
                          },
                          icon: Icon(
                            Icons.download,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Visibility(
                      visible:
                          hasAnyExpectation && !widget.state.allowOnlyDispatch,
                      child: Container(
                        margin: EdgeInsets.only(
                          right: 8,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.blue,
                        ),
                        child: IconButton(
                          onPressed: () async {
                            final shouldAutoResolve = await showDialog<bool>(
                                  context: context,
                                  builder: (_) => AlertDialog(
                                    title: Text('Auto Resolve?'),
                                    content: Text(
                                        'Do you want to auto resolve all the expectations?'),
                                    actions: [
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.pop(context, false),
                                        child: Text('No'),
                                      ),
                                      TextButton(
                                        onPressed: () =>
                                            Navigator.pop(context, true),
                                        child: Text('Yes'),
                                      ),
                                    ],
                                  ),
                                ) ??
                                false;

                            if (shouldAutoResolve) {
                              context
                                  .read<AllocationJourneyCubit>()
                                  .autoResolve();
                            }
                          },
                          icon: Icon(
                            Icons.warning,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: (hasAnyExpectation ||
                                    hasAnyPending ||
                                    hasAnyNotOrderedAndDistributed ||
                                    !isDispatchTimeOver) &&
                                !widget.state.allowOnlyDispatch
                            ? null
                            : () {
                                showDialog(
                                  context: context,
                                  builder: (_) => AllocationDispatchPopup(
                                    onPressed: () {
                                      context
                                          .read<AllocationJourneyCubit>()
                                          .dispatchAllocation();
                                      context.pop();
                                    },
                                  ),
                                );
                              },
                        child: AutoSizeText(
                          'Ready to Dispatch',
                          minFontSize: 12,
                        ),
                        style:
                            ElevatedButton.styleFrom(fixedSize: Size(140, 48)),
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
