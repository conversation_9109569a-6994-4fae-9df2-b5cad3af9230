import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/admin/weighing_machine_bypass/cubit/weighing_machine_bypass_cubit.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/mandi_dropdown.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';

class WeighingMachineBypassScreen extends StatelessWidget {
  const WeighingMachineBypassScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          centerTitle: false,
          title: Text(
            'weighingMachineBypass'.tr('Weighing Machine Bypass'),
          ),
        ),
        body: BlocConsumer<WeighingMachineBypassCubit,
            WeighingMachineBypassState>(
          listener: (context, state) {
            if (state.message != null) {
              showSnackBar(state.message!);
              context.read<WeighingMachineBypassCubit>().clearMessage();
            }
          },
          builder: (context, state) {
            if (state.isLoading) {
              return Center(
                child: CircularProgressIndicator(),
              );
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  color: Colors.grey[200],
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: IntrinsicHeight(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 1,
                            child: MandiDropdown(
                              selectedMandi: state.selectedMandi,
                              onChange: (val) {
                                if (val != null) {
                                  final ops = context
                                      .read<MandiBloc>()
                                      .state
                                      .mapOrNull(success: (s) {
                                    return s.liveOps ?? <SmoOps>[];
                                  });
                                  final smoId = ops
                                      ?.where((e) => e.mandiId == val.id)
                                      .firstOrNull
                                      ?.smoId;
                                  if (smoId == null) return;
                                  context
                                      .read<WeighingMachineBypassCubit>()
                                      .updateSelectedMandi(
                                        mandi: val,
                                        smoId: smoId,
                                      );
                                }
                              },
                              filterOnMandi: MandiType.pitstop,
                              hintText: 'weighingMachineBypass.selectPitstop'
                                  .tr('Select Pitstop'),
                              selectedLabel:
                                  'weighingMachineBypass.selectedPitstop'
                                      .tr('Selected Pitstop'),
                              noMandiMessage:
                                  'weighingMachineBypass.noPitstopFound'
                                      .tr('No pitstop found'),
                            ),
                          ),
                          SizedBox(
                            width: 32,
                          ),
                          Row(
                            children: [
                              ElevatedButton.icon(
                                onPressed: state.selectedMandi == null ||
                                        state.isLoading
                                    ? null
                                    : () async {
                                        await context
                                            .read<WeighingMachineBypassCubit>()
                                            .searchUsers();
                                      },
                                label: Text('searchUsers'.tr('Search Users')),
                                icon: state.isSearchLoading
                                    ? SizedBox(
                                        height: 18,
                                        width: 18,
                                        child: CircularProgressIndicator(
                                          color: Colors.grey,
                                        ),
                                      )
                                    : Icon(
                                        Icons.search,
                                      ),
                              ),
                              const SizedBox(width: 8),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Card(
                  color: Colors.grey[200],
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 100,
                          child: Text(
                            'serialNumber'.tr('S.No.'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'name'.tr('Name'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'emailorPhone'.tr('Email/Phone'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        SizedBox(
                          width: 150,
                          child: Text(
                            'weighingMachineBypass.isBypassed'
                                .tr('Allow Bypass'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: state.assignedUsers == null
                      ? EmptyScreen(
                          message: 'weighingMachineBypass.searchPitstopMessage'
                              .tr('Select Pitstop and Search'),
                          description:
                              'weighingMachineBypass.searchPitstopDescription'
                                  .tr('To get the list of pitstop users'),
                        )
                      : state.assignedUsers!.isEmpty
                          ? EmptyScreen(
                              message: 'weighingMachineBypass.noUserMessage'
                                  .tr('No user found for this pitstop!'),
                            )
                          : ListView.builder(
                              itemCount: state.assignedUsers!.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                final user = state.assignedUsers![index];
                                return Card(
                                  elevation: 0,
                                  color: Colors.white,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: 100,
                                          child: Text(
                                            (index + 1).toString(),
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 16,
                                        ),
                                        Expanded(
                                          child: Text(
                                            user.userName,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 16,
                                        ),
                                        Expanded(
                                          child: Text(
                                            user.user.mobileNumber ??
                                                user.user.email ??
                                                '-',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 16,
                                        ),
                                        SizedBox(
                                            width: 150,
                                            child: Align(
                                              alignment: Alignment.centerRight,
                                              child: Switch(
                                                value: user
                                                    .hasWeighingMachineByPass,
                                                onChanged: (val) {
                                                  context
                                                      .read<
                                                          WeighingMachineBypassCubit>()
                                                      .toggleBypass(
                                                          id: user.id);
                                                },
                                              ),
                                            )),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                ),
                // WStickyBottomCta(
                //   isEnabled:
                //       state.hasAnyUpdateActionDone && !state.isCtaLoading,
                //   isLoading: state.isCtaLoading,
                //   icon: Icons.check,
                //   label: Text('weighingMachineBypass.submit'.tr('Submit')),
                //   onPressed: () async {
                //     await context.read<WeighingMachineBypassCubit>().submit();
                //   },
                // ),
              ],
            );
          },
        ),
      ),
    );
  }
}
