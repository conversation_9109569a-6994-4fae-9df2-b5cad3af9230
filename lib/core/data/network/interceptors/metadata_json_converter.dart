// ignore_for_file: must_be_immutable

import 'dart:async';
import 'dart:convert';

import 'package:chopper/chopper.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:injectable/injectable.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/network/interceptors/network_info.dart';
import 'package:proc2/core/di/di.dart';

@singleton
class MetadataJsonConverter implements Converter {
  final jsonConverter = const JsonConverter();

  ConnectivityResult? connectivityResult;
  PackageInfo? packageInfo;
  DeviceInfoPlugin? deviceInfoPlugin;

  MetadataJsonConverter();

  @override
  FutureOr<Request> convertRequest(Request request) async {
    var parentRequest = jsonConverter.convertRequest(request);
    // if (parentRequest.method == HttpMethod.Get ||
    //     parentRequest.method == HttpMethod.Delete) return parentRequest;
    final metadata = await _getMetadata();
    try {
      Map<String, dynamic> originalBody = jsonDecode(parentRequest.body);
      originalBody = {
        ...originalBody,
        ...metadata,
      };

      parentRequest = parentRequest.copyWith(body: jsonEncode(originalBody));
    } catch (e, s) {
      talker.handle(e, s);
    } finally {
      return parentRequest;
    }
  }

  Future<Map<String, dynamic>> _getMetadata() async {
    try {
      if (this.packageInfo == null) {
        try {
          packageInfo = NetworkInfo.instance.packageInfo;
        } catch (_) {}
      }

      if (connectivityResult == null) {
        try {
          connectivityResult = await Connectivity().checkConnectivity();
        } catch (_) {}
      }

      if (deviceInfoPlugin == null) {
        try {
          deviceInfoPlugin = DeviceInfoPlugin();
        } catch (_) {}
      }

      AndroidDeviceInfo? androidInfo;

      try {
        androidInfo = await deviceInfoPlugin?.androidInfo;
      } catch (_) {}

      return {
        "__metadata": {
          "action": {
            "timestamp": DateTime.now().millisecondsSinceEpoch,
          },
          "request": {
            "async": false,
            "timestamp": DateTime.now().millisecondsSinceEpoch,
            "process": "background",
          },
          "app": {
            "langCode": "en",
            "version": packageInfo?.version,
            "versionCode": packageInfo?.buildNumber,
            "isDev": di.get<AppConfig>().isDev,
          },
          "device": {
            "brand": androidInfo?.brand,
            "model": androidInfo?.model,
            "deviceName": androidInfo?.device,
            "androidSdkVersion": androidInfo?.version.sdkInt,
            "androidId": androidInfo?.version.sdkInt,
            "network": {
              "networkType": connectivityResult?.name,
            },
          },
        }
      };
    } catch (_) {
      return {};
    }
  }

  @override
  FutureOr<Response<BodyType>> convertResponse<BodyType, InnerType>(
      Response response) {
    return jsonConverter.convertResponse(response);
  }
}
