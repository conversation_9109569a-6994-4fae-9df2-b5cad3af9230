part of 'edit_procurement_bloc.dart';

@freezed
class EditProcurementEvent with _$EditProcurementEvent {
  const factory EditProcurementEvent.started(SmoDetail smoDetail) = _Started;
  // update price
  const factory EditProcurementEvent.updatePrice({
    required int index,
    required String price,
    required bool isBulk,
  }) = _UpdatePrice;

  const factory EditProcurementEvent.updateSlips({
    required int index,
    required List<PickedFile> allFiles,
    required List<PickedFile> files,
    required bool isBulk,
  }) = _UpdateSlips;

  const factory EditProcurementEvent.update(
    int smoId, {
    required bool isSubmit,
    @Default(true) bool shouldPop,
  }) = _Update;
  const factory EditProcurementEvent.clearMessage() = _ClearMessage;
  const factory EditProcurementEvent.addSlips(
    int smoId,
    List<PickedFile> slips,
  ) = _AddSlips;
  const factory EditProcurementEvent.clearSlips() = _ClearSlips;
  const factory EditProcurementEvent.updateProcCharge(
    ProcFieldCharge charge,
    int index,
  ) = _UpdateProcCharge;
  const factory EditProcurementEvent.deleteProcCharge(
    int index,
  ) = _DeleteProcCharge;
  const factory EditProcurementEvent.addProcCharge(ProcFieldCharge charge) =
      _AddProcCharge;
}
