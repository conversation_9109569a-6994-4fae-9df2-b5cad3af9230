import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

part 'returns.freezed.dart';
part 'returns.g.dart';

@freezed
class Returns with _$Returns {
  const Returns._();
  const factory Returns({
    required int returnId,
    required List<String> terminals,
    required int createdAt,
    required String status,
    required String returnType,
    DriversDetails? driversDetails,
    required String returnOrderId,
    // required List<ReturnItem> items,
  }) = _Returns;

  bool get isSelfDrop => returnType == 'SELFDROP';

  factory Returns.fromJson(Map<String, dynamic> json) =>
      _$ReturnsFromJson(json);
}

@freezed
class ReturnsHistory with _$ReturnsHistory {
  const ReturnsHistory._();
  const factory ReturnsHistory({
    required String returnOrderId,
    required int smoId,
    required String acceptedBy,
    required int returnAt,
    required List<ReturnItem> returnItems,
    @Default(null) String? driverName,
    @Default(null) String? driverPhone,
    @Default(null) String? driverVehicleNumber,
    required String returnType,
    required String returnStatus,
  }) = _ReturnsHistory;

  bool get isAccepted => returnStatus == 'ACCEPTED';
  bool get isRejected => returnStatus == 'REJECTED';
  bool get isSelfDrop => returnType == 'SELFDROP';

  @override
  factory ReturnsHistory.fromJson(Map<String, dynamic> json) =>
      _$ReturnsHistoryFromJson(json);
}

@freezed
class ReturnItem with _$ReturnItem {
  const ReturnItem._();
  const factory ReturnItem({
    @Default(null) int? itemId,
    required SkuQuantity skuQuantity,
    @Default('') String acceptedQuantity,
    @Default(0.0) double receivedQuantity,
    @Default(null) String? weighingSource,
    @Default([]) List<int> compositionIds,
    @Default(true) bool isVerified,
  }) = _ReturnItem;

  bool get isComposedOfMoreThanOne => compositionIds.length > 1;
  bool get isDeviated => !isVerified;

  factory ReturnItem.fromJson(Map<String, dynamic> json) =>
      _$ReturnItemFromJson(json);
}

@freezed
class DriversDetails with _$DriversDetails {
  factory DriversDetails({
    String? driverName,
    String? driverMobile,
    String? vehicleNumber,
  }) = _DriversDetails;

  factory DriversDetails.fromJson(Map<String, dynamic> json) =>
      _$DriversDetailsFromJson(json);
}
