import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/sku_distribution_item.dart';

class GetSkuDistribution
    extends BaseRequest<dynamic, List<List<SkuDistributionItem>>> {
  final int allocationId;
  final SkuQuantity skuQuantity;
  final bool isAutoCopyDistributionQtyEnabled;
  final List<SupplyOrder> supplyOrders;

  GetSkuDistribution({
    required this.allocationId,
    required this.skuQuantity,
    required this.isAutoCopyDistributionQtyEnabled,
    required this.supplyOrders,
  });

  @override
  String getPath() => 'deliveryMemos/delivery-memo-items/$allocationId';

  @override
  Map<String, dynamic> getBody() {
    return {
      "searchSku": skuQuantity.toJson(),
    };
  }

  @override
  List<List<SkuDistributionItem>> mapper(data) {
    if (data is List<dynamic>) {
      final supplyOrderMap = Map.fromEntries(
        supplyOrders.map((e) => MapEntry(e.customer.orderSlNo, e)),
      );
      final backendItems = Map.fromEntries(
        data
            .map((e) => SkuDistributionItem.fromJson(e))
            .map((e) => e.copyWith(
                  weighingQuantity: e.fineTunedQuantity != null
                      ? WeighingQuantity.noSouce(
                          e.fineTunedQuantity!.asString())
                      : (isAutoCopyDistributionQtyEnabled
                          ? WeighingQuantity.auto(
                              e.skuQuantity.quantity?.asString())
                          : WeighingQuantity.noSouce('')),
                ))
            .map((e) => MapEntry(e.orderSlNo, e)),
      );
      final distributedItems = <SkuDistributionItem>[];
      for (final item in backendItems.values) {
        final supplyOrder = supplyOrderMap[item.orderSlNo];

        if (supplyOrder != null) {
          distributedItems.add(
            item.copyWith(
              priority: supplyOrder.customer.priority,
              variation: supplyOrder.distributions,
            ),
          );
        } else {
          // This case should never happen
          throw 'deliveryMemo.supplyOrderSlNotFound'
              .tr('Error while finding supply order for given orderSlNo');
        }
      }
      for (final order in supplyOrders) {
        if (!backendItems.containsKey(order.customer.orderSlNo)) {
          distributedItems.add(
            SkuDistributionItem(
              id: -1,
              skuQuantity: skuQuantity.copyWith(
                quantity: null,
              ),
              orderedQuantity: null,
              orderedPrice: null,
              allocatedPrice: null,
              deliveryMemoId: -1,
              customerKey: order.customer.key,
              supplyOrderId: order.id,
              orderSlNo: order.customer.orderSlNo,
              fineTunedQuantity: null,
              readOnly: true,
              priority: order.customer.priority,
              variation: order.distributions,
              refId: order.refId,
            ),
          );
        }
      }

      try {
        final mapOfGroupedItems = <String,
            List<SkuDistributionItem>>{}; // label, skuDistributionItems
        for (final item in distributedItems) {
          final label = item.priority?.label ?? 'Others';
          if (mapOfGroupedItems.containsKey(label)) {
            mapOfGroupedItems[label]!.add(item);
          } else {
            mapOfGroupedItems[label] = [item];
          }
        }
        final allGroups = mapOfGroupedItems.values.toList();
        // Sort these groups according to the priority.displayOrder
        allGroups.sort((a, b) {
          final aPriority = a.first.priority;
          final bPriority = b.first.priority;
          if (aPriority == null || bPriority == null) {
            return 0;
          }
          return aPriority.displayPriority.compareTo(bPriority.displayPriority);
        });
        for (final group in allGroups) {
          group.sort((a, b) {
            if (a.orderSlNo == null || b.orderSlNo == null) {
              return 0;
            }
            return a.orderSlNo!.compareTo(b.orderSlNo!);
          });
        }
        return allGroups;
      } catch (e) {
        throw 'deliveryMemo.orderSlNoNullError'
            .tr('Error while sorting distribution by serial number');
      }
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
