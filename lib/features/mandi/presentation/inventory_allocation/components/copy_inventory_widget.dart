import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class CopyInventoryWidget extends StatefulWidget {
  const CopyInventoryWidget({
    super.key,
    required this.state,
    this.padding,
    this.margin,
  });
  final AllocationJourneyState state;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  @override
  State<CopyInventoryWidget> createState() => _CopyInventoryWidgetState();
}

class _CopyInventoryWidgetState extends State<CopyInventoryWidget> {
  String? _dropDownValue;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      padding: widget.padding,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                'copyFrom'.tr('Copy From'),
                style: TextStyles.heading,
              ),
              SizedBox(
                width: 8,
              ),
              widget.state.isCustomerGroup
                  ? DropdownButton<String>(
                      value: _dropDownValue,
                      items: [
                        DropdownMenuItem(
                          child: Text(
                            'mandiInventory'.tr('Mandi Inventory'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                            ).merge(TextStyles.body),
                          ),
                          value: 'mandi',
                        ),
                        DropdownMenuItem(
                          child: Text(
                            'supplyOrders'.tr('Supply Orders'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                            ).merge(TextStyles.body),
                          ),
                          value: 'supplyOrder',
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _dropDownValue = value;
                        });
                      },
                    )
                  : Text(
                      'mandiInventory'.tr('Mandi Inventory'),
                      style: TextStyle(
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline)
                          .merge(TextStyles.body),
                    )
            ],
          ),
          ElevatedButton.icon(
            label: Text(
              'copy'.tr('Copy'),
              style: TextStyles.heading,
            ),
            onPressed: !widget.state.isCustomerGroup || _dropDownValue != null
                ? () {
                    bool shouldCopyFromMandi =
                        (_dropDownValue ?? 'mandi') == 'mandi';
                    if (shouldCopyFromMandi) {
                      context
                          .read<AllocationJourneyCubit>()
                          .copyFromMandiInventory();
                    } else {
                      context
                          .read<AllocationJourneyCubit>()
                          .copyFromSupplyOrders();
                    }
                  }
                : null,
            icon: Icon(
              Icons.copy,
              size: 18,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
