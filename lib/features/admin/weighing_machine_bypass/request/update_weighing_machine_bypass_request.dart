import 'package:proc2/core/data/network/base_request.dart';

class UpdateWeighingMachineBypassRequest extends BaseRequest<dynamic, String> {
  final int smoId;
  final int userId;
  final bool access;

  UpdateWeighingMachineBypassRequest(
      {required this.smoId, required this.userId, required this.access});

  @override
  String getPath() {
    return 'users/toggleWeighingMachineBypass';
  }

  @override
  Map<String, dynamic> getBody() {
    return {
      'smoId': smoId,
      'userId': userId,
      'access': access,
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
