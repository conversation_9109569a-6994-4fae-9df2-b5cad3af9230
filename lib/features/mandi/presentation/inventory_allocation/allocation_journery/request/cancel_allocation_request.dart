import 'package:proc2/core/data/network/base_request.dart';

class CancelAllocationRequest extends BaseRequest<dynamic, String> {
  final List<String> supplyOrderIds;
  final int allocationId;

  CancelAllocationRequest({
    required this.supplyOrderIds,
    required this.allocationId,
  });

  @override
  String getPath() => 'deliveryMemos/cancelSupplyOrders';

  @override
  Map<String, dynamic> getBody() {
    return {"supplyOrders": supplyOrderIds, "allocationId": allocationId};
  }

  @override
  String mapper(data) {
    if (data is String) {
      return data;
    } else {
      throw Exception('Invalid response');
    }
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
