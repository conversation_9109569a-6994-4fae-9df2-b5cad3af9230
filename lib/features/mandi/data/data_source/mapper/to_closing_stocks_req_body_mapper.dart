import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';

Map<String, dynamic> toClosingStocksReqBody(
  int smoId,
  int mandiId,
  List<SkuInputWithLoss> data,
) =>
    {
      'mandiId': mandiId,
      'smoId': smoId,
      'skuQuantityWithLosses': data
          .map(
            (e) => {
              'skuId': e.skuInputData.sku.id,
              'type': e.skuInputData.procurementType.value.toUpperCase(),
              'unit': e.skuInputData.procurementUnit!,
              'lotSize': e.skuInputData.lotSize,
              'quantity':
                  (e.skuInputData.quantity?.toDouble() ?? 0) + e.totalLoss(),
              'weighingSource': e.skuInputData.weighingSource,
              'images': e.files
                  .where((element) => element.uploadKey != null)
                  .map((e) => e.uploadKey)
                  .toList(),
              'losses': e.losses
                  .where((e) => e.lossValue.toDouble() > 0)
                  .map(
                    (loss) => {
                      'lossType': loss.lossType,
                      'quantity': loss.lossValue.toDouble(),
                      'comment': loss.comment,
                      'images': loss.files
                          .where((e) => e.uploadKey != null)
                          .map((e) => e.uploadKey!)
                          .toList(),
                    },
                  )
                  .toList()
            },
          )
          .toList(),
    };
