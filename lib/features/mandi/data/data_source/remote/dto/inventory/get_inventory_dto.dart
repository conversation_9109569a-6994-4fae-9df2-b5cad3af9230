import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/inventory/inventory_item_dto.dart';

part 'get_inventory_dto.freezed.dart';
part 'get_inventory_dto.g.dart';

@freezed
class GetInventoryDto with _$GetInventoryDto {
  const factory GetInventoryDto({
    required List<InventoryItemDto> skus,
  }) = _GetInventoryDto;

  @override
  factory GetInventoryDto.fromJson(Map<String, dynamic> json) =>
      _$GetInventoryDtoFromJson(json);
}
