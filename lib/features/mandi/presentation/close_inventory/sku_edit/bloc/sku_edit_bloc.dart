import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';
import 'package:uuid/uuid.dart';

part 'sku_edit_event.dart';
part 'sku_edit_state.dart';
part 'sku_edit_bloc.freezed.dart';

@injectable
class SkuEditBloc extends Bloc<SkuEditEvent, SkuEditState> {
  SkuEditBloc() : super(SkuEditState.empty) {
    on<SkuEditEvent>((event, emit) async {
      event.map(
        init: (init) {
          emit(
            state.copyWith(
              sku: init.skuInputData.sku,
              procurementType: init.skuInputData.procurementType,
              procurementUnit: init.skuInputData.procurementUnit,
              lotSize: init.skuInputData.lotSize,
              weighingSource: init.skuInputData.weighingSource,
              isCtaActive: _isCtaActive(
                sku: init.skuInputData.sku,
                procurementType: init.skuInputData.procurementType,
                procurementUnit: init.skuInputData.procurementUnit,
                lotSize: init.skuInputData.lotSize,
                quantity: init.skuInputData.quantity,
                amount: init.skuInputData.amount,
                weight: init.skuInputData.weight,
              ),
              showLotSizeField: _showLotSizeField(
                init.skuInputData.procurementType,
                init.skuInputData.procurementUnit,
              ),
              uuid: init.skuInputData.uuid,
              quantity: init.skuInputData.quantity,
              amount: init.skuInputData.amount,
              weight: init.skuInputData.weight,
              isOpenForEdit: init.isOpenForEdit,
            ),
          );
        },
        updateProcurementType: (updateProcurementType) {
          emit(
            state.copyWith(
              procurementType: updateProcurementType.procurementType,
              procurementUnit: null,
              lotSize: null,
              isCtaActive: _isCtaActive(
                sku: state.sku,
                procurementType: updateProcurementType.procurementType,
                procurementUnit: state.procurementUnit,
                lotSize: state.lotSize,
                quantity: state.quantity,
                amount: state.amount,
                weight: state.weight,
              ),
              showLotSizeField: _showLotSizeField(
                updateProcurementType.procurementType,
                state.procurementUnit,
              ),
            ),
          );
        },
        updateUnit: (updateUnit) {
          emit(
            state.copyWith(
              procurementUnit: updateUnit.unit,
              lotSize: null,
              isCtaActive: _isCtaActive(
                sku: state.sku,
                procurementType: state.procurementType,
                procurementUnit: updateUnit.unit,
                lotSize: state.lotSize,
                quantity: state.quantity,
                amount: state.amount,
                weight: state.weight,
              ),
              showLotSizeField: _showLotSizeField(
                state.procurementType,
                updateUnit.unit,
              ),
            ),
          );
        },
        updateLotSize: (updateLotSize) {
          emit(
            state.copyWith(
                lotSize: updateLotSize.lotSize,
                isCtaActive: _isCtaActive(
                  sku: state.sku,
                  procurementType: state.procurementType,
                  procurementUnit: state.procurementUnit,
                  lotSize: updateLotSize.lotSize,
                  quantity: state.quantity,
                  amount: state.amount,
                  weight: state.weight,
                )),
          );
        },
        updateQuanity: (updateQuanity) {
          emit(
            state.copyWith(
                quantity: updateQuanity.quantity,
                weighingSource: updateQuanity.source,
                isCtaActive: _isCtaActive(
                  sku: state.sku,
                  procurementType: state.procurementType,
                  procurementUnit: state.procurementUnit,
                  lotSize: state.lotSize,
                  quantity: updateQuanity.quantity,
                  amount: state.amount,
                  weight: state.weight,
                )),
          );
        },
        updateAmount: (updateAmount) {
          emit(
            state.copyWith(
              amount: updateAmount.amount,
              isCtaActive: _isCtaActive(
                sku: state.sku,
                procurementType: state.procurementType,
                procurementUnit: state.procurementUnit,
                lotSize: state.lotSize,
                quantity: state.quantity,
                amount: updateAmount.amount,
                weight: state.weight,
              ),
            ),
          );
        },
        updateWeight: (_UpdateWeight value) {
          emit(
            state.copyWith(
              weight: value.weight,
              isCtaActive: _isCtaActive(
                sku: state.sku,
                procurementType: state.procurementType,
                procurementUnit: state.procurementUnit,
                lotSize: state.lotSize,
                quantity: state.quantity,
                amount: state.amount,
                weight: value.weight,
              ),
            ),
          );
        },
      );
    });
  }

  bool _showLotSizeField(ProcurementType procurementType, String? unit) {
    return procurementType == ProcurementType.lots && unit != null;
  }

  bool _isCtaActive({
    required Sku? sku,
    required ProcurementType procurementType,
    required String? procurementUnit,
    required double? lotSize,
    required String? quantity,
    required String? amount,
    required String? weight,
  }) {
    if (sku == null) return false;
    if (procurementUnit == null || procurementUnit.isEmpty) {
      return false;
    }
    // if (quantity == null ||
    //     quantity.isEmpty ||
    //     quantity.isNotDouble() ||
    //     quantity.toDouble() == 0) {
    //   return false;
    // }
    if (weight != null && weight.isNotEmpty && weight.isNotDouble()) {
      return false;
    }
    if (procurementType == ProcurementType.lots && lotSize == null) {
      return false;
    }
    if (amount != null && amount.isNotDouble()) return false;

    return true;
  }

  SkuInputData? getSkuInputData() {
    if (state.sku == null) return null;
    if (state.procurementUnit == null) return null;
    // if (state.quantity == null || state.quantity!.isEmpty) return null;
    if (state.procurementType == ProcurementType.lots &&
        (state.lotSize == null)) return null;

    return SkuInputData(
      sku: state.sku!,
      procurementType: state.procurementType,
      procurementUnit: state.procurementUnit,
      lotSize: state.lotSize,
      quantity: state.quantity,
      amount: state.amount,
      uuid: state.uuid,
      weight: state.weight,
      weighingSource: state.weighingSource,
    );
  }
}
