import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/assigned_mandi.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/available_mandi.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/user_role.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/cubit/manage_users_cubit.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/view/widgets/multi_select_role_popup.dart';

class UserRoleMandiListPopup extends StatefulWidget {
  const UserRoleMandiListPopup({super.key});

  @override
  State<UserRoleMandiListPopup> createState() => _UserRoleMandiListPopupState();
}

class _UserRoleMandiListPopupState extends State<UserRoleMandiListPopup> {
  Map<int, List<UserRole>> _mandiToRoles = {};

  void _updateMandiToRoles(List<AssignedMandi> assignedMandis) {
    _mandiToRoles = {};
    assignedMandis.forEach(
      (e) {
        e.mandis.forEach((m) {
          if (_mandiToRoles[m] == null) {
            _mandiToRoles[m] = [
              UserRole(roleId: e.roleId, roleName: e.roleName)
            ];
          } else {
            _mandiToRoles[m]!
                .add(UserRole(roleId: e.roleId, roleName: e.roleName));
          }
        });
      },
    );
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      try {
        setState(() {
          final data =
              context.read<ManageUsersCubit>().state.userMandi?.assignedMandis;
          if (data != null) {
            _updateMandiToRoles(data);
          }
        });
      } catch (_) {}
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ManageUsersCubit, ManageUsersState>(
      listener: (context, state) {
        final data = state.userMandi?.assignedMandis;
        if (data != null) {
          setState(() {
            _updateMandiToRoles(data);
          });
        }
        if (state.message != null) {
          showSnackBar(state.message!);
          context.read<ManageUsersCubit>().clearErrorMsg(null);
        }
      },
      builder: (context, state) {
        return Popup(
          height: 0.8,
          maxWidth: MediaQuery.of(context).size.width,
          title: getLangText('mandiList', 'Mandi List'),
          children: [
            Expanded(
                child: Container(
              margin: const EdgeInsets.only(
                bottom: 8,
                left: 16,
                right: 16,
              ),
              padding: const EdgeInsets.only(
                top: 32,
                left: 16,
                right: 16,
              ),
              decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.shade300,
                      blurRadius: 10,
                      offset: const Offset(0, 0.75),
                    )
                  ],
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  )),
              child: ListView(
                children: [
                  for (AvailableMandi mandi
                      in state.userMandi?.availableMandis ?? [])
                    Card(
                      elevation: 0.6,
                      color: Colors.grey.shade300,
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.only(left: 10, right: 5),
                                child: SizedBox(
                                  width: 100,
                                  child: Text(
                                    mandi.name,
                                    style: TextStyle(
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ),
                              ),
                              Builder(builder: (context) {
                                final roles = _mandiToRoles[mandi.id] ?? [];
                                return SizedBox(
                                  width: 250,
                                  child: Text(
                                    roles.map((e) => e.roleName).join(', '),
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500),
                                  ),
                                );
                              }),
                              Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: IconButton(
                                    onPressed: () async {
                                      final roles =
                                          _mandiToRoles[mandi.id] ?? [];

                                      Map<UserRole, bool> mappedItems = {
                                        for (UserRole role in state.roleList)
                                          if (roles.any((val) =>
                                              val.roleId == role.roleId)) ...{
                                            role: true
                                          } else ...{
                                            role: false
                                          }
                                      };

                                      await showDialog(
                                          context: context,
                                          builder: (_) => BlocProvider.value(
                                                value: context
                                                    .read<ManageUsersCubit>(),
                                                child: MultiSelectRolePopup(
                                                    mappedItem: mappedItems,
                                                    items: state.roleList,
                                                    question: Text(getLangText(
                                                        'selectRoles',
                                                        "Select Roles")),
                                                    onSubmit: (selectedItems) {
                                                      context
                                                          .read<
                                                              ManageUsersCubit>()
                                                          .setNewRoles(
                                                              roles:
                                                                  selectedItems,
                                                              mandiId:
                                                                  mandi.id);
                                                    }),
                                              ));
                                    },
                                    icon: const Icon(Icons.edit, size: 23)),
                              ),
                            ]),
                      ),
                    )
                ],
              ),
            )),
            const SizedBox(height: 16),
            Center(
                child: ElevatedButton(
              onPressed: () async {
                bool isSuccess =
                    await context.read<ManageUsersCubit>().updateUserRole();
                if (isSuccess) {
                  Navigator.pop(context);
                  showSnackBar(getLangText('userMandiRoleUpdateSuccess',
                      'User mandi role updated successfully!'));
                  context.read<ManageUsersCubit>().getUserMandis();
                }
              },
              child: Text(
                getLangText('update', 'Update'),
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            )),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
}
