part of 'inventory_allocation_bloc.dart';

@freezed
class InventoryAllocationState with _$InventoryAllocationState {
  const factory InventoryAllocationState.initial() = _Initial;
  const factory InventoryAllocationState.input({
    @Default(AllotmentInit.empty) AllotmentInit initData,
    String? selectedDestinationType,
    DestinationInfo? selectedDestination,
    String? deliverySlot,
    DateTime? deliveryDate,
    @Default(false) bool isCtaLoading,
    String? message,
    int? allotmentIdToOpen,
    String? allotmentDestinationName,
    Map<String, dynamic>? allotmentQueryParams,
    @Default(null) List<AllotmentV2>? mandiAllotments,
    @Default(null) List<AllotmentV2>? customerAllotments,
    @Default(null) String? customerGroup,
    @Default(false) bool hasErrorInGetAllotments,
  }) = Input;
  const factory InventoryAllocationState.error(ErrorResult<dynamic> error) =
      Error;
}
