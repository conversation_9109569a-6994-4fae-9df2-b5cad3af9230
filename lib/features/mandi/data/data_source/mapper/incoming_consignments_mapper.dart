import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/consignment_dto.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/incoming_stock_item.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/sku_quantity.dart';

List<Consignment> toIncomingConsignmentsList(
  List<ConsignmentDto> consignmentList,
) {
  final consignments = <Consignment>[];

  for (final con in consignmentList) {
    final incomingStockItems = <IncomingStockItem>[];
    for (final isi in con.incomingStockItems) {
      final skuQuantity = SKUQuantity(
        skuId: isi.skuQuantity.skuId,
        type: isi.skuQuantity.type,
        unit: isi.skuQuantity.unit,
        lotSize: isi.skuQuantity.lotSize ?? 0,
        quantity: isi.skuQuantity.quantity,
      );
      incomingStockItems.add(
        IncomingStockItem(
          id: isi.id,
          expectedQuantity: isi.expectedQuantity,
          skuQuantity: skuQuantity,
        ),
      );
    }
    consignments.add(
      Consignment(
        consignmentId: con.consignmentId,
        id: con.incomingStockId,
        incomingStocks: incomingStockItems,
        receivedFromMandi: con.receivedFromMandi ?? -1,
        tripDetails: con.tripDetails
            .map((e) => TripDetails(
                driverName: e.driverName,
                driverPhone: e.driverPhone,
                vehicleNo: e.vehicleNo,
                expectedDeliveryTime: e.expectedDeliveryTime))
            .toList(),
      ),
    );
  }
  return consignments;
}
