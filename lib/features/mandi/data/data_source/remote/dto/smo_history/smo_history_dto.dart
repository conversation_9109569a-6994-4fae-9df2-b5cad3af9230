import 'package:freezed_annotation/freezed_annotation.dart';

part 'smo_history_dto.freezed.dart';
part 'smo_history_dto.g.dart';

@freezed
class SmoHistoryDto with _$SmoHistoryDto {
  const factory SmoHistoryDto({
    required int id,
    required int date,
    required String status,
    required int mandiId,
  }) = _SmoHistoryDto;

  @override
  factory SmoHistoryDto.fromJson(Map<String, dynamic> json) =>
      _$SmoHistoryDtoFromJson(json);
}
