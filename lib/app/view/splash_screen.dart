import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:proc2/app/request/get_true_date_time_request.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/mandatory_permission/mandatory_permission_manager.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/file_upload/s3_uploader.dart';
import 'package:proc2/core/utils/location_repository_utils.dart';
import 'package:proc2/core/utils/true_date_time.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});
  @override
  State<SplashScreen> createState() => _SplashScreen();
}

class _SplashScreen extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      S3Uploader.initialize(appName: "wow-Flow-app", entityMap: {}, host: '');
      unawaited(initializeTrueDateTime());
    });
  }

  Future<void> performInitialChecks({required bool isLoggedIn}) async {
    await handleAppUpdate();
    await checkForPermission(isLoggedIn: isLoggedIn);
  }

  Future<void> handleAppUpdate() async {
    try {
      if (kIsWeb) return;

      final AppUpdateInfo info = await InAppUpdate.checkForUpdate();

      if (!mounted) return;

      if (Platform.isAndroid &&
          info.updateAvailability == UpdateAvailability.updateAvailable) {
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              titlePadding: const EdgeInsets.only(top: 20),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
              title: Column(
                children: [
                  const Icon(Icons.system_update,
                      size: 48, color: Colors.orange),
                  const SizedBox(height: 10),
                  Text(
                    "appUpdateMsg".tr("App Update Available"),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              content: Text(
                "You need to update the app to continue using it smoothly.",
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[700]),
              ),
              actionsAlignment: MainAxisAlignment.spaceBetween,
              actionsPadding: const EdgeInsets.fromLTRB(20, 0, 20, 10),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    "later".tr("Later"),
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.of(context).pop();
                    try {
                      await InAppUpdate.performImmediateUpdate();
                    } catch (e) {
                      print("Update failed: $e");
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    "update".tr("Update"),
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      print("Update check failed: $e");
    }
  }

  Future<bool> performAppMandatorChecks() async {
    final shouldProceed = await MandatoryPermissionManager.check(context);
    if (shouldProceed) {
      await initializeLocationRepository();
      await startLocationChangeListener();
    }
    return shouldProceed;
  }

  Future<bool> initializeTrueDateTime() async {
    // Initialize TrueDateTime with the backend true time...
    try {
      TrueDateTime.initializeTrueTime(
        getTrueEpochSeconds: () async {
          final result = await GetTrueDateTimeRequest().execute();
          print("trueTimeData: $result and data: ${result.fold(
            (l) => 0,
            (r) => r,
          )}");
          return result.fold(
            (l) => 0,
            (r) => r,
          );
        },
      );
      return true;
    } catch (e) {
      print('Error in initializing TrueDateTime: $e');
      return false;
    }
  }

  Future<void> checkForPermission({
    required bool isLoggedIn,
    bool force = true,
  }) async {
    final isGranted = kIsWeb || await performAppMandatorChecks();
    if (!isGranted) {
      if (force) {
        return checkForPermission(isLoggedIn: isLoggedIn, force: true);
      }
    }
    if (isLoggedIn) {
      context
        ..go('/')
        ..fetchGlobal();
    } else {
      context.go('/login');
    }
  }

  @override
  Widget build(BuildContext buildContext) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        state.maybeWhen(
          orElse: () {},
          authenticated: (user) async {
            await performInitialChecks(isLoggedIn: true);
          },
          unauthenticated: (error) async {
            await performInitialChecks(isLoggedIn: false);
            context.go('/login');
          },
        );
      },
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            backgroundColor: Colors.white,
            body: Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.all(10),
              decoration: const BoxDecoration(color: Colors.white),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image(
                    width: MediaQuery.of(context).size.width * 0.8,
                    image: AssetImage(Config.logoImagePath),
                  ),
                  Text(
                    'WHEELOCITY',
                    style: TextStyle(
                      color: Config.primaryColor,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
