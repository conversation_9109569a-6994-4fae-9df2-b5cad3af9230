import 'package:injectable/injectable.dart';
import 'package:proc2/bootstrap.dart';
import 'package:proc2/core/di/di.dart';
import 'package:talker_flutter/talker_flutter.dart';

@module
abstract class AppConfigModule {
  @injectable
  AppConfig config(
    @Named('appTitle') String appTitle,
    @Named('isDev') bool isDev,
    @Named('talker') Talker talker,
  ) =>
      AppConfig(appTitle, isDev, talker);

  @prod
  @Named('appTitle')
  String get appTitleProd => 'Flow';

  @dev
  @Named('appTitle')
  String get appTitleDev => 'Flow Dev';

  @prod
  @Named('isDev')
  bool get isDev => false;

  @dev
  @Named('isDev')
  bool get isDev2 => true;

  @prod
  @Named('talker')
  @singleton
  Talker get talkerProd => TalkerFlutter.init(
        settings: TalkerSettings(
          useConsoleLogs: false,
        ),
        observer: CrashlyticsTalkerObserver(),
      );

  @dev
  @Named('talker')
  @singleton
  Talker get talkerDev => TalkerFlutter.init();
}

class AppConfig {
  final String appTitle;
  final bool isDev;
  final Talker talker;

  AppConfig(this.appTitle, this.isDev, this.talker);
}

Talker get talker => di.get<AppConfig>().talker;
