import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/lotting/lotting_deviation.dart';
import 'package:proc2/features/mandi/domain/use_case/submit_lotting_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/input_model/inventory_item_input_model.dart';

import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

import 'package:proc2/core/domain/entity/error_result.dart';

part 'lotting_conversion_event.dart';
part 'lotting_conversion_state.dart';
part 'lotting_conversion_bloc.freezed.dart';

@injectable
class LottingConversionBloc
    extends Bloc<LottingConversionEvent, LottingConversionState> {
  LottingConversionBloc(this._submitLottingUseCase, this._mandiRepository)
      : super(const LottingConversionState.loading()) {
    on<LottingConversionEvent>(
      (event, emit) async {
        await event.map(
          started: (s) async {
            final result = await _mandiRepository.getLottingDeviation();
            result.fold(
              (left) => emit(
                LottingConversionState.error(
                  smoId: s.smoId,
                  isLotting: s.isLotting,
                  error: left,
                ),
              ),
              (right) => emit(
                LottingConversionState.input(
                  deviation: right,
                  isLotting: s.isLotting,
                  smoId: s.smoId,
                ),
              ),
            );
          },
          addFrom: (e) {
            final currentState = state;
            if (currentState is _Input) {
              emit(
                currentState.copyWith(
                  from: e.clearPrevious
                      ? e.items
                      : [...currentState.from, ...e.items],
                  to: [
                    e.items[0].copyWith(lotSizeInput: '', quantityInput: '')
                  ],
                  unit: e.items[0].unit,
                  isSubmitActive: false,
                ),
              );
            }
          },
          addTo: (e) {
            final currentState = state;
            if (currentState is _Input) {
              emit(
                currentState.copyWith(
                  to: e.clearPrevious
                      ? e.items
                      : [...currentState.to, ...e.items],
                  isSubmitActive: false,
                ),
              );
            }
          },
          removeFrom: (_RemoveFrom value) {
            final currentState = state;
            if (currentState is _Input) {
              final items =
                  List<InventoryItemInputModel>.from(currentState.from)
                    ..removeAt(value.index);
              emit(
                _calculateLoss(
                  currentState.copyWith(
                    from: items,
                    to: items.isEmpty ? [] : currentState.to,
                  ),
                ),
              );
            }
          },
          removeTo: (_RemoveTo value) {
            final currentState = state;
            if (currentState is _Input) {
              final items = List<InventoryItemInputModel>.from(currentState.to)
                ..removeAt(value.index);
              emit(
                _calculateLoss(
                  currentState.copyWith(
                    to: items,
                  ),
                ),
              );
            }
          },
          addLot: (value) {
            final currentState = state;
            if (currentState is _Input) {
              emit(
                currentState.copyWith(
                  to: [
                    ...currentState.to,
                    currentState.to[0]
                        .copyWith(quantityInput: '', lotSizeInput: '')
                  ],
                  isSubmitActive: false,
                ),
              );
            }
          },
          updateFromQuantity: (_UpdateFromQuantity value) {
            final currentState = state;
            if (currentState is _Input) {
              final items =
                  List<InventoryItemInputModel>.from(currentState.from)
                    ..[value.index] = currentState.from[value.index]
                        .copyWith(quantityInput: value.value);
              emit(
                _calculateLoss(
                  currentState.copyWith(
                    from: items,
                  ),
                ),
              );
            }
          },
          updateToQuantityAndLotSize: (_UpdateToQuantity value) {
            final currentState = state;
            if (currentState is _Input) {
              final items = List<InventoryItemInputModel>.from(currentState.to)
                ..[value.index] = currentState.to[value.index].copyWith(
                    quantityInput: value.quantity, lotSizeInput: value.lotSize);
              emit(_calculateLoss(currentState.copyWith(to: items)));
            }
          },
          updateLoss: (value) {
            final currentState = state;
            if (currentState is _Input) {
              emit(
                _calculateSubmitButtonState(
                  currentState.copyWith(
                    losses: value.losses,
                  ),
                ),
              );
            }
          },
          submit: (submit) async {
            final currentState = state;
            if (currentState is _Input) {
              if (currentState.isSubmitLoading) return;
              emit(currentState.copyWith(isSubmitLoading: true));
              final result = await _submitLottingUseCase(
                smoId: currentState.smoId,
                from: currentState.from,
                to: currentState.to,
                losses: currentState.losses,
                isLotting: currentState.isLotting,
              );
              result.fold(
                (left) => emit(
                  currentState.copyWith(
                    isSubmitLoading: false,
                    message: left.message,
                  ),
                ),
                (right) => emit(
                  currentState.copyWith(
                    shouldGoBack: true,
                    isSubmitLoading: false,
                    message: right,
                  ),
                ),
              );
            }
          },
          clearMessage: (_) {
            final currentState = state;
            if (currentState is _Input) {
              emit(currentState.copyWith(message: null, shouldGoBack: false));
            }
          },
        );
      },
    );
  }

  final SubmitLottingUseCase _submitLottingUseCase;
  final MandiRepository _mandiRepository;

  LottingConversionState _calculateSubmitButtonState(
    _Input newState,
  ) {
    return newState.copyWith(isSubmitActive: _isSubmitActive(newState));
  }

  bool _isSubmitActive(_Input newState) {
    if (newState.from.isEmpty || newState.to.isEmpty) return false;
    final allFromFilled = newState.from.fold(
      true,
      (value, element) =>
          value &&
          element.hasFilledRequiredInputs(
            newState.isLotting,
            true,
          ),
    );
    if (!allFromFilled) return false;

    final allToFilled = newState.to.fold(
        true,
        (value, element) =>
            value &&
            element.hasFilledRequiredInputs(
              newState.isLotting,
              false,
            ));
    if (!allToFilled) return false;

    if (newState.isLotting) {
      final sumOfAllFromQuantity = newState.from.fold(
          0.0,
          (previousValue, element) =>
              previousValue + element.quantityInput.toDouble());
      final sumOfAllToQUantity = newState.to.fold(
          0.0,
          (previousValue, element) =>
              0 +
              element.lotSizeInput.toDouble() *
                  element.quantityInput.toDouble());
      return sumOfAllToQUantity <=
          (sumOfAllFromQuantity * (1 + newState.deviation.lottingDeviation));
    } else {
      final sumOfAllFromQty = newState.from.fold(
          0.0,
          (previousValue, element) =>
              previousValue +
              element.lotSize * element.quantityInput.toDouble());
      final sumOfAllToQty = newState.to.fold(
          0.0,
          (previousValue, element) =>
              previousValue + element.quantityInput.toDouble());

      return sumOfAllToQty <=
          sumOfAllFromQty * (1 + newState.deviation.deLottingDeviation);
    }
  }

  LottingConversionState _calculateLoss(_Input newState) {
    final quantityFrom = newState.from.fold<double>(
      0,
      (previousValue, element) =>
          previousValue + element.finalQuantity(newState.isLotting, true),
    );
    final quantityTo = newState.to.fold<double>(
      0,
      (previousValue, element) =>
          previousValue + element.finalQuantity(newState.isLotting, false),
    );
    return _calculateSubmitButtonState(
      newState.copyWith(
        calculatedLoss: quantityFrom - quantityTo,
        losses: [],
      ),
    );
  }

  bool shouldShowLossDialog() {
    final currentState = state;
    if (currentState is _Input) {
      final markedLoss = currentState.losses.fold<double>(
        0,
        (previousValue, element) =>
            previousValue + element.lossValue.toDouble(),
      );
      return markedLoss < currentState.calculatedLoss;
    }
    return false;
  }
}
