import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/extensions.dart';

class WMandiStatusCard extends StatelessWidget {
  WMandiStatusCard({
    super.key,
    required this.status,
    required this.mandiName,
    required this.startTime,
    this.closedAt = null,
    this.bottomCtaLabel = null,
    Color? cardBackgroundColor,
    Color? statusBackgroundColor,
    Color? statusTextColor,
    Color? bottomCtaBackgroundColor,
    Color? bottomCtaTextColor,
    this.elevation = 4,
    this.onTap,
  })  : _cardBackgroundColor = cardBackgroundColor ?? Color(0xFFF9FDFA),
        _statusBackgroundColor = statusBackgroundColor ?? Color(0x89dbefdc),
        _statusTextColor = statusTextColor ?? Colors.black,
        _bottomCtaBackgroundColor =
            bottomCtaBackgroundColor ?? Color(0xFFf1f2f0),
        _bottomCtaTextColor = bottomCtaTextColor ?? Colors.black;
  final String status;
  final String mandiName;
  final int startTime;
  final int? closedAt;
  final String? bottomCtaLabel;
  final Color? _bottomCtaBackgroundColor;
  final Color? _bottomCtaTextColor;
  final Color? _cardBackgroundColor;
  final Color? _statusBackgroundColor;
  final Color? _statusTextColor;
  final GestureTapCallback? onTap;
  final double elevation;
  static final String _dateFormat = 'dd MMM, yyyy | hh:mm a';
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      splashColor: Colors.green,
      child: Card(
        elevation: elevation,
        shadowColor: Colors.black,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: _cardBackgroundColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
                top: 8,
                bottom: 8,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: _statusBackgroundColor,
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                        left: 4,
                        right: 4,
                        top: 2,
                        bottom: 2,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            status,
                            style: TextStyle(
                              color: _statusTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  Text(
                    mandiName,
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w800,
                      fontSize: 20,
                    ),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  infoRowText(
                    Icons.start,
                    getLangText('startedAt', 'Started at'),
                    startTime.toDate(_dateFormat),
                  ),
                  if (closedAt != null) ...[
                    SizedBox(
                      height: 4,
                    ),
                    infoRowText(
                      Icons.stop,
                      getLangText('closedAt', 'Closed at'),
                      closedAt!.toDate(_dateFormat),
                    ),
                  ]
                ],
              ),
            ),
            if (bottomCtaLabel != null)
              Container(
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                  color: _bottomCtaBackgroundColor,
                ),
                child: Padding(
                  padding: const EdgeInsets.only(top: 4, bottom: 4, right: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        bottomCtaLabel!,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(
                        width: 4,
                      ),
                      Icon(
                        Icons.navigate_next,
                        color: _bottomCtaTextColor,
                        size: 16,
                      )
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

Widget infoRowText(
  IconData icon,
  String infoTitle,
  String infoValue,
) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    mainAxisSize: MainAxisSize.min,
    children: [
      Icon(
        icon,
        color: Colors.black87,
        size: 16,
      ),
      SizedBox(
        width: 4,
      ),
      Text(
        infoTitle,
        style: TextStyle(color: Colors.black),
      ),
      SizedBox(
        width: 4,
      ),
      Text(
        infoValue,
        style: TextStyle(
          color: Colors.black,
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    ],
  );
}
