import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';

final GlobalKey<ScaffoldMessengerState> rootScaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

void showSnackBar(String msg,
    {Duration duration = const Duration(seconds: 2)}) {
  rootScaffoldMessengerKey.currentState?.hideCurrentSnackBar();
  rootScaffoldMessengerKey.currentState?.showSnackBar(
    SnackBar(
      content: Text(msg),
      duration: duration,
      showCloseIcon: true,
      closeIconColor: Colors.white,
      behavior: SnackBarBehavior.floating,
    ),
  );
}

void showRetrySnackBar(String msg, void Function() onClick) {
  rootScaffoldMessengerKey.currentState?.hideCurrentSnackBar();
  rootScaffoldMessengerKey.currentState?.showSnackBar(
    SnackBar(
      content: Text(
        msg,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: Colors.red,
      action: SnackBarAction(
        label: 'retry'.tr('Retry'),
        textColor: Colors.white,
        onPressed: onClick,
      ),
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 10),
    ),
  );
}

void showReportBugSnackBar(void Function() onClick) {
  rootScaffoldMessengerKey.currentState?.hideCurrentSnackBar();
  rootScaffoldMessengerKey.currentState?.showSnackBar(
    SnackBar(
      content: Text(
        'reportBugMessage'.tr('Found a bug? Report it!'),
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: Colors.red,
      action: SnackBarAction(
        label: 'reportNow'.tr('Report Now'),
        backgroundColor: Colors.white,
        textColor: Colors.red,
        onPressed: onClick,
      ),
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 10),
    ),
  );
}
