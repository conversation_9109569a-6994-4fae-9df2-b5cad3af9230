import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';

class GetAllVendorsRequest extends BaseRequest<dynamic, List<Vendor>> {
  @override
  String getPath() => 'vendor/all';

  @override
  List<Vendor> mapper(data) {
    if (data is List<dynamic>) {
      return data
          .map((e) => Vendor.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
