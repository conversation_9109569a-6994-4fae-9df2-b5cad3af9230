import 'package:freezed_annotation/freezed_annotation.dart';

part 'inventory_item_dto.freezed.dart';
part 'inventory_item_dto.g.dart';

@freezed
class InventoryItemDto with _$InventoryItemDto {
  const factory InventoryItemDto({
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double quantity,
  }) = _InventoryItemDto;

  @override
  factory InventoryItemDto.fromJson(Map<String, dynamic> json) =>
      _$InventoryItemDtoFromJson(json);
}
