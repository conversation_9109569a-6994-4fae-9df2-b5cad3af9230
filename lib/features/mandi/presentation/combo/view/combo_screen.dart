import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/inventory_type.dart';

class ComboScreen extends StatelessWidget {
  final int mandiId;
  final int smoId;
  final ConversionConfig config;
  const ComboScreen({
    super.key,
    required this.mandiId,
    required this.smoId,
    required this.config,
  });

  void _openComboConversion(BuildContext context, InventoryType type) {
    context.push(
      context.namedLocation(
        'comboConversion',
        pathParameters: {
          'mandiId': mandiId.toString(),
          'smoId': smoId.toString(),
        },
      ),
      extra: {
        "type": type,
        "config": type == InventoryType.primary
            ? config.mandiInventory
            : type == InventoryType.returns
                ? config.returnsInventory
                : config.wastageReturnsInventory,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.grey.shade100,
        appBar: WAppBar.getAppBar(
          title: LangText('grading.title', 'Grading'),
          centerTitle: false,
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LangText(
                'grading.selectSourceInventory',
                'Select Source Inventory',
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    fontStyle: FontStyle.italic),
              ),
              SizedBox(
                height: 16,
              ),
              // Two list tiles one for Mandi and other for Return Invenory
              // Mandi
              Visibility(
                visible: config.mandiInventory.enabled,
                child: ListTile(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  tileColor: Colors.white,
                  title: LangText(
                    'grading.mandiInventory',
                    'Mandi Inventory',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  trailing: Icon(Icons.arrow_forward_ios_rounded),
                  onTap: () {
                    _openComboConversion(context, InventoryType.primary);
                  },
                ),
              ),
              SizedBox(
                height: 4,
              ),
              // Return Inventory
              Visibility(
                visible: config.returnsInventory.enabled,
                child: ListTile(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  tileColor: Colors.white,
                  title: LangText(
                    'grading.returnInventory',
                    'Return Inventory',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  trailing: Icon(Icons.arrow_forward_ios_rounded),
                  onTap: () {
                    _openComboConversion(context, InventoryType.returns);
                  },
                ),
              ),
              SizedBox(
                height: 4,
              ),
              // Grade C Inventory
              Visibility(
                visible: config.wastageReturnsInventory.enabled,
                child: ListTile(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  tileColor: Colors.white,
                  title: LangText(
                    'grading.wastageReturnsInventory',
                    'Wastage Returns Inventory',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  trailing: Icon(Icons.arrow_forward_ios_rounded),
                  onTap: () {
                    _openComboConversion(context, InventoryType.gradeC);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
