import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/allocation_list_column_heading.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/sliver_child_allocation_list.dart';
import 'package:sliver_tools/sliver_tools.dart';

class SliverAllocationList extends StatefulWidget {
  const SliverAllocationList({
    super.key,
    required this.state,
    this.isUpdateUi = true,
    required this.isSelectionActive,
    required this.selectedItems,
    required this.onSelectedItemChange,
    required this.isEditingAllowed,
  });
  final AllocationJourneyState state;
  final bool isUpdateUi;
  final bool isSelectionActive;
  final List<String> selectedItems;
  final Function(String) onSelectedItemChange;
  final bool isEditingAllowed;

  @override
  State<SliverAllocationList> createState() => _SliverAllocationListState();
}

class _SliverAllocationListState extends State<SliverAllocationList> {
  bool bothListVisible = true;
  bool partialListVisible = true;
  bool supplyListVisible = true;
  bool mandiListVisible = true;
  bool inSupplyInMandiNotAllocated = false;
  List<AllocationSkuItem> itemsInSupplyInMandiNotAllocated =
      <AllocationSkuItem>[];

  @override
  void initState() {
    _updateVisibility();
    super.initState();
  }

  void _updateVisibility() {
    final filterData = widget.isUpdateUi
        ? widget.state.allocationFilterData
        : widget.state.viewFilterData;
    bothListVisible = _checkIfVisible(
      filterItems(widget.state.items, widget.isUpdateUi),
      filterData,
    );
    partialListVisible = _checkIfVisible(
      filterItems(widget.state.itemsPartial, widget.isUpdateUi),
      filterData,
    );
    supplyListVisible = _checkIfVisible(
      widget.state.itemsInSupplyOrder,
      filterData,
    );
    mandiListVisible = _checkIfVisible(
      filterItems(widget.state.itemsInMandi, widget.isUpdateUi),
      filterData,
    );
    itemsInSupplyInMandiNotAllocated = !widget.state.isCustomerGroup
        ? []
        : [...widget.state.items, ...widget.state.itemsPartial]
            .where((e) =>
                !e.isValidId || e.backendAllocationQty.totalQty.toDouble() == 0)
            .toList();
    inSupplyInMandiNotAllocated = _checkIfVisible(
      itemsInSupplyInMandiNotAllocated,
      filterData,
    );
  }

  List<AllocationSkuItem> filterItems(
      List<AllocationSkuItem> items, bool isUpdateUi) {
    return isUpdateUi ? items : items.where((e) => e.isValidId).toList();
  }

  bool _checkIfVisible(List<AllocationSkuItem> items, FilterData filterData) {
    return items.isNotEmpty && items.any((e) => filterData.shouldShow(e));
  }

  @override
  void didUpdateWidget(covariant SliverAllocationList oldWidget) {
    final isFilterChanged = widget.isUpdateUi
        ? widget.state.allocationFilterData !=
            oldWidget.state.allocationFilterData
        : widget.state.viewFilterData != oldWidget.state.viewFilterData;
    final itemsChanged = widget.state.items != oldWidget.state.items ||
        widget.state.itemsPartial != oldWidget.state.itemsPartial ||
        widget.state.itemsInMandi != oldWidget.state.itemsInMandi ||
        widget.state.itemsInSupplyOrder != oldWidget.state.itemsInSupplyOrder;

    if (isFilterChanged || itemsChanged) {
      _updateVisibility();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final bool allowExpectation = widget.state.allocationConfig?.allocation
            .updateAllocation.allowExpectation ??
        false;
    return SliverStickyHeader(
      header: Container(
        color: Colors.white,
        child: AllocationListColumnHeading(
          state: widget.state,
          isUpdateUi: widget.isUpdateUi,
        ),
      ),
      sliver: !bothListVisible &&
              !partialListVisible &&
              !supplyListVisible &&
              !mandiListVisible &&
              !inSupplyInMandiNotAllocated
          ? SliverFillRemaining(
              child: Center(
                child: Text(
                  'No data found!',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            )
          : MultiSliver(
              children: [
                Visibility(
                  visible: (inSupplyInMandiNotAllocated &&
                      widget.state.isCustomerGroup),
                  child: SliverChildAllocationList(
                    heading:
                        'Sku in supply order, in mandi inventory but not allocated.',
                    headingColor: Colors.green.shade800,
                    items: itemsInSupplyInMandiNotAllocated,
                    onQtyChanged: (index, qty) {},
                    forceShow: true,
                    isUpdateUi: widget.isUpdateUi,
                    isCustomerOrder: widget.state.isCustomerGroup,
                    filterData: widget.isUpdateUi
                        ? widget.state.allocationFilterData
                        : widget.state.viewFilterData,
                    selectedItems: widget.selectedItems,
                    isSelectionActive: widget.isSelectionActive,
                    onSelectedItemChange: widget.onSelectedItemChange,
                    isEditingAllowed: widget.isEditingAllowed,
                    allowExpectation: allowExpectation,
                    onAutoAllocateClick: !widget.state.isEditingAllowed
                        ? null
                        : () {
                            context
                                .read<AllocationJourneyCubit>()
                                .autoAllocateRemainingSku();
                          },
                    configuration: widget.state.allocationConfig,
                  ),
                ),
                Visibility(
                  visible: bothListVisible,
                  child: SliverChildAllocationList(
                    items: !widget.state.isCustomerGroup
                        ? widget.state.items
                        : widget.state.items
                            .where((e) =>
                                e.backendAllocationQty.totalQty.toDouble() > 0)
                            .toList(),
                    heading: widget.state.isCustomerGroup
                        ? 'Sku in supply order and available in mandi inventory'
                        : null,
                    headingColor: Colors.blue,
                    onQtyChanged: (index, qty) {
                      context.read<AllocationJourneyCubit>().updateQtyBoth(
                            index: index,
                            qty: qty,
                          );
                    },
                    isUpdateUi: widget.isUpdateUi,
                    isCustomerOrder: widget.state.isCustomerGroup,
                    filterData: widget.isUpdateUi
                        ? widget.state.allocationFilterData
                        : widget.state.viewFilterData,
                    selectedItems: widget.selectedItems,
                    isSelectionActive: widget.isSelectionActive,
                    onSelectedItemChange: widget.onSelectedItemChange,
                    isEditingAllowed: widget.isEditingAllowed,
                    allowExpectation: allowExpectation,
                    configuration: widget.state.allocationConfig,
                  ),
                ),
                Visibility(
                  visible: partialListVisible,
                  child: SliverChildAllocationList(
                    items: widget.state.itemsPartial
                        .where((e) =>
                            e.backendAllocationQty.totalQty.toDouble() > 0)
                        .toList(),
                    heading: widget.state.isCustomerGroup
                        ? 'Sku in supply order and partially available in mandi inventory'
                        : null,
                    headingColor: Colors.indigo,
                    onQtyChanged: (index, qty) {
                      context.read<AllocationJourneyCubit>().updateQtyPartial(
                            index: index,
                            qty: qty,
                          );
                    },
                    isUpdateUi: widget.isUpdateUi,
                    isCustomerOrder: widget.state.isCustomerGroup,
                    filterData: widget.isUpdateUi
                        ? widget.state.allocationFilterData
                        : widget.state.viewFilterData,
                    selectedItems: widget.selectedItems,
                    isSelectionActive: widget.isSelectionActive,
                    onSelectedItemChange: widget.onSelectedItemChange,
                    isEditingAllowed: widget.isEditingAllowed,
                    allowExpectation: allowExpectation,
                    configuration: widget.state.allocationConfig,
                  ),
                ),
                Visibility(
                  visible: mandiListVisible && widget.isUpdateUi,
                  child: SliverChildAllocationList(
                    heading: widget.state.isEditingAllowed
                        ? 'Sku in mandi inventory but not in supply order.'
                        : 'Allocated Items',
                    headingColor: widget.state.isEditingAllowed
                        ? Colors.pink.shade300
                        : Colors.green.shade300,
                    items: widget.state.itemsInMandi
                        .where((e) => !e.isValidId)
                        .toList(),
                    onQtyChanged: (index, qty) {
                      context.read<AllocationJourneyCubit>().updateQtyMandi(
                            index: index,
                            qty: qty,
                            isValidIdOnly: false,
                          );
                    },
                    isUpdateUi: widget.isUpdateUi,
                    isCustomerOrder: widget.state.isCustomerGroup,
                    filterData: widget.isUpdateUi
                        ? widget.state.allocationFilterData
                        : widget.state.viewFilterData,
                    selectedItems: widget.selectedItems,
                    isSelectionActive: widget.isSelectionActive,
                    onSelectedItemChange: widget.onSelectedItemChange,
                    isEditingAllowed: widget.isEditingAllowed,
                    allowExpectation: allowExpectation,
                    configuration: widget.state.allocationConfig,
                  ),
                ),
                Visibility(
                  visible: mandiListVisible,
                  child: SliverChildAllocationList(
                    heading: widget.state.isEditingAllowed
                        ? 'Sku not in supply order, but allocated.'
                        : 'Allocated Items',
                    headingColor: widget.state.isEditingAllowed
                        ? Colors.pink.shade300
                        : Colors.green.shade300,
                    items: widget.isUpdateUi
                        ? widget.state.itemsInMandi
                            .where((e) => e.isValidId)
                            .toList()
                        : widget.state.itemsInMandi,
                    onQtyChanged: (index, qty) {
                      context.read<AllocationJourneyCubit>().updateQtyMandi(
                            index: index,
                            qty: qty,
                            isValidIdOnly: true,
                          );
                    },
                    isUpdateUi: widget.isUpdateUi,
                    isCustomerOrder: widget.state.isCustomerGroup,
                    filterData: widget.isUpdateUi
                        ? widget.state.allocationFilterData
                        : widget.state.viewFilterData,
                    selectedItems: widget.selectedItems,
                    isSelectionActive: widget.isSelectionActive,
                    onSelectedItemChange: widget.onSelectedItemChange,
                    isEditingAllowed: widget.isEditingAllowed,
                    allowExpectation: allowExpectation,
                    configuration: widget.state.allocationConfig,
                  ),
                ),
                Visibility(
                  visible: supplyListVisible,
                  child: SliverChildAllocationList(
                    heading: 'Sku in supply order, not in mandi inventory',
                    headingColor: Colors.orange,
                    items: widget.state.itemsInSupplyOrder,
                    onQtyChanged: (index, qty) {
                      context.read<AllocationJourneyCubit>().updateQtySupply(
                            index: index,
                            qty: qty,
                          );
                    },
                    forceShow: true,
                    isUpdateUi: widget.isUpdateUi,
                    isCustomerOrder: widget.state.isCustomerGroup,
                    filterData: widget.isUpdateUi
                        ? widget.state.allocationFilterData
                        : widget.state.viewFilterData,
                    selectedItems: widget.selectedItems,
                    isSelectionActive: widget.isSelectionActive,
                    onSelectedItemChange: widget.onSelectedItemChange,
                    isEditingAllowed: widget.isEditingAllowed,
                    allowExpectation: allowExpectation,
                    configuration: widget.state.allocationConfig,
                  ),
                ),
              ],
            ),
    );
  }
}
