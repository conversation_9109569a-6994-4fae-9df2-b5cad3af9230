import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class WTextForm<PERSON>ield extends StatefulWidget {
  const WTextFormField({
    super.key,
    this.initialValue,
    this.focusNode,
    this.decoration,
    this.keyboardType,
    this.inputFormatters,
    this.onChanged,
    this.textInputAction,
    this.enabled,
    this.maxLength,
    this.maxLines,
    this.minLines,
    this.textAlign,
    this.style,
    this.readOnly,
  });
  final String? initialValue;
  final FocusNode? focusNode;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String)? onChanged;
  final TextInputAction? textInputAction;
  final bool? enabled;
  final int? maxLength;
  final int? maxLines;
  final int? minLines;
  final TextAlign? textAlign;
  final TextStyle? style;
  final bool? readOnly;

  @override
  State<WTextFormField> createState() => _WTextFormFieldState();
}

class _WTextFormFieldState extends State<WTextFormField> {
  TextEditingController controller = TextEditingController();

  @override
  void initState() {
    if (widget.initialValue != null) {
      controller.text = widget.initialValue!;
    }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant WTextFormField oldWidget) {
    if (oldWidget.initialValue != widget.initialValue) {
      final cursorPos = controller.selection;
      controller.text = widget.initialValue!;
      if (cursorPos.start > controller.text.length) {
        controller.selection = TextSelection.fromPosition(
            TextPosition(offset: controller.text.length));
      } else {
        controller.selection = cursorPos;
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      focusNode: widget.focusNode,
      decoration: widget.decoration,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      onChanged: widget.onChanged,
      textInputAction: widget.textInputAction,
      enabled: widget.enabled,
      maxLength: widget.maxLength,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      textAlign: widget.textAlign ?? TextAlign.start,
      style: widget.style,
      readOnly: widget.readOnly ?? false,
    );
  }
}
