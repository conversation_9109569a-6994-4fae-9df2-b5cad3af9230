import 'package:either_dart/src/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/data/network/extensions/chopper_either_extension.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/features/auth/data/data_source/auth_datasource.dart';
import 'package:proc2/features/auth/data/data_source/remote/dto/login_dto.dart';
import 'package:proc2/features/auth/data/data_source/remote/service/auth_service.dart';

@Injectable(as: AuthDataSource)
class RemoteAuthDataSource implements AuthDataSource {
  RemoteAuthDataSource(this._authService);
  final AuthService _authService;

  @override
  Future<Either<ErrorResult<dynamic>, LoginDto>> login({
    required String idToken,
  }) {
    return _authService.safeCall(
      apiCall: () => _authService.login(idToken),
      transform: (data) {
        if (data is Map<String, dynamic>) {
          return LoginDto.fromJson(data);
        }
        throw Exception(LanguageEnum.jsonParseError.localized());
      },
      errorTransform: (data) => null,
    );
  }

  @override
  Future<Either<ErrorResult, dynamic>> sendOtp(
      {required String mobile, bool isResend = false}) {
    return _authService.safeCall(
      apiCall: () => _authService.sendOtp(
        mobile,
        isResend ? 'resendOtp' : 'sendOtp',
      ),
      transform: (data) {
        if (data is Map<String, dynamic>) {
          return data;
        }
        throw Exception(LanguageEnum.jsonParseError.localized());
      },
      errorTransform: (data) => null,
    );
  }

  @override
  Future<Either<ErrorResult, LoginDto>> verifyOtp(
      {required int otp, required String mobile}) {
    return _authService.safeCall(
      apiCall: () => _authService.verifyOtp(
        otp,
        mobile,
      ),
      transform: (data) {
        if (data is Map<String, dynamic>) {
          return LoginDto.fromJson(data);
        }
        throw Exception(LanguageEnum.jsonParseError.localized());
      },
      errorTransform: (data) => null,
    );
  }

  @override
  Future<Either<ErrorResult, String>> getFirebaseCustomToken(
      {required String jwt}) {
    return _authService.safeCall(
      apiCall: () => _authService.getFirebaseCustomToken('Bearer $jwt'),
      transform: (data) {
        if (data is Map<String, dynamic>) {
          return data['token'] as String;
        }
        throw Exception(LanguageEnum.jsonParseError.localized());
      },
      errorTransform: (data) => null,
    );
  }
}
