import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:proc2/app/route/router.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/di/di.dart';
import 'package:shake/shake.dart';

class RestartWidget extends StatefulWidget {
  const RestartWidget({super.key, required this.child});

  final Widget child;

  static void restartApp(BuildContext context) {
    context.findAncestorStateOfType<_RestartWidgetState>()?.restartApp();
  }

  @override
  State<RestartWidget> createState() => _RestartWidgetState();
}

class _RestartWidgetState extends State<RestartWidget> {
  Key key = UniqueKey();
  ShakeDetector? detector;
  bool hasLogsOpened = false;

  @override
  void initState() {
    final AppConfig config = di.get();

    if (kDebugMode || config.isDev) {
      detector = ShakeDetector.waitForStart(onPhoneShake: () async {
        if (!hasLogsOpened) {
          hasLogsOpened = true;
          await router.push(router.namedLocation('appLogs'));
          hasLogsOpened = false;
        }
      });
      detector?.startListening();
    }
    super.initState();
  }

  @override
  void dispose() {
    detector?.stopListening();
    super.dispose();
  }

  void restartApp() {
    setState(() {
      key = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: key,
      child: widget.child,
    );
  }
}
