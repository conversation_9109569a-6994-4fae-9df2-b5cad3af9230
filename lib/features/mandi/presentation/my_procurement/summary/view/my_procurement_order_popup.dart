import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/view/add_procurement.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';

class VendorProcDetail {
  final String vendorName;
  final ProcDetailItem item;
  final ProcDetail detail;

  VendorProcDetail({
    required this.vendorName,
    required this.item,
    required this.detail,
  });
}

class MyProcurementOrderPopup extends StatelessWidget {
  MyProcurementOrderPopup({
    super.key,
    required this.smoId,
    required List<ProcDetail> procDetail,
    required this.mandiId,
    required this.skuName,
    required this.compositeKey,
    required this.unitText,
  }) {
    procDetail.forEach((e) {
      final item =
          e.items.where((element) => element.compositeKey == compositeKey);
      if (item.isNotEmpty) {
        vendorItems.add(VendorProcDetail(
          vendorName: di
              .get<VendorCubit>()
              .getVendorLocation(e.vendorLocationId, e.vendorName)
              .vendorName,
          item: item.first,
          detail: e,
        ));
      }
    });
  }
  final int mandiId;
  final int smoId;
  final String skuName;
  final String unitText;
  final String compositeKey;
  final List<VendorProcDetail> vendorItems = [];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            height: MediaQuery.of(context).size.height * 0.5,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 16,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          skuName,
                          style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              color: Colors.white),
                        ),
                        InkWell(
                          onTap: () => context.pop(),
                          child: Icon(
                            Icons.close,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 4,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: Text(
                    unitText,
                    style: TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
                  ),
                ),
                Divider(),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Vendor Name',
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 16),
                      ),
                      Text(
                        'Pending Qty',
                        style: TextStyle(
                            fontWeight: FontWeight.w500, fontSize: 16),
                        textAlign: TextAlign.end,
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                ),
                Expanded(
                  child: Scrollbar(
                      child: ListView.builder(
                    padding: EdgeInsets.symmetric(
                      vertical: 16,
                    ),
                    itemCount: vendorItems.length,
                    itemBuilder: ((context, index) {
                      final vendorItem = vendorItems[index];
                      return InkWell(
                        onTap: () async {
                          await Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (ctx) {
                                final mandiName =
                                    context.read<MandiBloc>().state.maybeMap(
                                          orElse: () => '',
                                          success: (s) => s.allMandis
                                              .firstWhere((element) =>
                                                  element.id == mandiId)
                                              .name,
                                        );
                                return BlocProvider(
                                  create: (_) => context.read<SmoBloc>(),
                                  child: AddProcurement(
                                    mandiId: mandiId,
                                    mandiName: mandiName,
                                    smoId: smoId,
                                    procDetail: vendorItem.detail,
                                  ),
                                );
                              },
                            ),
                          );
                          context.pop();
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: index % 2 == 0
                                ? Colors.grey.shade50
                                : Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.5),
                                spreadRadius: 1,
                                blurRadius: 2,
                                offset:
                                    Offset(0, 1), // changes position of shadow
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                vendorItem.vendorName,
                                style: TextStyle(
                                    fontWeight: FontWeight.w500, fontSize: 16),
                              ),
                              Text(
                                vendorItem.item.pendingQty.asString(),
                                style: TextStyle(
                                    fontWeight: FontWeight.w500, fontSize: 16),
                                textAlign: TextAlign.end,
                              ),
                            ],
                          ),
                          margin: EdgeInsets.only(
                            bottom: 16,
                            left: 8,
                            right: 8,
                          ),
                          padding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 8,
                          ),
                        ),
                      );
                    }),
                  )),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
