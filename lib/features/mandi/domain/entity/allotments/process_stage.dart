import 'package:flutter/material.dart';
import 'package:proc2/core/utils/extensions.dart';

class ProcessStage {
  final Color color;
  final String key;
  final String value;

  const ProcessStage._({
    required this.color,
    required this.key,
    required this.value,
  });

  static const IN_PROGRESS = ProcessStage._(
    color: Colors.blue,
    key: "IN_PROGRESS",
    value: "In Progress",
  );

  static const COMPLETED = ProcessStage._(
    color: Colors.green,
    key: "COMPLETED",
    value: "Ready to Dispatch",
  );

  static const DISPATCHED = ProcessStage._(
    color: Colors.orange,
    key: "DISPATCHED",
    value: "Dispatched",
  );

  static const CANCELLED = ProcessStage._(
    color: Colors.red,
    key: "CANCELLED",
    value: "Cancelled",
  );

  static const DELIVERY_MEMO_PENDING = ProcessStage._(
    color: Colors.yellow,
    key: "DELIVERY_MEMO_PENDING",
    value: "Delivery Memo Pending",
  );

  static const DELIVERY_MEMO_ALLOTMENT_COMPLETED = ProcessStage._(
    color: Colors.purple,
    key: "DELIVERY_MEMO_ALLOTMENT_COMPLETED",
    value: "Allotment Completed",
  );

  static const DELIVERY_MEMO_TRIPS_ASSIGNED = ProcessStage._(
    color: Colors.teal,
    key: "DELIVERY_MEMO_TRIPS_ASSIGNED",
    value: "Trips Assigned",
  );

  static const DELIVERY_MEMO_PDF_GENERATED = ProcessStage._(
    color: Colors.indigo,
    key: "DELIVERY_MEMO_PDF_GENERATED",
    value: "PDF Generated",
  );

  static const DELIVERY_MEMO_ERROR_ALLOTMENT = ProcessStage._(
    color: Colors.red,
    key: "DELIVERY_MEMO_ERROR_ALLOTMENT",
    value: "Error Allotment",
  );

  static const DELIVERY_MEMO_ERROR_TRIP_ASSIGNMENT = ProcessStage._(
    color: Colors.red,
    key: "DELIVERY_MEMO_ERROR_TRIP_ASSIGNMENT",
    value: "Error Trip Assignment",
  );

  static const DELIVERY_MEMO_ERROR_PDF_GENERATION = ProcessStage._(
    color: Colors.red,
    key: "DELIVERY_MEMO_ERROR_PDF_GENERATION",
    value: "Error PDF Generation",
  );

  static const RECEIVED = ProcessStage._(
    color: Colors.green,
    key: "RECEIVED",
    value: "Received",
  );

  static ProcessStage from(String key) {
    switch (key) {
      case "IN_PROGRESS":
        return IN_PROGRESS;
      case "COMPLETED":
        return COMPLETED;
      case "DISPATCHED":
        return DISPATCHED;
      case "CANCELLED":
        return CANCELLED;
      case "DELIVERY_MEMO_PENDING":
        return DELIVERY_MEMO_PENDING;
      case "DELIVERY_MEMO_ALLOTMENT_COMPLETED":
        return DELIVERY_MEMO_ALLOTMENT_COMPLETED;
      case "DELIVERY_MEMO_TRIPS_ASSIGNED":
        return DELIVERY_MEMO_TRIPS_ASSIGNED;
      case "DELIVERY_MEMO_PDF_GENERATED":
        return DELIVERY_MEMO_PDF_GENERATED;
      case "DELIVERY_MEMO_ERROR_ALLOTMENT":
        return DELIVERY_MEMO_ERROR_ALLOTMENT;
      case "DELIVERY_MEMO_ERROR_TRIP_ASSIGNMENT":
        return DELIVERY_MEMO_ERROR_TRIP_ASSIGNMENT;
      case "DELIVERY_MEMO_ERROR_PDF_GENERATION":
        return DELIVERY_MEMO_ERROR_PDF_GENERATION;
      case "RECEIVED":
        return RECEIVED;
      default:
        return ProcessStage._(
          color: Colors.grey,
          key: key,
          value: key.replaceAll('_', ' ').capitalize(),
        );
    }
  }
}
