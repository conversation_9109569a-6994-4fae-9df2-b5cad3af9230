import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocating/bloc/allocating_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocating/input_model/allocate_item_input_model.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class AllocateInventory extends StatefulWidget {
  const AllocateInventory({
    super.key,
    required this.allocateId,
    required this.mandiId,
    required this.destinationName,
    required this.canAllocateExcess,
    required this.isMandiAllocation,
    this.customerGroup,
    this.deliverySlot,
    this.deliveryDate,
    required this.skuMap,
  });
  final int allocateId;
  final int mandiId;
  final String destinationName;
  final bool canAllocateExcess;
  final bool isMandiAllocation;
  final String? customerGroup;
  final String? deliverySlot;
  final String? deliveryDate;
  final Map<int, Sku> skuMap;
  @override
  State<AllocateInventory> createState() => _AllocateInventory();
}

class _AllocateInventory extends State<AllocateInventory> {
  String selectedCopyDropDown = 'Mandi';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: appBar(),
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: BlocConsumer<AllocatingInventoryBloc, AllocatingInventoryState>(
          listener: (context, state) {
            state.maybeMap(
              orElse: () {},
              error: (error) {
                showSnackBar(error.error.message);
              },
              input: (input) {
                if (input.shouldPopBack) {
                  context.pop();
                }
                if (input.message != null) {
                  showSnackBar(input.message!);
                  // clear message
                  context.read<AllocatingInventoryBloc>().add(
                        const AllocatingInventoryEvent.clearMessage(),
                      );
                }
              },
            );
          },
          builder: (context, state) {
            return state.map(
              initial: (i) => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (e) => ErrorScreen(
                onPressed: () {
                  context.read<AllocatingInventoryBloc>().add(
                        AllocatingInventoryEvent.started(
                            mandiId: widget.mandiId,
                            allotmentId: widget.allocateId,
                            customerGroup: widget.customerGroup,
                            deliveryDate: widget.deliveryDate,
                            deliverySlot: widget.deliverySlot,
                            skuMap: widget.skuMap),
                      );
                },
                message: e.error.message,
              ),
              input: (inputState) {
                if (inputState.items.isEmpty &&
                    inputState.onlyMandiItems.isEmpty &&
                    inputState.onlySupplyItems.isEmpty) {
                  return EmptyScreen(
                    message: widget.isMandiAllocation
                        ? LanguageEnum.allocateInventoryAllocateEmptyMessage
                            .localized()
                        : getLangText(
                            'allocateInventory.customerAllocationEmpty',
                            'No Supply Orders Found!',
                          ),
                  );
                }
                return Column(
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.blue.shade100,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.blue.shade50,
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 16,
                      ),
                      child: Text(
                        widget.canAllocateExcess
                            ? getLangText('allocationInventory.excessMessage',
                                'Excess inventory allocation is allowed for this customer group')
                            : getLangText('allocationInventory.noExcessMessage',
                                'Allocation can be done upto the supply order quantity for this customer group'),
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.blue),
                      ),
                    ),
                    sameAsInventoryButton(),
                    Container(
                      color: Colors.grey[100],
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8.0,
                          vertical: 4.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 2,
                              child: LangText(
                                'unit',
                                'Unit',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Expanded(
                              flex: 2,
                              child: LangText(
                                'mandiQty',
                                'Mandi\nQty',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            if (!widget.isMandiAllocation) ...[
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'orderedQty',
                                  'Ordered\nQty',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                            ],
                            Expanded(
                              flex: 2,
                              child: LangText(
                                'allocatedQty',
                                'Allocated\nQty',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Scrollbar(
                        child: ListView(
                          children: [
                            for (int i = 0; i < inputState.items.length; i++)
                              skuCard(inputState.items[i], i,
                                  AllocateListType.allList),
                            if (inputState.onlySupplyItems.isNotEmpty)
                              Container(
                                padding: EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 16,
                                ),
                                margin: EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 8,
                                ),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Colors.red.shade900,
                                  border: Border.all(
                                    color: Colors.red.shade100,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Center(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Icon(
                                        Icons.warning,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                      SizedBox(
                                        width: 8,
                                      ),
                                      Text(
                                        'Inventory in Supply Order, not in Mandi',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            for (int i = 0;
                                i < inputState.onlySupplyItems.length;
                                i++)
                              skuCard(inputState.onlySupplyItems[i], i,
                                  AllocateListType.supplyList),
                            if (inputState.onlyMandiItems.isNotEmpty)
                              Container(
                                padding: EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 16,
                                ),
                                margin: EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 8,
                                ),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade900,
                                  border: Border.all(
                                    color: Colors.orange.shade100,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Center(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Icon(
                                        Icons.warning,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                      SizedBox(
                                        width: 8,
                                      ),
                                      Text(
                                        'Inventory in Mandi, not in Supply Orders',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            for (int i = 0;
                                i < inputState.onlyMandiItems.length;
                                i++)
                              skuCard(inputState.onlyMandiItems[i], i,
                                  AllocateListType.mandiList),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          Expanded(child: saveAsDraftButton(inputState)),
                          SizedBox(
                            width: 16,
                          ),
                          Expanded(child: dispatchButton(inputState)),
                        ],
                      ),
                    ),
                  ],
                );
              },
            );
            // state.map();
          },
        ),
      ),
    ));
  }

  Widget skuCard(
    List<AllocateItemInputModel> inputData,
    int index,
    AllocateListType listType,
  ) {
    return Card(
      elevation: 0,
      color: listType == AllocateListType.allList
          ? Colors.white
          : listType == AllocateListType.supplyList
              ? Colors.red.shade100
              : Colors.orange.shade100,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 4,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              getSKU(context, skuID: inputData.first.skuId).name,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            for (int i = 0; i < inputData.length; i++)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        inputData[i].getUnitString(),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        inputData[i].quantity > 0
                            ? inputData[i].quantity.asString()
                            : '-',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    if (!widget.isMandiAllocation) ...[
                      Expanded(
                        flex: 2,
                        child: Text(
                          inputData[i].supplyQuantity?.asString() ?? '-',
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(width: 4),
                    ],
                    Expanded(
                      flex: 2,
                      child: listType != AllocateListType.allList
                          ? SizedBox(
                              child: Text(
                                '-',
                                textAlign: TextAlign.center,
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: TextFormField(
                                  initialValue: inputData[i].allotmentQuantity,
                                  decoration: const InputDecoration(
                                    isDense: true,
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 8,
                                    ),
                                    border: OutlineInputBorder(),
                                  ),
                                  textAlign: TextAlign.end,
                                  keyboardType: TextInputType.number,
                                  textInputAction: TextInputAction.next,
                                  inputFormatters: Config.getNumberFilter(
                                      isBulk: inputData[i].isBulk(),
                                      unit: inputData[i].unit),
                                  style: TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  onChanged: (val) {
                                    context.read<AllocatingInventoryBloc>().add(
                                          AllocatingInventoryEvent
                                              .updateAllotmentQuantity(
                                            listType: listType,
                                            listIndex: index,
                                            itemIndex: i,
                                            allotmentQuantity: val,
                                          ),
                                        );
                                  },
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<bool?> showExcessAlert(
    BuildContext context, {
    required bool showActionButtons,
    required String title,
    required String body,
  }) {
    return showDialog<bool?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(body),
          actions: showActionButtons
              ? <Widget>[
                  TextButton(
                    child: Text('Cancel'),
                    onPressed: () {
                      context.pop(false);
                    },
                  ),
                  TextButton(
                    child: Text('Ok'),
                    onPressed: () {
                      // Perform submit action here
                      context.pop(true);
                    },
                  ),
                ]
              : null,
        );
      },
    );
  }

  Widget dispatchButton(AllocatingInput inputState) {
    return ElevatedButton.icon(
      style: ButtonStyle(
        padding: MaterialStateProperty.all(const EdgeInsets.all(4)),
        shape: MaterialStateProperty.all(
          RoundedRectangleBorder(
            side: BorderSide(color: Config.primaryColor, width: 2),
            borderRadius: BorderRadius.circular(
              4,
            ),
          ),
        ),
        fixedSize: MaterialStateProperty.all(
          Size(
            MediaQuery.of(context).size.width,
            40,
          ),
        ),
      ),
      onPressed: !inputState.isValidData
          ? null
          : () async {
              bool shouldProceed = await showExcessAlert(
                    context,
                    showActionButtons: true,
                    title: getLangText('allocationInventory.excessSubmitTitle',
                        'Allow Excess?'),
                    body: widget.canAllocateExcess
                        ? getLangText(
                            'allocationInventory.excessSubmitMessage.',
                            'Any excess inventory quantity would be allocated based on proportion of product in the supply orders')
                        : getLangText(
                            'allocationInventory.noExcessSubmitMessage.',
                            'Allocation would be done based on the available inventory in the mandi.'),
                  ) ??
                  false;
              if (shouldProceed) {
                context.read<AllocatingInventoryBloc>().add(
                      AllocatingInventoryEvent.dispatch(widget.allocateId),
                    );
              }
            },
      icon: inputState.isSubmitLoading
          ? SizedBox()
          : Icon(
              Icons.send,
              color: Colors.white,
            ),
      label: inputState.isSubmitLoading
          ? const SizedBox(
              height: 18,
              width: 18,
              child: CircularProgressIndicator(
                color: Colors.white,
              ),
            )
          : Text(
              LanguageEnum.allocateInventoryAllocateSubmit.localized(),
              style: TextStyle(
                color: Colors.white,
              ),
            ),
    );
  }

  Future<bool> showExcessCopyPopup({bool isMandi = true}) async {
    return await showExcessAlert(
          context,
          showActionButtons: true,
          title: getLangText(
              'allocationInventory.excessCopyTitle', 'Allow Excess?'),
          body: isMandi
              ? (widget.canAllocateExcess
                  ? getLangText('allocationInventory.excessCopyMessage.',
                      'Allow all available mandi inventory to be distributed for allocation')
                  : getLangText('allocationInventory.noExcessCopyMessage.',
                      'Allow all available mandi inventory to be distributed for allocation'))
              : (widget.canAllocateExcess
                  ? getLangText('allocationInventory.excessCopySupplyMessage.',
                      'Allow supply order quantity to be distributed for allocation')
                  : getLangText(
                      'allocationInventory.noExcessCopySupplyMessage.',
                      'Allow supply order quantity to be distributed for allocation')),
        ) ??
        false;
  }

  Widget sameAsInventoryButton() {
    return Padding(
      padding: const EdgeInsets.only(left: 8, right: 8, top: 10, bottom: 10),
      child: widget.isMandiAllocation
          ? ElevatedButton(
              style: ButtonStyle(
                  padding: MaterialStateProperty.all(const EdgeInsets.all(4)),
                  backgroundColor: MaterialStateProperty.all(Colors.white),
                  shape: MaterialStateProperty.all(RoundedRectangleBorder(
                      side: BorderSide(color: Config.primaryColor, width: 2),
                      borderRadius: BorderRadius.circular(4))),
                  fixedSize: MaterialStateProperty.all(
                      Size(MediaQuery.of(context).size.width, 40))),
              onPressed: () async {
                bool shouldProceed = await showExcessCopyPopup();
                if (shouldProceed) {
                  context.read<AllocatingInventoryBloc>().add(
                        AllocatingInventoryEvent.copyFromInventory(),
                      );
                }
              },
              child: Text(
                LanguageEnum.allocateInventoryAllocateCopyInventoryLabel
                    .localized(),
                style: TextStyle(
                  color: Config.primaryColor,
                ),
              ),
            )
          : Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey[300]!,
                ),
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Copy from',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(width: 16),
                    DropdownButton<String>(
                      value: selectedCopyDropDown,
                      items: [
                        DropdownMenuItem(
                          value: 'Mandi',
                          child: Text('Mandi'),
                        ),
                        DropdownMenuItem(
                          value: 'Supply Orders',
                          child: Text('Supply Orders'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedCopyDropDown = value!;
                        });
                        // Handle dropdown value change
                      },
                    ),
                    SizedBox(width: 16),
                    OutlinedButton.icon(
                      icon: Icon(Icons.arrow_forward),
                      label: Text('Copy'),
                      onPressed: () async {
                        bool shouldProceed = await showExcessCopyPopup(
                            isMandi: selectedCopyDropDown == 'Mandi');
                        if (shouldProceed) {
                          context.read<AllocatingInventoryBloc>().add(
                              AllocatingInventoryEvent.copyFromInventory(
                                  isMandi: selectedCopyDropDown == 'Mandi'));
                        }
                        // Handle icon button press
                      },
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget saveAsDraftButton(AllocatingInput inputState) {
    return ElevatedButton.icon(
        style: ButtonStyle(
            padding: MaterialStateProperty.all(const EdgeInsets.all(4)),
            backgroundColor: MaterialStateProperty.all(Colors.white),
            shape: MaterialStateProperty.all(RoundedRectangleBorder(
                side: BorderSide(color: Config.primaryColor, width: 2),
                borderRadius: BorderRadius.circular(4))),
            fixedSize: MaterialStateProperty.all(
                Size(MediaQuery.of(context).size.width, 40))),
        onPressed: !inputState.isValidData
            ? null
            : () {
                context.read<AllocatingInventoryBloc>().add(
                      AllocatingInventoryEvent.saveAsDraft(widget.allocateId),
                    );
              },
        icon: inputState.isDraftLoading
            ? SizedBox()
            : Icon(
                Icons.save,
                color: Colors.green,
              ),
        label: inputState.isDraftLoading
            ? SizedBox(
                height: 18,
                width: 18,
                child: CircularProgressIndicator(
                  color: Config.primaryColor,
                ),
              )
            : Text(LanguageEnum.allocateInventoryAllocateDraftLabel.localized(),
                style: TextStyle(color: Config.primaryColor)));
  }

  AppBar appBar() {
    return WAppBar.getAppBar(
      title: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            LanguageEnum.allocateInventoryAllocateTitle
                .localized(params: {'name': widget.destinationName}),
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
          // Text(
          //   '5th April',
          //   style: TextStyle(fontSize: 14),
          // )
        ],
      ),
      centerTitle: false,
    );
  }
}
