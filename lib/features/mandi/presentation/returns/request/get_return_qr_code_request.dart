import 'package:either_dart/src/either.dart';
import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/domain/entity/error_result.dart';

class GetReturnQrCodeRequest extends BaseRequest<dynamic, String> {
  final int smoId;

  GetReturnQrCodeRequest(this.smoId);

  @override
  String getPath() {
    return 'returns/qrCode/generate';
  }

  @override
  Map<String, dynamic>? getQuery() {
    return {
      'smoId': smoId,
    };
  }

  @override
  Future<Either<ErrorResult, String>?> mockData() {
    return Future.value(Right("1234"));
  }

  @override
  String mapper(data) {
    if (data is Map<String, dynamic>) {
      return data['qrCode'] as String;
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
