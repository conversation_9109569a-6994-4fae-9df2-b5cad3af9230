import 'dart:math';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/total_allocation_popup.dart';

part 'allocation_configuration.freezed.dart';
part 'allocation_configuration.g.dart';

@freezed
class AllocationConfiguration with _$AllocationConfiguration {
  const AllocationConfiguration._();
  const factory AllocationConfiguration({
    required Allocation allocation,
    required Distribution distribution,
    int? minimumDispatchTime,
  }) = _AllocationConfiguration;
  // from json
  factory AllocationConfiguration.fromJson(Map<String, dynamic> json) =>
      _$AllocationConfigurationFromJson(json);

  bool get canDispatchAllocation {
    return minimumDispatchTime == null ||
        minimumDispatchTime! * 1000 < DateTime.now().millisecondsSinceEpoch;
  }

  String get dispatchTime {
    if (minimumDispatchTime == null) return '';
    return minimumDispatchTime!.toDate('dd MMM | hh:mm a');
  }
}

@freezed
class Allocation with _$Allocation {
  const factory Allocation({
    // required AutoAllocation autoAllocation,
    @Default(UpdateAllocation(allowExpectation: false))
    UpdateAllocation updateAllocation,
    @Default(false) bool canUnlockLockedSku,
    @Default(false) bool canManageOrders,
  }) = _Allocation;
  // from json
  factory Allocation.fromJson(Map<String, dynamic> json) =>
      _$AllocationFromJson(json);
}

@freezed
class UpdateAllocation with _$UpdateAllocation {
  const factory UpdateAllocation({
    @Default(false) bool allowExpectation,
  }) = _UpdateAllocation;

  @override
  factory UpdateAllocation.fromJson(Map<String, dynamic> json) =>
      _$UpdateAllocationFromJson(json);
}

// @freezed
// class AutoAllocation with _$AutoAllocation {
//   const factory AutoAllocation({
//     @Default(true) bool enabled,
//     required List<Strategy> strategies,
//   }) = _AutoAllocation;

//   // from Json
//   factory AutoAllocation.fromJson(Map<String, dynamic> json) =>
//       _$AutoAllocationFromJson(json);
// }

@freezed
class Strategy with _$Strategy {
  const Strategy._();
  const factory Strategy({
    @Default('any') String? shelfLife,
    @Default('any') String? grade,
    @Default('mandi') String? allocationSource,
    @Default('any') String? skuId,
    @Default(false) bool? enableExpectation,
  }) = _Strategy;

  bool isSkuIdMatching(int skuId) {
    if (this.skuId == null) return true;
    if (this.skuId!.toLowerCase() == 'any') return true;
    return this.skuId == skuId.toString();
  }

  bool isGradeMatching(String grade) {
    if (this.grade == null) return true;
    if (this.grade!.toLowerCase() == 'any') return true;
    return this.grade?.toLowerCase() == grade.toLowerCase();
  }

  bool isShelfLifeMatching(String shelfLife) {
    if (this.shelfLife == null) return true;
    if (this.shelfLife!.toLowerCase() == 'any') return true;
    return this.shelfLife?.toLowerCase() == shelfLife.toLowerCase();
  }

  bool isSkuMatching(Sku sku) {
    return isSkuIdMatching(sku.id) &&
        isGradeMatching(sku.grade ?? '') &&
        isShelfLifeMatching(sku.shelfLifeType ?? '');
  }

  bool isSourceMandi() => allocationSource?.toLowerCase() == 'mandi';

  AllocationSkuItem applyStrategy(AllocationSkuItem item, Sku sku) {
    if (!isSkuMatching(sku)) return item;
    if (item.isValidId) return item;
    final mandiQty = item.mandiQty;
    final orderedQty = item.orderedQty ?? 0;
    final sourceQty = isSourceMandi() ? mandiQty : orderedQty;
    if (enableExpectation == true) {
      final qty = AllocationQty.fromTotalQty(
        qty: sourceQty.asString(),
        mandiQty: mandiQty,
        allowExpectation: false,
      );
      return item.copyWith(
        updateAllocationQty: qty,
      );
    } else {
      final minQty = min(sourceQty, mandiQty);
      return item.copyWith(
          updateAllocationQty: AllocationQty.fromMandiAndExpected(
        mandiQty: minQty.asString(),
        expectedQty: '0',
        actualMandiQty: mandiQty,
      ));
    }
  }

  // from Json
  factory Strategy.fromJson(Map<String, dynamic> json) =>
      _$StrategyFromJson(json);
}

@freezed
class Distribution with _$Distribution {
  const factory Distribution({
    required bool canEditDistribution,
    required double maxExcessPercent,
    required bool allowAutoCopyFromDistributed,
    @Default(false) bool isEditOnlyFromWeighingMachine,
  }) = _Distribution;
  // from Json
  factory Distribution.fromJson(Map<String, dynamic> json) =>
      _$DistributionFromJson(json);
}
