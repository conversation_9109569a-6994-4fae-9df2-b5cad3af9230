import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku_types/bloc/sku_types_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class CreateReturnsSkuPopup extends StatefulWidget {
  const CreateReturnsSkuPopup({
    super.key,
    required this.showDelete,
    this.onDelete = null,
    required this.notAllowedSkus,
    this.inputModel,
  });
  final bool showDelete;
  final VoidCallback? onDelete;
  final List<String> notAllowedSkus;
  final ReturnItem? inputModel;

  @override
  State<CreateReturnsSkuPopup> createState() => _CreateReturnsSkuPopupState();
}

class _CreateReturnsSkuPopupState extends State<CreateReturnsSkuPopup> {
  final TextEditingController _skuSearchController = TextEditingController();
  final TextEditingController _receiveQtyController = TextEditingController();
  ProcurementType procurementType = ProcurementType.bulk;
  String? procurementUnit = null;
  double? lotSize = null;
  Sku? sku;
  bool isCtaActive = false;
  bool isDuplicate = false;

  @override
  void initState() {
    final model = widget.inputModel;
    if (model != null) {
      procurementType = model.skuQuantity.isBulk
          ? ProcurementType.bulk
          : ProcurementType.lots;
      procurementUnit = model.skuQuantity.unit;
      lotSize = model.skuQuantity.lotSize;
      _receiveQtyController.text = model.acceptedQuantity;
      isCtaActive = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          sku = getSKU(context, skuID: model.skuQuantity.skuId);
        });
      });
    }
    _skuSearchController.addListener(_skuSearchListener);
    super.initState();
  }

  @override
  void setState(VoidCallback fn) {
    super.setState(fn);
    validate();
  }

  String _compositeKey() {
    if (procurementType == ProcurementType.bulk) {
      return '${sku?.id}-BULK-$procurementUnit';
    } else {
      return '${sku?.id}-LOTS-$procurementUnit-$lotSize';
    }
  }

  void validate() {
    bool _isCtaActive = false;
    bool _isDuplicate = false;
    if (sku == null ||
        procurementUnit == null ||
        (procurementType == ProcurementType.lots && lotSize == null)) {
      _isCtaActive = false;
    } else if (widget.notAllowedSkus.contains(_compositeKey())) {
      _isDuplicate = true;
      _isCtaActive = false;
    } else if (_receiveQtyController.text.isEmpty) {
      _isCtaActive = false;
    } else {
      _isCtaActive = true;
    }

    if (_isCtaActive != isCtaActive || _isDuplicate != isDuplicate) {
      setState(() {
        isCtaActive = _isCtaActive;
        isDuplicate = _isDuplicate;
      });
    }
  }

  @override
  void dispose() {
    _skuSearchController.removeListener(_skuSearchListener);
    _skuSearchController.dispose();
    _receiveQtyController.dispose();
    super.dispose();
  }

  void _skuSearchListener() {
    try {
      final skuName = _skuSearchController.text;
      if (skuName == sku?.name) return;
      if (skuName.isNotEmpty) {
        final foundSku = context.read<SkuBloc>().state.mapOrNull(
              success: (s) => s.skus.firstWhere(
                (element) => element.name == skuName,
              ),
            );
        if (foundSku != null) {
          setState(() {
            sku = foundSku;
          });
        }
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.6,
            color: Colors.white,
            margin: const EdgeInsets.only(
              left: 20,
              right: 20,
            ),
            child: BlocBuilder<SkuTypesBloc, SkuTypesState>(
              builder: (context, skuTypesState) {
                return skuTypesState.map(
                  initial: (_) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  loading: (_) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  failure: (failure) {
                    return ErrorScreen(
                      onPressed: () {
                        context.read<SkuTypesBloc>().add(SkuTypesEvent.fetch());
                      },
                      message: failure.message,
                    );
                  },
                  success: (success) {
                    return BlocBuilder<SkuBloc, SkuState>(
                      builder: (context, skuState) {
                        return skuState.map(
                          initial: (_) => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          success: (skuBlockState) {
                            final quantityTypes = success.quantityTypes;
                            final unitTypes = success.unitTypes;
                            final skus = skuBlockState.skus;
                            return _createScreen(
                              skus: skus,
                              quantityTypes: quantityTypes,
                              unitTypes: unitTypes,
                            );
                          },
                          error: (e) => ErrorScreen(
                            onPressed: () {
                              context
                                  .read<SkuBloc>()
                                  .add(const SkuEvent.fetch());
                            },
                            message: e.errorr.message,
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _createScreen({
    required List<Sku> skus,
    required List<String> quantityTypes,
    required List<String> unitTypes,
  }) {
    bool isBulk = procurementType == ProcurementType.bulk;
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Container(
          color: Colors.green,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Add Sku',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                if (widget.showDelete)
                  InkWell(
                    onTap: () {
                      context.pop();
                      widget.onDelete?.call();
                    },
                    child: Icon(
                      Icons.delete,
                      color: Colors.white,
                    ),
                  ),
                SizedBox(
                  width: 16,
                ),
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          child: Scrollbar(
            child: ListView(
              children: [
                CustomDropdown.search(
                  hintText:
                      getLangText('liquidationPopup.searchSku', 'Search Sku'),
                  items: skus.map((e) => e.name).toList(),
                  controller: _skuSearchController,
                ),
                const SizedBox(height: 12),
                if (sku != null) ...[
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 16.0,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: procurementTypeField(
                            procurementType,
                            true,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Expanded(
                          child: unitField(
                            unit: procurementUnit,
                            isBulk: procurementType == ProcurementType.bulk,
                            bulkUnits: sku?.bulkUnitTypes ?? [],
                            lotSizesUnits: sku?.lotSizes.keys.toList() ?? [],
                            allowEditing: true,
                          ),
                        ),
                        if (!isBulk && procurementUnit != null)
                          Expanded(
                            child: lotSizeField(
                              lotSize: lotSize,
                              lotSizes: sku?.lotSizes[
                                      procurementUnit?.toUpperCase() ?? ''] ??
                                  [],
                              allowEditing: true,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 12.0,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: receiveQtyField(),
                        ),
                        Expanded(
                          child: SizedBox(),
                        ),
                      ],
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
        if (isDuplicate)
          Align(
              alignment: Alignment.center,
              child: Text('Sku already added!',
                  style: TextStyle(color: Colors.red, fontSize: 14))),
        WStickyBottomCta(
          isEnabled: isCtaActive,
          icon: Icons.check,
          label: LangText('liquidationPopup.submitCta', 'Submit'),
          onPressed: () {
            context.pop(
              ReturnItem(
                itemId: -1,
                skuQuantity: SkuQuantity(
                  skuId: sku!.id,
                  type: procurementType.value.toUpperCase(),
                  unit: procurementUnit!,
                  lotSize: lotSize,
                  quantity: _receiveQtyController.text.toDouble(),
                ),
                acceptedQuantity: _receiveQtyController.text,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget receiveQtyField() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(getLangText('returnsPopup.receiveQty', 'Receive Qty'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              )),
          const SizedBox(height: 8),
          TextFormField(
            controller: _receiveQtyController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
              counter: SizedBox(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.getNumberFilter(
              isBulk: procurementType == ProcurementType.bulk,
              unit: procurementUnit ?? '',
            ),
            onChanged: (val) {
              validate();
              // context.read<PlaceOrderCubit>().updateOrderedQty(val);
            },
          )
        ],
      ),
    );
  }

  Widget unitField({
    required String? unit,
    required bool isBulk,
    required List<String> bulkUnits,
    required List<String> lotSizesUnits,
    required bool allowEditing,
  }) {
    // final skuEditBloc = context.read<SkuEditBloc>();
    // final state = skuEditBloc.state;
    final items = isBulk ? bulkUnits : lotSizesUnits;
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectUnitLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: unit,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((String item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (String? newValue) {
                          setState(
                            () {
                              this.procurementUnit = newValue;
                              this.lotSize = null;
                            },
                          );
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    unit?.capitalize().localized() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget lotSizeField({
    required double? lotSize,
    required dynamic lotSizes,
    required bool allowEditing,
  }) {
    // final selectedLotSize =
    //     state.sku?.lotSizes[state.procurementUnit?.toUpperCase() ?? ''] ??
    //         <String>[];
    final items = <double>[];
    if (lotSizes is List) {
      for (final element in lotSizes) {
        items.add(double.parse(element.toString()));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectLotSizeLabel.localized(),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: lotSize,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((double item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.toString()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (double? newValue) {
                          setState(() {
                            this.lotSize = newValue;
                          });
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    lotSize.toString() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget procurementTypeField(
    ProcurementType procurementType,
    bool allowEditing,
  ) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectProcTypeLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton<ProcurementType>(
                  // Initial Value
                  value: procurementType,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: ProcurementType.values.map((ProcurementType item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.value.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (ProcurementType? newValue) {
                          setState(() {
                            this.procurementType = newValue!;
                            this.procurementUnit = null;
                            this.lotSize = null;
                          });
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    procurementType.value.capitalize().localized(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }
}
