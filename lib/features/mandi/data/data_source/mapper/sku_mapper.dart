import 'package:proc2/features/mandi/data/data_source/remote/dto/sku/sku_dto.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';

Sku toSku(SkuDto dto) {
  return Sku(
    id: dto.id,
    name: dto.name,
    // unit: dto.unit,
    // supplyLotSize: dto.supplyLotSize,
    lotSizes: dto.lotSizes,
    image: dto.image,
    bulkUnitTypes: dto.bulkUnitTypes,
    parentSkuId: dto.parentSkuId,
    alternateNames: dto.alternateNames,
    canProcure: dto.canProcure,
    grade: dto.grade,
    shelfLifeType: dto.shelfLifeType,
    groupKey: dto.pickingGroupKey,
    topLevelCategory: dto.topLevelCategory,
  );
}
