import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/bloc/inventory_recieving_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/ir_sku_edit.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/view/receive_edit_dialog.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/view/receive_info_dialog.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/view/recieve_success.dart';
import 'package:proc2/features/mandi/util/get_mandi_name.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';
import 'package:rflutter_alert/rflutter_alert.dart';
import 'package:url_launcher/url_launcher.dart';

class ReceivingInv extends StatefulWidget {
  const ReceivingInv({
    super.key,
    required this.consignment,
    required this.mandiId,
    required this.smoId,
    required this.isEditOnlyFromWeighingMachine,
  });
  final Consignment consignment;
  final int mandiId;
  final int smoId;
  final bool isEditOnlyFromWeighingMachine;
  @override
  State<ReceivingInv> createState() => _ReceivingInv();
}

class _ReceivingInv extends State<ReceivingInv> {
  late FocusNode _commentFocusNode;
  bool isCommentFocused = false;

  @override
  void initState() {
    super.initState();
    _commentFocusNode = FocusNode();
    _commentFocusNode.addListener(() {
      setState(() {
        isCommentFocused = _commentFocusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _commentFocusNode.removeListener(() {});
    _commentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<InventoryRecievingBloc, InventoryRecievingState>(
      listener: (context, state) {
        state.maybeWhen(
          orElse: () => null,
          recievedInventory: () {
            context.read<InventoryRecievingBloc>().add(
                  InventoryRecievingEvent.started(
                    widget.mandiId,
                  ),
                );
            Navigator.pushReplacement(
              context,
              // ignore: inference_failure_on_instance_creation
              MaterialPageRoute(
                builder: (context) => const RecieveSuccess(),
              ),
            );
          },
        );
      },
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            appBar: appBar(),
            body: BlocBuilder<InventoryRecievingBloc, InventoryRecievingState>(
              buildWhen: (previousState, currentState) => true,
              builder: (context, state) {
                return state.maybeMap(
                  orElse: Container.new,
                  data: content,
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget loading() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      padding: const EdgeInsets.all(10),
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
        ],
      ),
    );
  }

  Widget content(InventoryRecievingData dataState) {
    if (dataState.inputModel == null || dataState.inputModel!.isEmpty()) {
      return EmptyScreen(
        message: LanguageEnum.recieveInventoryEmptyMessage.localized(),
      );
    }
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: dataState.error != null,
            child: Text(
              dataState.error ?? '',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.red,
              ),
            ),
          ),
          ...showInventoryReceiving(dataState.inputModel!),
          commentBox(dataState.inputModel?.comments ?? ''),
          const SizedBox(
            height: 8,
          ),
          WStickyBottomCta(
            icon: Icons.upload,
            label: LangText(
              'receiveInventory.submit',
              'Submit',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            onPressed: () {
              confirmationAlert(context).show();
            },
            isEnabled: dataState.isInputModelValid,
            isLoading: dataState.isLoading,
          ),
        ],
      ),
    );
  }

  bool isBulkExpanded = true;
  bool isLotExpanded = true;
  List<Widget> showInventoryReceiving(InventoryRecievingInputModel model) {
    return [
      Expanded(
        child: Scrollbar(
          child: ListView(
            children: [
              ExpansionPanelList(
                elevation: 0,
                expandedHeaderPadding: EdgeInsets.zero,
                expansionCallback: (int index, bool isExpanded) {
                  setState(() {
                    final hasBulk = model.bulk.isNotEmpty;
                    if (index == 0 && hasBulk) {
                      isBulkExpanded = !isExpanded;
                    } else {
                      isLotExpanded = !isExpanded;
                    }
                  });
                },
                children: [
                  if (model.bulk.isNotEmpty)
                    ExpansionPanel(
                      headerBuilder: (BuildContext context, bool isExpanded) {
                        return ListTile(
                          title: Text(
                            LanguageEnum.bulk.localized(),
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      },
                      body: Padding(
                        padding: const EdgeInsets.only(left: 0, right: 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          // shrinkWrap: true,
                          // Add 16 px padding to left and right of column
                          children: [
                            // labels(true),
                            Container(
                              color: Colors.grey[300],
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 16,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.goodQty',
                                        'Good Qty',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.start,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.damages',
                                        'Damages',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.missing',
                                        'Missing',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.expectedQty',
                                        'Expected',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 48,
                                    )
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 8,
                            ),

                            for (int i = 0; i < model.bulk.length; i++)
                              skuCard(
                                i,
                                model.bulk[i],
                                true,
                              )
                          ],
                        ),
                      ),
                      isExpanded: isBulkExpanded,
                      canTapOnHeader: true,
                    ),
                  if (model.lots.isNotEmpty)
                    ExpansionPanel(
                      headerBuilder: (BuildContext context, bool isExpanded) {
                        return ListTile(
                          title: Text(
                            LanguageEnum.lots.localized(),
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        );
                      },
                      body: Padding(
                        padding: EdgeInsets.zero,
                        child: Column(
                          // Add 16 px padding to left and right of column
                          children: [
                            Container(
                              color: Colors.grey[300],
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 16,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.goodLots',
                                        'Good Lots',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.start,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.damages',
                                        'Damages',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.missing',
                                        'Missing',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: LangText(
                                        'receiveInventory.expectedQty',
                                        'Expected',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 48,
                                    )
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 8,
                            ),
                            for (int i = 0; i < model.lots.length; i++)
                              skuCard(
                                i,
                                model.lots[i],
                                false,
                              ),
                          ],
                        ),
                      ),
                      isExpanded: isLotExpanded,
                      canTapOnHeader: true,
                    ),
                ],
              ),
            ],
          ),
        ),
      )
    ];
  }

  void showEditDialog(
      int index, IRSKUEditInputModel inputModel, bool isBulk) async {
    final isManualWeightAllowed =
        !inputModel.sku.isBulkKg || !widget.isEditOnlyFromWeighingMachine;
    final result = await showDialog<IRSKUEditInputModel?>(
      context: context,
      builder: (BuildContext context) => ReceiveEditDialog(
        index: index,
        inputModel: inputModel,
        isBulk: isBulk,
        isManualWeightAllowed: isManualWeightAllowed,
      ),
    );
    if (result != null) {
      context.read<InventoryRecievingBloc>().add(
            InventoryRecievingEvent.updateInput(index, isBulk, result),
          );
    }
  }

  Widget skuCard(int index, IRSKUEditInputModel inputModel, bool isBulk) {
    final sku = getSKU(context, skuID: inputModel.sku.skuId);
    final totalLosses = inputModel.getTotalLosses();
    final received = inputModel.recieved.toDouble();
    final isReceivedEmpty = inputModel.recieved.isEmpty;
    final missing = inputModel.sku.quantity - received;

    return Padding(
      padding: const EdgeInsets.only(
        top: 8,
        bottom: 8,
        left: 8,
        right: 8,
      ),
      child: InkWell(
        onTap: () {
          showEditDialog(index, inputModel, isBulk);
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 8,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: Text(
                        isBulk
                            ? '${sku.name} - ${inputModel.sku.unit.localized()}'
                            : '${sku.name} | Lot Size - ${inputModel.sku.lotSize.asString()} ${inputModel.sku.unit}',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Divider(
                        height: 1,
                        color: Colors.grey[300],
                      ),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 8,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 1,
                            child: missing >= 0 || isReceivedEmpty
                                ? Text(
                                    isReceivedEmpty
                                        ? '-'
                                        : (received - totalLosses).asString(),
                                    textAlign: TextAlign.center,
                                  )
                                : Column(
                                    children: [
                                      Text(
                                        isReceivedEmpty
                                            ? '-'
                                            : (received - totalLosses + missing)
                                                .asString(),
                                        textAlign: TextAlign.center,
                                      ),
                                      if (missing < 0)
                                        Text(
                                          '+',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: Colors.green),
                                        ),
                                      if (missing < 0)
                                        Text(
                                          '${missing.abs().asString()}(E)',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(color: Colors.green),
                                        ),
                                    ],
                                  ),
                          ),
                          Text('+'),
                          Expanded(
                            flex: 1,
                            child: Text(
                              isReceivedEmpty ? '-' : totalLosses.asString(),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Text('+'),
                          Expanded(
                            flex: 1,
                            child: Text(
                              isReceivedEmpty
                                  ? '-'
                                  : missing >= 0
                                      ? missing.asString()
                                      : '0',
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Text('='),
                          Expanded(
                            flex: 1,
                            child: Column(
                              children: [
                                Text(
                                  inputModel.sku.quantity.toString(),
                                  textAlign: TextAlign.center,
                                ),
                                if (missing < 0)
                                  Text(
                                    '+',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: Colors.green),
                                  ),
                                if (missing < 0)
                                  Text(
                                    '${missing.abs().asString()}(E)',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(color: Colors.green),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 12,
              ),
              Icon(
                Icons.edit,
                size: 24,
                color: Colors.green,
              ),
              SizedBox(
                width: 12,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget submitButton(bool isLoading, bool isValid) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ElevatedButton(
        style: ButtonStyle(
          fixedSize: MaterialStateProperty.all(
            Size(
              MediaQuery.of(context).size.width,
              50,
            ),
          ),
        ),
        onPressed: isLoading || !isValid
            ? null
            : () {
                confirmationAlert(context).show();
              },
        child: isLoading
            ? const SizedBox(
                height: 18,
                width: 18,
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              )
            : Text(
                LanguageEnum.recieveInventorySubmitBtn.localized(),
                style: const TextStyle(fontSize: 18),
              ),
      ),
    );
  }

  Widget commentBox(String comment) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            maxLines: isCommentFocused ? 3 : 1,
            focusNode: _commentFocusNode,
            initialValue: comment,
            onChanged: (txt) {
              context.read<InventoryRecievingBloc>().add(
                    InventoryRecievingEvent.onCommentChange(txt),
                  );
            },
            decoration: InputDecoration(
                hintText: LanguageEnum.recieveInventoryComments.localized(),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                )),
          ),
        ],
      ),
    );
  }

  Widget labels(bool isBulk) {
    return Container(
      width: MediaQuery.of(context).size.width,
      color: Colors.grey.shade200,
      padding: const EdgeInsets.all(8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              LanguageEnum.sku.localized(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              isBulk
                  ? LanguageEnum.quantity.localized()
                  : LanguageEnum.lotSize.localized(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
          if (!isBulk) ...[
            Expanded(
              flex: 2,
              child: Text(
                LanguageEnum.noOfLots.localized(),
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
                maxLines: 2,
                softWrap: true,
              ),
            ),
            const SizedBox(
              width: 4,
            )
          ],
          Expanded(
            flex: 2,
            child: Text(
              LanguageEnum.recieveInventoryTxtRecieved.localized(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              LanguageEnum.damage.localized(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  AppBar appBar() {
    final consignment = context
        .read<InventoryRecievingBloc>()
        .state
        .mapOrNull(data: (d) => d.selectedConsignment);

    return WAppBar.getAppBar(
      centerTitle: false,
      title: consignment == null
          ? Container()
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    LangText(
                      'receiveInventory.from',
                      'From',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(
                      width: 6,
                    ),
                    Text(
                      getMandiName(context,
                          mandiId: consignment.receivedFromMandi),
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (consignment.tripDetails.isNotEmpty &&
                    consignment.tripDetails.first.vehicleNo != null)
                  Row(
                    children: [
                      Icon(
                        Icons.local_taxi,
                        color: Colors.white,
                        size: 16,
                      ),
                      SizedBox(width: 2),
                      Text(
                        consignment.tripDetails.first.vehicleNo!,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  )
              ],
            ),
      actions: consignment == null || consignment.tripDetails.isEmpty
          ? []
          : [
              if (consignment.tripDetails.first.driverPhone != null)
                InkWell(
                  onTap: () async {
                    final number = consignment.tripDetails.first.driverPhone;
                    final url = Uri.parse('tel:$number');

                    if (await canLaunchUrl(url)) {
                      await launchUrl(url);
                    } else {
                      throw 'Could not open the dialer.';
                    }
                  },
                  child: Icon(
                    Icons.call,
                    size: 24,
                    color: Colors.white,
                  ),
                ),
              SizedBox(
                width: 16,
              ),
              InkWell(
                onTap: () {
                  showDialog(
                      context: context,
                      builder: (context) =>
                          ReceiveInfoDialog(consignment: consignment));
                },
                child: Icon(
                  Icons.info,
                  size: 24,
                  color: Colors.white,
                ),
              ),
              SizedBox(
                width: 16,
              )
            ],
    );
  }

  Alert confirmationAlert(BuildContext pageContext) {
    return Alert(
      context: context,
      type: AlertType.warning,
      padding: const EdgeInsets.all(16),
      title: LanguageEnum.recieveInventoryConfirmationAlertText.localized(),
      style: const AlertStyle(
        descStyle: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),
      buttons: [
        DialogButton(
          color: Colors.red,
          child: Text(LanguageEnum.falseButton.localized(),
              style: const TextStyle(color: Colors.white)),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        DialogButton(
          color: Config.primaryColor,
          child: Text(
            LanguageEnum.trueButton.localized(),
            style: const TextStyle(color: Colors.white),
          ),
          onPressed: () {
            // ignore: lines_longer_than_80_chars
            pageContext.read<InventoryRecievingBloc>().add(
                  InventoryRecievingEvent.submit(
                    widget.smoId,
                  ),
                );
            context.pop();
          },
        )
      ],
    );
  }
}
