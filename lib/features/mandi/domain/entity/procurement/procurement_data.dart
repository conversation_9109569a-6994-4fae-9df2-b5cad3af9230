import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/sku_input.dart';

part 'procurement_data.freezed.dart';

@freezed
class ProcurementData with _$ProcurementData {
  const factory ProcurementData({
    required int smoId,
    required String mandiName,
    required String procuredBy,
    required List<SkuInput> skus,
    required List<String> images,
    required String? vendorName,
  }) = _ProcurementData;

  static const empty = ProcurementData(
    smoId: -1,
    procuredBy: "",
    skus: [],
    images: [],
    mandiName: "",
    vendorName: '',
  );
}
