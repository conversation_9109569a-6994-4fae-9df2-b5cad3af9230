part of 'inventory_recieving_bloc.dart';

@freezed
class InventoryRecievingEvent with _$InventoryRecievingEvent {
  const factory InventoryRecievingEvent.started(int mandiId) = _Started;
  const factory InventoryRecievingEvent.submit(
    int smoId,
  ) = _Submit;
  const factory InventoryRecievingEvent.onConsignmentSelect(
    Consignment consignment,
  ) = _onConsignmentSelect;
  const factory InventoryRecievingEvent.updateReceivedQty({
    required String received,
    required int index,
    required bool isBulk,
  }) = _UpdateRecQty;
  const factory InventoryRecievingEvent.addLossess({
    required List<LossInputModel> loss,
    required int index,
    required bool isBulk,
  }) = _AddLosses;
  const factory InventoryRecievingEvent.onCommentChange(String comment) =
      _onCommentChange;
  const factory InventoryRecievingEvent.reset() = _Reset;
  const factory InventoryRecievingEvent.updateInput(
      int index, bool isBulk, IRSKUEditInputModel input) = _UpdateInput;
}
