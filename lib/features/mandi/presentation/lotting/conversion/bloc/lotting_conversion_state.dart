part of 'lotting_conversion_bloc.dart';

@freezed
class LottingConversionState with _$LottingConversionState {
  const factory LottingConversionState.loading() = _Loading;
  const factory LottingConversionState.error({
    required int smoId,
    required bool isLotting,
    required ErrorResult<dynamic> error,
  }) = _Error;
  const factory LottingConversionState.input({
    @Default(false) bool isLotting,
    @Default(-1) int smoId,
    @Default([]) List<InventoryItemInputModel> from,
    @Default([]) List<InventoryItemInputModel> to,
    @Default(0) double calculatedLoss,
    @Default([]) List<LossInputModel> losses,
    @Default('') String unit,
    @Default(false) bool isSubmitLoading,
    @Default(false) bool isSubmitActive,
    @Default(false) bool shouldGoBack,
    String? message,
    required LottingDeviation deviation,
  }) = _Input;
}
