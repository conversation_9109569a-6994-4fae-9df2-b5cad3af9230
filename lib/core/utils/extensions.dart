import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_machine_controller.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/language/cubit/language_cubit.dart';
import 'package:proc2/features/mandi/presentation/loss_types/bloc/loss_types_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku_types/bloc/sku_types_bloc.dart';

extension LoggerExtension<T> on T {
  // ignore: no_runtimetype_tostring
  Logger get logger => Logger(T.toString());
}

extension StringExtension on String {
  String capitalize() {
    try {
      return this[0].toUpperCase() + substring(1).toLowerCase();
    } catch (e) {
      return this;
    }
  }

  bool isDouble() {
    return double.tryParse(this) != null;
  }

  bool isNotDouble() {
    return !isDouble();
  }

  bool isInt() {
    return int.tryParse(this) != null;
  }

  bool isNotInt() {
    return !isInt();
  }

  double toDouble({double defaultValue = 0.0}) {
    return double.tryParse(this) ?? defaultValue;
  }
}

extension BlocFetchGlobal on BuildContext {
  void fetchGlobal() async {
    read<MandiBloc>().add(const MandiEvent.start());
    // ignore: avoid_dynamic_calls
    read<SkuBloc>().add(const SkuEvent.fetch());
    read<LossTypesBloc>().add(const LossTypesEvent.fetch());
    read<SkuTypesBloc>().add(const SkuTypesEvent.fetch());
    read<LanguageCubit>().init();
    di.get<VendorCubit>().fetchVendors();
    if (!kIsWeb) {
      await WeighingMachineController.instance.init();
    }
  }

  double get maxScreenWidth {
    if (_maxScreenWidth == null) {
      _maxScreenWidth = MediaQuery.of(this).size.width;
      if (kIsWeb && _maxScreenWidth! > 900) {
        _maxScreenWidth = 900;
      }
    }
    return _maxScreenWidth!;
  }
}

double? _maxScreenWidth;

extension DateTimeExtension on DateTime {
  String toFormattedString() {
    // Flutter update DateTime timezone to Kolkata
    // ignore: avoid_dynamic_calls
    // ignore: avoid_print
    final istTime = this.toUtc().add(Duration(hours: 5, minutes: 30));
    final format = DateFormat('dd/MM/yyyy hh:mm aa');
    return format.format(istTime);
  }

  int toIstUtcEpoch() {
    return millisecondsSinceEpoch;
  }
}

extension DateExt on int {
  DateTime toIstLocal() {
    return DateTime.fromMillisecondsSinceEpoch(this * 1000).toUtc().add(
          const Duration(hours: 5, minutes: 30),
        );
  }

  String toDate(String format, {bool skipTimezone = false}) {
    DateFormat formatter = DateFormat(format);
    return formatter.format(skipTimezone
        ? DateTime.fromMillisecondsSinceEpoch(this * 1000).toUtc()
        : toIstLocal());
  }
}

extension DoubleExtensions on double {
  String asString({int maxDecimalDigits = 3}) {
    String value = this.toStringAsFixed(maxDecimalDigits);
    if (maxDecimalDigits > 0) {
      value = value.replaceAll(RegExp(r'0+$'), '');
      value = value.replaceAll(RegExp(r'\.$'), '');
    }
    return value;
  }
}
