part of 'select_sku_bloc.dart';

@freezed
class SelectSkuState with _$SelectSkuState {
  const factory SelectSkuState.initial() = _Initial;
  const factory SelectSkuState.error(ErrorResult<dynamic> errorResult) = _Error;
  const factory SelectSkuState.success({
    required List<InventoryItem> bulkItems,
    required List<InventoryItem> lotItems,
    @Default([]) List<String> selectedItems,
    @Default([]) List<int> selectedSkuIds,
  }) = _Success;
}
