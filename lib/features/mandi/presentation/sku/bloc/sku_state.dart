part of 'sku_bloc.dart';

@freezed
class SkuState with _$SkuState {
  const SkuState._();
  const factory SkuState.initial() = _Initial;
  const factory SkuState.success({
    required List<Sku> skus,
    required String searchTerm,
    Sku? selectedSku,
    required List<Sku> filteredSku,
  }) = Success;
  const factory SkuState.error(ErrorResult<dynamic> errorr) = _Error;

  String getSkuName(int skuId) {
    try {
      return mapOrNull(
            success: (s) =>
                s.skus.firstWhere((element) => element.id == skuId).name,
          ) ??
          '';
    } catch (_) {
      return '';
    }
  }
}
