import 'package:proc2/core/data/network/base_request.dart';

class RemoveAssignedUserRequest extends BaseRequest<dynamic, String> {
  final int attendanceRoleId;

  RemoveAssignedUserRequest({
    required this.attendanceRoleId,
  });

  @override
  String getPath() =>
      'attendance/attendanceUserRole/$attendanceRoleId/role/remove';

  @override
  String mapper(data) {
    if (data is String) {
      return data;
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
