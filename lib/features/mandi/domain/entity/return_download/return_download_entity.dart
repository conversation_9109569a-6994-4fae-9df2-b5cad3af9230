import 'package:freezed_annotation/freezed_annotation.dart';

part 'return_download_entity.freezed.dart';

@freezed
class ReturnDownloadEntity with _$ReturnDownloadEntity {
  const ReturnDownloadEntity._();

  const factory ReturnDownloadEntity({
    required List<String> returnOrderIds,
    required int skuId,
    required double receivedQuantity,
  }) = _ReturnDownloadEntity;

  factory ReturnDownloadEntity.fromJson(Map<String, dynamic> json) {
    return ReturnDownloadEntity(
      returnOrderIds: [json['returnOrderId'] as String],
      skuId: json['skuId'] as int,
      receivedQuantity: (json['receivedQuantity'] as num).toDouble(),
    );
  }
}
