@echo off
echo Releasing Flavor %1
for /f "tokens=*" %%i in ('python get_flutter_version.py') do set flutter_version=%%i
echo Flutter version: %flutter_version%
@REM shorebird upgrade

if "%1"=="prod" (
    rmdir /s /q build
    fvm flutter build apk --release --target lib/main_production.dart --flavor production
    python .\rename_release.py -f production -t apk
    echo APK GENERATED
    python auto_upload_apk_to_slack.py PROD Flow
) else if "%1"=="dev" (
    rmdir /s /q build
    fvm flutter build apk --release --target lib/main_development.dart --flavor development
    python .\rename_release.py -f development -t apk
    echo APK GENERATED
    python auto_upload_apk_to_slack.py TEST Flow
)  else (
    echo Invalid flavor
)
