// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/cubit/manage_users_cubit.dart';

class CreateUserPopup extends StatefulWidget {
  const CreateUserPopup({super.key});

  @override
  State<CreateUserPopup> createState() => _CreateUserPopupState();
}

class _CreateUserPopupState extends State<CreateUserPopup> {
  TextEditingController userFirstNameController = TextEditingController();
  TextEditingController userLastNameController = TextEditingController();
  TextEditingController userEmailController = TextEditingController();
  TextEditingController userMobileController = TextEditingController();

  @override
  void dispose() {
    userFirstNameController.dispose();
    userLastNameController.dispose();
    userEmailController.dispose();
    userMobileController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ManageUsersCubit, ManageUsersState>(
      builder: (context, state) {
        final isCreateUserActive =
            (state.userFirstName != null && state.userEmail != null) &&
                ((state.userMobile != null && state.userMobile!.isNotEmpty)
                    ? state.userMobile!.length == 10
                    : true);

        return Popup(
          height: 0.8,
          maxWidth: 400,
          title: getLangText('createUser', 'Create User'),
          children: [
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(
                  bottom: 8,
                  left: 16,
                  right: 16,
                ),
                padding: const EdgeInsets.only(
                  top: 32,
                  left: 16,
                  right: 16,
                ),
                decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.shade300,
                        blurRadius: 10,
                        offset: const Offset(0, 0.75),
                      )
                    ],
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    )),
                child: ListView(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  children: [
                    TextFormField(
                      readOnly: state.isLoading,
                      controller: userFirstNameController,
                      decoration: InputDecoration(
                        labelText:
                            getLangText('createUser.firstName', 'First Name*'),
                        border: const OutlineInputBorder(),
                        isDense: true,
                        contentPadding: const EdgeInsets.only(
                          left: 8,
                          right: 8,
                          top: 12,
                          bottom: 12,
                        ),
                      ),
                      textInputAction: TextInputAction.next,
                      onChanged: (String? val) {
                        context.read<ManageUsersCubit>().setUserFirstName(val);
                      },
                    ),
                    const SizedBox(height: 24),
                    TextFormField(
                      readOnly: state.isLoading,
                      controller: userLastNameController,
                      decoration: InputDecoration(
                        labelText:
                            getLangText('createUser.lastName', 'Last Name'),
                        border: const OutlineInputBorder(),
                        isDense: true,
                        contentPadding: const EdgeInsets.only(
                          left: 8,
                          right: 8,
                          top: 12,
                          bottom: 12,
                        ),
                      ),
                      textInputAction: TextInputAction.next,
                      onChanged: (String? val) {
                        context.read<ManageUsersCubit>().setUserLastName(val);
                      },
                    ),
                    const SizedBox(height: 24),
                    TextFormField(
                      readOnly: state.isLoading,
                      controller: userEmailController,
                      decoration: InputDecoration(
                        labelText: getLangText('createUser.email', 'Email*'),
                        border: const OutlineInputBorder(),
                        isDense: true,
                        contentPadding: const EdgeInsets.only(
                          left: 8,
                          right: 8,
                          top: 12,
                          bottom: 12,
                        ),
                      ),
                      textInputAction: TextInputAction.next,
                      onChanged: (String? val) {
                        context.read<ManageUsersCubit>().setUserEmail(val);
                      },
                    ),
                    const SizedBox(height: 24),
                    TextFormField(
                      readOnly: state.isLoading,
                      controller: userMobileController,
                      decoration: InputDecoration(
                          labelText: getLangText('createUser.phone', 'Phone*'),
                          border: const OutlineInputBorder(),
                          isDense: true,
                          contentPadding: const EdgeInsets.only(
                            left: 8,
                            right: 8,
                            top: 12,
                            bottom: 12,
                          ),
                          counter: const SizedBox()),
                      maxLength: 10,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^(0|[1-9]\d*)?'))
                      ],
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.next,
                      onChanged: (String? val) {
                        context.read<ManageUsersCubit>().setUserMobile(val);
                      },
                    )
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            if (state.message != null)
              Padding(
                padding: const EdgeInsets.only(
                  bottom: 16,
                ),
                child: Text(
                  state.message!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            Center(
              child: ElevatedButton(
                onPressed: state.isLoading || !isCreateUserActive
                    ? null
                    : () async {
                        bool isSuccess =
                            await context.read<ManageUsersCubit>().createUser();
                        if (isSuccess) {
                          context
                              .read<ManageUsersCubit>()
                              .resetCreateUserData();
                          Navigator.of(context).pop();
                          showSnackBar(getLangText('userCreationSuccess',
                              'User created successfully!'));
                          context.read<ManageUsersCubit>().load();
                        }
                      },
                child: state.isLoading
                    ? const SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator())
                    : Text(getLangText('createUser.create', 'Create')),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
          ],
        );
      },
    );
  }
}
