import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

part 'procurement_history_event.dart';
part 'procurement_history_state.dart';
part 'procurement_history_bloc.freezed.dart';

@injectable
class ProcurementHistoryBloc extends Bloc<ProcurementHistoryEvent, ProcurementHistoryState> {
  ProcurementHistoryBloc(
    this._mandiRepository,
  ) : super(const ProcurementHistoryState.initial()) {
    on<ProcurementHistoryEvent>((event, emit) async {
      await event.map(
        started: (started) async {
          final result = await _mandiRepository.getAllProcDetail(started.smoId, null, null, null);
          result.fold(
            (error) => emit(ProcurementHistoryState.error(error)),
            (data) => emit(ProcurementHistoryState.success(data)),
          );
        },
      );
    });
  }

  final MandiRepository _mandiRepository;
}
