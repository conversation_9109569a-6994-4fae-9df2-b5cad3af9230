import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';

@singleton
class AddProcDb {
  AddProcDb(@Named('HiveDirPath') this._hiveDirPath) {
    init();
  }
  final String _hiveDirPath;
  final String boxName = 'addProcBox';

  Future<void> init() async {
    await Hive.initFlutter();
    final box = await open(boxName);
  }

  Future<Box<Map<dynamic, dynamic>>> open(
    String boxName,
  ) async {
    if (Hive.isBoxOpen(boxName)) {
      return Hive.box<Map<dynamic, dynamic>>(boxName);
    }
    return Hive.openBox<Map<dynamic, dynamic>>(boxName, path: _hiveDirPath);
  }

  Future<List<SkuInputData>> getSkus() async {
    final result = <SkuInputData>[];
    final box = await open(boxName);
    final mp = box.get('addProc') ?? {};
    if (!mp.containsKey('data')) return result;
    final data = mp['data'] as List<dynamic>;
    for (final d in data) {
      result.add(SkuInputData.fromJson(d as Map<dynamic, dynamic>));
    }
    return result;
  }

  Future<void> clearSkus() async {
    final box = await open(boxName);
    await box.clear();
  }

  Future<void> updateSkus(List<SkuInputData> skus) async {
    await clearSkus();
    final box = await open(boxName);
    final mp = <dynamic, dynamic>{};
    mp['data'] = skus.map((e) => e.toJson()).toList();
    await box.put('addProc', mp);
  }
}
