import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/sku_quantity.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';

part 'ir_sku_edit.freezed.dart';

@freezed
class IRSKUEditInputModel with _$IRSKUEditInputModel {
  const factory IRSKUEditInputModel({
    required int itemId,
    required SKUQuantity sku,
    @Default('') String recieved,
    @Default(<LossInputModel>[]) List<LossInputModel> losses,
  }) = _IRSKUEditInputModel;

  const IRSKUEditInputModel._();

  double getTotalLosses() {
    return losses.fold(
      0.0,
      (previousValue, element) => previousValue + element.lossValue.toDouble(),
    );
  }
}
