part of 'returns_submit_cubit.dart';

@freezed
class ReturnsSubmitState with _$ReturnsSubmitState {
  const factory ReturnsSubmitState.initial({
    @Default('') String returnOrderId,
    @Default([]) List<List<ReturnItem>> items,
    @Default(false) bool isCtaLoading,
    @Default(null) String? message,
    @Default(false) bool shouldPop,
    @Default(null) DriversDetails? driverDetails,
    @Default('') String returnType,
    @Default(null) int? createdAt,
    @Default(false) bool isLoading,
    @Default(null) String? subHeading,
    @Default([]) List<SummaryItem> summaryItems,
    @Default(false) bool isHistory,
    @Default(null) String? errorMessage,
  }) = _Initial;
}
