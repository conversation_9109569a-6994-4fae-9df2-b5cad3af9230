import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/procurement_data.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/sku_input.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';

@injectable
class AddProcurementUseCase {
  AddProcurementUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, void>> call({
    required ProcurementData procurementData,
    required List<SkuInputData> skuInputData,
    required int vendorLocationId,
    required List<ProcFieldCharge> fieldCharges,
    required String comments,
    required DateTime? supplyDate,
  }) async {
    // Validate the logic
    final skuInput = skuInputData.map(
      (e) => SkuInput(
        skuId: e.sku.id,
        type: e.procurementType.value,
        unit: e.procurementUnit ?? '',
        quantity: e.totalQty,
        orderedQuantity: e.orderedQuantity?.toDouble() ?? 0,
        lotSize: e.lotSize ?? 0,
        weight: (e.weight ?? '').toDouble(),
        amount: (e.amount ?? '').toDouble(),
        finalQuantity: e.isFinal,
        weighingSource: e.weighingSource,
      ),
    );
    final result = await _mandiRepository.addProcurement(
      procurementData.copyWith(skus: skuInput.toList()),
      vendorLocationId,
      fieldCharges,
      comments,
      supplyDate,
    );
    return result;
  }
}
