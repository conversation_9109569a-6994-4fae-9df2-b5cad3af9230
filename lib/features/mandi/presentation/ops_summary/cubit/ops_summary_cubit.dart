import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_detail_v2.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

part 'ops_summary_state.dart';
part 'ops_summary_cubit.freezed.dart';

@injectable
class OpsSummaryCubit extends Cubit<OpsSummaryState> {
  final MandiRepository _mandiRepository;
  OpsSummaryCubit(this._mandiRepository)
      : super(const OpsSummaryState.initial());

  void init(
      int smoId, int mandiId, String status, int startTime, int? endTime) {
    emit(OpsSummaryState.data(
      smoId: smoId,
      mandiId: mandiId,
      status: status,
      startTime: startTime,
      endTime: endTime,
    ));
  }

  Future<void> load() async {
    final currentState = state;
    if (currentState is _Data) {
      final result = await _mandiRepository.getSmoDetailV2(currentState.smoId);
      result.fold(
        (error) {
          emit(currentState.copyWith(error: error));
        },
        (success) {
          emit(currentState.copyWith(smoDetailV2: success));
        },
      );
    }
  }

  void clearError() {
    final currentState = state;
    if (currentState is _Data) {
      emit(currentState.copyWith(error: null));
    }
  }
}
