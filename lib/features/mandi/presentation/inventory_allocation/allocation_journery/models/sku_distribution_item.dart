import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';

part 'sku_distribution_item.freezed.dart';
part 'sku_distribution_item.g.dart';

@freezed
class SkuDistributionItem with _$SkuDistributionItem {
  const SkuDistributionItem._();
  const factory SkuDistributionItem({
    required int id,
    required SkuQuantity skuQuantity,
    required double? orderedQuantity,
    required double? orderedPrice,
    required double? allocatedPrice,
    required int? deliveryMemoId,
    required String? customerKey,
    required String? supplyOrderId,
    required int? orderSlNo,
    required String? refId,
    @JsonKey(
      includeFromJson: false,
      includeToJson: false,
    )
    @Default(null)
    WeighingQuantity? weighingQuantity,
    required double? fineTunedQuantity,
    @Default(false) bool readOnly,
    @Default(null)
    CustomerPriority?
        priority, // This will be set by referencing the data from supply order
    @Default(null)
    DistributionVariation?
        variation, // This will be set by referencing the data from supply order
    @Default(null) String? errorMessage,
  }) = _SkuDistributionItem;

  double get quantity => weighingQuantity?.value?.toDouble() ?? 0;

  @override
  factory SkuDistributionItem.fromJson(Map<String, dynamic> json) =>
      _$SkuDistributionItemFromJson(json);

  bool get isAllocatedAndFineTunedDiff {
    final allocatedQty = skuQuantity.quantity ?? 0;
    final fineTunedQty = weighingQuantity?.value?.toDouble() ?? 0;

    if (allocatedQty == fineTunedQty) {
      return false;
    }

    if (readOnly) {
      return false;
    }

    var variation = this.variation;
    if (variation == null ||
        variation.minValueVariation == null ||
        variation.maxValueVariation == null) {
      variation = DistributionVariation(
        minValueVariation: variation?.minValueVariation ?? 0,
        maxValueVariation: variation?.maxValueVariation ?? 0,
      );
    }

    final result = variation.validate(
      targetValue: allocatedQty,
      valueToCheck: fineTunedQty,
    );
    return !result;
  }

  double get pendingQuantity {
    final allocatedQty = skuQuantity.quantity ?? 0;
    final fineTunedQty = weighingQuantity?.value?.toDouble() ?? 0;
    return allocatedQty - fineTunedQty;
  }
}
