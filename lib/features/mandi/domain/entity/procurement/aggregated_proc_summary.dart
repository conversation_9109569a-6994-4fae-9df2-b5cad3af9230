import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';

part 'aggregated_proc_summary.freezed.dart';

@freezed
class AgreegatedProcSummary with _$AgreegatedProcSummary {
  const AgreegatedProcSummary._();
  const factory AgreegatedProcSummary({
    required int skuId,
    required String skuName,
    required String type,
    required String unit,
    required double? lotSize,
    required double totalOrdered,
    required double totalReceived,
    required double totalPending,
    required bool hasPending,
    required double totalAmount,
    required List<int> procDetailIds,
  }) = _AgreegatedProcSummary;

  bool get isBulk => type.toLowerCase() == 'bulk';
  String get compositeKey =>
      isBulk ? '$skuId-$type-$unit' : '$skuId-$type-$unit-$lotSize';
  String get pendingText => !hasPending ? '-' : totalPending.asString();
  String get unitText => isBulk ? 'Bulk - $unit' : 'Lots - $lotSize $unit';
}

class AgreegatedProcSummaryResult {
  final Map<int, ProcDetail> procDetails;
  final List<List<AgreegatedProcSummary>> result;

  AgreegatedProcSummaryResult(
      {required this.procDetails, required this.result});

  List<ProcDetail> getProcDetailsById(List<int> procDetailIds) {
    final List<ProcDetail> result = [];
    for (final id in procDetailIds) {
      final procDetail = procDetails[id];
      if (procDetail != null) {
        result.add(procDetail);
      }
    }
    return result;
  }
}
