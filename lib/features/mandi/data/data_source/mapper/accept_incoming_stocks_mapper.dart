import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';

Map<String, dynamic> toAcceptIncomingStocksBody(
  int smoId,
  InventoryRecievingInputModel inventoryRecievingInp,
) {
  return {
    'comment': inventoryRecievingInp.comments,
    'incomingStockId': inventoryRecievingInp.consignment.id,
    'smoId': smoId,
    'incomingStockItemList':
        [...inventoryRecievingInp.bulk, ...inventoryRecievingInp.lots]
            .map(
              (e) => {
                'itemId': e.itemId,
                'quantity': e.recieved.toDouble(),
                'losses': e.losses
                    .where((element) => element.lossValue.toDouble() > 0)
                    .map(
                      (f) => {
                        'comment': f.comment,
                        'lossType': f.lossType,
                        'quantity': f.lossValue,
                        'images': f.files
                            .where((e) => e.uploadKey != null)
                            .map(
                              (e) => e.uploadKey,
                            )
                            .toList(),
                      },
                    )
                    .toList()
              },
            )
            .toList()
  };
}
