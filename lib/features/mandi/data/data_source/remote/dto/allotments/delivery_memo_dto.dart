import 'package:freezed_annotation/freezed_annotation.dart';

part 'delivery_memo_dto.freezed.dart';
part 'delivery_memo_dto.g.dart';

@freezed
class DeliveryMemoDto with _$DeliveryMemoDto {
  const factory DeliveryMemoDto({
    String? errorCode,
    String? errorMessage,
    String? status,
    String? file,
  }) = _DeliveryMemoDto;

  @override
  factory DeliveryMemoDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryMemoDtoFromJson(json);
}
