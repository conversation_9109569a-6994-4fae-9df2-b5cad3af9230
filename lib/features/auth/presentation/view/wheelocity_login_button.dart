import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';

class WheelocityLoginButton extends StatelessWidget {
  const WheelocityLoginButton({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return state.maybeMap(
          orElse: () => ElevatedButton(
            style: ButtonStyle(
              fixedSize: MaterialStateProperty.all(
                Size(MediaQuery.of(context).size.width, 50),
              ),
              padding: MaterialStateProperty.all(
                const EdgeInsets.all(10),
              ),
            ),
            onPressed: () {
              context.read<AuthBloc>().add(
                    const AuthEvent.performLogin(),
                  ); // This is the event that will be handled by the bloc
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const FaIcon(
                  FontAwesomeIcons.google,
                  size: 18,
                ),
                const SizedBox(width: 10),
                Text(LanguageEnum.loginButtonTitle.localized()),
              ],
            ),
          ),
          loading: (_) => const CircularProgressIndicator(),
        );
      },
    );
  }
}
