import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/incoming_stock_item.dart';

part 'consignment.freezed.dart';

@freezed
class Consignment with _$Consignment {
  const factory Consignment({
    required String consignmentId,
    required int id,
    required List<IncomingStockItem> incomingStocks,
    required int receivedFromMandi,
    @Default([]) List<TripDetails> tripDetails,
  }) = _Consignment;
}

@freezed
class TripDetails with _$TripDetails {
  const factory TripDetails({
    String? driverName,
    String? driverPhone,
    String? vehicleNo,
    int? expectedDeliveryTime,
  }) = _TripDetails;
}
