import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/input_model/mandi_inv_sku_inp.dart';

part 'mandi_inventory_input_model.freezed.dart';

@freezed
class MandiInventoryInputModel with _$MandiInventoryInputModel {
  const factory MandiInventoryInputModel({
    required int mandiId,
    required List<MandiInvSkuInp> skuList,
  }) = _MandiInventoryInputModel;
}
