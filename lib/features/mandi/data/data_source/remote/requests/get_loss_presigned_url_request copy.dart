import 'package:proc2/core/data/network/base_request.dart';

class GetLossPreSignedUrlRequest
    extends BaseRequest<dynamic, Map<String, String>> {
  final int smoId;
  final List<String> fileExtensions;
  final String lossType;
  final String opsStage;

  GetLossPreSignedUrlRequest({
    required this.smoId,
    required this.fileExtensions,
    required this.lossType,
    required this.opsStage,
  });

  @override
  String getPath() => 'losses/images/upload';

  @override
  Map<String, dynamic> getBody() => {
        "smoId": smoId,
        "fileExtensions": fileExtensions,
        "lossType": lossType,
        "opsStage": opsStage
      };

  @override
  Map<String, String> mapper(data) {
    if (data is Map<String, dynamic>) {
      return data.map((key, value) => MapEntry(key, value.toString()));
    }
    throw Exception('Invalid json for loss image upload.');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
