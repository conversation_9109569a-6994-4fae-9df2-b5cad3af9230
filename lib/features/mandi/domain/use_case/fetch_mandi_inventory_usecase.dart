import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/mandi_inventory/mandi_inventory.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class FetchMandiInventoryUseCase {
  FetchMandiInventoryUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, MandiInventory>> call({
    required int mandiId,
  }) {
    return _mandiRepository.getMandiInventory(mandiId);
  }
}
