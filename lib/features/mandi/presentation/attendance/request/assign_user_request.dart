import 'package:geolocator/geolocator.dart';
import 'package:proc2/core/data/network/base_request.dart';

class AssignUserRequest extends BaseRequest<dynamic, String> {
  final int smoId;
  final String phoneNumber;
  final String? name;
  final String path;
  final bool isSelfMarked;
  final Position? location;

  AssignUserRequest._({
    required this.smoId,
    required this.phoneNumber,
    this.name,
    required this.path,
    this.isSelfMarked = false,
    required this.location,
  });

  factory AssignUserRequest.assignUser({
    required int smoId,
    required String phoneNumber,
    required Position? location,
  }) {
    return AssignUserRequest._(
      smoId: smoId,
      phoneNumber: phoneNumber,
      path: 'attendance/mark',
      location: location,
    );
  }

  factory AssignUserRequest.createAndAssignUser({
    required int smoId,
    required String phoneNumber,
    required String name,
    required Position? location,
  }) {
    return AssignUserRequest._(
      smoId: smoId,
      phoneNumber: phoneNumber,
      name: name,
      path: 'attendance/create-and-mark',
      location: location,
    );
  }

  factory AssignUserRequest.selfMark({
    required int smoId,
    required Position? location,
  }) {
    return AssignUserRequest._(
      smoId: smoId,
      phoneNumber: '',
      path: 'attendance/mark',
      isSelfMarked: true,
      location: location,
    );
  }

  @override
  String getPath() => path;

  @override
  Map<String, dynamic> getBody() {
    if (name == null) {
      return {
        'smoId': smoId,
        'phoneNumber': phoneNumber,
        'isSelfMarked': isSelfMarked,
        'lat': location?.latitude ?? 0,
        'lng': location?.longitude ?? 0,
      }..removeWhere((key, value) => value.toString().isEmpty);
    }

    final splits = name!.split(' ');
    final firstName = splits.first;
    final lastName = splits.length > 1 ? splits.last : '';

    return {
      'smoId': smoId,
      'phoneNumber': phoneNumber,
      'firstName': firstName,
      'lastName': lastName,
      'lat': location?.latitude ?? 0,
      'lng': location?.longitude ?? 0,
    };
  }

  @override
  String mapper(data) {
    if (data is String) {
      return data;
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
