part of 'grading_conversion_cubit.dart';

@freezed
class GradingConversionState with _$GradingConversionState {
  const factory GradingConversionState.initial({
    @Default(null) List<InventoryItem>? allInventory,
    InventoryItem? fromInventory,
    @Default([]) List<PickedFile> dumpFiles,
    @Default([]) List<GradingInput> toInventory,
    @Default(null) String? message,
    @Default(false) bool isCtaLoading,
    @Default(false) bool shouldPop,
    @Default(null) double? gradingInventoryDeviation,
    @Default(null) double? gradingConversionDeviation,
  }) = _Initial;
}

@freezed
class GradingInput with _$GradingInput {
  const factory GradingInput({
    required Sku sku,
    required List<PickedFile> files,
    required SkuQuantity skuQuantity,
    required String quantity,
    required WeighingQuantity? weighingQuantity,
  }) = _GradingInput;
}
