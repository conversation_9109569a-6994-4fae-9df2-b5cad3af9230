import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/delivery_memo.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/process_action.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/process_stage.dart';

part 'allotment_v2.freezed.dart';

@freezed
class AllotmentV2 with _$AllotmentV2 {
  const AllotmentV2._();
  const factory AllotmentV2({
    required int id,
    required int smoId,
    required int destinationId,
    required String destinationName,
    @Default('') String customerGroup,
    @Default('') String deliverySlot,
    @Default(0) int deliveryDate,
    required ProcessStage status,
    @Default([]) List<ProcessStage> processStage,
    @Default([]) List<ProcessAction> actions,
    @Default(null) DeliveryMemo? deliveryMemo,
    required String consignmentId,
  }) = _AllotmentV2;

  bool showTimeline() {
    if (processStage.isEmpty) return false;
    if (processStage.length == 1 && processStage[0].key == status.key)
      return false;
    return true;
  }

  bool canAllot() {
    return !processStage.contains(ProcessStage.COMPLETED);
  }

  bool canDispatch() {
    return actions.contains(ProcessAction.DISPATCH);
  }

  bool get showMenu => actions.length > 1;

  ProcessAction? primaryAction() {
    if (actions.isEmpty) return null;
    if (actions.length == 1) return actions.first;
    for (final action in actions) {
      if (action != ProcessAction.CANCEL) {
        return action;
      }
    }
    return null;
  }

  List<ProcessAction> extraActions() {
    if (actions.length <= 1) return [];
    final p = primaryAction();
    final remainingActions =
        actions.where((element) => element.key != p?.key).toList();
    return remainingActions;
  }
}
