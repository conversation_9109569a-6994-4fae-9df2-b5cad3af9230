import 'package:either_dart/either.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class CreateAllotmentUseCase {
  CreateAllotmentUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, int>> call(
      {required int smoId,
      required int destinationId,
      required String destinationType,
      required int deliveryDate,
      required String deliverySlot,
      required String? customerGroup}) async {
    try {
      return _mandiRepository.createAllotment(
        smoId: smoId,
        destinationId: destinationId,
        destinationType: destinationType,
        deliveryDate: deliveryDate,
        deliverySlot: deliverySlot,
        customerGroup: customerGroup,
      );
    } catch (e, s) {
      talker.handle(e, s);
      return Left(
        ErrorResult<dynamic>(
          message: kDebugMode ? e.toString() : 'Something went wrong',
          code: '',
          timestamp: DateTime.now().millisecondsSinceEpoch,
        ),
      );
    }
  }
}
