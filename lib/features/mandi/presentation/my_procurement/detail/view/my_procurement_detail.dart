import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/view/add_procurement.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/detail/cubit/my_proc_detail_cubit.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/detail/cubit/my_proc_detail_state.dart';

class MyProcDetail extends StatefulWidget {
  final int smoId;
  final int mandiId;

  const MyProcDetail({Key? key, required this.smoId, required this.mandiId}) : super(key: key);
  @override
  _MyProcDetailState createState() => _MyProcDetailState(); // Create the state
}

class _MyProcDetailState extends State<MyProcDetail> {
  final ScrollController _scrollController = ScrollController();
  TextEditingController statusController = TextEditingController(text: "PLACED");
  int limit = 10;
  int offset = 0;
  int currentPage = 0;

  @override
  void dispose() {
    _scrollController.dispose();
    statusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shouldShowAddProc = false || (di.get<AppConfig>().isDev);
    // context
    //     .read<SmoBloc>()
    //     .state
    //     .maybeMap(orElse: () => false, success: (s) => s.smo.menuConfig.proc);
    return Scaffold(
      appBar: WAppBar.getAppBar(
        title: LangText(
          'allProcDetail.title',
          'All Procurements',
          style: TextStyle(fontSize: 18),
        ),
        centerTitle: false,
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[200],
        child: BlocBuilder<MyProcDetailCubit, MyProcDetailState>(
          builder: (context, state) {
            return state.maybeWhen(
              orElse: () => Center(
                child: CircularProgressIndicator(),
              ),
              error: (e) => ErrorScreen(
                onPressed: () {
                  context.read<MyProcDetailCubit>().loadDetail(widget.smoId);
                },
                message: e,
              ),
              loaded: (data) {
                // if (data.isEmpty)
                //   return Column(
                //     children: [
                //       Expanded(
                //         child: EmptyScreen(
                //           message: getLangText('myProcDetail.noData', 'No Procurements Found!'),
                //         ),
                //       ),
                //       if (shouldShowAddProc) _addProcButton(context),
                //     ],
                //   );
                final completedItems = data.where((e) => e.status.toLowerCase() == 'completed');
                final priceUpdatedCount = completedItems.length;
                final totalPrice = completedItems.fold(0.0, (p, e) => p + e.totalAmount());

                return Column(
                  children: [
                    filter(),
                    data.isEmpty
                        ? Expanded(
                            child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              EmptyScreen(
                                message: getLangText('myProcDetail.noData', 'No Procurements Found!'),
                              ),
                              // if (shouldShowAddProc) _addProcButton(context),
                            ],
                          ))
                        : Expanded(
                            child: ListView.builder(
                              controller: _scrollController,
                              itemCount: data.length,
                              itemBuilder: (context, index) {
                                final myProcDetail = data[index];

                                return Container(
                                  margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                                  padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12.0),
                                    color: Colors.white,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0),
                                        spreadRadius: 1,
                                        blurRadius: 5,
                                        offset: Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      if ((myProcDetail.vendorName != null && myProcDetail.vendorName!.isNotEmpty) || (myProcDetail.vendorLocationId != null))
                                        Container(
                                          width: double.infinity,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 4,
                                              horizontal: 8,
                                            ),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  di.get<VendorCubit>().getVendorLocation(myProcDetail.vendorLocationId, myProcDetail.vendorName).vendorName,
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                                if (myProcDetail.refId != null) ...[
                                                  SizedBox(
                                                    height: 4,
                                                  ),
                                                  Text(
                                                    myProcDetail.refId! + ' (${myProcDetail.refSource})',
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.green.shade50,
                                            borderRadius: BorderRadius.circular(16),
                                          ),
                                        ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 8),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              flex: 2,
                                              child: LangText(
                                                'dateAndTime',
                                                'Date & Time',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black87,
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 8,
                                            ),
                                            Expanded(
                                              flex: 1,
                                              child: LangText(
                                                'numberOfItems',
                                                '# of Items',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black87,
                                                ),
                                                textAlign: TextAlign.end,
                                              ),
                                            ),
                                            SizedBox(
                                              width: 8,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Divider(
                                        height: 1,
                                        color: Colors.grey[300],
                                      ),
                                      SizedBox(
                                        height: 8,
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 0,
                                          horizontal: 0,
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              flex: 2,
                                              child: Text(
                                                myProcDetail.createdAt.toDate(
                                                  'dd MMM - hh:mm a',
                                                ),
                                                style: TextStyle(
                                                  color: Colors.black87,
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 8,
                                            ),
                                            Expanded(
                                              flex: 1,
                                              child: Text(
                                                myProcDetail.items.length.toString(),
                                                style: TextStyle(
                                                  color: Colors.black87,
                                                ),
                                                textAlign: TextAlign.end,
                                              ),
                                            ),
                                            SizedBox(
                                              width: 8,
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 4,
                                      ),
                                      ...[
                                        Divider(),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: myProcDetail.status.toLowerCase() == 'placed'
                                                  ? OutlinedButton.icon(
                                                      onPressed: () async {
                                                        await Navigator.of(context).push(
                                                          MaterialPageRoute(
                                                            builder: (ctx) {
                                                              final mandiName = context.read<MandiBloc>().state.maybeMap(
                                                                    orElse: () => '',
                                                                    success: (s) => s.allMandis.firstWhere((element) => element.id == widget.mandiId).name,
                                                                  );
                                                              return BlocProvider(
                                                                create: (_) => context.read<SmoBloc>(),
                                                                child: AddProcurement(
                                                                  mandiId: widget.mandiId,
                                                                  mandiName: mandiName,
                                                                  smoId: widget.smoId,
                                                                  procDetail: myProcDetail,
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                        );
                                                        context.read<MyProcDetailCubit>().loadDetail(widget.smoId);
                                                      },
                                                      icon: Icon(Icons.read_more),
                                                      label: myProcDetail.isParentOrder
                                                          ? LangText('myProcDetails.updateOrder', 'Update Order')
                                                          : LangText(
                                                              'myProcDetails.receiveOrder',
                                                              'Receive Order',
                                                            ),
                                                    )
                                                  : myProcDetail.status.toLowerCase() == 'quantity_submitted'
                                                      ? OutlinedButton.icon(
                                                          onPressed: () async {
                                                            await context.push(
                                                                context.namedLocation(
                                                                  'editProcurement',
                                                                  pathParameters: {
                                                                    'smoId': widget.smoId.toString(),
                                                                    'mandiId': widget.mandiId.toString(),
                                                                  },
                                                                ),
                                                                extra: SmoDetail.fromProcDetail(myProcDetail)
                                                                // extra: SmoDetail.fromMyProc(),
                                                                );
                                                            context.read<MyProcDetailCubit>().loadDetail(widget.smoId);
                                                          },
                                                          icon: Icon(Icons.cloud_upload_outlined),
                                                          label: LangText(
                                                            'myProcDetails.updatePrice',
                                                            'Update Price',
                                                          ),
                                                        )
                                                      : Container(
                                                          padding: EdgeInsets.symmetric(
                                                            vertical: 8,
                                                            horizontal: 16,
                                                          ),
                                                          decoration: BoxDecoration(
                                                            color: Colors.green.shade50,
                                                            borderRadius: BorderRadius.circular(
                                                              12,
                                                            ),
                                                          ),
                                                          child: Text(
                                                            '₹ ${myProcDetail.totalAmount().asString(maxDecimalDigits: 2)}',
                                                            textAlign: TextAlign.center,
                                                            style: TextStyle(
                                                              fontSize: 15,
                                                              fontWeight: FontWeight.w600,
                                                            ),
                                                          ),
                                                        ),
                                            ),
                                            SizedBox(
                                              width: 16,
                                            ),
                                            Expanded(
                                              child: ElevatedButton.icon(
                                                onPressed: () async {
                                                  context.pushNamed(
                                                    'procItem',
                                                    extra: myProcDetail,
                                                  );
                                                },
                                                icon: Icon(Icons.navigate_next_rounded),
                                                label: LangText(
                                                  'myProcDetails.view',
                                                  'View',
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ]
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                    if (priceUpdatedCount > 0)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.blue.shade200,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        margin: EdgeInsets.symmetric(
                          horizontal: 8,
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 16,
                        ),
                        child: Row(
                          children: [
                            Text(
                              getLangText('myProc.vendorSummary', 'Vendor Summary: '),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(
                              width: 2,
                            ),
                            Text(
                              '${priceUpdatedCount}',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '/${data.length}',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Text(
                              'completed',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Spacer(),
                            Text(
                              ' ₹ ${totalPrice.asString(maxDecimalDigits: 2)}',
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                            width: kIsWeb ? 200 : 120,
                            child: Padding(
                              padding: const EdgeInsets.only(left: 20, top: 6, bottom: 6),
                              child: ElevatedButton(
                                onPressed: currentPage > 0
                                    ? () {
                                        setState(() {
                                          currentPage--;
                                          offset = currentPage * limit; // Update offset
                                        });
                                        context.read<MyProcDetailCubit>().loadDetail(widget.smoId, offset: offset, limit: limit);
                                      }
                                    : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                ),
                                child: Text('Previous'),
                              ),
                            )),
                        Container(
                          width: kIsWeb ? 50 : 35,
                          height: kIsWeb ? 50 : 35,
                          child: Card(
                            color: Colors.green,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(kIsWeb ? 25 : 15),
                            ),
                            child: Center(
                              child: Padding(
                                padding: const EdgeInsets.all(kIsWeb ? 4 : 3),
                                child: Text(
                                  "${currentPage + 1}",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                            width: kIsWeb ? 200 : 120,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 20, top: 6, bottom: 6),
                              child: ElevatedButton(
                                onPressed: data.isEmpty
                                    ? null
                                    : () {
                                        setState(() {
                                          currentPage++;
                                          offset = currentPage * limit; // Update offset
                                        });
                                        context.read<MyProcDetailCubit>().loadDetail(widget.smoId, offset: offset, limit: limit);
                                      },
                                child: Text('Next'),
                              ),
                            )),
                      ],
                    ),
                    if (shouldShowAddProc) _addProcButton(context),
                  ],
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget filter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.all(8),
          child: SizedBox(
            height: 40,
            width: kIsWeb ? 400 : 200,
            child: CustomDropdown(
              controller: statusController,
              items: [
                "PLACED",
                "QUANTITY_SUBMITTED",
                "COMPLETED"
              ],
              hintText: "Select Status",
              onChanged: (status) {
                context.read<MyProcDetailCubit>().updateStatus(status);
              },
            ),
          ),
        ),
        ElevatedButton(
            onPressed: () {
              currentPage = 0;
              context.read<MyProcDetailCubit>().loadDetail(widget.smoId, offset: 0, limit: 10);
            },
            child: Text('Apply'))
      ],
    );
  }

  Widget _addProcButton(BuildContext context) {
    return WStickyBottomCta(
      icon: Icons.add_box_rounded,
      label: LangText('mandiMenu.addProcurement', 'Add Procurement'),
      onPressed: () async {
        await context.push(
          context.namedLocation(
            'addProcurement',
            pathParameters: {
              'mandiId': '${widget.mandiId}'
            },
          ),
          extra: context,
        );
        context.read<MyProcDetailCubit>().loadDetail(widget.smoId);
      },
    );
  }
}
