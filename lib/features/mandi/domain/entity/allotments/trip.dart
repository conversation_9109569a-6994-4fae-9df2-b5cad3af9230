import 'package:freezed_annotation/freezed_annotation.dart';

part 'trip.freezed.dart';

@freezed
class Trip with _$Trip {
  const factory Trip({
    required int id,
    required String routeId,
    required String status,
    required bool canDispatch,
    int? tripStartedAt,
    required DriverDetail driverDetails,
    required VehicleDetail vehicleDetails,
  }) = _Trip;
}

@freezed
class DriverDetail with _$DriverDetail {
  const DriverDetail._();
  const factory DriverDetail({
    @Default('') String name,
    @Default('') String mobile,
  }) = _DriverDetail;

  bool isValid() {
    return name.isNotEmpty && mobile.isNotEmpty && mobile.length == 10;
  }
}

@freezed
class VehicleDetail with _$VehicleDetail {
  const VehicleDetail._();
  const factory VehicleDetail({
    @Default('') String vehicleNumber,
  }) = _VehicleDetail;

  bool isValid() {
    return vehicleNumber.isNotEmpty;
  }
}
