import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_detail_v2.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/sku_history.dart';

part 'smo_detail.freezed.dart';

@freezed
class SmoDetail with _$SmoDetail {
  SmoDetail._();
  factory SmoDetail({
    required int id,
    required int createdAt,
    required String status,
    required int smoId,
    required List<String> images,
    @Default({}) Map<String, dynamic> urls,
    required List<SkuHistory> items,
    required String? vendorName,
    @Default(null) int? vendorLocationId,
    @Default([]) List<dynamic> charges,
    @Default(null) String? refId,
    @Default(null) String? refSource,
  }) = _SmoDetail;

  factory SmoDetail.fromMyProc(MyProc myProc) {
    return SmoDetail(
      id: myProc.id,
      createdAt: myProc.createdAt,
      status: myProc.status,
      smoId: myProc.smoId,
      images: myProc.images,
      urls: myProc.urls,
      items:
          myProc.items.map((e) => SkuHistory.fromItem(e, myProc.urls)).toList(),
      vendorName: myProc.vendorName,
      vendorLocationId: myProc.vendorLocationId,
      charges: myProc.charges,
    );
  }

  factory SmoDetail.fromProcDetail(ProcDetail myProc) {
    return SmoDetail(
      id: myProc.id,
      createdAt: myProc.createdAt,
      status: myProc.status,
      smoId: myProc.smoId,
      images: myProc.images,
      urls: myProc.urls,
      items: myProc.items
          .map((e) => SkuHistory.fromProcDetailItem(e, myProc.urls))
          .toList(),
      vendorName: myProc.vendorName,
      vendorLocationId: myProc.vendorLocationId,
      charges: myProc.charges,
      refId: myProc.refId,
      refSource: myProc.refSource,
    );
  }

  bool isCompleted() {
    return status.toLowerCase() == 'completed';
  }
}
