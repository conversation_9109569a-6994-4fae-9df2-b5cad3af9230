import 'package:bloc/bloc.dart';
import 'package:either_dart/either.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/domain/entity/lang/supported_language.dart';
import 'package:proc2/features/mandi/domain/use_case/get_language_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/update_language_usecase.dart';

part 'language_state.dart';
part 'language_cubit.freezed.dart';

@injectable
class LanguageCubit extends Cubit<LanguageState> {
  LanguageCubit(
    this._getLanguageUseCase,
    this._updateLanguageUseCase,
    this._language,
  ) : super(const LanguageState.initial()) {
    getLanguages();
  }

  final GetLanguageUseCase _getLanguageUseCase;
  final UpdateLanguageUseCase _updateLanguageUseCase;
  final Language _language;

  Future<void> init() async {
    final currentLang = await _language.currentLanguage();
    if (currentLang != null) {
      await _updateLanguageUseCase(currentLang);
    }
  }

  Future<void> getLanguages() async {
    await _getLanguageUseCase().fold(
      (left) => emit(
        LanguageState.error(
          left,
        ),
      ),
      (right) async => emit(
        LanguageState.success(
          supportedLanguages: right,
          selectedLanguage: await _language.currentLanguage(),
        ),
      ),
    );
  }

  void updateSelectedLanguage(String languageId) {
    final currentState = state;
    if (currentState is LanguageSuccess) {
      emit(
        currentState.copyWith(
          selectedLanguage: languageId,
        ),
      );
    }
  }

  Future<bool> updateLanguage() async {
    final currentState = state;
    var success = false;
    if (currentState is LanguageSuccess &&
        currentState.selectedLanguage != null) {
      emit(currentState.copyWith(isLoading: true));
      await _updateLanguageUseCase(currentState.selectedLanguage!).fold(
        (left) => emit(
          currentState.copyWith(
            message: left.message,
            isLoading: false,
          ),
        ),
        (right) {
          emit(
            currentState.copyWith(
              message: 'Language Updated Successfully!',
              isLoading: false,
            ),
          );
          success = true;
        },
      );
    }

    return success;
  }
}
