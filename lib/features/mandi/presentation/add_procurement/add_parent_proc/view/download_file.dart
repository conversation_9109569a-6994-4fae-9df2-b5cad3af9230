import 'file_downloader/web_file_downloader.dart'
    if (dart.library.io) 'file_downloader/io_file_downloader.dart';

Future<void> downloadText(String fileName, String data) async {
  final fileDownloader = FileDownloaderImpl();
  await fileDownloader.downloadText(fileName, data);
}

Future<void> downloadBlob(String fileName, List<int>? bytes) async {
  final fileDownloader = FileDownloaderImpl();
  await fileDownloader.downloadBlob(fileName, bytes);
}
