import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/uploader/image_picker_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/sku_history.dart';

class EditPriceDialog extends StatefulWidget {
  const EditPriceDialog({
    super.key,
    required this.inputModel,
    required this.index,
    required this.allSlips,
    required this.smoId,
  });

  final SkuHistory inputModel;
  final int index;
  final List<PickedFile> allSlips;
  final int smoId;

  @override
  State<EditPriceDialog> createState() => _EditPriceDialogState();
}

class _EditPriceDialogState extends State<EditPriceDialog> {
  List<PickedFile> files = [];
  List<PickedFile> allSlips = [];
  String amount = '';
  String get fileCountText => files.length == 0 ? '' : ' (${files.length})';

  bool _shouldShowConfirmCheckbox = false;
  bool _isConfirmChecked = false;

  @override
  void initState() {
    files = widget.inputModel.files;
    allSlips = widget.allSlips;
    amount = widget.inputModel.amount ?? '';
    super.initState();
  }

  @override
  void didUpdateWidget(covariant EditPriceDialog oldWidget) {
    if (oldWidget.inputModel != widget.inputModel) {
      files = widget.inputModel.files;
      allSlips = widget.allSlips;
      amount = widget.inputModel.amount ?? '';
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Popup(
      height: 0.5,
      title: 'editProc.editPrice'.tr('Edit Price'),
      children: [
        Expanded(
          child: ListView(
            padding: EdgeInsets.symmetric(
              vertical: 8,
              horizontal: 16,
            ),
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: widget.inputModel.amount,
                      inputFormatters: Config.numberInputFilters,
                      onChanged: (value) {
                        var requireCheckbox = false;
                        if (value.isNotEmpty) {
                          final doubleValue = double.tryParse(value) ?? 1;
                          final billedCostPrice =
                              (doubleValue) / (widget.inputModel.quantity ?? 1);
                          requireCheckbox = billedCostPrice !=
                              widget.inputModel.orderedCostPrice;
                        }
                        setState(() {
                          amount = value;
                          _shouldShowConfirmCheckbox = requireCheckbox;
                        });
                      },
                      textAlign: TextAlign.end,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        label: Text('amount'.tr('Amount')),
                        isDense: true,
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.only(
                          left: 8,
                          right: 8,
                          top: 8,
                          bottom: 8,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      final allFilesMap = <String, PickedFile>{};
                      for (final f in allSlips) {
                        if (f.uploadKey != null) {
                          allFilesMap[f.uploadKey!] = f;
                        }
                      }
                      for (final f in files) {
                        if (f.uploadKey != null) {
                          allFilesMap[f.uploadKey!] = f;
                        }
                      }
                      final result = await Navigator.push<PickerResult?>(
                        context,
                        // ignore: inference_failure_on_instance_creation
                        MaterialPageRoute(
                          builder: (context) => ImagePickerScreen(
                            pageTitle:
                                LanguageEnum.addProcUploadSplitsBtn.localized(),
                            reqBody: {'smoId': widget.smoId},
                            module: UploadFileModule.procurementsInvoice,
                            allowMultiple: true,
                            initialImages: allFilesMap.values.toList(),
                            enableSelection: true,
                            selectionFiles: files,
                          ),
                        ),
                      );
                      if (result != null) {
                        // ignore: use_build_context_synchronously
                        setState(() {
                          files = result.selectionFiles;
                          allSlips = result.files;
                        });
                      }
                    },
                    child: Text(
                      'editProc.addImage'.tr(
                        'Add Image${fileCountText}',
                      ),
                    ),
                  ),
                ],
              ),
              if (_shouldShowConfirmCheckbox) ...[
                SizedBox(height: 8),
                Row(
                  children: [
                    Checkbox(
                      value: _isConfirmChecked,
                      onChanged: (value) {
                        setState(() {
                          _isConfirmChecked = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Text(
                        'editProc.confirmPrice'.tr(
                          'Received price is different from ordered price',
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
              ]
            ],
          ),
        ),
        WStickyBottomCta(
          isEnabled: _shouldShowConfirmCheckbox
              ? _isConfirmChecked
              : amount.isNotEmpty,
          icon: Icons.check,
          label: Text('Submit'),
          onPressed: () {
            context.pop({
              'amount': amount,
              'allSlips': allSlips,
              'selectedFiles': files,
            });
          },
        ),
      ],
    );
  }
}
