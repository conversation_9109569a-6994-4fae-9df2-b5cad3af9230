import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class DispatchTripUseCase {
  DispatchTripUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, String>> call({
    required int tripId,
    required String driverName,
    required String driverPhone,
    required String vehicleNumber,
    required int smoId,
  }) async {
    return _mandiRepository.dispatchTrip(
      tripId: tripId,
      driverName: driverName,
      driverPhone: driverPhone,
      vehicleNumber: vehicleNumber,
      smoId: smoId,
    );
  }
}
