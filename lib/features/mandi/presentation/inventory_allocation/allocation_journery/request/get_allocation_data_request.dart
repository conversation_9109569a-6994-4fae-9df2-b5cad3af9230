import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/total_allocation_popup.dart';

class GetAllocationDataRequest
    extends BaseRequest<dynamic, List<AllocationSkuItem>> {
  final int allocationId;

  GetAllocationDataRequest({
    required this.allocationId,
  });

  @override
  String getPath() => 'allotments/$allocationId/items';

  @override
  List<AllocationSkuItem> mapper(data) {
    if (data is List<dynamic>) {
      List<AllocationSkuItem> list = [];
      data.map((e) => e as Map<String, dynamic>).forEach((e) {
        try {
          double expectedQty = e['expectedQuantity'] ?? 0;
          final skuQuantity = SkuQuantity.fromJson(e['skuQuantity']);
          final mandiQty = skuQuantity.quantity! - expectedQty;
          final allocationQty = AllocationQty.fromMandiAndExpected(
            mandiQty: mandiQty.asString(),
            expectedQty: expectedQty.asString(),
            actualMandiQty: mandiQty,
          );
          list.add(
            AllocationSkuItem(
              id: e['id'],
              skuName: '',
              skuImage: '',
              skuQuantity: skuQuantity,
              orderedQty: null,
              mandiQty: 0,
              distributionStatus: e['distributionStatus'] == null
                  ? 'PENDING'
                  : e['distributionStatus'],
              backendAllocationQty: allocationQty,
              updateAllocationQty: allocationQty,
              isManuallyEdited: e['isManuallyEdited'] ?? false,
              distributionQuantity: e['distributionQuantity'] as double?,
              groupKey: '',
            ),
          );
        } catch (e, s) {
          talker.handle(e, s);
        }
      });
      return list;
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
