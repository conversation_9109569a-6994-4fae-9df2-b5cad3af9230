import 'package:chopper/chopper.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/data/network/util/network_constants.dart';

part 'auth_service.chopper.dart';

@ChopperApi()
@injectable
abstract class AuthService extends ChopperService {
  @factoryMethod
  static AuthService create([ChopperClient? client]) => _$AuthService(client);

  @Post(path: 'login', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> login(
    @Field('idToken') String idToken,
  );

  @Post(path: 'auth/sendOtp', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> sendOtp(
    @Field('mobile') String mobile,
    @Field('action') String action,
  );

  @Post(path: 'auth/verifyOtp', headers: {NetworkConstants.noAuthKey: ''})
  Future<Response<Map<String, dynamic>>> verifyOtp(
    @Field('otp') int otp,
    @Field('mobile') String mobile,
  );

  @Post(path: 'auth/getCustomFirebaseToken')
  Future<Response<Map<String, dynamic>>> getFirebaseCustomToken(
    @Header('Authorization') String bearerToken,
  );
}
