part of 'liquidation_cubit.dart';

@freezed
class LiquidationState with _$LiquidationState {
  const factory LiquidationState.initial({
    @Default(null) List<LiquidationOrder>? activeOrders,
    @Default(null) List<LiquidationOrder>? pastOrders,
    @Default(null) String? activeOrdersError,
    @Default(null) String? pastOrdersError,
    @Default(false) bool activeOrdersLoading,
    @Default(false) bool pastOrdersLoading,
  }) = _Initial;
}
