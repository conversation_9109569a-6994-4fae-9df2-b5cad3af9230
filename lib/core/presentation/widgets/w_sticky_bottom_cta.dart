import 'package:flutter/material.dart';

class WStickyBottomCta extends StatelessWidget {
  const WStickyBottomCta({
    super.key,
    required this.icon,
    required this.label,
    required this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.color = null,
  });
  final IconData icon;
  final Widget label;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      color: color ?? Colors.grey[100],
      child: Padding(
        padding: const EdgeInsets.only(
          left: 16,
          right: 16,
          top: 16,
          bottom: 16,
        ),
        child: ElevatedButton.icon(
          onPressed: isEnabled ? onPressed : null,
          icon: isLoading ? Container() : Icon(icon),
          label: isLoading
              ? SizedBox(
                  height: 24,
                  width: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                )
              : label,
          style: ElevatedButton.styleFrom(
            minimumSize: Size(
              MediaQuery.of(context).size.width - 32,
              48,
            ),
          ),
        ),
      ),
    );
  }
}
