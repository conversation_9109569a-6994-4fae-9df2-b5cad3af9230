import os
import sys
import re
import json

def write_dict_to_json(data):
    with open('lang_export.json', 'w') as file:
        json.dump(transform_dict(data), file)
    merge_json_files('lang_export.json', 'backend_lang.json', 'new_backend_lang.json')

def transform_dict(input_dict):
    output_dict = {}
    for key, value in input_dict.items():
        keys = key.split('.')
        current_dict = output_dict
        for sub_key in keys[:-1]:
            if not isinstance(current_dict.get(sub_key, {}), dict):
                current_dict[sub_key] = {}
            current_dict = current_dict.setdefault(sub_key, {})
        current_dict[keys[-1]] = value
    return output_dict

def merge_json_files(file1, file2, output_file):
    with open(file1, 'r') as f1, open(file2, 'r') as f2:
        data1 = json.load(f1)
        data2 = json.load(f2)

    def merge(d1, d2):
        for key, value in d2.items():
            if key in d1 and isinstance(d1[key], dict) and isinstance(value, dict):
                merge(d1[key], value)
            else:
                d1[key] = value

    merge(data1, data2)

    with open(output_file, 'w') as f3:
        json.dump(data1, f3)

def search_langtext_invocations(directory):
    langtext_invocation_pattern = r'LangText\s*\(\s*[\'|"]([^"\']*)[\'|"],\s*[\'|"]([^"\']*)[\'|"]'
    langtext_invocation_pattern2 = r'getLangText\s*\(\s*[\'|"]([^"\']*)[\'|"],\s*[\'|"]([^"\']*)[\'|"]'
    langtext_invocation_pattern3 = r'[\'|"]([^"\']*)[\'|"]\.tr\s*\(\s*[\'|"]([^"\']*)[\'|"]'

    d = {}

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                with open(file_path, 'r') as f:
                    content = f.read()
                    matches = re.findall(langtext_invocation_pattern, content, re.DOTALL)
                    matches2 = re.findall(langtext_invocation_pattern2, content, re.DOTALL)
                    matches3 = re.findall(langtext_invocation_pattern3, content, re.DOTALL)

                    for match in matches:
                        d[match[0]] = match[1]
                    for match in matches2:
                        d[match[0]] = match[1]
                    for match in matches3:
                        d[match[0]] = match[1]


    write_dict_to_json(d)

# Get directory path from command-line argument
if len(sys.argv) < 2:
    print('Please provide the directory path as a command-line argument.')
    sys.exit(1)

directory_path = sys.argv[1]

# Call the function to search for LangText invocations
search_langtext_invocations(directory_path)
