import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_item.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_item_row.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_machine_controller.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/row_item_value.dart';

class WeighingCapturePopupBase extends StatefulWidget {
  const WeighingCapturePopupBase({
    super.key,
    required this.title,
    this.headers,
    required this.initalWeight,
    required this.allowManualEdit,
    required this.inputFormatters,
    required this.enableWeighingMachine,
    this.onTotalWeightChanged,
  });
  final String title;
  final List<Widget>? headers;
  final double? initalWeight;
  final bool allowManualEdit;
  final List<FilteringTextInputFormatter> inputFormatters;
  final bool enableWeighingMachine;
  final void Function(WeighingQuantity)? onTotalWeightChanged;

  @override
  State<WeighingCapturePopupBase> createState() =>
      _WeighingCapturePopupBaseState();
}

class _WeighingCapturePopupBaseState extends State<WeighingCapturePopupBase> {
  List<WeightItem> weights = [];
  WeighingQuantity totalWeight = WeighingQuantity.manual('0');
  String? errorMessage;
  StreamSubscription<double>? subscription;

  bool get enableWeighingMachine => !kIsWeb && widget.enableWeighingMachine;

  void addWeight(WeightItemType type) {
    if (weights.last.weightDouble == 0) {
      setState(() {
        errorMessage =
            'emptyQuantityMessage'.tr('Please fill the previous quantity!');
      });
      return;
    }
    weights.add(WeightItem(
      weight: '',
      type: type,
    ));
    setState(() {
      weights = [...weights];
      onWeightChanged();
    });
  }

  void removeWeight(int index) {
    weights.removeAt(index);
    setState(() {
      weights = [...weights];
      onWeightChanged();
    });
  }

  void updateWeight(int index, String weight) {
    weights[index] = weights[index].copyWith(weight: weight);
    setState(() {
      weights = [...weights];
      onWeightChanged();
    });
    totalWeight = WeighingQuantity.manual(totalWeight.value);
    if (widget.onTotalWeightChanged != null) {
      widget.onTotalWeightChanged!(totalWeight);
    }
  }

  void onWeightChanged() {
    double total = 0;
    weights.forEach((element) {
      if (element.type == WeightItemType.reduce) {
        total -= element.weightDouble;
      } else {
        total += element.weightDouble;
      }
    });
    totalWeight = totalWeight.copyWith(value: total.asString());
    errorMessage = null;
  }

  @override
  void initState() {
    final weight = widget.initalWeight ?? 0;
    weights.add(WeightItem(
      weight: weight > 0 ? weight.asString() : '',
      type: WeightItemType.none,
    ));
    onWeightChanged();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (enableWeighingMachine) {
        try {
          addWeighingControlListener();
        } catch (_) {}
      }
    });
    super.initState();
  }

  void addWeighingControlListener() {
    subscription?.cancel();
    final controller = context.read<WeighingMachineController>();
    if (!controller.isConnected) return;
    subscription = controller.connectedStream?.listen((event) {
      if (event < 0) return;
      setState(() {
        weights.last = weights.last.copyWith(weight: event.asString());
        weights = [...weights];
        totalWeight = WeighingQuantity.weighingMachine(totalWeight.value);
        onWeightChanged();
      });
    });
  }

  @override
  void dispose() {
    subscription?.cancel();
    subscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WeighingMachineController weighingMachineController =
        context.watch<WeighingMachineController>();
    return Popup(
      height: 0.7,
      title: widget.title,
      children: [
        if (enableWeighingMachine) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 16,
              ),
              Text(
                'weighingMachineStatus'.tr('Weighing Scale Status'),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  fontStyle: FontStyle.italic,
                ),
              ),
              Spacer(),
              Container(
                margin: EdgeInsets.only(right: 16),
                decoration: BoxDecoration(
                    color: Colors.grey.shade200, shape: BoxShape.circle),
                child: IconButton(
                  onPressed: () async {
                    await context
                        .push(context.namedLocation('weighingMachine'));
                    addWeighingControlListener();
                  },
                  icon: weighingMachineController.isConnected
                      ? Icon(
                          Icons.bluetooth_connected,
                          color: Colors.blue,
                        )
                      : Icon(
                          Icons.bluetooth_disabled,
                          color: Colors.grey.shade600,
                        ),
                  style: IconButton.styleFrom(
                    foregroundColor: Colors.blue,
                  ),
                ),
              ),
            ],
          ),
          Divider(),
        ],
        Expanded(
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            shrinkWrap: true,
            children: [
              if (widget.headers != null) ...[
                ...widget.headers!,
                const Divider(
                  height: 16,
                ),
              ],
              Text(
                'weights'.tr('Weights'),
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(
                height: 4,
              ),
              ...List.generate(
                weights.length,
                (index) => WeightItemRow(
                  canDelete: index != 0,
                  isActive: index == weights.length - 1,
                  canEdit: widget.allowManualEdit,
                  type: weights[index].type,
                  weight: weights[index].weight,
                  onDelete: () {
                    removeWeight(index);
                  },
                  onWeightChanged: (value) {
                    if (weights[index].weight == value) return;
                    updateWeight(index, value);
                  },
                  inputFormatters: widget.inputFormatters,
                  showCaptureButton: enableWeighingMachine &&
                      weighingMachineController.showCaptureButton,
                  hasError: errorMessage != null && index == weights.length - 1,
                  onCapture: () {
                    weighingMachineController.captureWeight();
                  },
                ),
              ),
              Divider(
                height: 32,
              ),
              if (errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    errorMessage!,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                    ),
                  ),
                ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: () {
                      addWeight(WeightItemType.reduce);
                    },
                    icon: Icon(
                      Icons.remove,
                      color: Colors.red,
                    ),
                    style: IconButton.styleFrom(
                      foregroundColor: Colors.blue,
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  IconButton(
                    onPressed: () {
                      addWeight(WeightItemType.added);
                    },
                    icon: Icon(
                      Icons.add,
                      color: Colors.green,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
        Divider(
          height: 16,
        ),
        if (!totalWeight.isPositive)
          Padding(
            padding: const EdgeInsets.only(
              bottom: 8.0,
              left: 16,
              right: 16,
            ),
            child: Center(
              child: Text(
                'totalWeightLessThanZeroError'
                    .tr('Total weight can not be less than 0'),
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        RowItemValue(
          label: 'total'.tr('Total'),
          value: totalWeight.value ?? '',
          padding: EdgeInsets.symmetric(
            horizontal: 16,
          ),
        ),
        SizedBox(
          height: 12,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: const <BoxShadow>[
              BoxShadow(
                color: Colors.black54,
                blurRadius: 10,
                offset: Offset(0, 0.75),
              )
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    context.pop();
                  },
                  icon: Icon(
                    Icons.close,
                    color: Colors.red,
                  ),
                  label: LangText(
                    'cancel',
                    'Cancel',
                    style: TextStyle(
                      color: Colors.red,
                    ),
                  ),
                  style:
                      ElevatedButton.styleFrom(backgroundColor: Colors.white),
                ),
              ),
              SizedBox(
                width: 16,
              ),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: !totalWeight.isPositive
                      ? null
                      : () {
                          context.pop(totalWeight);
                        },
                  icon: Icon(
                    Icons.check,
                    color: Colors.white,
                  ),
                  label: LangText(
                    'update',
                    'Update',
                    style: TextStyle(
                      color: Colors.white,
                    ),
                  ),
                  style:
                      ElevatedButton.styleFrom(backgroundColor: Colors.green),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
