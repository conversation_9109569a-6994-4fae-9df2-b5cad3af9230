import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

class SubmitReturnsRequest extends BaseRequest<dynamic, String> {
  final String returnOrderId;
  final List<ReturnItem> returnItems;
  final int smoId;
  final DriversDetails? driversDetails;
  final String returnType;
  final bool isWastageReturns;
  final int? createdAt;

  SubmitReturnsRequest({
    required this.returnOrderId,
    required this.returnItems,
    required this.smoId,
    this.driversDetails,
    required this.returnType,
    required this.isWastageReturns,
    required this.createdAt,
  });

  @override
  Map<String, dynamic> getBody() {
    return {
      'createdAt': createdAt,
      'inventoryType': isWastageReturns ? 'WASTAGE' : 'INVENTORY',
      'returnOrderId': returnOrderId,
      'smoId': smoId,
      'status': 'ACCEPTED',
      "driversDetails": {
        "driverName": driversDetails?.driverName,
        "driverMobile": driversDetails?.driverMobile,
        "vehicleNumber": driversDetails?.vehicleNumber,
      },
      'returnType': returnType,
      'itemsReturned': returnItems
          .map(
            (e) => {
              'skuId': e.skuQuantity.skuId,
              'qty': e.skuQuantity.quantity,
              "unit": e.skuQuantity.unit,
              "type": e.skuQuantity.type,
              "lotSize": e.skuQuantity.lotSize,
              "acceptedQuantity": e.acceptedQuantity.toDouble(),
              "weighingSource": e.weighingSource,
            },
          )
          .toList(),
    };
  }

  @override
  String getPath() => 'returns/submitReturnOrder';

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
