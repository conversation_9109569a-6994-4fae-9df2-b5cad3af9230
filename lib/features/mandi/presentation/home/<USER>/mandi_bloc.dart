import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/domain/use_case/get_all_mandis_usecase.dart';

part 'mandi_bloc.freezed.dart';
part 'mandi_event.dart';
part 'mandi_state.dart';

@injectable
class MandiBloc extends Bloc<MandiEvent, MandiState> {
  MandiBloc(this._mandiRepository, this._getAllMandisUseCase)
      : super(const MandiState.loading()) {
    on<MandiEvent>((event, emit) async {
      await event.when(
        start: () async {
          emit(const MandiState.loading());
          final allMandiFuture = _getAllMandisUseCase().then((result) {
            result.fold(
              (error) => emit(
                MandiState.success(
                  error: error,
                  allMandis: [],
                ),
              ),
              (list) => emit(
                state.maybeMap(
                  orElse: () => MandiState.success(
                    allMandis: list,
                  ),
                  success: (st) => st.copyWith(allMandis: list),
                ),
              ),
            );
          });

          await Future.wait([allMandiFuture]);
        },
        clearData: () {
          emit(const MandiState.loading());
        },
        getOps: (String status, bool forceRefresh) async {
          final currentState = state;
          final isLiveOps = status == 'current';
          if (currentState is MandiSuccess) {
            final shouldLoad = forceRefresh ||
                isLiveOps && currentState.liveOps == null ||
                !isLiveOps && currentState.pendingOps == null;
            if (shouldLoad) {
              final result = await _mandiRepository.getSmoOps(status);
              final newState = result.fold(
                  (left) => currentState
                      .copyWith(error: left, liveOps: [], pendingOps: []),
                  (right) => isLiveOps
                      ? currentState.copyWith(liveOps: right)
                      : currentState.copyWith(pendingOps: right));
              emit(newState);
            }
          }
        },
        clearError: () {
          final currentState = state;
          if (currentState is MandiSuccess) {
            emit(currentState.copyWith(error: null));
          }
        },
      );
    });
  }

  final GetAllMandisUseCase _getAllMandisUseCase;
  final MandiRepository _mandiRepository;
}
