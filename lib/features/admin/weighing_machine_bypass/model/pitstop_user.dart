class PitstopUser {
  final int id;
  final int smoId;
  final UserData user;
  final String status;
  final int? checkedInAt;
  final int? checkedOutAt;
  final int? siftExpiresAt;
  final bool hasWeighingMachineByPass;

  String get userName => '${user.firstName} ${user.lastName}'.trim();

  PitstopUser({
    required this.id,
    required this.smoId,
    required this.user,
    required this.status,
    required this.checkedInAt,
    required this.checkedOutAt,
    required this.siftExpiresAt,
    required this.hasWeighingMachineByPass,
  });

  PitstopUser copyWith({
    int? id,
    int? smoId,
    UserData? user,
    String? status,
    int? checkedInAt,
    int? checkedOutAt,
    int? siftExpiresAt,
    bool? hasWeighingMachineByPass,
  }) {
    return PitstopUser(
      id: id ?? this.id,
      smoId: smoId ?? this.smoId,
      user: user ?? this.user,
      status: status ?? this.status,
      checkedInAt: checkedInAt ?? this.checkedInAt,
      checkedOutAt: checkedOutAt ?? this.checkedOutAt,
      siftExpiresAt: siftExpiresAt ?? this.siftExpiresAt,
      hasWeighingMachineByPass:
          hasWeighingMachineByPass ?? this.hasWeighingMachineByPass,
    );
  }

  factory PitstopUser.fromMap(Map<String, dynamic> map) {
    return PitstopUser(
      id: map['id'],
      smoId: map['smoId'],
      user: UserData.fromMap(map['user']),
      status: map['status'],
      checkedInAt: map['checkedInAt'],
      checkedOutAt: map['checkedOutAt'],
      siftExpiresAt: map['siftExpiresAt'],
      hasWeighingMachineByPass: map['hasWeighingMachineByPass'],
    );
  }
}

class UserData {
  final int id;
  final String firstName;
  final String lastName;
  final String? mobileNumber;
  final String status;
  final String? email;
  final String userType;

  UserData({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.mobileNumber,
    required this.status,
    required this.email,
    required this.userType,
  });

  UserData copyWith({
    int? id,
    String? firstName,
    String? lastName,
    String? mobileNumber,
    String? status,
    String? email,
    String? userType,
  }) {
    return UserData(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      status: status ?? this.status,
      email: email ?? this.email,
      userType: userType ?? this.userType,
    );
  }

  factory UserData.fromMap(Map<String, dynamic> map) {
    return UserData(
      id: map['id'],
      firstName: map['firstName'],
      lastName: map['lastName'],
      mobileNumber: map['mobileNumber'],
      status: map['status'],
      email: map['email'],
      userType: map['userType'],
    );
  }
}
