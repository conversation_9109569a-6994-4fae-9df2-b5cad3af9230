import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';

class GetLiquidationOrderByIdRequest
    extends BaseRequest<dynamic, LiquidationOrder> {
  final int itemId;

  GetLiquidationOrderByIdRequest({required this.itemId});

  @override
  String getPath() => 'liquidation/item-liquidation-details';

  @override
  Map<String, dynamic>? getQuery() {
    return {
      'itemId': itemId,
    };
  }

  @override
  LiquidationOrder mapper(data) {
    if (data is Map<String, dynamic>) return LiquidationOrder.fromJson(data);
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
