import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/features/mandi/domain/use_case/add_charges_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_charges_type_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_field_charges/input_models/charge_input_model.dart';

part 'add_field_charge_bloc.freezed.dart';
part 'add_field_charge_event.dart';
part 'add_field_charge_state.dart';

@injectable
class AddFieldChargeBloc
    extends Bloc<AddFieldChargeEvent, AddFieldChargeState> {
  AddFieldChargeBloc(this._getChargesTypeUseCase, this._addChargesUseCase)
      : super(const AddFieldChargeState.initial()) {
    on<AddFieldChargeEvent>((event, emit) async {
      final isSubmitting = state.maybeMap(
        orElse: () => false,
        input: (input) => input.isSubmitLoading,
      );
      if (isSubmitting) return;
      await event.map(
        fetchChargesType: (fetchChargesType) async {
          await _getChargesTypeUseCase().then(
            (value) => value.fold(
              (left) => emit(AddFieldChargeState.error(left)),
              (right) => emit(
                AddFieldChargeState.input(
                  chargeTypes: right.types,
                  charges: [],
                  files: [],
                  isSubmitLoading: false,
                ),
              ),
            ),
          );
        },
        addCharge: (addCharge) async {
          final newState = state.maybeMap(
            orElse: () => state,
            input: (input) {
              return input.copyWith(
                charges: [
                  ...input.charges,
                  ChargeInputModel(
                    chargeType: input.chargeTypes.first,
                    amount: '',
                    comment: '',
                  ),
                ],
              );
            },
          );
          emit(newState);
        },
        removeCharge: (removeCharge) async {
          final newState = state.maybeMap(
            orElse: () => state,
            input: (input) {
              return input.copyWith(
                charges: [
                  ...input.charges,
                ]..removeAt(removeCharge.index),
              );
            },
          );
          emit(newState);
        },
        updateChargeType: (updateChargeType) async {
          final newState = state.maybeMap(
            orElse: () => state,
            input: (input) {
              final charges = List<ChargeInputModel>.from(input.charges);

              charges[updateChargeType.index] =
                  input.charges[updateChargeType.index].copyWith(
                chargeType: updateChargeType.chargeType,
              );
              return input.copyWith(
                charges: [
                  ...charges,
                ],
              );
            },
          );
          emit(newState);
        },
        updateAmount: (updateAmount) async {
          final newState = state.maybeMap(
            orElse: () => state,
            input: (input) {
              final charges = List<ChargeInputModel>.from(input.charges);

              charges[updateAmount.index] =
                  input.charges[updateAmount.index].copyWith(
                amount: updateAmount.amount,
              );
              return input.copyWith(
                charges: [
                  ...charges,
                ],
              );
            },
          );
          emit(newState);
        },
        updateComment: (updateComment) async {
          final newState = state.maybeMap(
            orElse: () => state,
            input: (input) {
              final charges = List<ChargeInputModel>.from(input.charges);

              charges[updateComment.index] =
                  input.charges[updateComment.index].copyWith(
                comment: updateComment.comment,
              );
              return input.copyWith(
                charges: [
                  ...charges,
                ],
              );
            },
          );
          emit(newState);
        },
        submit: (submit) async {
          await state.mapOrNull(
            input: (inputState) async {
              emit(inputState.copyWith(isSubmitLoading: true));
              await _addChargesUseCase(
                      submit.smoId,
                      inputState.files.map((e) => e.uploadKey ?? '').toList(),
                      inputState.charges)
                  .then(
                (value) => value.fold(
                  (left) => emit(
                    inputState.copyWith(
                      isSubmitLoading: false,
                      message: left.message,
                    ),
                  ),
                  (right) => emit(
                    inputState.copyWith(
                      isSubmitLoading: false,
                      message: right,
                      files: [],
                      charges: [],
                    ),
                  ),
                ),
              );
            },
          );
        },
        clearMessage: (_ClearMessage value) {
          emit(
            state.maybeMap(
              orElse: () => state,
              input: (input) => input.copyWith(
                message: null,
              ),
            ),
          );
        },
        addSlips: (_AddSlips value) {
          final currentState = state;
          if (currentState is InputChargeState) {
            emit(
              currentState.copyWith(
                files: value.slips,
              ),
            );
          }
        },
      );
    });
  }

  final GetChargesTypeUseCase _getChargesTypeUseCase;
  final AddChargesUseCase _addChargesUseCase;
}
