import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/admin/procurement_update/cubit/procurement_update_edit_cubit.dart';
import 'package:table_sticky_headers/table_sticky_headers.dart';

class ProcurementUpdateEditScreen extends StatelessWidget {
  const ProcurementUpdateEditScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text('updateProcurement.title'.tr('Update Procurement')),
          centerTitle: false,
        ),
        body: BlocConsumer<ProcurementUpdateEditCubit,
            ProcurementUpdateEditState>(
          listener: (context, state) {
            if (state.message != null) {
              showSnackBar(state.message!);
              context.read<ProcurementUpdateEditCubit>().clearMessage();
            }
          },
          builder: (context, state) {
            if (state.isLoading) {
              return Center(
                child: SizedBox(
                  child: CircularProgressIndicator(),
                  height: 48,
                  width: 48,
                ),
              );
            }
            final table = state.procTable;
            if (table == null) {
              return ErrorScreen(
                message: 'updateProcurement.errorLoadingChildOrders'
                    .tr('Error while loading child orders. Please try again.'),
                onPressed: () {
                  context
                      .read<ProcurementUpdateEditCubit>()
                      .loadOrder(parentId: state.procurementOrder!.id);
                },
              );
            }
            final order = state.procurementOrder;
            return Container(
              child: Column(
                children: [
                  if (order != null)
                    Card(
                      elevation: 0,
                      color: Colors.grey[300],
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                order.createdAt.toDate('dd MMM yyyy | hh:mm a'),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 16,
                            ),
                            Expanded(
                              child: Text(
                                order.createdBy ?? '-',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 16,
                            ),
                            Expanded(
                              child: Text(order.vendor?.displayText ?? '-',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                  )),
                            ),
                            SizedBox(
                              width: 16,
                            ),
                            Expanded(
                              child: Text(
                                order.pitstops
                                    .map((e) =>
                                        state.mandis[e]?.displayText ?? '-')
                                    .join(', '),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 16,
                            ),
                            Expanded(
                              child: Text(
                                order.skus
                                    .map((e) => state.skuMap[e]?.name ?? '-')
                                    .join(', '),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 16,
                            ),
                            Expanded(
                              child: Text(
                                order.status,
                                maxLines: 1,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  Expanded(
                    child: StickyHeadersTable(
                      columnsLength: table.getColumnLength(),
                      rowsLength: table.getRowLength(),
                      columnsTitleBuilder: (i) => Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          border: Border(
                            top: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                            left: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                            right: i == table.getColumnLength() - 1
                                ? BorderSide(
                                    color: Colors.grey.shade300,
                                    width: 1,
                                  )
                                : BorderSide.none,
                          ),
                        ),
                        child: table.getColumnWidget(context, i),
                      ),
                      cellDimensions: CellDimensions.fixed(
                        contentCellWidth: 200,
                        contentCellHeight: 80,
                        stickyLegendWidth: 200,
                        stickyLegendHeight: 120,
                      ),
                      showHorizontalScrollbar: true,
                      showVerticalScrollbar: true,
                      rowsTitleBuilder: (i) => Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          border: Border(
                            top: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                            left: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                            bottom: i == table.getRowLength() - 1
                                ? BorderSide(
                                    color: Colors.grey.shade300,
                                    width: 1,
                                  )
                                : BorderSide.none,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            table.getRowName(i),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      contentCellBuilder: (colIndex, rowIndex) => Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border(
                            top: BorderSide(
                              color: Colors.grey.shade200,
                              width: 1,
                            ),
                            left: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                            right: colIndex == table.getColumnLength() - 1
                                ? BorderSide(
                                    color: Colors.grey.shade300,
                                    width: 1,
                                  )
                                : BorderSide.none,
                            bottom: rowIndex == table.getRowLength() - 1
                                ? BorderSide(
                                    color: Colors.grey.shade300,
                                    width: 1,
                                  )
                                : BorderSide.none,
                          ),
                        ),
                        child: Center(
                          child: table.getCellWidget(
                            columnIndex: colIndex,
                            rowIndex: rowIndex,
                            focusedItemId: state.focusItemId,
                            onFoucsedItemIdChanged: (id) {
                              context
                                  .read<ProcurementUpdateEditCubit>()
                                  .setFocusItemId(id);
                            },
                            showBottomBorder:
                                rowIndex == table.getRowLength() - 1,
                            showRightBorder:
                                colIndex == table.getColumnLength() - 1,
                            onChanged: (value) {
                              context
                                  .read<ProcurementUpdateEditCubit>()
                                  .updateSkuOrderedQuantity(
                                    columnIndex: colIndex,
                                    rowIndex: rowIndex,
                                    value: value,
                                  );
                            },
                          ),
                        ),
                      ),
                      legendCell: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          border: Border(
                            top: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                            left: BorderSide(
                              color: Colors.grey.shade300,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            'pitstops'.tr('Pitstops'),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  WStickyBottomCta(
                    isEnabled: !state.isCtaLoading && table.hasChanges,
                    isLoading: state.isCtaLoading,
                    icon: Icons.check,
                    label: Text('update'.tr('Update')),
                    onPressed: () async {
                      final shouldProceed = await context.showAlertDialog(
                              title: 'procurementUpdate.submitTitle'
                                  .tr('Are you sure?'),
                              message: 'procurementUpdate.submitMessage'
                                  .tr('You want to submit the changes?')) ??
                          false;
                      if (shouldProceed) {
                        await context
                            .read<ProcurementUpdateEditCubit>()
                            .submitChanges();
                      }
                    },
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
