import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/sku_quantity_dto.dart';

part 'supply_order_input.freezed.dart';

@freezed
class Terminal with _$Terminal {
  const factory Terminal({
    required String terminalName,
    required List<SupplyOrderSku> skus,
    required bool isEditAllowed,
  }) = _Terminal;
}

@freezed
class SupplyOrderSku with _$SupplyOrderSku {
  const SupplyOrderSku._();
  const factory SupplyOrderSku({
    required SkuQuantityDto skuQuantity,
    required String skuName,
    required String quantity,
    @Default('') String initialValue,
    @Default(false) bool isChecked, // Only valid for the first one
    @Default(false) bool wasOrdered,
  }) = _SupplyOrderSku;

  bool get hasChanged => initialValue != quantity;
}
