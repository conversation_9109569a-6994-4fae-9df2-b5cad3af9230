import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/sku_distribution_item.dart';

part 'terminal_distribution.freezed.dart';

@freezed
class TerminalDistribution with _$TerminalDistribution {
  const TerminalDistribution._();
  const factory TerminalDistribution({
    required String skuName,
    required List<List<SkuDistributionItem>> distributionInfo,
    required String wastage,
    required double totalAllocated,
    required double mandiQty,
    required double expectationQty,
    required SkuQuantity skuQuantity,
  }) = _TerminalDistribution;

  String get skuQuantityTitle =>
      skuName + ' - ' + skuQuantity.unit + ' - ' + skuQuantity.type;

  bool isEmptyOrderedAndDistributed() {
    return distributionInfo.isEmpty && totalAllocated == 0;
  }

  double get allocatedMandiQty => totalAllocated - expectationQty;

  List<SkuDistributionItem> get allDistributionItems =>
      distributionInfo.expand((e) => e).toList();

  bool get isAnyNotDistributed => allDistributionItems.any((element) =>
      element.id != -1 &&
      (element.weighingQuantity?.value == null ||
          element.weighingQuantity!.value!.trim().isEmpty));

  double get totalAllocatingNow => (allDistributionItems.fold(
        0.0,
        (p, e) => p + e.quantity,
      )).asString().toDouble();

  double get wastageAndAllocation =>
      (totalAllocatingNow + wastage.toDouble().asString().toDouble())
          .asString()
          .toDouble();

  double get leftToAllocate => totalAllocated - wastageAndAllocation;

  double maxAllowedForAllocatedMandiQty(double maxExcessPercent) {
    return ((allocatedMandiQty * (1 + (maxExcessPercent / 100.0)))
        // +expectationQty
        )
        .asString()
        .toDouble();
  }

  double maxAllowedForTotalMandiQty(double maxExcessPercent) {
    return ((mandiQty * (1 + (maxExcessPercent / 100.0)))
        // + expectationQty
        )
        .asString()
        .toDouble();
  }

  // Returns updated TerminalDistribution if error with error message
  // Returns null if no error
  TerminalDistribution? isValidMinMax() {
    bool hasError = false;
    final groups = <List<SkuDistributionItem>>[];
    for (final group in distributionInfo) {
      final updatedGroup = group.map((e) {
        if (e.id == -1) {
          return e;
        } else {
          final quantity = e.quantity;
          final targetQuantity = e.skuQuantity.quantity;
          if (targetQuantity == null) return e;
          if (e.variation == null) return e;
          final isWithingRange = e.variation!
              .validate(targetValue: targetQuantity, valueToCheck: quantity);
          if (isWithingRange) {
            return e;
          } else {
            hasError = true;
            return e.copyWith(
                errorMessage:
                    e.variation!.getRangeMessage(targetValue: targetQuantity));
          }
        }
      });
      groups.add(updatedGroup.toList());
    }

    return hasError ? copyWith(distributionInfo: groups) : null;
  }
}
