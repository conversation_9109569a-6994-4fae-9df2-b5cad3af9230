import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/sku_quantity_dto.dart';

part 'alloted_item_dto.freezed.dart';
part 'alloted_item_dto.g.dart';

@freezed
class AllotedItemDto with _$AllotedItemDto {
  const factory AllotedItemDto({
    required int id,
    required int allotmentId,
    required double availableInventory,
    required SkuQuantityDto skuQuantity,
  }) = _AllotedItemDto;

  @override
  factory AllotedItemDto.fromJson(Map<String, dynamic> json) =>
      _$AllotedItemDtoFromJson(json);
}
