import 'dart:async';

import 'package:flutter/material.dart';

class DelayedButton extends StatefulWidget {
  final Widget label;
  final int delayInSeconds;
  final void Function() onPressed;

  const DelayedButton({
    Key? key,
    required this.label,
    required this.delayInSeconds,
    required this.onPressed,
  }) : super(key: key);

  @override
  _DelayedButtonState createState() => _DelayedButtonState();
}

class _DelayedButtonState extends State<DelayedButton> {
  bool _isButtonEnabled = false;
  double _progress = 0;
  Timer? _timer;
  late int delayInMilliSeconds;

  @override
  void initState() {
    super.initState();
    delayInMilliSeconds = widget.delayInSeconds * 1000;
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(milliseconds: 16), (timer) {
      setState(() {
        _progress += 16 / delayInMilliSeconds;
        if (_progress >= 1) {
          _isButtonEnabled = true;
          _timer?.cancel();
        }
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _isButtonEnabled ? widget.onPressed : null,
      child: Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 8,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Row(
                children: [
                  Expanded(
                      flex: (_progress * 1000).toInt(),
                      child: Container(
                        color: Colors.green,
                        height: 46,
                      )),
                  Expanded(
                      flex: ((1 - _progress) * 1000).toInt(),
                      child: Container(
                        color: Colors.grey.shade200,
                        height: 46,
                      )),
                ],
              ),
              widget.label,
            ],
          ),
        ),
      ),
    );
  }
}
