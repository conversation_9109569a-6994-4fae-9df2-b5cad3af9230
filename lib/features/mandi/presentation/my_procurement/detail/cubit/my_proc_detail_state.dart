import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';

part 'my_proc_detail_state.freezed.dart';

@freezed
abstract class MyProcDetailState with _$MyProcDetailState {
  const factory MyProcDetailState.initial() = MyProcDetailInitial;

  const factory MyProcDetailState.loading() = MyProcDetailLoading;

  const factory MyProcDetailState.loaded(List<ProcDetail> myProcDetailList) =
      MyProcDetailLoaded;

  const factory MyProcDetailState.error(String errorMessage) =
      MyProcDetailError;
}
