import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/base_view_controller.dart';
import 'dart:developer';

import 'package:proc2/features/auth/domain/repository/auth_repository.dart';

class OTPLoginController extends BaseViewController {
  String _phone = kDebugMode ? "9019301344" : "";
  int? _otp;
  bool _codeSent = false;
  bool _canResendOTP = false;
  final Duration otpWatchDuration = const Duration(minutes: 1);
  String? _verificationId;
  int? _resendToken;
  final Stopwatch _otpSentStopwatch = Stopwatch();
  final FirebaseAuth fireAuth = FirebaseAuth.instance;

  String get phone => _phone;
  int? get otp => _otp;
  bool get codeSent => _codeSent;
  bool get canResendOTP => _canResendOTP;
  Stopwatch get otpSentStopwatch => _otpSentStopwatch;

  String get phoneWithCountryCode =>
      _phone.startsWith('+91') ? _phone : '+91$_phone';

  int get resendInSeconds =>
      (otpWatchDuration.inSeconds - _otpSentStopwatch.elapsed.inSeconds) < 0
          ? 0
          : (otpWatchDuration.inSeconds - _otpSentStopwatch.elapsed.inSeconds);

  void setPhone(String val) {
    _phone = val;
    notifyListeners();
  }

  void setOTP(String otp) {
    _otp = int.tryParse(otp);
    notifyListeners();
  }

  void clear() {
    _phone = "";
    _codeSent = false;
    _otp = null;
    _verificationId = null;
    _canResendOTP = false;
    //_resendToken = null;
    setLoading(false);
    setErrorMessage(null);
  }

  void changePhoneNumber() async {
    clear();
  }

  void resendOTP() async {
    if (resendInSeconds > 0) {
      return;
    }
    await sendOtp(isResend: true);
    // verifyPhoneNumber(forceResendingToken: _resendToken);
  }

  Future<bool?> _userLogin() async {
    final user = fireAuth.currentUser;
    String? idToken = await user?.getIdToken();
    if (user == null) {
      setErrorMessage('invalidUser'.tr('Invalid User'));
      return null;
    }
    if (idToken == null) {
      setErrorMessage('loginFailed'.tr('Login Failed'));
      return null;
    }
    AuthRepository authRepository = di.get();
    final result = await authRepository.login(idToken: idToken);
    return result.isRight;
  }

  Future<void> userLogin() async {
    bool? success = await _userLogin();

    if (success == null || success == false) {
      clear();
      await fireAuth.signOut();
      throw Exception('wheelocityFailedLogin'.tr('Login Failed by Wheelocity'));
    }
  }

  // Future<bool> verifyOTP({PhoneAuthCredential? credential}) async {
  //   if (_verificationId == null) {
  //     setErrorMessage("waitingForOTPToBeSent".tr("Waiting For OTP To be sent"));
  //     return false;
  //   }
  //   if (credential == null && _verificationId == null) {
  //     setErrorMessage('failedOTPVerification'.tr("Failed OTP Verification"));

  //     return false;
  //   }
  //   if (_otp.length != 6) {
  //     setErrorMessage('enter6DigitsOTP'.tr('Enter Six Digits OTP'));

  //     return false;
  //   }
  //   setLoading(true);
  //   setErrorMessage(null);
  //   try {
  //     print("Trying to Sign in with otp");
  //     try {
  //       await fireAuth.signInWithCredential(credential ??
  //           PhoneAuthProvider.credential(
  //               verificationId: _verificationId!, smsCode: otp));
  //     } on FirebaseAuthException catch (e) {
  //       print(e);
  //       print("error code = ${e.code}");
  //       if (e.code == 'invalid-verification-code') {
  //         throw Exception('invalidOTP'.tr('Invalid OTP'));
  //       }
  //       throw Exception(e);
  //     }

  //     await userLogin();
  //     clear();
  //     print("Signed in with OTP . USer = ${fireAuth.currentUser.toString()}");
  //     setLoading(false);
  //     setErrorMessage(null);
  //     return true;
  //   } catch (e) {
  //     print("Failed to verify OTP. ${e.toString()}");
  //     setLoading(false);

  //     setErrorMessage(e.toString());
  //     return false;
  //   }
  // }

  //   Future<void> verifyPhoneNumber({int? forceResendingToken}) async {
  //   if (_phone.length != 10) {
  //     setLoading(false);
  //     setErrorMessage("pleaseEnter10Digit".tr('Please Enter 10 Digits Number'));
  //     return;
  //   }

  //   try {
  //     LoginBypassConfigInterface loginBypassConfigInterface = di.get();
  //     LoginConfig config = await loginBypassConfigInterface.getConfig();

  //     if (config.checkOTPSkip(phone)) {
  //       await otpSkipLogin(phone);
  //       return;
  //     }

  //     setLoading(true);
  //     setErrorMessage(null);
  //     _otp = "";
  //     _codeSent = false;
  //     forceResendingToken = _resendToken;
  //     notifyListeners();
  //     print(
  //         "Verifing Phone Number. Force Resend Token = $forceResendingToken phone = $phone");

  //     _otpSentStopwatch.reset();
  //     _otpSentStopwatch.start();
  //     await fireAuth
  //         .verifyPhoneNumber(
  //           phoneNumber: '+91 $phone',
  //           forceResendingToken: forceResendingToken,
  //           verificationCompleted: (PhoneAuthCredential credential) async {
  //             print(
  //                 "Auto Verification. Signin method = ${credential.signInMethod} Verification ID = ${credential.verificationId} OTP = ${credential.smsCode}");

  //             verifyOTP(credential: credential);
  //           },
  //           verificationFailed: (FirebaseAuthException e) {
  //             print("Verification Failed. ${e.toString()} ");

  //             setErrorMessage(e.message);
  //             if (e.code == 'invalid-verification-code') {
  //               setErrorMessage('invalidOTP'.tr('Invalid OTP'));
  //             }
  //             setLoading(false);
  //           },
  //           codeSent: (String verificationId, int? resendToken) {
  //             print(
  //                 "Code Sent Succesfully . Verification ID = $verificationId Resend Token = $resendToken");
  //             _codeSent = true;
  //             _verificationId = verificationId;
  //             _resendToken = resendToken;
  //             setLoading(false);
  //             notifyListeners();
  //           },
  //           timeout: otpWatchDuration,
  //           codeAutoRetrievalTimeout: (String verificationId) {
  //             print("Code Auto Retrieval Timed out");
  //           },
  //         )
  //         .onError((error, stackTrace) => print(error));
  //   } catch (e) {
  //     print(e);
  //     setErrorMessage(e.toString());
  //     setLoading(false);
  //   }
  // }

  Future<void> otpSkipLogin(String phone) async {
    setLoading(true);
    setErrorMessage(null);
    try {
      UserCredential credential =
          await FirebaseAuth.instance.signInAnonymously();
      await credential.user?.updateDisplayName("+91$phone");
      await userLogin();
      clear();
    } catch (e) {
      setLoading(false);
      setErrorMessage(e.toString());
      return;
    }
    setLoading(false);
    setErrorMessage(null);
  }

  Future<void> sendOtp({bool isResend = false}) async {
    if (_phone.length != 10) {
      setLoading(false);
      setErrorMessage(
        "pleaseEnter10Digit".tr('Please Enter 10 Digits Number'),
      );
      return;
    }

    try {
      setLoading(true);
      setErrorMessage(null);
      _otp = null;
      _codeSent = false;
      notifyListeners();

      _otpSentStopwatch.reset();
      _otpSentStopwatch.start();

      AuthRepository authRepository = di.get();
      final result = await authRepository.sendOtp(
          mobile: phoneWithCountryCode, isResend: isResend);

      result.fold(
        (error) {
          setErrorMessage(error.message);
          _codeSent = false;
        },
        (data) {
          _codeSent = true;
        },
      );

      setLoading(false);
    } catch (e) {
      print(e);
      setErrorMessage(e.toString());
      setLoading(false);
      _codeSent = false;
    }
  }

  Future<bool> verifyOTP() async {
    setLoading(true);
    setErrorMessage(null);
    try {
      print("Trying to Sign in with otp");
      try {
        if (_otp == null) {
          setErrorMessage("Enter 4 Digit OTP");
          setLoading(false);
          return false;
        }
        AuthRepository authRepository = di.get();
        // Verify OTP
        final result = await authRepository.verifyOtp(
          otp: _otp!,
          mobile: phoneWithCountryCode,
        );

        if (result.isLeft) {
          setErrorMessage(result.left.message);
          setLoading(false);
          return false;
        } else if (result.isRight) {
          // Getting Firebase Custom Token
          final customTokenResponse = await authRepository
              .getFirebaseCustomToken(jwt: result.right.jwt);

          return customTokenResponse.fold(
            (error) {
              setErrorMessage(error.message);
              setLoading(false);
              return false;
            },
            (customToken) async {
              print("Custom Token --->$customToken");
              final userCred =
                  await fireAuth.signInWithCustomToken(customToken);

              final firebaseUser = userCred.user;
              if (firebaseUser != null && firebaseUser.displayName == null) {
                await firebaseUser.updateDisplayName(phoneWithCountryCode);
              }
              clear();
              setLoading(false);
              setErrorMessage(null);
              return true;
            },
          );
        } else {
          return false;
        }
      } on FirebaseAuthException catch (e) {
        print(e);
        print("error code = ${e.code}");
        if (e.code == 'invalid-verification-code') {
          throw Exception('invalidOTP'.tr('Invalid OTP'));
        } else if (e.code == 'session-expired') {
          throw Exception(
            'sessionExpired'.tr(
              'The SMS code has expired. Please resend the verification code.',
            ),
          );
        } else if (e.code == 'unknown' &&
            e.message?.contains('Error code:39') == true) {
          throw Exception(
            'internalError'.tr(
              'An internal error has occurred. Please try again.',
            ),
          );
        }
        throw Exception(e.toString());
      }
    } catch (e) {
      await FirebaseAuth.instance.signOut();
      print("Failed to verify OTP. ${e.toString()}");
      print(e);
      setLoading(false);
      setErrorMessage(e.toString());
      return false;
    }
  }

  void print(dynamic d) {
    log("OTP Login : $d");
  }
}
