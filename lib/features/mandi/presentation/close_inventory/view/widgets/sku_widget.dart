// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:proc2/core/lang/language.dart';
// import 'package:proc2/core/lang/language_enum.dart';
// import 'package:proc2/core/utils/config.dart';
// import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_loss.dart';
// import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
// import 'package:proc2/features/mandi/presentation/add_losses/view/add_losses.dart';
// import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
// import 'package:proc2/features/mandi/presentation/close_inventory/input_model/close_inventory_sku_inp_model.dart';
// import 'package:proc2/features/mandi/util/get_sku_name.dart';

// class SkuWidget extends StatelessWidget {
//   const SkuWidget({
//     super.key,
//     required this.index,
//     required this.isBulk,
//     required this.skuInput,
//   });

//   final int index;
//   final CloseInventorySkuInput skuInput;
//   final bool isBulk;

//   @override
//   Widget build(BuildContext context) {
//     final sku = getSKU(context, skuID: skuInput.inventoryItem.skuId);
//     //print("sKU ID = ${sku} unit = $unit ${skuInput.losses} total loss = ${getTotalLoss()}");
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8),
//       child: Row(
//         children: [
//           Expanded(
//             flex: 2,
//             child: Text(
//               sku.name,
//               style: const TextStyle(
//                 color: Colors.black,
//                 fontSize: 14,
//               ),
//             ),
//           ),
//           Expanded(
//             flex: 2,
//             child: Center(
//               child: Text(
//                 isBulk
//                     ? skuInput.inventoryItem.unit
//                     : '${skuInput.inventoryItem.lotSize} ${skuInput.inventoryItem.unit}',
//                 style: const TextStyle(
//                   color: Colors.black,
//                   fontSize: 14,
//                 ),
//               ),
//             ),
//           ),
//           Expanded(
//             flex: 3,
//             child: Padding(
//               padding: const EdgeInsets.only(left: 15, right: 15),
//               child: SizedBox(
//                 height: 40,
//                 child: TextFormField(
//                   initialValue: skuInput.quantity,
//                   onChanged: (txt) {
//                     context.read<CloseInventoryBloc>().add(
//                           CloseInventoryEvent.onEdit(
//                             input: skuInput.copyWith(
//                               quantity: txt,
//                             ),
//                             index: index,
//                             isBulk: isBulk,
//                           ),
//                         );
//                   },
//                   decoration: InputDecoration(
//                     contentPadding: const EdgeInsets.only(left: 8, right: 8),
//                     isDense: false,
//                     border: OutlineInputBorder(
//                       borderSide: BorderSide(
//                         color: Colors.grey.shade200,
//                       ),
//                     ),
//                     counterStyle: const TextStyle(
//                       height: double.minPositive,
//                     ),
//                     counterText: '',
//                   ),
//                   keyboardType: TextInputType.number,
//                   maxLength: 10,
//                   inputFormatters: isBulk
//                       ? Config.numberInputFilters
//                       : [
//                           FilteringTextInputFormatter.allow(RegExp('[0-9]')),
//                         ],
//                 ),
//               ),
//             ),
//           ),
//           Expanded(
//             flex: 3,
//             child: OutlinedButton.icon(
//               icon: const Icon(
//                 Icons.edit,
//                 color: Colors.black,
//                 size: 16,
//               ),
//               onPressed: () async {
//                 final result = await showDialog<List<LossInputModel>>(
//                   context: context,
//                   builder: (context) => AddLosses(
//                     unit: skuInput.inventoryItem.unit,
//                     losses: skuInput.losses,
//                     title: LanguageEnum.addLossLossesLabel.localized(),
//                     shouldAddHardcodeLosses: true,
//                   ),
//                 );
//                 if (result != null) {
//                   // ignore: use_build_context_synchronously
//                   context.read<CloseInventoryBloc>().add(
//                         CloseInventoryEvent.onEdit(
//                           input: skuInput.copyWith(
//                             losses: result,
//                           ),
//                           index: index,
//                           isBulk: isBulk,
//                         ),
//                       );
//                 }
//               },
//               label: Text(
//                 skuInput.totalLoss().toString(),
//                 style: const TextStyle(
//                   color: Colors.black,
//                   fontSize: 14,
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
