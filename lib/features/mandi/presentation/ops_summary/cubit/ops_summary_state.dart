part of 'ops_summary_cubit.dart';

@freezed
class OpsSummaryState with _$OpsSummaryState {
  const factory OpsSummaryState.initial() = _Initial;
  const factory OpsSummaryState.data({
    required int smoId,
    required int mandiId,
    required int startTime,
    required int? endTime,
    required String status,
    @Default(null) ErrorResult? error,
    @Default(null) SmoDetailV2? smoDetailV2,
  }) = _Data;
}
