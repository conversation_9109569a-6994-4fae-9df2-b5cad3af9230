import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'smo_detail_v2.freezed.dart';
part 'smo_detail_v2.g.dart';

@freezed
class SmoDetailV2 with _$SmoDetailV2 {
  const factory SmoDetailV2({
    required List<MyProc> myProc,
    required List<OtherProc> othersProc,
    @Default(SmoTime()) SmoTime time,
  }) = _SmoDetailV2;

  factory SmoDetailV2.fromJson(Map<String, dynamic> json) =>
      _$SmoDetailV2FromJson(json);
}

@freezed
class SmoTime with _$SmoTime {
  const factory SmoTime({
    @Default(null) int? start,
    @Default(null) int? closeOps,
    @Default(null) int? closingInventory,
    @Default(null) int? end,
  }) = _SmoTime;
  factory SmoTime.fromJson(Map<String, dynamic> json) =>
      _$SmoTimeFromJson(json);
}

@freezed
class MyProc with _$MyProc {
  const MyProc._();
  const factory MyProc({
    required int id,
    required int createdAt,
    required String status,
    required int smoId,
    required List<String> images,
    required List<Item> items,
    @Default({}) Map<String, dynamic> urls,
    required String procuredBy,
    required String? vendorName,
    required int? vendorLocationId,
    required List<dynamic> charges,
  }) = _MyProc;

  bool get canUpdatePrice =>
      status == 'PLACED' || status == 'QUANTITY_SUBMITTED';

  factory MyProc.fromJson(Map<String, dynamic> json) => _$MyProcFromJson(json);
}

@freezed
class Item with _$Item {
  const factory Item({
    required int id,
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double? quantity,
    required double? weight,
    required double? amount,
    required double? orderedQuantity,
    required bool? finalSubmit,
    @Default([]) List<String> images,
    @Default(null) double? orderedCostPrice,
    @Default(null) double? billedCostPrice,
  }) = _Item;

  factory Item.fromJson(Map<String, dynamic> json) => _$ItemFromJson(json);
}

@freezed
class OtherProc with _$OtherProc {
  const factory OtherProc({
    required String procuredBy,
    required int nosOpenProc,
  }) = _OtherProc;

  factory OtherProc.fromJson(Map<String, dynamic> json) =>
      _$OtherProcFromJson(json);
}
