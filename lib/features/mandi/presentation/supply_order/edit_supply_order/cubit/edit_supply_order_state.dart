part of 'edit_supply_order_cubit.dart';

@freezed
class EditSupplyOrderState with _$EditSupplyOrderState {
  factory EditSupplyOrderState({
    required HashSet<String> allAddedSkusCompositeKeys,
    @Default([]) List<SupplyOrder> supplyOrders,
    @Default([]) List<Terminal> terminals,
    @Default(null) String? message,
    @Default(false) bool shouldPop,
    @Default(false) bool isUpdating,
  }) = _Initial;
}
