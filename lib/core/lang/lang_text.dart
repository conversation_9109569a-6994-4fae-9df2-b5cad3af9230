import 'package:flutter/material.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';

class LangText extends StatefulWidget {
  LangText(
    this.langKey,
    this.data, {
    super.key,
    this.style,
    this.strutStyle,
    this.textAlign,
    this.textDirection,
    this.locale,
    this.softWrap,
    this.overflow,
    this.textScaleFactor,
    this.maxLines,
    this.semanticsLabel,
    this.textWidthBasis,
    this.selectionColor,
    this.params,
  });
  final String langKey;
  final String data;
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextAlign? textAlign;
  final TextDirection? textDirection;
  final Locale? locale;
  final bool? softWrap;
  final TextOverflow? overflow;
  final double? textScaleFactor;
  final int? maxLines;
  final String? semanticsLabel;
  final TextWidthBasis? textWidthBasis;
  final Color? selectionColor;
  final Map<String, dynamic>? params;

  @override
  State<LangText> createState() => _LangTextState();
}

class _LangTextState extends State<LangText> {
  String langString = '';

  @override
  void initState() {
    langString = di.get<Language>().getString(
        key: widget.langKey,
        defaultValue: widget.data,
        params: widget.params,
        skipParsing: widget.params == null);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant LangText oldWidget) {
    if (oldWidget.langKey != widget.langKey ||
        widget.data != oldWidget.data ||
        oldWidget.params != widget.params) {
      langString = di.get<Language>().getString(
          key: widget.langKey,
          defaultValue: widget.data,
          params: widget.params,
          skipParsing: widget.params == null);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      langString,
      key: widget.key,
      style: widget.style,
      strutStyle: widget.strutStyle,
      textAlign: widget.textAlign,
      textDirection: widget.textDirection,
      locale: widget.locale,
      softWrap: widget.softWrap,
      overflow: widget.overflow,
      textScaleFactor: widget.textScaleFactor,
      maxLines: widget.maxLines,
      semanticsLabel: widget.semanticsLabel,
      textWidthBasis: widget.textWidthBasis,
      selectionColor: widget.selectionColor,
    );
  }
}
