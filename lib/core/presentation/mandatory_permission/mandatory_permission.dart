import 'package:permission_handler/permission_handler.dart';

class MandatoryPermission {
  final Permission permission;
  final ImagePathProvider imagePath;
  final String Function() overviewText;
  final Permission? dependsOnPermission;
  final String Function()? subText;

  MandatoryPermission({
    required this.permission,
    required this.imagePath,
    required this.overviewText,
    this.dependsOnPermission,
    this.subText,
  });
}

class ImagePathProvider {
  final String defaultPath;
  final String grantedPath;

  const ImagePathProvider({
    required this.defaultPath,
    required this.grantedPath,
  });

  String getPath(PermissionStatus status) {
    if (status.isGranted) {
      return grantedPath;
    }
    return defaultPath;
  }
}

enum PermissionGrantStatus {
  allGranted,
  permanentlyDenied,
  denied,
}
