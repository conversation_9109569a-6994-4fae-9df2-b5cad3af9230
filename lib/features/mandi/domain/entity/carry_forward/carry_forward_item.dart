import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_loss.dart';

part 'carry_forward_item.freezed.dart';

@freezed
class CarryForwardItem with _$CarryForwardItem {
  const CarryForwardItem._();

  const factory CarryForwardItem({
    required int id,
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double quantity,
    @Default([]) List<CarryForwardLoss> loss,
    @Default([]) List<CarryForwardLoss> shortOrExcess,
  }) = _CarryForwardItem;

  double getLoss() {
    return loss.fold(0, (previousValue, element) {
      return previousValue + (double.tryParse(element.lossValue) ?? 0.0);
    });
  }

  double getShortOrExcess() {
    return shortOrExcess.fold(0, (previousValue, element) {
      return previousValue + (double.tryParse(element.lossValue) ?? 0.0);
    });
  }

  String getShortOrExcessString() {
    if (shortOrExcess.isEmpty) {
      return '0';
    }
    final l = shortOrExcess[0];
    if (l.lossValue.toDouble() == 0) return '0';
    if (l.lossType == 'SHORTAGE') {
      return '-${l.lossValue}';
    }
    return '+${l.lossValue}';
  }
}
