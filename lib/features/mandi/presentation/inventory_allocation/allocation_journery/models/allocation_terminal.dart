import 'package:freezed_annotation/freezed_annotation.dart';

part 'allocation_terminal.freezed.dart';

@freezed
class AllocationTerminal with _$AllocationTerminal {
  const AllocationTerminal._();
  const factory AllocationTerminal({
    required String id,
    required int? groupValue,
    required int serialNumber,
  }) = _AllocationTerminal;

  String get groupValueString => groupValue == null ? '' : 'G${groupValue}';
}
