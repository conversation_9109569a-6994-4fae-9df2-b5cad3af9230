import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/liquidation/cubit/update_liquidation_cubit.dart';

class CreateLiquidationOrderRequest extends BaseRequest<dynamic, String> {
  final int smoId;
  final String vendorName;
  final String mobileNumber;
  final List<LiquidationInputModel> inputModels;
  final List<String> images;

  CreateLiquidationOrderRequest({
    required this.smoId,
    required this.vendorName,
    required this.inputModels,
    required this.images,
    required this.mobileNumber,
  });
  @override
  String getPath() => 'liquidation/create-order';

  @override
  Map<String, dynamic> getBody() {
    return {
      "smoId": smoId,
      "vendor": {
        "name": vendorName,
        "mobile": mobileNumber,
      },
      "skus": inputModels
          .map((e) => {
                'skuId': e.sku.id,
                'type': e.procurementType.value.toUpperCase(),
                'unit': e.procurementUnit,
                'lotSize': e.lotSize ?? 0,
                'quantity': e.quantity.toDouble(),
                'weighingSource': e.weighingSource,
                'agreedAmount': e.agreedPrice?.toDouble() ?? 0,
                "comment": "",
                "isFinalized": false,
                "receivedAmount": 0,
                "images": e.files.map((e) => e.uploadKey ?? '').toList(),
              })
          .toList(),
      "images": images,
      "comments": ""
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
