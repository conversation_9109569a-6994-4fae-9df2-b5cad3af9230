import 'package:injectable/injectable.dart';
import 'package:proc2/core/lang/db/language_db.dart';
import 'package:proc2/core/lang/parser/language_parser.dart';

@Singleton(as: LanguageParser)
class LanguageParserImpl implements LanguageParser {
  const LanguageParserImpl(this._languageDb);

  final LanguageDb _languageDb;

  @override
  String parse({
    required String text,
    required String language,
    Map<String, dynamic>? params,
  }) {
    final regex = RegExp(r'##\S+##');
    final matches = regex.allMatches(text);

    final keys = <String>[];
    final memory = <String, dynamic>{};
    final inputParams = params ?? <String, dynamic>{};

    for (final match in matches) {
      final keyWithHash = match.group(0);

      if (keyWithHash != null) {
        final key = keyWithHash.substring(2, keyWithHash.length - 2);
        final value = memory[keyWithHash] ??
            inputParams[key] ??
            _internalKnowledge(key) ??
            '';
        memory[keyWithHash] = value;
        keys.add(keyWithHash);
      }
    }

    final buffer = StringBuffer();
    final splits = text.split(regex);
    var index = 0;

    for (final key in keys) {
      buffer.write(splits[index]);
      index++;
      buffer.write(memory[key]!.toString());
    }

    for (; index < splits.length; index++) {
      buffer.write(splits[index]);
    }

    return buffer.toString();
  }

  String? _internalKnowledge(String key) {
    if (key == 'username') return 'John Doe';

    return null;
  }
}
