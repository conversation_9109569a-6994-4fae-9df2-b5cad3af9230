import 'package:flutter/material.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:url_launcher/url_launcher.dart';

class ReceiveInfoDialog extends StatefulWidget {
  final Consignment consignment;

  const ReceiveInfoDialog({
    super.key,
    required this.consignment,
  });

  @override
  _ReceiveInfoDialogState createState() => _ReceiveInfoDialogState();
}

class _ReceiveInfoDialogState extends State<ReceiveInfoDialog> {
  @override
  Widget build(BuildContext context) {
    final heightMultiplier =
        widget.consignment.tripDetails.length > 1 ? 0.5 : 0.3;
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            height: MediaQuery.of(context).size.height * heightMultiplier,
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(0.0)),
              child: Padding(
                padding: const EdgeInsets.all(0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      decoration: BoxDecoration(color: Colors.grey[300]),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 4,
                          horizontal: 16,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            LangText(
                              'receiveInventory.infoTitle',
                              'Trip Info',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              visualDensity: VisualDensity.compact,
                              icon: Icon(Icons.close),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Center(
                        child: Scrollbar(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 16,
                            ),
                            child: ListView.builder(
                              itemCount: widget.consignment.tripDetails.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return _tripCard(
                                    widget.consignment.tripDetails[index]);
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _tripCard(TripDetails detail) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: InkWell(
        onTap: () async {
          final number = detail.driverPhone;
          if (number != null) {
            final url = Uri.parse('tel:$number');

            if (await canLaunchUrl(url)) {
              await launchUrl(url);
            } else {
              throw 'Could not open the dialer.';
            }
          }
        },
        child: Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (detail.driverName != null)
                  _headingAndValue(
                      heading: getLangText('driverName', 'Driver Name'),
                      value: detail.driverName!),
                if (detail.driverPhone != null)
                  _headingAndValue(
                      heading: getLangText('driverPhone', 'Driver Phone'),
                      value: detail.driverPhone!),
                if (detail.vehicleNo != null)
                  _headingAndValue(
                      heading: getLangText('vehicleNumber', 'Vehicle Number'),
                      value: detail.vehicleNo!),
                if (detail.expectedDeliveryTime != null)
                  _headingAndValue(
                    heading: getLangText('eta', 'ETA'),
                    value: detail.expectedDeliveryTime!.toDate(
                      'dd MMM, yyyy | hh:mm a',
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _headingAndValue({required String heading, required String value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          heading,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
