import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';

part 'inventory_item_input_model.freezed.dart';

@freezed
class InventoryItemInputModel with _$InventoryItemInputModel {
  const InventoryItemInputModel._();

  const factory InventoryItemInputModel({
    required int skuId,
    required List<double> lots,
    required String type,
    required String unit,
    required double lotSize,
    required double quantity,
    required String quantityInput,
    required String lotSizeInput,
  }) = _InventoryItemInputModel;

  bool isBulk() {
    return type.toLowerCase() == 'bulk';
  }

  String getCompositeKey() {
    if (isBulk()) {
      return '$skuId-$type-$unit';
    } else {
      return '$skuId-$type-$unit-$lotSize';
    }
  }

  double finalQuantity(bool isLotting, bool isFrom) {
    if (isFrom) {
      if (isLotting) return quantityInput.toDouble();
      return lotSize * quantityInput.toDouble();
    }
    if (!isLotting) return quantityInput.toDouble();
    return lotSizeInput.toDouble() * quantityInput.toDouble();
  }

  bool hasFilledRequiredInputs(bool isLotting, bool isFrom) {
    if (isFrom) {
      return quantityInput.isNotEmpty;
    }

    if (!isLotting) return quantityInput.isNotEmpty;
    return quantityInput.isNotEmpty && lotSizeInput.isNotEmpty;
  }
}
