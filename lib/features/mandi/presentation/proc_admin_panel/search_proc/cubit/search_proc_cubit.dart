import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_submitted_procurements_request.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';

part 'search_proc_state.dart';
part 'search_proc_cubit.freezed.dart';

@injectable
class SearchProcCubit extends Cubit<SearchProcState> {
  SearchProcCubit() : super(SearchProcState.initial());

  void setMandi(MandiInfo mandi) {
    emit(state.copyWith(mandi: mandi));
  }

  void setDate(DateTime? date) {
    emit(state.copyWith(selectedDate: date));
  }

  void setVendor(VendorLocation? vendor) {
    emit(state.copyWith(selectedVendor: vendor));
  }

  void clearMessage(String? message) {
    emit(state.copyWith(message: message));
  }

  void getProcurements() async {
    final mandi = state.mandi;
    final date = state.selectedDate;
    final vendor = state.selectedVendor;
    if (mandi == null || date == null || vendor == null || state.isLoading) {
      emit(state.copyWith(message: 'Please select all fields'));
      return;
    }
    emit(state.copyWith(isLoading: true, message: null, procurements: null));
    final result = await GetSubmittedProcurementsRequest(
      mandiId: mandi.id,
      timestamp: date.millisecondsSinceEpoch ~/ 1000,
      vendorLocationId: vendor.locationId,
    ).execute();
    final newState = result.fold(
      (left) => state.copyWith(isLoading: false, message: left.message),
      (right) => state.copyWith(isLoading: false, procurements: right),
    );
    emit(newState);
  }
}
