import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/admin/bug_report/cubit/bug_report_admin_cubit.dart';
import 'package:proc2/features/admin/bug_report/view/bug_report_card.dart';

class BugReportAdminScreen extends StatelessWidget {
  const BugReportAdminScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        title: Text('reportBugAdmin.title'.tr('Active Bug Reports')),
        centerTitle: false,
      ),
      body: BlocConsumer<BugReportAdminCubit, BugReportAdminState>(
        listener: (context, state) {
          if (state.message != null) {
            showSnackBar(state.message!);
            context.read<BugReportAdminCubit>().clearMessage();
          }
        },
        builder: (context, state) {
          if (state.bugReports == null) {
            return Center(child: CircularProgressIndicator());
          }
          if (state.bugReports!.isEmpty) {
            return Center(
                child: Text('noBugReportFound'.tr('No active bug reports')));
          }
          return Container(
            child: ListView.builder(
              itemCount: state.bugReports?.length ?? 0,
              padding: EdgeInsets.only(top: 16),
              itemBuilder: (context, index) {
                final bugReport = state.bugReports![index];
                return Padding(
                  padding: const EdgeInsets.only(
                    bottom: 16.0,
                    left: 16,
                    right: 16,
                  ),
                  child: BugReportCard(
                    bugReport: bugReport,
                    onResolve: () {
                      context
                          .read<BugReportAdminCubit>()
                          .markResolved(bugReport);
                    },
                  ),
                );
              },
            ),
          );
        },
      ),
    ));
  }
}
