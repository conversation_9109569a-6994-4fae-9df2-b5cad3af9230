part of 'return_download_cubit.dart';

@freezed
class ReturnDownloadState with _$ReturnDownloadState {
  const factory ReturnDownloadState.initial({
    @Default(null) String? message,
    @Default(false) bool isLoading,
    @Default([]) List<MandiInfo> allMandisList,
    @Default(null) int? selectedMandiId,
    @Default(null) DateTime? selectedStartDateTime,
    @Default(null) DateTime? selectedEndDateTime,
    @Default(null) String? selectedType,
    @Default([]) List<ReturnDownloadEntity> returnList,
  }) = _Initial;
}
