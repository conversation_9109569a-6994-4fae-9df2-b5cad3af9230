import 'package:proc2/features/admin/procurement_update/model/proc_table_item.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';

class ProcTableRow {
  final MandiInfo mandi;
  final List<ProcTableItem> items;
  final List<ProcTableItem> _deletedItems;
  final int procurementId;

  ProcTableRow({
    required this.mandi,
    required this.items,
    required List<ProcTableItem> deletedItems,
    required this.procurementId,
  }) : _deletedItems = deletedItems;

  ProcTableRow copyWith({
    MandiInfo? mandi,
    List<ProcTableItem>? items,
    List<ProcTableItem>? deletedItems,
    int? procurementId,
  }) {
    return ProcTableRow(
      mandi: mandi ?? this.mandi,
      items: items ?? this.items,
      deletedItems: deletedItems ?? this._deletedItems,
      procurementId: procurementId ?? this.procurementId,
    );
  }

  ProcTableRow updateItem({required int index, required ProcTableItem item}) {
    final newItems = List<ProcTableItem>.from(items);
    newItems[index] = item;
    return copyWith(items: newItems);
  }

  ProcTableRow deleteItem({required int index}) {
    final newItems = List<ProcTableItem>.from(items);
    final deletedItem = newItems.removeAt(index);
    final newDeletedItems = List<ProcTableItem>.from(_deletedItems);
    newDeletedItems.add(deletedItem);
    return copyWith(items: newItems, deletedItems: newDeletedItems);
  }

  List<ProcTableItem> getUpdatedItems() {
    final updatedItems = items
        .where(
            (element) => element.orderedQuantity != element.lastOrderedQuantity)
        .toList();
    final deletedItems =
        _deletedItems.map((e) => e.copyWith(orderedQuantity: '0'));
    return [...updatedItems, ...deletedItems]
        .where((e) => e.itemId != -1)
        .toList();
  }
}
