import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';

part 'loss_input_model.freezed.dart';

@freezed
class LossInputModel with _$LossInputModel {
  const factory LossInputModel({
    required String lossType,
    required String lossValue,
    required String unit,
    required String comment,
    @Default([]) List<PickedFile> files,
    @Default(0) int maxFileUploadCount,
    @Default(0) int minFileUploadCount,
  }) = _LossInputModel;
}
