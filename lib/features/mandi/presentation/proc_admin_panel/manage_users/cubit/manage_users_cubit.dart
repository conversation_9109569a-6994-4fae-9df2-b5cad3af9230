import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/create_user_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_user_list_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_user_mandis_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/update_user_role_request.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/proc_user.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/user_mandi.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/user_role.dart';

part 'manage_users_state.dart';
part 'manage_users_cubit.freezed.dart';

@injectable
class ManageUsersCubit extends Cubit<ManageUsersState> {
  ManageUsersCubit() : super(ManageUsersState.initial());

  void setSelectedUser(ProcUser? user) {
    emit(state.copyWith(selectedUser: user, userMandi: null));
  }

  void setUserFirstName(String? val) {
    emit(state.copyWith(userFirstName: val));
  }

  void setUserLastName(String? val) {
    emit(state.copyWith(userLastName: val));
  }

  void setUserEmail(String? val) {
    emit(state.copyWith(userEmail: val));
  }

  void setUserMobile(String? val) {
    emit(state.copyWith(userMobile: val));
  }

  Future<void> setSelectedUserRoles(List<UserRole> roles) async {
    emit(state.copyWith(selectedUserRoles: roles));
  }

  void resetCreateUserData() {
    emit(state.copyWith(
        message: null,
        userFirstName: null,
        userLastName: null,
        userEmail: null,
        userMobile: null));
  }

  void load() async {
    emit(state.copyWith(isLoading: true, message: null));

    final result = await GetUserListRequest().execute();

    final newState = result.fold(
        (left) => state.copyWith(isLoading: false, message: left.message),
        (right) => state.copyWith(isLoading: false, userList: right));
    emit(newState);
  }

  Future<void> setNewRoles(
      {required List<UserRole> roles, required int mandiId}) async {
    final updatedUserMandi =
        state.userMandi?.assignRole(newRoles: roles, mandiId: mandiId);

    emit(state.copyWith(userMandi: updatedUserMandi));
  }

  void clearErrorMsg(String? msg) {
    emit(state.copyWith(message: msg));
  }

  void getUserMandis() async {
    final procUser = state.selectedUser;

    emit(state.copyWith(userMandi: null));

    if (procUser == null || state.isLoading) {
      emit(state.copyWith(message: 'Please select User'));
      return;
    }

    emit(state.copyWith(isLoading: true, message: null));

    final result = await GetUserMandisRequest(
      email: procUser.email ?? "",
    ).execute();

    final newState = result.fold(
        (left) => state.copyWith(isLoading: false, message: left.message),
        (right) => state.copyWith(
            isLoading: false,
            userMandi: right,
            roleList: right.assignedMandis
                .map((e) => UserRole(roleId: e.roleId, roleName: e.roleName))
                .toList()));
    emit(newState);
  }

  Future<bool> createUser() async {
    final userFirstName = state.userFirstName;
    final userLastName = state.userLastName;
    final userEmail = state.userEmail;
    final userMobile = state.userMobile;

    if (userFirstName == null || userEmail == null) {
      emit(state.copyWith(isLoading: false, message: "Please fill all fields"));
      return false;
    }
    emit(state.copyWith(isLoading: true, message: null));

    final result = await CreateUserRequest(
            userFirstName: userFirstName,
            userLastName: userLastName,
            userEmail: userEmail,
            userMobile: userMobile)
        .execute();
    final newState = result.fold(
        (left) => state.copyWith(isLoading: false, message: left.message),
        (right) => state.copyWith(isLoading: false));
    emit(newState);
    return result.isRight;
  }

  Future<bool> updateUserRole() async {
    final procUser = state.selectedUser;
    final newUserMandi = state.userMandi;

    if (procUser == null || state.isLoading) {
      emit(state.copyWith(isLoading: false, message: 'Please select User'));
      return false;
    }

    emit(state.copyWith(isLoading: true, message: null));

    final result = await UpdateUserRoleRequest(
            email: procUser.email ?? "", userMandi: newUserMandi)
        .execute();

    final newState = result.fold(
        (left) => state.copyWith(isLoading: false, message: left.message),
        (right) => state.copyWith(isLoading: false));

    emit(newState);
    if (newState.message == null) {
      return true;
    }
    return false;
  }
}
