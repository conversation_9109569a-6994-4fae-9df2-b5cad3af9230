import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';

class CreateVendorRequest extends BaseRequest<dynamic, VendorLocation> {
  final String vendorName;
  final String pocName;
  final String vendorPhone;
  final String pocPhone;
  final int procAreaId;

  CreateVendorRequest({
    required this.vendorName,
    required this.pocName,
    required this.vendorPhone,
    required this.pocPhone,
    required this.procAreaId,
  });

  @override
  String getPath() {
    return 'vendor/create';
  }

  @override
  Map<String, dynamic> getBody() {
    return {
      "vendorName": vendorName,
      "vendorPhone": vendorPhone,
      "pocName": pocName,
      "pocPhone": pocPhone,
      "areaOfProcurement": procAreaId,
    };
  }

  @override
  VendorLocation mapper(data) {
    if (data is Map<String, dynamic>) {
      final locations = Vendor.fromJson(data).locations;
      final myLocations = locations.where((e) =>
          e.procurementAreaId == procAreaId &&
          (pocName.isEmpty ? e.pocName == vendorName : e.pocName == pocName) &&
          (pocPhone.isEmpty
              ? e.pocPhone == vendorPhone
              : e.pocPhone == pocPhone));
      try {
        if (pocPhone.isEmpty || pocName.isEmpty) {
          return myLocations.last;
        } else {
          return myLocations.first;
        }
      } catch (e) {
        throw 'Error while getting location';
      }
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
