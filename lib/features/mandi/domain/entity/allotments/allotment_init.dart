import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/delivery_date_range.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/destination_info.dart';

part 'allotment_init.freezed.dart';

@freezed
class AllotmentInit with _$AllotmentInit {
  const AllotmentInit._();
  const factory AllotmentInit({
    required Map<String, List<DestinationInfo>> destinations,
    required DeliveryDateRange deliveryDateRange,
    required List<String> deliverySlot,
  }) = _AllotmentInit;

  static const empty = AllotmentInit(
    destinations: {},
    deliveryDateRange: DeliveryDateRange.empty,
    deliverySlot: [],
  );

  bool canExcessAllowed(String destinationName) {
    final customers = destinations['customers'];
    if (customers == null) return false;
    return customers
            .where((element) =>
                element.name.toLowerCase() == destinationName.toLowerCase())
            .firstOrNull
            ?.canAllocateExcess ??
        false;
  }
}
