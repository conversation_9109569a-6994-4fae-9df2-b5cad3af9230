import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';
import 'package:proc2/features/mandi/presentation/returns/create/cubit/create_returns_cubit.dart';
import 'package:proc2/features/mandi/presentation/returns/create/view/create_returns_sku_popup.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class CreateReturnsScreen extends StatefulWidget {
  const CreateReturnsScreen({
    super.key,
    required this.smoId,
    required this.mandiId,
  });
  final int smoId;
  final int mandiId;

  @override
  State<CreateReturnsScreen> createState() => _CreateReturnsScreenState();
}

class _CreateReturnsScreenState extends State<CreateReturnsScreen> {
  final TextEditingController _terminalIdController = TextEditingController();

  @override
  void dispose() {
    _terminalIdController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        color: Colors.white,
        child: BlocConsumer<CreateReturnsCubit, CreateReturnsState>(
          listener: (context, state) {
            bool shouldPop = state.shouldPop;
            if (state.message != null) {
              showSnackBar(state.message!);
              context.read<CreateReturnsCubit>().clearMessage();
            }
            if (shouldPop) {
              context.pop();
            }
          },
          builder: (context, state) {
            return Scaffold(
              backgroundColor: Colors.grey.shade100,
              appBar: AppBar(
                centerTitle: false,
                title: Text('New Returns'),
                elevation: 0,
              ),
              body: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Container(
                    color: Colors.green,
                    padding: const EdgeInsets.all(0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 16,
                        ),
                        _headerButton('Add Sku ${state.skuCountText}',
                            () async {
                          if (state.isCtaLoading) return;
                          final result = await showDialog<ReturnItem?>(
                            context: context,
                            builder: (ctx) => CreateReturnsSkuPopup(
                              showDelete: false,
                              notAllowedSkus: state.notAllowedSkus,
                            ),
                          );
                          if (result != null) {
                            context.read<CreateReturnsCubit>().addItem(result);
                          }
                        }),
                      ],
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 16,
                      ),
                      child: TextFormField(
                        controller: _terminalIdController,
                        decoration: InputDecoration(
                          labelText: 'Terminal Id',
                          hintText: 'Enter Terminal ID',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          isDense: true,
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 16,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    color: Colors.grey[300],
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 4.0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 3,
                            child: LangText(
                              'unit',
                              'Unit',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'receiveQty',
                              'Rec Qty',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ),
                          SizedBox(
                            width: 48,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: state.inputItems.isEmpty
                        ? EmptyScreen(
                            message: 'Please add skus!',
                          )
                        : Scrollbar(
                            child: ListView(
                              children: [
                                for (final list in state.inputItems.values)
                                  skuCard(list, state)
                              ],
                            ),
                          ),
                  ),
                  WStickyBottomCta(
                    isEnabled: !state.isCtaLoading &&
                        state.inputItems.isNotEmpty &&
                        _terminalIdController.text.isNotEmpty,
                    isLoading: state.isCtaLoading,
                    icon: Icons.check,
                    label: Text('Submit'),
                    onPressed: () async {
                      final result = await context.showAlertDialog(
                              title: 'createReturnsScreen.submitPopupTitle'
                                  .tr('Are you sure?'),
                              message: 'createReturnsScreen.submitPopupMessage'.tr(
                                  'These inventory will be added to the returns inventory!')) ??
                          false;
                      if (result) {
                        context.read<CreateReturnsCubit>().submit(
                              widget.smoId,
                              _terminalIdController.text,
                            );
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget skuCard(List<ReturnItem> inputModel, CreateReturnsState state) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 4,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              getSKU(context, skuID: inputModel.first.skuQuantity.skuId).name,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            for (int i = 0; i < inputModel.length; i++)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        // 'Lots - 2.5kg',
                        inputModel[i].skuQuantity.getUnitString(),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),

                    SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Text(
                            inputModel[i].acceptedQuantity,
                            style: TextStyle(
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ),
                      ),
                    ),
                    // SizedBox(width: 4),

                    InkWell(
                      onTap: () async {
                        if (state.isCtaLoading) return;
                        final notAllowedSkus =
                            List<String>.from(state.notAllowedSkus);
                        notAllowedSkus
                            .remove(inputModel[i].skuQuantity.compositeKey);
                        final result = await showDialog<ReturnItem?>(
                          context: context,
                          builder: (ctx) => CreateReturnsSkuPopup(
                            showDelete: true,
                            notAllowedSkus: notAllowedSkus,
                            inputModel: inputModel[i],
                            onDelete: () {
                              context.read<CreateReturnsCubit>().deleteItem(
                                  inputModel[i].skuQuantity.skuId, i);
                            },
                            // inputModel: inputModel[i],
                          ),
                        );
                        if (result != null) {
                          context
                              .read<CreateReturnsCubit>()
                              .updateItem(result, i);
                        }
                      },
                      child: Container(
                        width: 44,
                        child: const Icon(
                          Icons.edit,
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _headerButton(String title, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        side: BorderSide(
          color: Colors.white,
        ),
        elevation: 0,
      ),
      child: Text(title),
    );
  }
}
