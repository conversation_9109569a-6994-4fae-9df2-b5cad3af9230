import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/procurement_data.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';

Map<String, dynamic> procurementDataToJson(
  ProcurementData data,
  int vendorLocationId,
  List<ProcFieldCharge> fieldCharges,
  String comments,
  DateTime? supplyDate,
) =>
    <String, dynamic>{
      'smoId': data.smoId,
      'vendorLocationId': vendorLocationId,
      'comments': comments,
      'images': data.images,
      'skus': data.skus
          .map(
            (e) => {
              'skuId': e.skuId,
              'type': e.type.toUpperCase(),
              'unit': e.unit.toUpperCase(),
              'quantity': e.quantity,
              'lotSize': e.lotSize,
              'weight': e.weight,
              'amount': e.amount,
              'orderedQuantity': e.orderedQuantity,
              'finalSubmit': e.finalQuantity,
              'weighingSource': e.weighingSource,
              'orderedCostPrice':
                  (e.quantity ?? 0) > 0 ? (e.amount) / (e.quantity!) : 0,
            },
          )
          .toList(),
      'charges': fieldCharges
          .map(
            (e) => {
              'type': e.type,
              'amount': e.amount.toDouble(),
              'comment': e.comment,
              'images': e.images.map((e) => e.uploadKey).toList(),
            },
          )
          .toList(),
      'supplyDate':
          supplyDate == null ? null : supplyDate.millisecondsSinceEpoch ~/ 1000,
    };
