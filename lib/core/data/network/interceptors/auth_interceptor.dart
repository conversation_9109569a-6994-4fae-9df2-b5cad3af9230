import 'package:chopper/chopper.dart';
import 'package:proc2/core/data/network/interceptors/network_info.dart';
import 'package:proc2/core/data/network/util/network_constants.dart';
import 'package:proc2/core/data/preference/preference.dart';
import 'package:proc2/core/di/di.dart';

Request authInterceptor(Request request) {
  if (request.headers.containsKey(NetworkConstants.noAuthKey)) {
    final headers = request.headers..remove(NetworkConstants.noAuthKey);
    return request.copyWith(headers: headers);
  }

  final preference = di.get<Preference>();
  return request.copyWith(
    headers: {
      ...request.headers,
      'Authorization':
          'Bearer ${preference.get<String?>(Preference.authTokenKey)}',
    },
    uri: request.url.replace(
      queryParameters: {
        ...request.url.queryParameters,
        'versionCode': NetworkInfo.instance.packageInfo?.buildNumber,
      },
    ),
  );
}
