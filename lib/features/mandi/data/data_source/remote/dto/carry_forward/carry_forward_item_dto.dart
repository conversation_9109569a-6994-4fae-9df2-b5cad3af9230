import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/sku_quantity_dto.dart';

part 'carry_forward_item_dto.freezed.dart';
part 'carry_forward_item_dto.g.dart';

@freezed
class CarryForwardItemDto with _$CarryForwardItemDto {
  const factory CarryForwardItemDto({
    required int id,
    required SkuQuantityDto skuQuantity,
  }) = _CarryForwardItemDto;

  @override
  factory CarryForwardItemDto.fromJson(Map<String, dynamic> json) =>
      _$CarryForwardItemDtoFromJson(json);
}
