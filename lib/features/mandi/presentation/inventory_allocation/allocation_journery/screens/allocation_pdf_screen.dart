import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:url_launcher/url_launcher.dart';

class AllocationPdfScreen extends StatefulWidget {
  const AllocationPdfScreen({super.key, required this.state});
  final AllocationJourneyState state;

  @override
  State<AllocationPdfScreen> createState() => _AllocationPdfScreenState();
}

class _AllocationPdfScreenState extends State<AllocationPdfScreen> {
  int? selectedPdfIndex;
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (selectedPdfIndex != null) {
          setState(() {
            selectedPdfIndex = null;
          });
        } else {
          context.read<AllocationJourneyCubit>().moveBack();
        }
      },
      child: SafeArea(
        child: Scaffold(
          appBar: WAppBar.getAppBar(
            title: Text('Allocation PDF'),
            actions: [
              if (selectedPdfIndex != null)
                IconButton(
                  onPressed: () async {
                    final downloadUrl =
                        Uri.parse(widget.state.pdfs[selectedPdfIndex!]);
                    await launchUrl(
                      downloadUrl,
                      mode: LaunchMode.externalApplication,
                    );
                  },
                  icon: Icon(Icons.download),
                ),
            ],
          ),
          body: Container(
            child: selectedPdfIndex == null
                ? ListView(
                    children: widget.state.pdfs
                        .asMap()
                        .map((index, pdf) => MapEntry(
                              index,
                              Padding(
                                padding: const EdgeInsets.only(
                                  bottom: 8,
                                ),
                                child: ListTile(
                                  tileColor: index % 2 != 0
                                      ? Colors.grey.shade200
                                      : Colors.grey.shade100,
                                  leading: Icon(Icons.picture_as_pdf),
                                  onTap: () {
                                    setState(() {
                                      selectedPdfIndex = index;
                                    });
                                  },
                                  title: Text('PDF ${index + 1}'),
                                ),
                              ),
                            ))
                        .values
                        .toList(),
                  )
                : SfPdfViewer.network(
                    widget.state.pdfs[selectedPdfIndex!],
                    onDocumentLoadFailed: (details) {
                      showSnackBar('Failed to load PDF');
                      setState(() {
                        selectedPdfIndex = null;
                      });
                    },
                  ),
          ),
        ),
      ),
    );
  }
}
