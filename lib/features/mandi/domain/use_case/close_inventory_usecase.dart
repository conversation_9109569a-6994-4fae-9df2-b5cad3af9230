import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';

@injectable
class CloseInventoryUseCase {
  CloseInventoryUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, void>> call({
    required int smoId,
    required int mandiId,
    required List<SkuInputWithLoss> data,
  }) async {
    // In the remote data source which is a dependedency of mandiRepository
    // we are changing the quantity of the sku to be quantity + totalLosses,
    // In the UI, the quantity field represents the good quantity and not the
    // entire quantity, so we need to add the losses to the quantity before
    // sending it to the backend.
    // Ps: We should do this in usecase rather than any other place, but leaving
    // it for later.
    return _mandiRepository.closeInventory(
      smoId,
      mandiId,
      data.toList(),
    );
  }
}
