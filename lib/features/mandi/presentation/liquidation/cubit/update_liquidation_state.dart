part of 'update_liquidation_cubit.dart';

@freezed
class UpdateLiquidationState with _$UpdateLiquidationState {
  const UpdateLiquidationState._();
  const factory UpdateLiquidationState.initial({
    @Default('') String vendorName,
    @Default([]) List<PickedFile> files,
    @Default(null) LiquidationOrder? order,
    @Default({}) Map<int, List<LiquidationInputModel>> inputOrders,
    @Default(true) bool isCreateOrder,
    @Default([]) List<String> notAllowedSkus,
    @Default(false) bool isCtaLoading,
    @Default(null) String? message,
    @Default(false) bool shouldPop,
    @Default(false) bool shouldShowLoading,
    @Default([]) List<SummaryItem> summaryItems,
  }) = _Initial;

  String get skuCountText =>
      inputOrders.isEmpty ? '' : '(${inputOrders.length})';
  String get imageCountText => files.isEmpty ? '' : '(${files.length})';
}

@freezed
class LiquidationInputModel with _$LiquidationInputModel {
  const LiquidationInputModel._();
  const factory LiquidationInputModel({
    @Default(null) LiquidationItem? item,
    @Default([]) List<PickedFile> files,
    required Sku sku,
    required ProcurementType procurementType,
    required String procurementUnit,
    required double? lotSize,
    required String quantity,
    required String? agreedPrice,
    @Default(false) bool closeLiquidationOrder,
    @Default('') String liquidationQtyNow,
    @Default('') String dumpQtyNow,
    @Default('') String receiveAmountNow,
    @Default(null) String? weighingSource,
  }) = _LiquidationInputModel;

  bool get isBulk => procurementType == ProcurementType.bulk;
  double get _receivedAmountTillNow =>
      (item?.receivedAmount ?? 0) + receiveAmountNow.toDouble();
  String get receiveAmountTillNow =>
      _receivedAmountTillNow > 0 ? _receivedAmountTillNow.toString() : '-';

  String getUnitString() {
    if (isBulk) {
      return 'Bulk - $procurementUnit';
    }
    return 'Lots - $lotSize $procurementUnit';
  }

  String getCompositeKey() {
    if (isBulk) {
      return '${sku.id}-BULK-$procurementUnit';
    } else {
      return '${sku.id}-LOTS-$procurementUnit-$lotSize';
    }
  }
}
