import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/uploader/image_picker_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/bloc/add_procurement_bloc.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/place_order_popup/cubit/place_order_cubit.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/place_order_popup/view/place_order_popup.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';

class AddProcHeader extends StatefulWidget {
  const AddProcHeader({
    super.key,
    required this.smoId,
    required this.mandiId,
    required this.isUploadSlipsEnabled,
    required this.onAddedSku,
    required this.onBack,
    required this.procCount,
    required this.files,
    required this.comment,
    required this.onCommentUpdated,
    required this.isParentOrder,
    required this.procConfig,
  }) : slipsCount = files.length;
  final int smoId;
  final int mandiId;
  final VoidCallback onAddedSku;
  final VoidCallback onBack;
  final Function(String comment) onCommentUpdated;
  final bool isUploadSlipsEnabled;
  final int procCount;
  final List<PickedFile> files;
  final int slipsCount;
  final String comment;
  final bool isParentOrder;
  final ProcurementConfig? procConfig;

  @override
  State<AddProcHeader> createState() => _AddProcHeaderState();
}

class _AddProcHeaderState extends State<AddProcHeader> {
  late TextEditingController controller;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController();
    controller.text = widget.comment;
  }

  @override
  void didUpdateWidget(covariant AddProcHeader oldWidget) {
    if (widget.comment != oldWidget.comment) {
      controller.text = widget.comment;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AddProcurementBloc, AddProcurementState>(
      builder: (context, addProcurementState) {
        final canAddSku = !addProcurementState.isParentOrder &&
            (!addProcurementState.isOpenToReceive
                ? true
                : (widget.procConfig?.receiveProcurement.canAddSku ?? false));
        return Container(
          color: Config.primaryColor,
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  InkWell(
                      onTap: widget.onBack,
                      child: const Icon(Icons.arrow_back, color: Colors.white)),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context
                            .watch<AddProcurementBloc>()
                            .state
                            .data
                            .mandiName
                            .capitalize(),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      if (addProcurementState.procDetail?.refId != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          addProcurementState.procDetail!.refId! +
                              ' (${addProcurementState.procDetail!.refSource})',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (addProcurementState.procDetail?.pendingChildOrders !=
                          null &&
                      addProcurementState
                          .procDetail!.pendingChildOrders!.isNotEmpty) ...[
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (_) {
                            final childOrders = addProcurementState
                                .procDetail!.pendingChildOrders!;
                            final mandiInfo = context
                                    .read<MandiBloc>()
                                    .state
                                    .mapOrNull(success: (s) => s.allMandis) ??
                                <MandiInfo>[];
                            return Popup(
                              title: 'childOpenOrders'.tr('Child Open Orders'),
                              children: [
                                Expanded(
                                  child: ListView(
                                    shrinkWrap: true,
                                    children: childOrders
                                        .map(
                                          (e) => ListTile(
                                            title: Text(mandiInfo
                                                    .where((m) =>
                                                        m.id == e.mandiId)
                                                    .firstOrNull
                                                    ?.name ??
                                                ''),
                                            subtitle: Text(e.status),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      child: const Icon(
                        Icons.info,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(
                      width: 16,
                    ),
                  ],
                ],
              ),
              const SizedBox(
                height: 16,
              ),
              Wrap(
                runSpacing: 8,
                children: [
                  if (canAddSku) addSKUButton(context),
                  if (!addProcurementState.isBulkPlacement ||
                      widget.isParentOrder) ...[
                    const SizedBox(width: 12),
                    uploadSlipsButton(context),
                  ],
                  const SizedBox(width: 12),
                  if (!addProcurementState.isParentOrder)
                    addCommentButton(context),
                  const SizedBox(width: 12),
                  if (!addProcurementState.isOpenToReceive ||
                      addProcurementState.selectedSupplyDate != null)
                    InkWell(
                      onTap: addProcurementState.isOpenToReceive ||
                              addProcurementState.procDetail?.supplyDate != null
                          ? null
                          : () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: DateTime.now(),
                                firstDate:
                                    DateTime.now().subtract(Duration(days: 4)),
                                lastDate: DateTime.now().add(
                                  Duration(days: 7),
                                ),
                              );
                              context.read<AddProcurementBloc>().add(
                                  AddProcurementEvent.updateSupplyDate(date));
                            },
                      child: SizedBox(
                        width: 200,
                        child: TextFormField(
                          key: Key(addProcurementState.selectedSupplyDate
                                  ?.toString() ??
                              ''),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                          enabled: false,
                          initialValue:
                              addProcurementState.selectedSupplyDate == null
                                  ? ''
                                  : (addProcurementState.selectedSupplyDate!
                                              .millisecondsSinceEpoch ~/
                                          1000)
                                      .toDate('dd MMM, yyyy'),
                          decoration: InputDecoration(
                            prefixIcon: Icon(
                              Icons.calendar_month,
                              color: Colors.white,
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.white,
                              ),
                            ),
                            border: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.white,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.white,
                              ),
                            ),
                            disabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.white,
                              ),
                            ),
                            label: LangText(
                              'addProc.supplyDateMandatory',
                              'Supply Date*',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                            isDense: false,
                            contentPadding: EdgeInsets.symmetric(
                              vertical: 0,
                              horizontal: 8,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              )
            ],
          ),
        );
      },
    );
  }

  Widget addCommentButton(BuildContext context) {
    return InkWell(
        onTap: () async {
          final result = await showDialog<String?>(
            context: context,
            builder: (_) => Popup(
              height: 0.4,
              title: widget.comment.isEmpty
                  ? 'addProc.addComment'.tr('Add Comment')
                  : 'addProc.editComment'.tr('Edit Comment'),
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: TextFormField(
                      controller: controller,
                      minLines: 2,
                      maxLines: 4,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ),
                WStickyBottomCta(
                  icon: Icons.check,
                  label: Text('submit'.tr('Submit')),
                  onPressed: () {
                    context.pop(controller.text);
                  },
                ),
              ],
            ),
          );
          if (result != null) {
            widget.onCommentUpdated(result);
            controller.clear();
          } else {
            if (widget.comment.isEmpty) {
              controller.clear();
            }
          }
        },
        child: Container(
          padding:
              const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Config.primaryColor,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color:
                  widget.isUploadSlipsEnabled ? Colors.white : Colors.white38,
            ),
            boxShadow: const [
              BoxShadow(
                color: Colors.white54,
                blurRadius: 1,
              ),
            ],
          ),
          child: Text(
            widget.comment.isEmpty
                ? 'addProc.addComment'.tr('Add Comment')
                : 'addProc.editComment'.tr('Edit Comment'),
            style: TextStyle(fontWeight: FontWeight.w600, color: Colors.white),
          ),
        ));
  }

  Widget uploadSlipsButton(BuildContext context) {
    return InkWell(
        onTap: !widget.isUploadSlipsEnabled
            ? null
            : () async {
                final result = await Navigator.push<PickerResult?>(
                  context,
                  // ignore: inference_failure_on_instance_creation
                  MaterialPageRoute(
                    builder: (context) => ImagePickerScreen(
                      pageTitle:
                          LanguageEnum.addProcUploadSplitsBtn.localized(),
                      reqBody: {'smoId': widget.smoId},
                      module: UploadFileModule.procurementsInvoice,
                      allowMultiple: true,
                      initialImages: widget.files,
                    ),
                  ),
                );
                if (result != null) {
                  if (result.message.isNotEmpty) {
                    showSnackBar(result.message);
                  }
                  // ignore: use_build_context_synchronously
                  context
                      .read<AddProcurementBloc>()
                      .add(AddProcurementEvent.addSlips(result.files));
                }
              },
        child: Container(
          padding:
              const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Config.primaryColor,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color:
                  widget.isUploadSlipsEnabled ? Colors.white : Colors.white38,
            ),
            boxShadow: const [
              BoxShadow(
                color: Colors.white54,
                blurRadius: 1,
              ),
            ],
          ),
          child: Text(
            LanguageEnum.addProcUploadSplitsBtn.localized() +
                (widget.slipsCount == 0 ? "" : " (${widget.slipsCount})"),
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color:
                  widget.isUploadSlipsEnabled ? Colors.white : Colors.white38,
            ),
          ),
        ));
  }

  Widget addSKUButton(BuildContext context) {
    return InkWell(
      onTap: () async {
        final state = context.read<AddProcurementBloc>().state;
        final notAllowedSku = state.inputSku.values
            .expand((e) => e)
            .map((e) => e.getCompositeKey())
            .toList();
        // final selectedSku = await showDialog<Sku?>(
        //   context: context,
        //   builder: (_) => const SearchSKU(),
        // );
        // if (selectedSku != null) {
        // ignore: use_build_context_synchronously
        final skuInputData = await showDialog<SkuInputData?>(
          context: context,
          builder: (_) => BlocProvider(
            create: (context) => di.get<PlaceOrderCubit>()
              ..updateNotAllowedSkus(notAllowedSku)
              ..disableAmountEdit(state.procDetail?.parentOrder != null),
            child: PlaceOrderPopup(
              receiveProcurementConfig: widget.procConfig?.receiveProcurement,
            ),
          ),
        );

        if (skuInputData != null) {
          // ignore: use_build_context_synchronously
          context
              .read<AddProcurementBloc>()
              .add(AddProcurementEvent.add(skuInputData));
          widget.onAddedSku();
        }
        // }
      },
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
        decoration: BoxDecoration(
            color: Config.primaryColor,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: Colors.white),
            boxShadow: const [
              BoxShadow(
                color: Colors.white54,
                blurRadius: 1.0,
              ),
            ]),
        child: Text(
          LanguageEnum.addProcAddSKUBtn.localized() +
              (widget.procCount == 0 ? "" : " (${widget.procCount})"),
          style:
              const TextStyle(fontWeight: FontWeight.w600, color: Colors.white),
        ),
      ),
    );
  }

  Widget procSummaryBtn(BuildContext context) {
    return InkWell(
      onTap: () async {
        await context.push(
          context.namedLocation(
            'procurementHistory',
            pathParameters: {
              'smoId': widget.smoId.toString(),
              'mandiId': widget.mandiId.toString(),
            },
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
        decoration: BoxDecoration(
            color: Config.primaryColor,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: Colors.white),
            boxShadow: const [
              BoxShadow(
                color: Colors.white54,
                blurRadius: 1.0,
              ),
            ]),
        child: Text(
          LanguageEnum.addProcProcSummaryBtn.localized(),
          style:
              const TextStyle(fontWeight: FontWeight.w600, color: Colors.white),
        ),
      ),
    );
  }

  Widget myProcurementBtn(BuildContext context) {
    return InkWell(
      onTap: () async {
        await context.push(
          context.namedLocation(
            'myProcurement',
            pathParameters: {
              'smoId': widget.smoId.toString(),
              'mandiId': widget.mandiId.toString(),
            },
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
        decoration: BoxDecoration(
            color: Config.primaryColor,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: Colors.white),
            boxShadow: const [
              BoxShadow(
                color: Colors.white54,
                blurRadius: 1.0,
              ),
            ]),
        child: Text(
          LanguageEnum.addProcMyProcyBtn.localized(),
          style:
              const TextStyle(fontWeight: FontWeight.w600, color: Colors.white),
        ),
      ),
    );
  }
}
