import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

part 'liquidation_order.freezed.dart';
part 'liquidation_order.g.dart';

@freezed
class LiquidationOrder with _$LiquidationOrder {
  const LiquidationOrder._();
  factory LiquidationOrder({
    required int smoId,
    required int? smoCreatedAt,
    required int? smoClosedAt,
    required VendorDetails vendor,
    required List<LiquidationItem> liquidationItems,
    required List<String> images,
    required String comments,
    required String status,
    required int liquidationId,
    required String liquidationOrderId,
    required int? createdAt,
    required int? updatedAt,
    required String? createdBy,
  }) = _LiquidationOrder;

  factory LiquidationOrder.fromJson(Map<String, dynamic> json) =>
      _$LiquidationOrderFromJson(json);

  String get orderedDate => createdAt?.toDate('dd MMM, yyyy | hh:mm a') ?? '-';
  String get age => createdAt == null
      ? '-'
      : DateTime.now()
          .difference(DateTime.fromMillisecondsSinceEpoch(createdAt! * 1000))
          .inDays
          .toString();
}

@freezed
class VendorDetails with _$VendorDetails {
  const VendorDetails._();
  factory VendorDetails({
    required String name,
    required String mobile,
  }) = _VendorDetails;

  factory VendorDetails.fromJson(Map<String, dynamic> json) =>
      _$VendorDetailsFromJson(json);
}

@freezed
class LiquidationItem with _$LiquidationItem {
  const LiquidationItem._();
  factory LiquidationItem({
    required int id,
    required SkuQuantity skuQuantity,
    required double? agreedAmount,
    required String? comment,
    required List<String> images,
    required double? receivedAmount,
    required double? dumpedQuantity,
    required double? receivedQuantity,
    required bool isFinalized,
  }) = _LiquidationItem;

  factory LiquidationItem.fromJson(Map<String, dynamic> json) =>
      _$LiquidationItemFromJson(json);

  double get availableForLiquidation =>
      (skuQuantity.quantity ?? 0) -
      (receivedQuantity ?? 0) -
      (dumpedQuantity ?? 0);
}
