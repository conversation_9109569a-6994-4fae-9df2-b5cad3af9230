import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetInventoryUseCase {
  GetInventoryUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, Inventory>> call(
    int mandiId,
    {String type = 'primary',}
  ) async {
    return _mandiRepository.getInventory(
      mandiId,
      type: type,
    );
  }
}
