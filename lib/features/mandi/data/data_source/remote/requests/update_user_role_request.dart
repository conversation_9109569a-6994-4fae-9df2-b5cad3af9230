import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/user_mandi.dart';

class UpdateUserRoleRequest extends BaseRequest<dynamic, String> {
  final String email;
  final UserMandi? userMandi;

  UpdateUserRoleRequest({required this.email, required this.userMandi});

  @override
  String getPath() => 'users/updateUserMandi/$email';

  @override
  Map<String, dynamic> getBody() {
    if (userMandi != null) {
      return userMandi!.toJson();
    }
    return {};
  }

  @override
  mapper(data) {
    if (data is String) {
      return data;
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.PUT;
}
