// ignore_for_file: omit_local_variable_types
import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/lang/db/language_db.dart';

@Singleton(as: LanguageDb)
class LanguageDbImpl implements LanguageDb {
  LanguageDbImpl(@Named('HiveDirPath') this._hiveDirectoryPath) {
    Hive.initFlutter();
  }

  final String _hiveDirectoryPath;

  static const String _defaultNodeKey = '__default';

  @override
  String? getString({required String key, required String language}) {
    try {
      final box = Hive.box<Map<dynamic, dynamic>>(language);

      final splits = key.split('.');

      if (splits.length > 1) {
        final groupKey = splits[0];
        final nodeKey = splits[1];

        final result = box.get(groupKey)?[nodeKey]?.toString();

        if (result != null) {
          return result;
        }
      }

      const groupKey = _defaultNodeKey;
      final nodeKey = splits.length >= 2 ? splits[1] : splits[0];
      return box.get(groupKey)?[nodeKey]?.toString();
    } catch (e, s) {
      talker.handle(e, s);
      return null;
    }
  }

  @override
  Future<void> updateDb({
    required String language,
    required Map<String, dynamic> json,
  }) async {
    final box = await open(language);
    await box.clear();
    final boxes = {_defaultNodeKey};
    final Map<String, dynamic> defaultEntries = {};

    for (final entry in json.entries) {
      if (entry.value is Map<String, dynamic>) {
        boxes.add(entry.key);
      } else {
        defaultEntries[entry.key] = entry.value;
      }
    }

    final List<Future<void>> allFutures = [];
    for (final boxName in boxes) {
      if (boxName == _defaultNodeKey && defaultEntries.isNotEmpty) {
        allFutures.add(box.put(boxName, defaultEntries));
      } else {
        allFutures.add(box.put(boxName, json[boxName] as Map<String, dynamic>));
      }
    }

    await Future.wait(allFutures);
  }

  @override
  Future<Box<Map<dynamic, dynamic>>> open(
    String language,
  ) async {
    if (Hive.isBoxOpen(language)) {
      return Hive.box<Map<dynamic, dynamic>>(language);
    }
    return Hive.openBox<Map<dynamic, dynamic>>(
      language,
      path: _hiveDirectoryPath,
    );
  }

  @override
  Future<void> close(String language) {
    if (Hive.isBoxOpen(language)) {
      final previousBox = Hive.box<Map<dynamic, dynamic>>(language);
      return previousBox.close();
      // return Hive.box(language).close();
    }
    return Future.value();
  }
}
