part of 'mandi_inventory_bloc.dart';

@freezed
class MandiInventoryEvent with _$MandiInventoryEvent {
  const factory MandiInventoryEvent.fetchAll(
    int mandiId,
  ) = _FetchAll;
  const factory MandiInventoryEvent.fetchMandiInventory(
    int mandiId, {
    required InventoryType type,
  }) = _FetchMandiInventoryList;
  const factory MandiInventoryEvent.transferGradeA({
    required int smoId,
    required List<InventoryItem> skus,
    required int mandiId,
  }) = _TransferGradeA;
  const factory MandiInventoryEvent.transferGradeB({
    required int smoId,
    required List<InventoryItem> skus,
    required int mandiId,
  }) = _TransferGradeB;
  const factory MandiInventoryEvent.transferGradeD1({
    required int smoId,
    required List<InventoryItem> skus,
    required int mandiId,
  }) = _TransferGradeD1;
  const factory MandiInventoryEvent.transferGradeC({
    required int smoId,
    required List<InventoryItem> skus,
    required int mandiId,
  }) = _TransferGradeC;
  const factory MandiInventoryEvent.clearMessage() = _ClearMessage;
}
