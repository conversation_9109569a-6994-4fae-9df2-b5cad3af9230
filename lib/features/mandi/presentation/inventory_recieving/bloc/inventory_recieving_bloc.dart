import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/domain/use_case/accept_incoming_stock_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_incoming_stocks_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/ir_sku_edit.dart';

part 'inventory_recieving_bloc.freezed.dart';
part 'inventory_recieving_event.dart';
part 'inventory_recieving_state.dart';

@injectable
class InventoryRecievingBloc
    extends Bloc<InventoryRecievingEvent, InventoryRecievingState> {
  InventoryRecievingBloc(
      this._getIncomingStockUseCase, this._acceptIncomingStockUseCase)
      : super(const _Initial()) {
    on<InventoryRecievingEvent>(
      (event, emit) async {
        await event.when(
          started: (int mandiId) async {
            await _getIncomingStockUseCase(mandiId).then(
              (value) => value.fold(
                (left) => emit(InventoryRecievingState.error(left)),
                (right) {
                  emit(InventoryRecievingState.data(consignmentList: right));
                },
              ),
            );
          },
          submit: (int smoId) async {
            final currentState = state;
            if (currentState is InventoryRecievingData) {
              if (currentState.inputModel == null) return;
              emit(currentState.copyWith(isLoading: true));
              await _acceptIncomingStockUseCase(smoId, currentState.inputModel!)
                  .then(
                (value) => value.fold(
                  (left) {
                    emit(
                      currentState.copyWith(
                        error: left.message,
                        isLoading: false,
                      ),
                    );
                  },
                  (right) {
                    emit(const InventoryRecievingState.recievedInventory());
                  },
                ),
              );
            }
          },
          onConsignmentSelect: (Consignment consignment) async {
            final currentState = state;
            if (currentState is InventoryRecievingData) {
              // final newState =
              // await compute(
              //   (message) {
              final bulk = consignment.incomingStocks
                  .where((e) => e.skuQuantity.isBulk())
                  .map((e) =>
                      IRSKUEditInputModel(itemId: e.id, sku: e.skuQuantity))
                  .toList();
              final lots = consignment.incomingStocks
                  .where((e) => !e.skuQuantity.isBulk())
                  .map((e) =>
                      IRSKUEditInputModel(itemId: e.id, sku: e.skuQuantity))
                  .toList();
              final newState = currentState.copyWith(
                inputModel: InventoryRecievingInputModel(
                  consignment: consignment,
                  bulk: bulk,
                  lots: lots,
                  comments: '',
                ),
                selectedConsignment: consignment,
              );
              // },
              // currentState,
              // );
              emit(newState);
            }
          },
          updateReceivedQty: (String received, int index, bool isBulk) {
            final currentState = state;
            if (currentState is InventoryRecievingData) {
              final inputModel = currentState.inputModel;
              if (inputModel == null) return;
              final skuEditList = List<IRSKUEditInputModel>.from(
                  isBulk ? inputModel.bulk : inputModel.lots);
              final skuEdit = skuEditList[index];
              final newSkuEdit = skuEdit.copyWith(recieved: received);
              skuEditList[index] = newSkuEdit;
              final newInputModel = inputModel.copyWith(
                bulk: isBulk ? skuEditList : inputModel.bulk,
                lots: isBulk ? inputModel.lots : skuEditList,
              );
              emit(
                _validateInputModel(
                  currentState.copyWith(
                    inputModel: newInputModel,
                  ),
                ),
              );
            }
          },
          addLossess: (
            List<LossInputModel> loss,
            int index,
            bool isBulk,
          ) {
            final currentState = state;
            if (currentState is InventoryRecievingData) {
              final inputModel = currentState.inputModel;
              if (inputModel == null) return;
              final skuEditList = List<IRSKUEditInputModel>.from(
                  isBulk ? inputModel.bulk : inputModel.lots);
              final skuEdit = skuEditList[index];
              final newSkuEdit = skuEdit.copyWith(losses: loss);
              skuEditList[index] = newSkuEdit;
              final newInputModel = inputModel.copyWith(
                bulk: isBulk ? skuEditList : inputModel.bulk,
                lots: isBulk ? inputModel.lots : skuEditList,
              );
              emit(
                _validateInputModel(
                  currentState.copyWith(
                    inputModel: newInputModel,
                  ),
                ),
              );
            }
          },
          onCommentChange: (comment) {
            final currentState = state;
            if (currentState is InventoryRecievingData) {
              final inputModel = currentState.inputModel;
              if (inputModel == null) return;
              final newInputModel = inputModel.copyWith(comments: comment);
              emit(
                _validateInputModel(
                  currentState.copyWith(
                    inputModel: newInputModel,
                  ),
                ),
              );
            }
          },
          reset: () {
            emit(const InventoryRecievingState.initial());
          },
          updateInput: (int index, bool isBulk, IRSKUEditInputModel input) {
            final currentState = state;
            if (currentState is InventoryRecievingData) {
              final inputModel = currentState.inputModel;
              if (inputModel == null) return;
              final skuEditList = List<IRSKUEditInputModel>.from(
                  isBulk ? inputModel.bulk : inputModel.lots);
              final newSkuEdit = input;
              skuEditList[index] = newSkuEdit;
              final newInputModel = inputModel.copyWith(
                bulk: isBulk ? skuEditList : inputModel.bulk,
                lots: isBulk ? inputModel.lots : skuEditList,
              );
              emit(
                _validateInputModel(
                  currentState.copyWith(
                    inputModel: newInputModel,
                  ),
                ),
              );
            }
          },
        );
      },
    );
  }

  final GetIncomingStocksUseCase _getIncomingStockUseCase;
  final AcceptIncomingStockUseCase _acceptIncomingStockUseCase;

  InventoryRecievingState _validateInputModel(
    InventoryRecievingData currentState,
  ) {
    final input = currentState.inputModel;
    if (input == null || input.comments == null || input.comments!.isEmpty) {
      return currentState.copyWith(
        isInputModelValid: false,
      );
    }

    final bulkError = input.bulk.any((element) =>
        element.recieved.toDouble() == 0 ||
        // element.sku.quantity < element.recieved.toDouble() ||
        element.recieved.toDouble() < element.getTotalLosses());
    if (bulkError) {
      return currentState.copyWith(
        isInputModelValid: false,
      );
    }
    final lotError = input.lots.any((element) =>
        element.recieved.toDouble() == 0 ||
        // element.sku.quantity < element.recieved.toDouble() ||
        element.recieved.toDouble() < element.getTotalLosses());
    if (lotError) {
      return currentState.copyWith(
        isInputModelValid: false,
      );
    }
    return currentState.copyWith(
      isInputModelValid: true,
    );
  }
}
