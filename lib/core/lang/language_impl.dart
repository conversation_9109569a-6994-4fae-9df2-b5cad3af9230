import 'package:injectable/injectable.dart';
import 'package:proc2/core/data/preference/preference.dart';
import 'package:proc2/core/lang/db/language_db.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/lang/parser/language_parser.dart';

@Singleton(as: Language)
class LanguageImpl implements Language {
  LanguageImpl(
    this._languageDb,
    this._languageParser,
    this._preference,
  );

  final LanguageDb _languageDb;
  final LanguageParser _languageParser;
  final Preference _preference;

  static const String languageKey = 'user_lang';
  String? currentSelectedLanguage;

  @override
  String getString({
    String? key,
    String? defaultValue,
    LanguageEnum? languageEnum,
    String? language,
    Map<String, dynamic>? params,
    bool skipParsing = false,
  }) {
    // lastSelectedLanguage ??= await currentLanguage() ?? 'en';
    if ((key == null || defaultValue == null) && languageEnum == null) {
      throw Exception('key and defaultValue or languageEnum must be provided');
    }

    final finalKey = key ?? languageEnum!.key;
    final finalDefaultValue = defaultValue ?? languageEnum!.defaultValue;
    final shouldTransform = languageEnum?.isTemplated ?? true;
    final finalLanguage = language ?? currentSelectedLanguage!;

    final finalText = _languageDb.getString(
          key: finalKey,
          language: finalLanguage,
        ) ??
        finalDefaultValue;

    if (skipParsing || !shouldTransform) {
      return finalText;
    }

    return _languageParser.parse(
      text: finalText,
      language: finalLanguage,
      params: params,
    );
  }

  @override
  Future<bool> updateCurrentLanguage({
    required String language,
  }) async {
    if (currentSelectedLanguage != null &&
        currentSelectedLanguage != language) {
      await _languageDb.close(currentSelectedLanguage!);
    }
    _preference.put(languageKey, language);
    await init(language);
    return true;
  }

  @override
  Future<void> updateLangDb({
    required String language,
    required Map<String, dynamic> json,
  }) {
    return _languageDb.updateDb(language: language, json: json);
  }

  @override
  Future<String?> currentLanguage() async {
    return _preference.get(languageKey);
  }

  @override
  Future<void> init(String defaultLanguage) async {
    var preferenceLang = await currentLanguage();
    if (preferenceLang == null) {
      _preference.put(languageKey, defaultLanguage);
      preferenceLang = defaultLanguage;
    }
    currentSelectedLanguage = preferenceLang;
    await _languageDb.open(currentSelectedLanguage!);
  }
}
