import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:styled_text/styled_text.dart';

class SearchSKU extends StatefulWidget {
  const SearchSKU({super.key});
  @override
  State<SearchSKU> createState() => _SearchSKU();
}

class _SearchSKU extends State<SearchSKU> {
  TextEditingController searchSkuController = TextEditingController();

  @override
  void initState() {
    super.initState();
    searchSkuController.addListener(_onSkuSearchChange);
  }

  @override
  void dispose() {
    super.dispose();
    searchSkuController.removeListener(_onSkuSearchChange);
  }

  void _onSkuSearchChange() {
    context
        .read<SkuBloc>()
        .add(SkuEvent.updateSearch(searchSkuController.text));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SkuBloc, SkuState>(
      builder: (context, state) {
        return Scaffold(
            backgroundColor: Colors.transparent,
            body: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: SizedBox(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                child: state.map(
                  initial: (initial) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  success: (skuState) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                            onTap: () {},
                            child: Card(
                              elevation: 5,
                              shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(15),
                                      topRight: Radius.circular(15))),
                              child: Container(
                                height: MediaQuery.of(context).size.height -
                                    (140 +
                                        MediaQuery.of(context)
                                            .viewInsets
                                            .bottom),
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10)),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    searchBar(),
                                    const SizedBox(height: 10),
                                    Expanded(
                                        child: Container(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          selectedList(),
                                          const SizedBox(height: 10),
                                          Expanded(
                                            child: Scrollbar(
                                              child: ListView(
                                                children: [
                                                  ...(skuState.searchTerm == ''
                                                          ? skuState.skus
                                                          : skuState
                                                              .filteredSku)
                                                      .map((sku) {
                                                    return skuWidget(
                                                      sku: sku,
                                                      skuState: skuState,
                                                    );
                                                  }),
                                                ],
                                              ),
                                            ),
                                          ),
                                          ElevatedButton(
                                            style: ButtonStyle(
                                              fixedSize:
                                                  MaterialStateProperty.all(
                                                Size(
                                                  MediaQuery.of(context)
                                                      .size
                                                      .width,
                                                  50,
                                                ),
                                              ),
                                            ),
                                            onPressed: skuState.selectedSku ==
                                                    null
                                                ? null
                                                : () {
                                                    final currentSku =
                                                        skuState.selectedSku!;
                                                    context.pop(currentSku);
                                                    context.read<SkuBloc>()
                                                      ..add(const SkuEvent
                                                          .updateSearch(''))
                                                      ..add(const SkuEvent
                                                          .selectSku(null));
                                                  },
                                            child: Text(LanguageEnum
                                                .addProcSelectSkuDoneBtn
                                                .localized()),
                                          )
                                        ],
                                      ),
                                    ))
                                  ],
                                ),
                              ),
                            ))
                      ],
                    );
                  },
                  error: (error) => Container(),
                ),
              ),
            ));
      },
    );
  }

  Widget skuWidget({required Sku sku, required Success skuState}) {
    final isChecked = skuState.selectedSku?.id == sku.id;
    return Card(
        elevation: 5,
        margin: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
        child: InkWell(
          onTap: () {
            context
                .read<SkuBloc>()
                .add(SkuEvent.selectSku(isChecked ? null : sku));
          },
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding:
                const EdgeInsets.only(bottom: 0, left: 10, right: 10, top: 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  sku.name,
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w600),
                ),
                Checkbox(
                  value: isChecked,
                  onChanged: (bool? value) {},
                ),
              ],
            ),
          ),
        ));
  }

  Widget selectedList() {
    final selectedName = context.watch<SkuBloc>().state.mapOrNull(
          success: (state) {
            return state.selectedSku?.name;
          },
        ) ??
        LanguageEnum.none.localized();
    return Container(
        margin: const EdgeInsets.only(left: 15, right: 10),
        child: StyledText(
          text:
              '<label>${LanguageEnum.addProcSelectedLabel.localized()} :</label> <value>$selectedName</value>',
          tags: {
            'label': StyledTextTag(
                style: TextStyle(
                    color: Colors.grey.shade400, fontWeight: FontWeight.w500)),
            'value': StyledTextTag(style: const TextStyle(color: Colors.black))
          },
        ));
  }

  Widget searchBar() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
          color: Config.primaryColor,
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15), topRight: Radius.circular(15))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                LanguageEnum.addProcSelectSkuSearchSKU.localized(),
                style: const TextStyle(fontSize: 18, color: Colors.white),
              ),
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                ),
              )
            ],
          ),
          const SizedBox(height: 15),
          SizedBox(
            height: 54,
            child: TextField(
              controller: searchSkuController,
              decoration: InputDecoration(
                fillColor: Colors.grey.shade200,
                filled: true,
                hintText: LanguageEnum.addProcSearchSkuHint.localized(),
                hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
                suffixIcon: Icon(Icons.search, color: Colors.grey.shade400),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
