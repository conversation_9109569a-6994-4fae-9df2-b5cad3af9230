part of 'edit_procurement_bloc.dart';

@freezed
class EditProcurementState with _$EditProcurementState {
  const factory EditProcurementState.initial() = _Initial;
  const factory EditProcurementState.edit({
    required List<SkuHistory> bulkItems,
    required List<SkuHistory> lotItems,
    required int procId,
    @Default([]) List<PickedFile> files,
    String? message,
    @Default(false) bool shouldGoBack,
    @Default(false) bool isUpdateLoading,
    @Default(false) bool isSubmitLoading,
    @Default([]) List<ProcFieldCharge> procFieldCharges,
    required VendorLocation vendorLocation,
    String? refId,
    String? refSource,
  }) = EditPriceState;
}
