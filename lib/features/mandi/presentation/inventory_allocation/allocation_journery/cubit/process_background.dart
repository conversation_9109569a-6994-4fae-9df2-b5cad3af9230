import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_configuration.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/total_allocation_popup.dart';

Map<String, SkuQuantity> _supplyOrderToSkuQuantityMap(
    List<SupplyOrder> supplyOrders) {
  final supplyOrderMap = <String, SkuQuantity>{};
  for (final supplyOrder in supplyOrders) {
    for (final item in supplyOrder.items) {
      final skuQuantity = item.skuQuantity.toSkuQuantity;
      final mapEntry = supplyOrderMap[skuQuantity.compositeKey];
      if (mapEntry == null) {
        supplyOrderMap[skuQuantity.compositeKey] = skuQuantity;
      } else {
        supplyOrderMap[skuQuantity.compositeKey] = mapEntry.copyWith(
          quantity: (mapEntry.quantity ?? 0) + (skuQuantity.quantity ?? 0),
        );
      }
    }
  }
  supplyOrderMap.removeWhere((key, value) => (value.quantity ?? 0) == 0);
  return supplyOrderMap;
}

Map<String, dynamic> processAllocationBackground(Map<String, dynamic> input) {
  final items = input['items'] as List<AllocationSkuItem>;
  final skuMap = input['skuMap'] as Map<int, Sku>;
  final mandiInventory = input['mandiInventory'] as Map<String, SkuQuantity>?;
  final bool shouldAutoAllocate = input['shouldAutoAllocate'] ?? false;
  final AllocationConfiguration? allocationConfiguration =
      input['allocationConfig'];
  if (mandiInventory == null) {
    // This should Never happen
    return {'error': 'Mandi Inventory not found!'};
  }
  final supplyOrders = input['supplyOrders'] as List<SupplyOrder>?;
  if (supplyOrders == null) return {'error': true};
  final supplyOrderSkuMap = _supplyOrderToSkuQuantityMap(supplyOrders);
  final allocationItemSkuMap = Map<String, AllocationSkuItem>.fromIterable(
    items,
    key: (item) => item.skuQuantity.compositeKey,
    value: (item) => item,
  );

  final itemsInBoth = <AllocationSkuItem>[];
  final itemsInSupplyOrder = <AllocationSkuItem>[];
  final itemsInMandi = <AllocationSkuItem>[];

  for (final supplyOrderItem in supplyOrderSkuMap.values) {
    final mandiItem = mandiInventory[supplyOrderItem.compositeKey];
    final allocationItem = allocationItemSkuMap[supplyOrderItem.compositeKey];
    if (allocationItem != null) {
      allocationItemSkuMap.remove(supplyOrderItem.compositeKey);
    }
    final sku = skuMap[supplyOrderItem.skuId];
    final mandiQty =
        (allocationItem?.backendAllocationQty.mandiQty.toDouble() ?? 0) +
            (mandiItem?.quantity ?? 0);

    final allocationQuantity = allocationItem?.backendAllocationQty ??
        AllocationQty.fromMandiAndExpected(
            mandiQty: '', expectedQty: '', actualMandiQty: mandiQty);

    final baseObj = AllocationSkuItem(
      id: allocationItem?.id ?? -1,
      skuName: sku?.name ?? '',
      skuImage: sku?.image ?? '',
      skuQuantity:
          allocationItem != null ? allocationItem.skuQuantity : supplyOrderItem,
      orderedQty: supplyOrderItem.quantity,
      mandiQty: mandiQty,
      backendAllocationQty: allocationQuantity,
      updateAllocationQty: allocationQuantity,
      distributionStatus: allocationItem?.distributionStatus ?? 'PENDING',
      isManuallyEdited: allocationItem?.isManuallyEdited ?? false,
      distributionQuantity: allocationItem?.distributionQuantity,
      groupKey: sku?.groupKey ?? '',
    );
    if (mandiItem != null || baseObj.mandiQty > 0) {
      itemsInBoth.add(baseObj);
    } else {
      itemsInSupplyOrder.add(baseObj);
    }
  }

  for (final mandiItem in mandiInventory.values) {
    if (supplyOrderSkuMap[mandiItem.compositeKey] == null) {
      final allocationItem = allocationItemSkuMap[mandiItem.compositeKey];
      if (allocationItem != null) {
        allocationItemSkuMap.remove(mandiItem.compositeKey);
      }
      final mandiQty =
          (allocationItem?.backendAllocationQty.mandiQty.toDouble() ?? 0) +
              (mandiItem.quantity ?? 0);
      final sku = skuMap[mandiItem.skuId];
      final allocationQty = allocationItem?.backendAllocationQty ??
          AllocationQty.fromMandiAndExpected(
            mandiQty: '',
            expectedQty: '',
            actualMandiQty: mandiQty,
          );
      final baseObj = AllocationSkuItem(
        id: allocationItem?.id ?? -1,
        skuName: sku?.name ?? '',
        skuImage: sku?.image ?? '',
        skuQuantity:
            allocationItem != null ? allocationItem.skuQuantity : mandiItem,
        orderedQty: null,
        mandiQty: mandiQty,
        backendAllocationQty: allocationQty,
        updateAllocationQty: allocationQty,
        distributionStatus: allocationItem?.distributionStatus ?? 'PENDING',
        isManuallyEdited: allocationItem?.isManuallyEdited ?? false,
        distributionQuantity: allocationItem?.distributionQuantity,
        groupKey: sku?.groupKey ?? '',
      );
      itemsInMandi.add(baseObj);
    }
  }

  final remainingItems = allocationItemSkuMap.values
      .toList()
      .map((e) => e.copyWith(
          mandiQty: e.backendAllocationQty.mandiQty.toDouble(),
          skuName: skuMap[e.skuQuantity.skuId]?.name ?? '',
          skuImage: skuMap[e.skuQuantity.skuId]?.image ?? ''))
      .toList();

  itemsInMandi.addAll(remainingItems);
  // TODO: This code was needed, but commented out as we are not using auto allocation
  // on the frontend.
  // if (shouldAutoAllocate && allocationConfiguration != null) {
  //   return autoAllocate(
  //     strategies: allocationConfiguration.allocation.autoAllocation.strategies,
  //     items: itemsInBoth,
  //     itemsInSupplyOrder: itemsInSupplyOrder,
  //     skuMap: skuMap,
  //   );
  // }
  return {
    'itemsInBoth':
        itemsInBoth.where((e) => e.mandiQty >= (e.orderedQty ?? 0)).toList(),
    'itemsInSupplyOrder': itemsInSupplyOrder,
    'itemsInMandi': itemsInMandi,
    'itemsPartial':
        itemsInBoth.where((e) => e.mandiQty < (e.orderedQty ?? 0)).toList(),
  };
}

Map<String, dynamic> autoAllocate({
  required List<Strategy> strategies,
  required List<AllocationSkuItem> items,
  required List<AllocationSkuItem> itemsInSupplyOrder,
  required Map<int, Sku> skuMap,
}) {
  var itemsInBoth = items.toList();
  var itemsInSupply = itemsInSupplyOrder.toList();
  for (final strategy in strategies) {
    itemsInBoth = itemsInBoth.map((e) {
      final sku = skuMap[e.skuQuantity.skuId];
      if (sku == null) return e;
      return strategy.applyStrategy(e, sku);
    }).toList();
    itemsInSupply = itemsInSupply.map((e) {
      final sku = skuMap[e.skuQuantity.skuId];
      if (sku == null) return e;
      return strategy.applyStrategy(e, sku);
    }).toList();
  }
  return {
    'itemsInBoth':
        itemsInBoth.where((e) => e.mandiQty >= (e.orderedQty ?? 0)).toList(),
    'itemsInSupplyOrder': itemsInSupply,
    'itemsInMandi':
        <AllocationSkuItem>[], //itemsInMandi, // TODO: Uncomment this if wants to show mandi items
    'itemsPartial':
        itemsInBoth.where((e) => e.mandiQty < (e.orderedQty ?? 0)).toList(),
  };
}
