import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';

class GetLiquidationOrderRequest
    extends BaseRequest<dynamic, List<LiquidationOrder>> {
  final int smoId;
  final LiquidationOrderStatus status;

  GetLiquidationOrderRequest({required this.smoId, required this.status});

  @override
  String getPath() => 'liquidation/orders';

  @override
  Map<String, dynamic>? getQuery() {
    return {
      'smoId': smoId,
      'status': status.name,
    };
  }

  @override
  List<LiquidationOrder> mapper(data) {
    if (data is List<dynamic>) {
      return data
          .map((e) => LiquidationOrder.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}

enum LiquidationOrderStatus { pending, completed }
