// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_production.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCiY4SOxj99uSHRWpv1Ox88Opg5HNkyiiM',
    appId: '1:175834420930:web:bc957cd4cc798742410d5b',
    messagingSenderId: '175834420930',
    projectId: 'signintest-84632',
    authDomain: 'signintest-84632.firebaseapp.com',
    storageBucket: 'signintest-84632.appspot.com',
    measurementId: 'G-M1KHVB31CK',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE',
    appId: '1:175834420930:android:bf3d0df245e1430a410d5b',
    messagingSenderId: '175834420930',
    projectId: 'signintest-84632',
    storageBucket: 'signintest-84632.appspot.com',
  );
}
