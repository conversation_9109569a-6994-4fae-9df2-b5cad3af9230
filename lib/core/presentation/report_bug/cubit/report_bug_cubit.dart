import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/services.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/report_bug/api/bug_reporter_interface.dart';
import 'package:proc2/core/presentation/report_bug/model/bug_report_log.dart';
import 'package:proc2/features/auth/domain/entity/user.dart';
import 'package:share_plus/share_plus.dart';

part 'report_bug_cubit.freezed.dart';
part 'report_bug_state.dart';

@injectable
class ReportBugCubit extends Cubit<ReportBugState> {
  final BugReporter bugReporter;
  ReportBugCubit(this.bugReporter) : super(ReportBugState.initial());

  void addFiles(List<PlatformFile> files) {
    final newFiles = List<PlatformFile>.from(state.files);
    newFiles.addAll(files);
    emit(state.copyWith(files: newFiles));
  }

  void removeFile(PlatformFile file) {
    final files = List<PlatformFile>.from(state.files);
    files.remove(file);
    emit(state.copyWith(files: files));
  }

  void updateComment(String comment) {
    emit(state.copyWith(comment: comment));
  }

  void clearMessage() {
    emit(state.copyWith(
      message: null,
      shouldPop: false,
    ));
  }

  Future<void> submit({
    required File talkerLogFile,
    required User user,
  }) async {
    if (state.isCtaLoading) return;
    emit(state.copyWith(isCtaLoading: true));
    final bugReportLog = BugReportLog(
      userId: user.id,
      name: user.name,
      email: user.email,
      attachments: state.files,
      comment: state.comment,
      logFile: talkerLogFile,
      hasResolved: false,
    );
    bugReporter.reportBug(bugReportLog).listen((event) {
      emit(state.copyWith(
        message: event,
      ));
    }, onDone: () async {
      await talkerLogFile.delete();
      if (state.comment.isNotEmpty) {
        await Clipboard.setData(ClipboardData(text: state.comment));
      }
      final files = state.files.map((e) => XFile(e.path ?? '')).toList();
      await files.isEmpty
          ? Share.share(state.comment)
          : Share.shareXFiles(files, text: state.comment);
      emit(
        state.copyWith(
          isCtaLoading: false,
          shouldPop: true,
          message: 'commentCopied'.tr('Comment copied!'),
        ),
      );
    });
  }
}
