import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_mandi_status_card.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/presentation/widgets/w_tab_bar.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/true_date_time.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_init.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_v2.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/process_action.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/process_stage.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_allocation_pdf_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/home/<USER>/inventory_allocation_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class AllocationScreen extends StatefulWidget {
  final int smoId;
  final int mandiId;

  const AllocationScreen({super.key, required this.smoId, required this.mandiId});

  @override
  _AllocationScreenState createState() => _AllocationScreenState();
}

class _AllocationScreenState extends State<AllocationScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      vsync: this,
      length: 2,
      initialIndex: 1,
    );
  }

  void _showBottomSheet(BuildContext ctx) {
    final bloc = context.read<InventoryAllocationBloc>();
    final val = _tabController.index == 0 ? 'mandi' : 'customers';
    bloc.add(
      InventoryAllocationEvent.destinationTypeChanged(
        val,
      ),
    );

    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      context: ctx,
      builder: (context) {
        return BlocConsumer<InventoryAllocationBloc, InventoryAllocationState>(
          bloc: bloc,
          listener: (context, state) async {
            state.maybeMap(
              orElse: () => {},
              input: (input) async {
                if (input.allotmentIdToOpen != null || input.message != null) {
                  context.pop();
                }
                final msg = input.message;
                if (msg != null) {
                  // show snackbar
                  context.pop();
                  showSnackBar(msg);
                }
              },
            );
          },
          builder: (context, state) {
            return state.maybeMap(
              orElse: () => Container(),
              input: (input) {
                final destinations = input.selectedDestinationType == null ? null : input.initData.destinations[input.selectedDestinationType!];

                final isCustomerSelected = input.selectedDestinationType == 'customers';

                bool isDestinationSelected = input.selectedDestination != null;
                bool isSlotTimeSelected = input.deliveryDate != null;
                bool isLoading = input.isCtaLoading;

                bool isCtaActive = isDestinationSelected && (isCustomerSelected ? isSlotTimeSelected && isSlotTimeSelected : true);
                return Container(
                  padding: EdgeInsets.only(top: 16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 8,
                            ),
                            Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  LangText(
                                    'allocation.mandi',
                                    'Mandi',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: !isCustomerSelected ? FontWeight.w600 : FontWeight.w400,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Switch(
                                    value: isCustomerSelected,
                                    onChanged: (value) {
                                      final val = value ? 'customers' : 'mandi';
                                      bloc.add(
                                        InventoryAllocationEvent.destinationTypeChanged(
                                          val,
                                        ),
                                      );
                                    },
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  LangText(
                                    'allocation.customer',
                                    'Customers',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: isCustomerSelected ? FontWeight.w600 : FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            SizedBox(
                              height: 8,
                            ),
                            SizedBox(
                              height: 8,
                            ),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade500),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: DropdownButton<String>(
                                isExpanded: true,
                                isDense: true,
                                hint: AutoSizeText(
                                  LanguageEnum.allocateInventoryDestinationLabel.localized(),
                                  maxFontSize: 16,
                                  maxLines: 1,
                                ),
                                underline: const SizedBox(),
                                value: input.selectedDestination?.name,
                                items: destinations
                                    ?.map(
                                      (e) => DropdownMenuItem<String>(
                                        value: e.name,
                                        child: AutoSizeText(
                                          e.name.capitalize(),
                                          maxFontSize: 16,
                                          maxLines: 1,
                                        ),
                                      ),
                                    )
                                    .toList(),
                                onChanged: (String? val) {
                                  bloc.add(
                                    InventoryAllocationEvent.destinationChanged(val),
                                  );
                                },
                              ),
                            ),
                            SizedBox(
                              height: 16,
                            ),
                            if (isCustomerSelected)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: Row(
                                  children: [
                                    Container(
                                      width: MediaQuery.of(context).size.width * 0.45,
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey.shade500),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: DropdownButton<String>(
                                        isExpanded: true,
                                        isDense: true,
                                        hint: AutoSizeText(
                                          LanguageEnum.allocateInventoryDestinationSlotLabel.localized(),
                                          maxFontSize: 16,
                                          maxLines: 1,
                                        ),
                                        underline: const SizedBox(),
                                        value: input.deliverySlot,
                                        items: input.initData.deliverySlot
                                            .map(
                                              (e) => DropdownMenuItem<String>(
                                                value: e,
                                                child: AutoSizeText(
                                                  e,
                                                  maxFontSize: 16,
                                                  maxLines: 1,
                                                ),
                                              ),
                                            )
                                            .toList(),
                                        onChanged: (String? val) {
                                          bloc.add(
                                            InventoryAllocationEvent.deliverySlotChanged(val),
                                          );
                                        },
                                      ),
                                    ),
                                    const Spacer(
                                      flex: 1,
                                    ),
                                    InkWell(
                                      onTap: () async {
                                        final selectedDate = showDatePicker(
                                          context: context,
                                          initialDate: input.deliveryDate ?? TrueDateTime.now(),
                                          firstDate: TrueDateTime.now().subtract(
                                            Duration(
                                              days: input.initData.deliveryDateRange.past,
                                            ),
                                          ),
                                          lastDate: TrueDateTime.now().add(
                                            Duration(
                                              days: input.initData.deliveryDateRange.future,
                                            ),
                                          ),
                                        );
                                        bloc.add(
                                          InventoryAllocationEvent.deliveryDateChanged(
                                            await selectedDate,
                                          ),
                                        );
                                      },
                                      child: Container(
                                        width: MediaQuery.of(context).size.width * 0.45,
                                        height: 40,
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey.shade500),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            AutoSizeText(
                                              input.deliveryDate?.toFormattedString() ?? LanguageEnum.allocateInventoryDeliveryDateLabel.localized(),
                                              maxLines: 1,
                                              maxFontSize: 16,
                                            ),
                                            const Icon(Icons.calendar_month),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                            // Add more list tiles or widgets as needed
                          ],
                        ),
                      ),
                      WStickyBottomCta(
                        icon: Icons.check,
                        label: LangText('allocation.allocateCta', 'Allocate'),
                        color: Colors.grey[200],
                        isEnabled: isCtaActive && !isLoading,
                        isLoading: isLoading,
                        onPressed: () {
                          bloc
                            ..add(
                              InventoryAllocationEvent.createAllotment(
                                widget.smoId,
                                input.selectedDestination?.name ?? '',
                              ),
                            );
                        },
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: BlocConsumer<InventoryAllocationBloc, InventoryAllocationState>(
        listener: (context, state) {
          state.maybeMap(
            orElse: () {},
            error: (e) {
              showSnackBar(e.error.message);
            },
            input: (input) async {
              int? allotmentIdToOpen;

              if (input.allotmentIdToOpen != null) {
                allotmentIdToOpen = input.allotmentIdToOpen;
                context.read<InventoryAllocationBloc>().add(const InventoryAllocationEvent.clearOpenAllotment());
              }

              if (input.mandiAllotments == null && !input.hasErrorInGetAllotments) {
                context.read<InventoryAllocationBloc>().add(InventoryAllocationEvent.getAllotments(widget.smoId));
              }
              if (input.message != null) {
                showSnackBar(input.message!);

                context.read<InventoryAllocationBloc>().add(const InventoryAllocationEvent.clearMessage());
              }

              if (allotmentIdToOpen != null) {
                final params = {
                  ...(input.allotmentQueryParams ?? {})
                };
                params['allocationId'] = allotmentIdToOpen.toString();
                params['smoId'] = widget.smoId.toString();
                await context.push(
                  context.namedLocation('allocationJourney', queryParameters: params, pathParameters: {
                    'mandiId': widget.mandiId.toString(),
                  }),
                  extra: context,
                );
                // clear
                // ignore: use_build_context_synchronously
                context.read<InventoryAllocationBloc>().add(
                      InventoryAllocationEvent.getAllotments(
                        widget.smoId,
                      ),
                    );
              }
            },
          );
        },
        builder: (context, state) {
          return state.maybeMap(
            orElse: () => Scaffold(
              appBar: WAppBar.getAppBar(
                centerTitle: false,
                title: Text('allocation'.tr('Allocation')),
              ),
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
            input: (input) {
              return Scaffold(
                appBar: WAppBar.getAppBar(
                  centerTitle: false,
                  title: Text('allocation'.tr('Allocation')),
                  bottom: WTabBar(
                    controller: _tabController,
                    tabs: [
                      Tab(text: 'Mandi'),
                      Tab(text: 'Customer'),
                    ],
                  ),
                ),
                body: input.hasErrorInGetAllotments
                    ? Column(
                        children: [
                          Expanded(
                            child: ErrorScreen(
                              message: 'Failed to load allotments',
                              onPressed: () {
                                context.read<InventoryAllocationBloc>().add(
                                      InventoryAllocationEvent.getAllotments(
                                        widget.smoId,
                                      ),
                                    );
                              },
                            ),
                          ),
                        ],
                      )
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          MandiScreen(
                            allotments: input.mandiAllotments,
                            mandiId: widget.mandiId,
                            smoId: widget.smoId,
                            initData: input.initData,
                          ),
                          CustomerScreen(
                            allotments: input.customerAllotments,
                            mandiId: widget.mandiId,
                            smoId: widget.smoId,
                            initData: input.initData,
                          ),
                        ],
                      ),
                floatingActionButton: FloatingActionButton(
                  onPressed: () => _showBottomSheet(
                    context,
                  ),
                  child: Icon(Icons.add),
                ),
              );
            },
          );
        },
      ),
    );
  }
}

class MandiScreen extends StatelessWidget {
  final List<AllotmentV2>? allotments;
  final int mandiId;
  final int smoId;
  final AllotmentInit initData;

  const MandiScreen({
    super.key,
    this.allotments,
    required this.mandiId,
    required this.smoId,
    required this.initData,
  });

  @override
  Widget build(BuildContext context) {
    if (allotments == null)
      return Center(
        child: CircularProgressIndicator(),
      );
    if (allotments!.isEmpty)
      return EmptyScreen(
        message: getLangText('allocation.noAllocationFoundTitle', 'No Allocation Found!'),
        description: getLangText('allocation.noAllocationrDescription', 'Please create new allocation!'),
      );

    return RefreshIndicator(
      onRefresh: () async {
        context.read<InventoryAllocationBloc>().add(InventoryAllocationEvent.getAllotments(smoId));
        return Future.delayed(Duration(seconds: 1));
      },
      child: ListView.builder(
        itemCount: allotments!.length, // Just for demonstration purposes
        itemBuilder: (context, index) => ExpandableCard(
          allotment: allotments![index],
          mandiId: mandiId,
          smoId: smoId,
          isCustomer: false,
          initData: initData,
        ),
      ),
    );
  }
}

class CustomerScreen extends StatelessWidget {
  final List<AllotmentV2>? allotments;
  final int mandiId;
  final int smoId;
  final AllotmentInit initData;

  const CustomerScreen({
    super.key,
    required this.allotments,
    required this.mandiId,
    required this.smoId,
    required this.initData,
  });
  @override
  Widget build(BuildContext context) {
    if (allotments == null)
      return Center(
        child: CircularProgressIndicator(),
      );
    if (allotments!.isEmpty)
      return EmptyScreen(
        message: getLangText('allocation.noAllocationFoundTitle', 'No Allocation Found!'),
        description: getLangText('allocation.noAllocationrDescription', 'Please create new allocation!'),
      );

    return RefreshIndicator(
      onRefresh: () async {
        context.read<InventoryAllocationBloc>().add(InventoryAllocationEvent.getAllotments(smoId));
        return Future.delayed(Duration(seconds: 1));
      },
      child: ListView.builder(
        itemCount: allotments!.length, // Just for demonstration purposes
        itemBuilder: (context, index) => ExpandableCard(
          allotment: allotments![index],
          mandiId: mandiId,
          smoId: smoId,
          isCustomer: true,
          initData: initData,
        ),
      ),
    );
  }
}

class ExpandableCard extends StatefulWidget {
  final AllotmentV2 allotment;
  final int mandiId;
  final int smoId;
  final bool isCustomer;
  final AllotmentInit initData;

  const ExpandableCard({
    super.key,
    required this.allotment,
    required this.mandiId,
    required this.smoId,
    required this.isCustomer,
    required this.initData,
  });
  @override
  _ExpandableCardState createState() => _ExpandableCardState();
}

class _ExpandableCardState extends State<ExpandableCard> {
  bool isExpanded = false;
  ProcessAction? primaryAction;
  List<ProcessAction> extraActions = [];
  bool isDownloadingPdf = false;

  @override
  void initState() {
    primaryAction = widget.allotment.primaryAction();
    extraActions = widget.allotment.extraActions();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant ExpandableCard oldWidget) {
    primaryAction = widget.allotment.primaryAction();
    extraActions = widget.allotment.extraActions();
    super.didUpdateWidget(oldWidget);
  }

  void onAction(ProcessAction processAction) async {
    if (processAction == ProcessAction.DISPATCH) {
      await context.push(
        context.namedLocation(
          'trips',
          pathParameters: {
            'allotmentId': widget.allotment.id.toString(),
            'mandiId': widget.mandiId.toString(),
            'smoId': widget.smoId.toString(),
          },
        ),
        extra: context,
      );
      // ignore: use_build_context_synchronously
      context.read<InventoryAllocationBloc>().add(InventoryAllocationEvent.getAllotments(widget.smoId));
    } else if (processAction == ProcessAction.CANCEL) {
      final shouldCancel = await context.showAlertDialog(title: 'Cancel!', message: 'Are you sure you want to cancel the allocation?') ?? false;
      if (shouldCancel) {
        context.read<InventoryAllocationBloc>().add(InventoryAllocationEvent.cancelAllocation(widget.allotment.id, widget.smoId));
      }
    } else if (processAction == ProcessAction.DOWNLOAD_PDF) {
      showSnackBar('Downloading PDF...');
      if (isDownloadingPdf) {
        return;
      }

      final result = await GetAllocationPdfRequest(allocationId: widget.allotment.id, customerGroup: widget.allotment.customerGroup, pdfType: 'TRIPS', selectedSkus: []).execute();
      if (result.isLeft) {
        showSnackBar('Error while downloading pdf');
        isDownloadingPdf = false;
        setState(() {});
        return;
      }
      setState(() {
        isDownloadingPdf = false;
      });
      final url = result.right.firstOrNull;
      if (url != null) {
        final downloadUrl = Uri.parse(url);
        await launchUrl(downloadUrl, mode: LaunchMode.externalApplication);
      }
    }
  }

  List<Widget> _showCustomerGroupData() {
    if (!widget.isCustomer)
      return [
        SizedBox()
      ];

    return [
      infoRowText(Icons.punch_clock_outlined, 'deliverySlot'.tr('Delivery Slot:') + " :", widget.allotment.deliverySlot),
      infoRowText(Icons.alarm, 'deliveryDate'.tr('Delivery Date') + " :", widget.allotment.deliveryDate.toDate("dd/MM/yyyy")),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () async {
          if (widget.allotment.canAllot()) {
            context.read<InventoryAllocationBloc>().add(
                  InventoryAllocationEvent.openAllotment(
                      widget.allotment.id,
                      widget.allotment.destinationName,
                      widget.isCustomer
                          ? {
                              'customerGroup': widget.allotment.customerGroup,
                              'deliverySlot': widget.allotment.deliverySlot,
                              'deliveryDate': widget.allotment.deliveryDate.toString(),
                              'status': widget.allotment.status.key,
                              'canAllocateExcess': widget.initData.canExcessAllowed(widget.allotment.destinationName).toString()
                            }
                          : {
                              'status': widget.allotment.status.key,
                            }),
                );
          } else if (widget.allotment.canDispatch()) {
            await context.push(
              context.namedLocation(
                'trips',
                pathParameters: {
                  'allotmentId': widget.allotment.id.toString(),
                  'mandiId': widget.mandiId.toString(),
                  'smoId': widget.smoId.toString(),
                },
              ),
              extra: context,
            );
            // ignore: use_build_context_synchronously
            context.read<InventoryAllocationBloc>().add(InventoryAllocationEvent.getAllotments(widget.smoId));
          }
        },
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 0,
              vertical: 0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Left 80% section
                    Expanded(
                      flex: 6,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16, top: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.isCustomer ? widget.allotment.customerGroup : widget.allotment.destinationName,
                              style: TextStyle(
                                fontSize: 20,
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(
                              height: 4,
                            ),
                            Text(
                              widget.allotment.consignmentId,
                              softWrap: true,
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            ..._showCustomerGroupData(),
                          ],
                        ),
                      ),
                    ),
                    if (widget.allotment.actions.isNotEmpty)
                      Center(
                        child: Container(
                          height: 70,
                          color: Colors.grey[300],
                          width: 1,
                        ),
                      ),
                    // Right 20% section
                    if (widget.allotment.actions.isNotEmpty)
                      Expanded(
                        flex: 3,
                        child: Container(
                          height: 80,
                          child: Stack(
                            children: [
                              if (widget.allotment.showMenu)
                                Align(
                                  alignment: Alignment.topRight,
                                  child: PopupMenuButton(
                                    itemBuilder: (context) => extraActions
                                        .map((e) => PopupMenuItem(
                                              child: Text(e.value),
                                              value: e,
                                            ))
                                        .toList(),
                                    onSelected: (value) async {
                                      onAction(value);
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                        horizontal: 8,
                                      ),
                                      child: Icon(
                                        Icons.more_vert,
                                      ),
                                    ),
                                  ),
                                ),
                              if (primaryAction != null)
                                Padding(
                                  padding: EdgeInsets.only(
                                    top: 32,
                                  ),
                                  // alignment: Alignment.bottomCenter,
                                  child: Center(
                                    child: InkWell(
                                      onTap: () {
                                        onAction(primaryAction!);
                                      },
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Icon(
                                            primaryAction!.icon,
                                            color: primaryAction!.color,
                                            size: 24,
                                          ),
                                          Text(
                                            primaryAction!.value,
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: primaryAction!.color,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 2,
                    horizontal: 16,
                  ),
                  child: Divider(
                    thickness: 0.5,
                    color: Colors.grey[300],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    left: 16,
                    right: 16,
                    bottom: 10,
                  ),
                  child: GestureDetector(
                    onTap: !widget.allotment.showTimeline()
                        ? null
                        : () => setState(() {
                              isExpanded = !isExpanded;
                            }),
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        decoration: BoxDecoration(color: widget.allotment.status.color, borderRadius: BorderRadius.all(Radius.circular(14))),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          child: Text(
                            widget.isCustomer && widget.allotment.status == ProcessStage.COMPLETED ? 'Completed' : widget.allotment.status.value,
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                if (isExpanded) _buildTimeline(widget.isCustomer),
                if (isExpanded)
                  SizedBox(
                    height: 8,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimeLineItem({
    bool showLine = true,
    Color lineColor = Colors.black,
    Color dotColor = Colors.green,
    required Widget child,
  }) {
    return Stack(
      children: [
        Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 0, bottom: 20, left: 32),
                child: child,
              ),
            ],
          ),
        ),
        if (showLine)
          Positioned(
            top: 6,
            bottom: 0.0,
            left: 16.0,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 8,
              ),
              child: new Container(
                height: double.infinity,
                width: 1.1,
                color: lineColor,
              ),
            ),
          ),
        Positioned(
          top: 6.0,
          left: 12.0,
          child: Padding(
            padding: const EdgeInsets.only(top: 0),
            child: new Container(
              height: 10.0,
              width: 10.0,
              decoration: new BoxDecoration(
                shape: BoxShape.circle,
                color: dotColor,
              ),
              child: new Container(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeline(bool isCustomer) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < widget.allotment.processStage.length; i++)
          _buildTimeLineItem(
            child: Text(
              isCustomer && widget.allotment.processStage[i] == ProcessStage.COMPLETED ? 'Completed' : widget.allotment.processStage[i].value,
              style: TextStyle(
                color: i == widget.allotment.processStage.length - 1 ? widget.allotment.processStage[i].color : Colors.black87,
                fontSize: i == widget.allotment.processStage.length - 1 ? 16 : 14,
                fontWeight: i == widget.allotment.processStage.length - 1 ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            showLine: i != widget.allotment.processStage.length - 1,
            dotColor: i == widget.allotment.processStage.length - 1 ? widget.allotment.processStage[i].color : Colors.black87,
          ),
      ],
    );
  }
}
