import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';

@singleton
class PhoneNumberCache {
  PhoneNumberCache(@Named('HiveDirPath') this._hiveDirPath) {
    init();
  }

  Future<void> init() async {
    await Hive.initFlutter();
  }

  final String _hiveDirPath;
  static const String boxName = 'phoneNumberBox';

  Future<Box<String>> get _box async =>
      await Hive.openBox<String>(boxName, path: _hiveDirPath);

  Future<List<String>> getPhoneNumbers() async {
    final box = await _box;
    return box.values.toList();
  }

  Future<void> updatePhoneNumbers(List<String> phoneNumbers) async {
    final box = await _box;
    await box.clear();
    await box.addAll(phoneNumbers);
  }

  Future<void> addPhoneNumber(String phoneNumber) async {
    final numbers = await getPhoneNumbers();
    if (numbers.contains(phoneNumber)) return;
    await updatePhoneNumbers([...numbers, phoneNumber]);
  }
}
