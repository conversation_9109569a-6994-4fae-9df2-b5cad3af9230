import 'package:flutter/material.dart';

class ViewFullImage extends StatelessWidget {
  const ViewFullImage({super.key, required this.image});

  final ImageProvider image;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              title: const Text('View'),
            ),
            body: Padding(
              padding: const EdgeInsets.all(16),
              child:Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                  image: DecorationImage(image: image, fit: BoxFit.contain)),
            ))));
  }
}
