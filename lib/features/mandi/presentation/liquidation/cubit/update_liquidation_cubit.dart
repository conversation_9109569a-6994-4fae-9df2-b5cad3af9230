import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/weight_summary_card.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/ui/vendor_cache.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/create_liquidation_order_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/download_liquidation_image_request%20copy.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_liquidation_order_by_id_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/update_liquidation_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';

part 'update_liquidation_state.dart';
part 'update_liquidation_cubit.freezed.dart';

@injectable
class UpdateLiquidationCubit extends Cubit<UpdateLiquidationState> {
  UpdateLiquidationCubit() : super(UpdateLiquidationState.initial());

  void init(LiquidationOrder? order, List<Sku> skus) async {
    if (order != null) {
      final inputOrders = <int, List<LiquidationInputModel>>{};
      final skuMap = Map.fromEntries(skus.map((e) => MapEntry(e.id, e)));
      final imageKeys = List<String>.from(order.images);
      order.liquidationItems.forEach((e) {
        final list = inputOrders[e.skuQuantity.skuId] ?? [];
        final sku = skuMap[e.skuQuantity.skuId];
        if (sku != null) {
          list.add(LiquidationInputModel(
              item: e,
              sku: sku,
              files: e.images.map((e) => PickedFile.fromUploadPath(e)).toList(),
              procurementType: e.skuQuantity.isBulk
                  ? ProcurementType.bulk
                  : ProcurementType.lots,
              procurementUnit: e.skuQuantity.unit,
              lotSize: e.skuQuantity.lotSize,
              quantity: e.skuQuantity.quantity?.toString() ?? '',
              agreedPrice: e.agreedAmount?.toString() ?? ''));
        }
        // list.add(LiquidationInputModel(
        //   sku: Sku(id: id, name: name, lotSizes: lotSizes)
        // ));
        inputOrders[e.skuQuantity.skuId] = list;
      });
      emit(state.copyWith(
        isCreateOrder: false,
        vendorName: order.vendor.name,
        files: order.images.map((e) => PickedFile.fromUploadPath(e)).toList(),
        order: order,
        inputOrders: inputOrders,
        summaryItems:
            getSummaryItems(inputOrders.values.expand((e) => e).toList()),
        notAllowedSkus: inputOrders.values
            .expand((element) => element)
            .map((e) => e.getCompositeKey())
            .toList(),
      ));
      if (imageKeys.isNotEmpty) {
        // Download images here
        final downloadResult = await DownloadLiquidationImageRequest(
                smoId: order.smoId, imageKeys: imageKeys)
            .execute();
        if (downloadResult.isRight) {
          final imagesMap = downloadResult.right;
          final files = state.files
              .map((e) => e.copyWith(uploadUrl: imagesMap[e.uploadKey]))
              .toList();
          emit(state.copyWith(files: files));
        }
      }
    }
  }

  List<SummaryItem> getSummaryItems(List<LiquidationInputModel> items) {
    final Map<String, double> quantity = {};
    final Map<String, int> count = {};
    for (final item in items) {
      if (item.isBulk) {
        final key = item.procurementUnit.toUpperCase();
        quantity[key] = (quantity[key] ?? 0) + item.quantity.toDouble();
        count[key] = (count[key] ?? 0) + 1;
      }
    }

    return quantity.entries
        .map((e) => SummaryItem(
              unit: e.key.capitalize(),
              quantity: e.value,
              count: count[e.key] ?? 0,
            ))
        .toList();
  }

  // void _uploadSkuImages() async {
  //   final allImages = <PickedFile>[];
  //   state.inputOrders.values.expand((element) => element).forEach((element) {
  //     allImages.fi
  //     element.files.forEach((e) async {
  //       if (e.uploadKey == null) {
  //         final uploadResult = await e.upload();
  //         if (uploadResult.isRight) {
  //           final uploadKey = uploadResult.right;
  //           final files = state.files
  //               .map((e) => e.copyWith(uploadKey: uploadKey))
  //               .toList();
  //           emit(state.copyWith(files: files));
  //         }
  //       }
  //     });
  //   });
  // }

  void addOrder(LiquidationInputModel input) {
    final list =
        List<LiquidationInputModel>.from(state.inputOrders[input.sku.id] ?? []);
    list.add(input);
    final orders = Map.fromEntries(state.inputOrders.entries);
    orders[input.sku.id] = list;
    final notAllowedSkus = List<String>.from(state.notAllowedSkus);
    notAllowedSkus.add(input.getCompositeKey());
    emit(state.copyWith(
        inputOrders: orders,
        summaryItems: getSummaryItems(orders.values.expand((e) => e).toList()),
        notAllowedSkus: notAllowedSkus));
  }

  void updateOrder(LiquidationInputModel input, int i) {
    final list =
        List<LiquidationInputModel>.from(state.inputOrders[input.sku.id] ?? []);
    final previousCompositeKey = list[i].getCompositeKey();
    list[i] = input;
    final orders = Map.fromEntries(state.inputOrders.entries);
    orders[input.sku.id] = list;
    final notAllowedSkus = List<String>.from(state.notAllowedSkus);
    notAllowedSkus.remove(previousCompositeKey);
    notAllowedSkus.add(input.getCompositeKey());
    emit(state.copyWith(
        inputOrders: orders,
        summaryItems: getSummaryItems(orders.values.expand((e) => e).toList()),
        notAllowedSkus: notAllowedSkus));
  }

  void deleteOrder(int skuId, int i) {
    final list =
        List<LiquidationInputModel>.from(state.inputOrders[skuId] ?? []);
    final itemRemoved = list.removeAt(i);
    final orders = Map.fromEntries(state.inputOrders.entries);
    if (list.isEmpty) {
      orders.remove(skuId);
    } else {
      orders[skuId] = list;
    }
    final notAllowedSkus = List<String>.from(state.notAllowedSkus);
    notAllowedSkus.remove(itemRemoved.getCompositeKey());
    emit(state.copyWith(
        inputOrders: orders,
        summaryItems: getSummaryItems(orders.values.expand((e) => e).toList()),
        notAllowedSkus: notAllowedSkus));
  }

  void submit(
    int smoId,
    String vendorName,
    String mobileNumber,
  ) async {
    emit(state.copyWith(isCtaLoading: true));
    if (state.isCreateOrder) {
      final result = await CreateLiquidationOrderRequest(
              smoId: smoId,
              vendorName: vendorName,
              mobileNumber: mobileNumber,
              inputModels: state.inputOrders.values
                  .expand((element) => element)
                  .toList(),
              images: state.files.map((e) => e.uploadKey ?? '').toList())
          .execute();
      final newState = result.fold(
        (left) => state.copyWith(message: left.message, isCtaLoading: false),
        (right) => state.copyWith(
          message: 'Liquidation Order Created Successfully!',
          shouldPop: true,
        ),
      );
      if (result.isRight) {
        await di.get<VendorCache>().updateVendorNameAndMobile(
              vendorName,
              vendorMobile: mobileNumber,
            );
      }
      emit(newState);
    } else {
      final result = await UpdateLiquidationOrderRequest(
              order: state.order!.copyWith(
                  images: state.files.map((e) => e.uploadKey ?? '').toList()),
              smoId: smoId,
              inputOrders: state.inputOrders.values
                  .expand((element) => element)
                  .toList())
          .execute();
      final newState = result.fold(
        (left) => state.copyWith(message: left.message, isCtaLoading: false),
        (right) => state.copyWith(
          message: 'Liquidation Order Updated Successfully!',
          shouldPop: true,
        ),
      );
      emit(newState);
    }
  }

  void clearMessage() {
    emit(state.copyWith(message: null, shouldPop: false));
  }

  void updateFiles(List<PickedFile> files) {
    emit(state.copyWith(files: files));
  }

  void loadByItemId(int itemId, List<Sku> skus) async {
    emit(state.copyWith(shouldShowLoading: true));
    final result =
        await GetLiquidationOrderByIdRequest(itemId: itemId).execute();
    final newState = result.fold(
      (left) => state.copyWith(message: left.message, shouldShowLoading: false),
      (right) => state.copyWith(
        shouldShowLoading: false,
      ),
    );
    emit(newState);
    if (result.isRight) {
      init(result.right, skus);
    }
  }
}
