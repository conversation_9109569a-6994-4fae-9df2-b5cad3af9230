import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

part 'sku_quantity_dto.freezed.dart';
part 'sku_quantity_dto.g.dart';

@freezed
class SkuQuantityDto with _$SkuQuantityDto {
  const factory SkuQuantityDto({
    required int skuId,
    required String type,
    required String unit,
    @Default(null) double? lotSize,
    required double quantity,
  }) = _SkuQuantityDto;

  const SkuQuantityDto._();
  bool get isBulk => type.toLowerCase() == 'bulk';
  String get compositeKey =>
      isBulk ? '$skuId-$type-$unit' : '$skuId-$type-$unit-$lotSize';

  SkuQuantity get toSkuQuantity => SkuQuantity(
        skuId: skuId,
        type: type,
        unit: unit,
        lotSize: lotSize,
        quantity: quantity,
      );

  @override
  factory SkuQuantityDto.fromJson(Map<String, dynamic> json) =>
      _$SkuQuantityDtoFromJson(json);
}
