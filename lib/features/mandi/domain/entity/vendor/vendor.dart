import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'vendor.freezed.dart';
part 'vendor.g.dart';

@freezed
class Vendor with _$Vendor implements DropDownItem {
  const Vendor._();

  const factory Vendor({
    required int vendorId,
    required String name,
    required String mobile,
    required List<VendorLocation> locations,
  }) = _Vendor;

  @override
  factory Vendor.fromJson(Map<String, dynamic> json) => _$VendorFromJson(json);

  @override
  String get displayText => name;
}

@freezed
class VendorLocation with _$VendorLocation implements DropDownItem {
  const VendorLocation._();
  const factory VendorLocation({
    required int locationId,
    required String? address,
    required String vendorName,
    required String vendorPhone,
    required String pocName,
    required String pocPhone,
    required String searchKey,
    @Default(null) int? procurementAreaId,
  }) = _VendorLocation;

  @override
  factory VendorLocation.fromJson(Map<String, dynamic> json) =>
      _$VendorLocationFromJson(json);

  @override
  String get displayText => pocName;
}
