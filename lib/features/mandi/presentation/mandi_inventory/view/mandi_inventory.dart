import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/presentation/widgets/w_tab_bar.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/inventory_type.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/mandi_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/view/mandi_transfer_poup.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/util/get_mandi_name.dart';

class MandiInventoryPage extends StatefulWidget {
  const MandiInventoryPage({
    super.key,
    required this.mandiId,
    required this.smoId,
  });
  final int mandiId;
  final int smoId;
  @override
  State<MandiInventoryPage> createState() => _MandiInventoryPage();
}

class _MandiInventoryPage extends State<MandiInventoryPage>
    with TickerProviderStateMixin {
  late TabController controller;
  late TabController mandiController;
  late TabController ungradeController;
  Map<int, Sku> skuMap = {};
  Timer? timer;

  String? gradeBTimerMessage;
  String? gradeD1TimerMessage;
  String? gradeCTimerMessage;
  String? gradeATimerMessage;

  @override
  void initState() {
    controller = TabController(length: 2, vsync: this);
    mandiController = TabController(length: 5, vsync: this);
    ungradeController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final state = context.read<SkuBloc>().state;
      _handleSkuStateChange(state);
      final inventoryConfig = context
          .read<SmoBloc>()
          .state
          .mapOrNull(success: (s) => s.config.inventory);

      if (inventoryConfig != null) {
        timer = Timer.periodic(Duration(seconds: 1), (timer) {
          updateGradeD1Message(inventoryConfig);
          updateGradeCMessage(inventoryConfig);
          updateGradeBMessage(inventoryConfig);
          updateGradeAMessage(inventoryConfig);
          setState(() {});
        });
      }
    });

    super.initState();
  }

  void updateGradeBMessage(InventoryConfig inventoryConfig) {
    final allowedTime = inventoryConfig.transferTiming.gradeB
        .getNearestAllowedTime(DateTime.now());
    if (allowedTime != null) {
      gradeBTimerMessage =
          calculateTime(allowedTime.startTime, allowedTime.endTime);
    } else {
      gradeBTimerMessage = null;
    }
  }

  void updateGradeAMessage(InventoryConfig inventoryConfig) {
    final allowedTime = inventoryConfig.transferTiming.gradeA
        .getNearestAllowedTime(DateTime.now());
    if (allowedTime != null) {
      gradeATimerMessage =
          calculateTime(allowedTime.startTime, allowedTime.endTime);
    } else {
      gradeATimerMessage = null;
    }
  }

  void updateGradeCMessage(InventoryConfig inventoryConfig) {
    final allowedTime = inventoryConfig.transferTiming.gradeC
        .getNearestAllowedTime(DateTime.now());
    if (allowedTime != null) {
      gradeCTimerMessage =
          calculateTime(allowedTime.startTime, allowedTime.endTime);
    } else {
      gradeCTimerMessage = null;
    }
  }

  void updateGradeD1Message(InventoryConfig inventoryConfig) {
    final allowedTime = inventoryConfig.transferTiming.gradeD1
        .getNearestAllowedTime(DateTime.now());
    if (allowedTime != null) {
      gradeD1TimerMessage =
          calculateTime(allowedTime.startTime, allowedTime.endTime);
    } else {
      gradeD1TimerMessage = null;
    }
  }

  @override
  void dispose() {
    controller.dispose();
    mandiController.dispose();
    ungradeController.dispose();
    timer?.cancel();
    super.dispose();
  }

  String? calculateTime(String? startTime, String? endTime) {
    if (startTime == null || endTime == null) return null;
    final startTimeSplits = startTime.split(':');
    final startHour = startTimeSplits.length > 0 ? startTimeSplits[0] : '0';
    final startMinute = startTimeSplits.length > 1 ? startTimeSplits[1] : '0';
    final startSecond = startTimeSplits.length > 2 ? startTimeSplits[2] : '0';
    final endTimeSplits = endTime.split(":");
    final endHour = endTimeSplits.length > 0 ? endTimeSplits[0] : '0';
    final endMinute = endTimeSplits.length > 1 ? endTimeSplits[1] : '0';
    final endSecond = endTimeSplits.length > 2 ? endTimeSplits[2] : '0';
    final now = DateTime.now();
    DateTime start = DateTime(now.year, now.month, now.day,
        int.parse(startHour), int.parse(startMinute), int.parse(startSecond));
    DateTime end = DateTime(now.year, now.month, now.day, int.parse(endHour),
        int.parse(endMinute), int.parse(endSecond));

    if (now.isBefore(start)) {
      Duration difference = start.difference(now);
      return 'Starts in ${difference.inHours}:${difference.inMinutes.remainder(60)}:${difference.inSeconds.remainder(60)}';
    } else if (now.isAfter(end)) {
      DateTime nextStart = start.add(Duration(days: 1));
      Duration difference = nextStart.difference(now);
      return 'Starts in ${difference.inHours}:${difference.inMinutes.remainder(60)}:${difference.inSeconds.remainder(60)}';
    } else {
      return '';
      // Duration difference = end.difference(now);
      // return 'Ends in ${difference.inHours}:${difference.inMinutes.remainder(60)}:${difference.inSeconds.remainder(60)}';
    }
  }

  void _generateSkuMap(List<Sku> skus) {
    final map = <int, Sku>{};
    for (final sku in skus) {
      map[sku.id] = sku;
    }
    setState(() {
      skuMap = map;
    });
  }

  void _handleSkuStateChange(SkuState state) {
    state.maybeMap(
      orElse: () {},
      success: (s) {
        _generateSkuMap(s.skus);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MandiInventoryBloc, MandiInventoryState>(
      listener: (context, state) {
        final message = state.message;
        if (message != null) {
          showSnackBar(message);
          context.read<MandiInventoryBloc>().add(
                MandiInventoryEvent.clearMessage(),
              );
        }
      },
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            appBar: appBar(),
            body: BlocConsumer<SkuBloc, SkuState>(
              listener: (context, state) {
                _handleSkuStateChange(state);
              },
              builder: (context, skuState) {
                return skuState.map(
                  initial: (_) {
                    return const Center(child: CircularProgressIndicator());
                  },
                  error: (error) {
                    return ErrorScreen(
                      message: 'Error while fetching skus!',
                      onPressed: () {
                        context.read<SkuBloc>().add(SkuEvent.fetch());
                      },
                    );
                  },
                  success: (success) {
                    return TabBarView(
                      controller: controller,
                      children: [
                        mandiContent(state),
                        ungradedContent(state),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget mandiContent(MandiInventoryState state) {
    final gradeA = state.mandi.copyWith(
        items: state.mandi.items
            .where((e) => skuMap[e.first.skuId]?.grade == 'A')
            .toList());
    final gradeC = state.mandi.copyWith(
        items: state.mandi.items
            .where((e) => skuMap[e.first.skuId]?.grade == 'C')
            .toList());
    final gradeD1 = state.mandi.copyWith(
        items: state.mandi.items
            .where((e) => skuMap[e.first.skuId]?.grade == 'D1')
            .toList());

    final gradeB = state.mandi.copyWith(
        items: state.mandi.items
            .where((e) => skuMap[e.first.skuId]?.grade == 'B')
            .toList());
    return Column(
      children: [
        WTabBar(
          tabs: [
            Tab(
              text: 'all'.tr('All'),
            ),
            Tab(
              text: 'gradeA'.tr('Grade A'),
            ),
            Tab(
              text: 'gradeB'.tr('Grade B'),
            ),
            Tab(
              text: 'gradeD1'.tr('Grade D1'),
            ),
            Tab(
              text: 'gradeC'.tr('Grade C'),
            ),
          ],
          controller: mandiController,
          isScrollable: true,
        ),
        Expanded(
          child: TabBarView(
            controller: mandiController,
            children: [
              content(
                state.mandi,
                () {
                  context.read<MandiInventoryBloc>().add(
                        MandiInventoryEvent.fetchMandiInventory(
                          widget.mandiId,
                          type: InventoryType.primary,
                        ),
                      );
                },
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: content(
                      gradeA,
                      () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.fetchMandiInventory(
                                widget.mandiId,
                                type: InventoryType.primary,
                              ),
                            );
                      },
                    ),
                  ),
                  if (gradeA.items.isNotEmpty)
                    transferWidget(
                      transferMessage: gradeATimerMessage,
                      isLoading: state.isGradeATransferLoading,
                      label: 'transferToB'.tr('Transfer to B'),
                      onPressed: () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.transferGradeA(
                                smoId: widget.smoId,
                                skus: gradeA.items.expand((e) => e).toList(),
                                mandiId: widget.mandiId,
                              ),
                            );
                      },
                    ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: content(
                      gradeB,
                      () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.fetchMandiInventory(
                                widget.mandiId,
                                type: InventoryType.primary,
                              ),
                            );
                      },
                    ),
                  ),
                  if (gradeB.items.isNotEmpty)
                    transferWidget(
                      transferMessage: gradeBTimerMessage,
                      isLoading: state.isGradeBTransferLoading,
                      label: 'transferToD1'.tr('Transfer to D1'),
                      onPressed: () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.transferGradeB(
                                smoId: widget.smoId,
                                skus: gradeB.items.expand((e) => e).toList(),
                                mandiId: widget.mandiId,
                              ),
                            );
                      },
                    ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: content(
                      gradeD1,
                      () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.fetchMandiInventory(
                                widget.mandiId,
                                type: InventoryType.primary,
                              ),
                            );
                      },
                    ),
                  ),
                  if (gradeD1.items.isNotEmpty)
                    transferWidget(
                      transferMessage: gradeD1TimerMessage,
                      isLoading: state.isGradeD1TransferLoading,
                      label: 'transferToC'.tr('Transfer to C'),
                      onPressed: () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.transferGradeD1(
                                smoId: widget.smoId,
                                skus: gradeD1.items.expand((e) => e).toList(),
                                mandiId: widget.mandiId,
                              ),
                            );
                      },
                    ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: content(
                      gradeC,
                      () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.fetchMandiInventory(
                                widget.mandiId,
                                type: InventoryType.primary,
                              ),
                            );
                      },
                    ),
                  ),
                  if (gradeC.items.isNotEmpty)
                    transferWidget(
                      transferMessage: gradeCTimerMessage,
                      isLoading: state.isGradeCTransferLoading,
                      label: 'transferToDump'.tr('Transfer to Dump'),
                      onPressed: () {
                        context.read<MandiInventoryBloc>().add(
                              MandiInventoryEvent.transferGradeC(
                                smoId: widget.smoId,
                                skus: gradeC.items.expand((e) => e).toList(),
                                mandiId: widget.mandiId,
                              ),
                            );
                      },
                    ),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget transferWidget({
    required String? transferMessage,
    required bool isLoading,
    required String label,
    required VoidCallback onPressed,
  }) {
    if (transferMessage == null) return SizedBox();

    return Column(
      children: [
        if (transferMessage.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              transferMessage,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.amber,
              ),
            ),
          ),
        WStickyBottomCta(
          isEnabled: transferMessage.isEmpty && !isLoading,
          isLoading: isLoading,
          icon: Icons.transfer_within_a_station,
          label: Text(label),
          onPressed: () {
            showDialog(
              context: context,
              builder: (_) => MandiTransferPopup(
                onPressed: () {
                  onPressed();
                  context.pop();
                },
                title: 'transferTitle'.tr('Are you sure'),
                body:
                    'transferBody'.tr('Do you want to transfer the inventory?'),
                message: 'transferMessage'
                    .tr('Once transferred, you can not revert it back.'),
                ctaTitle: 'transferCtaTitle'.tr('Transfer'),
              ),
            );
          },
        )
      ],
    );
  }

  Widget ungradedContent(MandiInventoryState state) {
    return Column(
      children: [
        WTabBar(
          tabs: [
            Tab(
              text: 'returns'.tr('Returns'),
            ),
            Tab(
              text: 'wastageReturns'.tr('Wastage Returns'),
            ),
          ],
          controller: ungradeController,
        ),
        Expanded(
          child: TabBarView(
            controller: ungradeController,
            children: [
              content(
                state.returns,
                () {
                  context.read<MandiInventoryBloc>().add(
                        MandiInventoryEvent.fetchMandiInventory(
                          widget.mandiId,
                          type: InventoryType.returns,
                        ),
                      );
                },
              ),
              content(
                state.wastageReturns,
                () {
                  context.read<MandiInventoryBloc>().add(
                        MandiInventoryEvent.fetchMandiInventory(
                          widget.mandiId,
                          type: InventoryType.gradeC,
                        ),
                      );
                },
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget content(
    InventoryState state,
    VoidCallback onRefresh,
  ) {
    if (state.items.isEmpty && state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (state.items.isEmpty && state.errorMessage != null) {
      return ErrorScreen(
          onPressed: onRefresh,
          message: state.errorMessage ?? 'Error while loading data!');
    }

    if (state.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'No data found!',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 16,
            ),
            ElevatedButton(
              onPressed: onRefresh,
              child: Text('Refresh'),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (state.isLoading)
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            color: Colors.green.shade200,
            child: Text('Loading...'),
          ),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          color: Colors.grey.shade200,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: LangText(
                  'unit',
                  'Unit',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(
                width: 4,
              ),
              Expanded(
                flex: 2,
                child: LangText(
                  'quantity',
                  'Quantity',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
        Expanded(
            child: RefreshIndicator(
          onRefresh: () {
            onRefresh();
            return Future.value();
          },
          child: Scrollbar(
            child: ListView(
              children: [
                for (final items in state.items) skuCard(items),
              ],
            ),
          ),
        )),
      ],
    );
  }

  Widget skuCard(List<InventoryItem> items) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 16,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              skuMap[items.first.skuId]?.name ?? '-',
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            for (final input in items)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        input.unitString(),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        input.quantity.asString(),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  AppBar appBar() {
    final mandiName = getMandiName(context, mandiId: widget.mandiId);

    return WAppBar.getAppBar(
      title: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
              LanguageEnum.mandiInventoryTitle.localized(
                params: {
                  'mandiName': mandiName,
                },
              ),
              style: const TextStyle(fontSize: 18)),
        ],
      ),
      centerTitle: false,
      bottom: WTabBar(
        tabs: [
          Tab(
            text: 'mandi'.tr('Mandi'),
          ),
          Tab(
            text: 'ungraded'.tr('Ungraded'),
          ),
        ],
        controller: controller,
      ),
    );
  }
}
