import 'package:hive_flutter/hive_flutter.dart';

class LocalStorageInterface {
  final String storageKey;
  final String dirPath;

  LocalStorageInterface(this.storageKey, this.dirPath) {
    init();
  }

  Future<void> init() async {
    await Hive.initFlutter();
  }

  Future<Box<String>> get _box async =>
      await Hive.openBox<String>(storageKey, path: dirPath);

  Future<String?> read(String key) async {
    try {
      final box = await _box;
      return box.get(key);
    } catch (e) {
      return null;
    }
  }

  Future<void> write(String key, String value) async {
    try {
      final box = await _box;
      await box.put(key, value);
    } catch (e) {}
  }

  Future<void> remove(String key) async {
    try {
      final box = await _box;
      await box.delete(key);
    } catch (e) {}
  }
}
