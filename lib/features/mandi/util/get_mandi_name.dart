import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';

String getMandiName(BuildContext context, {required int mandiId}) {
  try {
    final mandiName = context.read<MandiBloc>().state.maybeMap(
          orElse: () => '',
          success: (s) =>
              s.allMandis.firstWhere((element) => element.id == mandiId).name,
        );
    return mandiName;
  } catch (e) {
    return '';
  }
}
