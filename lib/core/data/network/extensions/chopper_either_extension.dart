// ignore_for_file: avoid_dynamic_calls

import 'package:chopper/chopper.dart';
import 'package:either_dart/either.dart';
import 'package:http_status_code/http_status_code.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/network/model/error_dto.dart';
import 'package:proc2/core/data/network/util/network_status_code.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/show_snackbar.dart';

extension EitherChopperCall on ChopperService {
  Future<Either<ErrorResult<E?>, T>> safeCall<E, T>({
    required Future<Response<Map<String, dynamic>>> Function() apiCall,
    required T Function(dynamic data) transform,
    required E? Function(dynamic data) errorTransform,
    bool shouldShowSnackBar = true,
  }) async {
    try {
      return apiCall().then((response) {
        if (response.statusCode >= 500 && response.statusCode < 600) {
          return Left(
            ErrorResult<E>(
              message: response.error.toString(),
              code: response.statusCode.toString(),
              timestamp: DateTime.now().millisecondsSinceEpoch,
            ),
          );
        }
        final statusError = checkStatus(response.body ?? {});

        if (statusError != null) {
          talker.handle(statusError);
          if (shouldShowSnackBar) showSnackBar(statusError.message);
          return Left(
            ErrorResult<E>(
              message: statusError.message,
              code: statusError.code,
              timestamp: statusError.timestamp,
            ),
          );
        }

        return _parseResponse(
          response,
          transform,
          errorTransform,
        );
      });
    } catch (e, s) {
      talker.handle(e, s);
      if (shouldShowSnackBar)
        showSnackBar(LanguageEnum.jsonParseError.localized());
      return Left(
        ErrorResult<E>(
          message: LanguageEnum.jsonParseError.localized(),
          code: NetworkStatusCode.jsonParseMisMatchedException,
          timestamp: DateTime.now().millisecondsSinceEpoch,
        ),
      );
    }
  }
}

ErrorResult<dynamic>? checkStatus(Map<String, dynamic> body) {
  final status = body['status'] as bool? ?? false;

  if (!status) {
    final msg = body['error']['message'] as String? ?? 'NO MESSAGE';
    final code = body['error']['code'] as String? ?? 'NO_CODE';
    // ignore: inference_failure_on_instance_creation, only_throw_errors
    return ErrorResult(
      message: code.localized(convertKey: false, defaultValue: msg),
      code: code,
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );
  }
  return null;
}

Either<ErrorResult<E>, T> _parseResponse<E, T>(
  Response<Map<String, dynamic>> response,
  T Function(dynamic data) transform,
  E Function(dynamic data) errorTransform,
) {
  final isSuccessful = response.isSuccessful;
  ;
  final statusCode = response.statusCode;
  final error = response.error;

  if (!isSuccessful) {
    return Left(_parseHttpError(error, statusCode));
  }

  return _parseResponseBody(
    response.body ?? {},
    statusCode,
    transform,
    errorTransform,
  );
}

Either<ErrorResult<E>, T> _parseResponseBody<E, T>(
  Map<String, dynamic> body,
  int statusCode,
  T Function(dynamic data) transform,
  E? Function(dynamic data) errorTransform,
) {
  final status = body['status'] as bool? ?? true;
  final errorBody = body['error'];
  final data = body['data'];

  try {
    if (!status) {
      final errorData = errorTransform(data);
      final error =
          _parseHttpError(errorBody, statusCode, errorData: errorData);
      return Left(error);
    }

    return Right(transform(data));
  } catch (e, s) {
    talker.handle(e, s);
    var errorMessage = LanguageEnum.jsonParseError.localized();
    if (e is String && e.isNotEmpty) {
      errorMessage = e;
    } else if (e is Exception) {
      errorMessage = e.toString();
    }
    return Left(
      ErrorResult(
        message: errorMessage,
        code: NetworkStatusCode.jsonParseMisMatchedException,
        timestamp: DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }
}

ErrorResult<E> _parseHttpError<E>(Object? error, int statusCode,
    {E? errorData}) {
  final defaultError = ErrorResult<E>(
    message: getStatusMessage(statusCode),
    code: statusCode.toString(),
    timestamp: DateTime.now().millisecondsSinceEpoch,
    data: errorData,
  );

  try {
    if (error != null && error is Map<String, dynamic>) {
      final errorDto = ErrorDto.fromJson(error);
      return ErrorResult<E>(
        code: errorDto.code ?? statusCode.toString(),
        message: errorDto.code?.localized(
              convertKey: false,
            ) ??
            getStatusMessage(statusCode),
        timestamp: errorDto.timestamp ?? DateTime.now().millisecondsSinceEpoch,
        data: errorData,
      );
    }
    return defaultError;
  } catch (e, s) {
    talker.handle(e, s);
    return defaultError;
  }
}
