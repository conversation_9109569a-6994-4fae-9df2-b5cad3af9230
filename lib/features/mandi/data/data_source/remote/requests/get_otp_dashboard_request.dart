import 'package:proc2/core/data/network/base_request.dart';

class GetOtpDashboardRequest extends BaseRequest<dynamic, String> {
  final String mobile;

  GetOtpDashboardRequest({required this.mobile});

  @override
  String getPath() => 'auth/getOtp';

  @override
  Map<String, dynamic> getBody() {
    return {
      "mobile": mobile,
    };
  }

  @override
  String mapper(data) {
    if (data != null) {
      return data['otp'];
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
