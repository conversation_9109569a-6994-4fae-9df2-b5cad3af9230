import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/carry_forward/carry_forward_item_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/carry_forward/carry_forward_user_dto.dart';

part 'carry_forward_dto.freezed.dart';
part 'carry_forward_dto.g.dart';

@freezed
class CarryForwardDto with _$CarryForwardDto {
  const factory CarryForwardDto({
    required int carryForwardId,
    required List<CarryForwardItemDto> carryForwardItems,
    @Default(null) CarryForwardUserDto? inventoryPreviouslyClosedBy,
  }) = _CarryForwardDto;

  @override
  factory CarryForwardDto.fromJson(Map<String, dynamic> json) =>
      _$CarryForwardDtoFromJson(json);
}
