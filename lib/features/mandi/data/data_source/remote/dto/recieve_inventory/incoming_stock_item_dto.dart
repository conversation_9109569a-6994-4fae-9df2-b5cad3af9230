import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/sku_quantity_dto.dart';

part 'incoming_stock_item_dto.freezed.dart';
part 'incoming_stock_item_dto.g.dart';

@freezed
class IncomingStockItemDto with _$IncomingStockItemDto {
  const factory IncomingStockItemDto({
    required int id,
    required double expectedQuantity,
    required SkuQuantityDto skuQuantity,
  }) = _IncomingStockItemDto;

  @override
  factory IncomingStockItemDto.fromJson(Map<String, dynamic> json) =>
      _$IncomingStockItemDtoFromJson(json);
}
