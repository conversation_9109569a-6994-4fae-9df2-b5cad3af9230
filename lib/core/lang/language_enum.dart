enum LanguageEnum {
  logoutError(key: 'logoutError', defaultValue: 'Error while logging out!'),
  jsonParseError(
    key: 'jsonParseError',
    defaultValue: 'Error while parsing the json.',
  ),
  appGreetMessage(key: 'appGreetMessage', defaultValue: 'WELCOME TO FLOW'),
  appCompanyName(key: 'appCompanyName', defaultValue: 'WHEELOCITY'),
  selectLanguageHint(key: 'selectLangHint', defaultValue: 'Select a language'),
  loginButtonTitle(
    key: 'loginButtonTitle',
    defaultValue: 'Login with Wheelocity',
  ),
  signInAborted(
    key: 'signInAborted',
    defaultValue: 'SignIn Aborted',
  ),
  firebaseUserNotFound(
    key: 'firebaseUserNotFound',
    defaultValue: 'Unable to get firebase user info.',
  ),
  firebaseSignInRequired(
    key: 'firebaseSignInRequired',
    defaultValue: 'User must be sign-in via firebase.',
  ),
  falseButton(key: 'falseButton', defaultValue: 'No'),
  trueButton(key: 'trueButton', defaultValue: 'Yes'),
  cancelButton(key: 'cancelButton', defaultValue: 'Cancel'),
  confirmButton(key: 'confirmButton', defaultValue: 'Confirm'),
  unit(key: 'unit', defaultValue: 'Unit'),
  lots(key: 'lots', defaultValue: 'Lots'),
  quantity(key: 'quantity', defaultValue: 'Quantity'),
  qty(key: 'qty', defaultValue: 'Qty'),
  shortageOrExcess(key: 'shortageOrExcess', defaultValue: '+/-'),
  weightKg(key: 'weightKg', defaultValue: 'Weight(Kg)'),
  weight(key: 'weight', defaultValue: 'Weight'),
  totalAmount(
    key: 'totalAmount',
    defaultValue: 'Total Amount',
  ),
  none(key: 'none', defaultValue: 'None'),
  bulk(key: 'bulk', defaultValue: 'Bulk'),
  retry(key: 'retry', defaultValue: 'Retry'),
  home(key: 'homeLabel', defaultValue: 'Home'),
  price(
    key: 'price',
    defaultValue: 'Price',
  ),
  remove(
    key: 'remove',
    defaultValue: 'Remove',
  ),
  availableQty(
    key: 'availableQty',
    defaultValue: 'Avl. Qty',
  ),
  invalid(
    key: 'invalid',
    defaultValue: 'Invalid',
  ),
  totalQty(
    key: 'totalQty',
    defaultValue: 'Total Qty',
  ),
  lotSize(
    key: 'lotSize',
    defaultValue: 'Lot Size',
  ),
  sku(
    key: 'sku',
    defaultValue: 'Sku',
  ),
  type(
    key: 'type',
    defaultValue: 'Type',
  ),
  noOfLots(
    key: 'noOfLots',
    defaultValue: 'No. of Lots',
  ),
  loss(key: 'loss', defaultValue: 'Loss'),
  damage(key: 'damage', defaultValue: 'Damages'),
  back(key: 'back', defaultValue: 'Back'),
  //home page
  welcomeBack(
    key: 'home.welcomeBack',
    defaultValue: 'Welcome Back',
  ),
  selectAMandi(
    key: 'home.selectAMandi',
    defaultValue: 'Select a Mandi',
  ),
  // viewProcurementSummary(
  //   key: 'home.viewProcSummary',
  //   defaultValue: 'View Procurement Summary',
  // ),
  signout(
    key: 'home.signout',
    defaultValue: 'Signout',
  ),
  updateLanguageMenu(
    key: 'home.updateLanguageMenu',
    defaultValue: 'Update Language',
  ),
  //end of home page

  // Language Modal
  updateLanguageDialogTitle(
    key: 'lang.alertTitle',
    defaultValue: 'Update Language',
  ),
  updateLanguageDialogCtaLabel(
    key: 'lang.alertCtaLabel',
    defaultValue: 'Update',
  ),

  // Upload Files
  uploadFileCtaLabel(
    key: 'uploadFile.ctaLabel',
    defaultValue: 'Add File',
  ),
  uploadFileEmptyMessage(
    key: 'uploadFile.emptyMessage',
    defaultValue: "Nothing's here",
  ),
  uploadFileCtaUploadLabel(
    key: 'uploadFile.ctaUploadLabel',
    defaultValue: 'Upload Files',
  ),

  //mandi menu
  // slotClosesIn(
  //   key: 'mandiMenu.slotClosesIn',
  //   defaultValue: 'Slot Closes in',
  // ),
  // hours(
  //   key: 'mandiMenu.hours',
  //   defaultValue: 'Hours',
  // ),
  // minutes(
  //   key: 'mandiMenu.minutes',
  //   defaultValue: 'Minutes',
  // ),
  // seconds(
  //   key: 'mandiMenu.seconds',
  //   defaultValue: 'Seconds',
  // ),
  addProcurement(
    key: 'mandiMenu.addProcurement',
    defaultValue: 'Add Procurement',
  ),
  addCarryForward(
    key: 'mandiMenu.addCarryForward',
    defaultValue: 'Add Carry Forward',
  ),
  addFieldCharges(
    key: 'mandiMenu.addFieldCharges',
    defaultValue: 'Add Field Charges',
  ),
  recieveInventory(
    key: 'mandiMenu.recieveInventory',
    defaultValue: 'Recieve Inventory',
  ),
  closeInventory(
    key: 'mandiMenu.closeInventory',
    defaultValue: 'Close Inventory',
  ),
  mandiInventory(
    key: 'mandiMenu.mandiInventory',
    defaultValue: 'Mandi Inventory',
  ),
  skuAllocation(
    key: 'mandiMenu.skuAllocation',
    defaultValue: 'SKU Allocation',
  ),
  mandiProcSummary(
    key: 'mandiMenu.mandiProcSummary',
    defaultValue: 'Mandi Procurement Summary',
  ),
  lotting(
    key: 'mandiMenu.Lotting',
    defaultValue: 'Lotting',
  ),
  // viewLoadingPlan(
  //   key: 'mandiMenu.viewLoadingPlan',
  //   defaultValue: 'View Loading Plan',
  // ),
  smoHistory(
    key: 'mandiMenu.smoHistory',
    defaultValue: 'Smo History',
  ),
  closeOps(
    key: 'mandiMenu.closeOps',
    defaultValue: 'Close Ops',
  ),
  grading(
    key: 'mandiMenu.grading',
    defaultValue: 'Grading',
  ),
  returns(
    key: 'mandiMenu.returns',
    defaultValue: 'Returns',
  ),
  //end of mandi menu

  // add procurement
  addProcSubmitSuccessMessage(
    key: 'addProc.submitSuccessMessage',
    defaultValue: 'Procurement Submitted Successfully!',
  ),
  addProcSearchBoxHint(
    key: 'addProc.searchBoxHint',
    defaultValue: 'Search SKU',
  ),
  addProcSkuSelectCtaTitle(
    key: 'addProc.selectSkuCtaTitle',
    defaultValue: 'Done',
  ),
  addProcSkuSelectAmountLabel(
    key: 'addProc.selectSkuAmountLabel',
    defaultValue: 'Amount',
  ),
  addProcSkuSelectQuantityLabel(
    key: 'addProc.selectSkuQuantityLabel',
    defaultValue: 'Quantity*',
  ),
  addProcSkuSelectWeightLabel(
    key: 'addProc.selectSkuWeightLabel',
    defaultValue: 'Weight(Kg)',
  ),
  addProcSkuSelectUnitLabel(
    key: 'addProc.selectSkuUnitLabel',
    defaultValue: 'Unit',
  ),
  addProcSkuSelectLotSizeLabel(
    key: 'addProc.selectSkuLotSizeLabel',
    defaultValue: 'Lot Size',
  ),
  addProcSkuSelectProcTypeLabel(
    key: 'addProc.selectSkuProcTypeLabel',
    defaultValue: 'Type',
  ),
  addProcSkuSelectHeadingEdit(
    key: 'addProc.selectSkuHeadingEdit',
    defaultValue: 'Edit SKU',
  ),
  addProcSkuSelectHeadingAdd(
    key: 'addProc.selectSkuHeadingAdd',
    defaultValue: 'Add SKU',
  ),
  addProcAddSKUBtn(
    key: 'addProc.addSKUBtn',
    defaultValue: 'Add SKU',
  ),
  addProcUploadSplitsBtn(
    key: 'addProc.uploadSplitsBtnName',
    defaultValue: 'Upload Slips',
  ),
  addProcProcSummaryBtn(
    key: 'addProc.procSummaryBtnName',
    defaultValue: 'Proc Summary',
  ),
  addProcMyProcyBtn(
    key: 'addProc.myProcBtn',
    defaultValue: 'My Procurement',
  ),
  // addProcNoSkuMessage(
  //   key: 'addProc.emptySkuMessage',
  //   defaultValue: 'No Sku Added!',
  // ),
  addProcNoSkuDescription(
    key: 'addProc.emptySkuDescription',
    defaultValue: 'Please add SKUs to submit for procurement!',
  ),
  addProExitPopupTitle(
    key: 'addProc.exitPopupTitle',
    defaultValue: 'Are you sure?',
  ),
  addProExitPopupMessage(
    key: 'addProc.exitPopupMessage',
    defaultValue: 'You will lose all the data if you go back!',
  ),
  addedSlipsMessage(
    key: 'addProc.addedSlipsMessage',
    defaultValue: 'Added Slips!',
    isTemplated: true,
  ),
  addProcSubmitBtn(
    key: 'addProc.submitBtn',
    defaultValue: 'Submit',
  ),

  // lot(
  //   key: 'addProc.lot',
  //   defaultValue: 'Lot',
  // ),
  // kgUOM(
  //   key: 'addProc.kgUOM',
  //   defaultValue: 'KG/UOM',
  // ),
  addProcSelectSkuSearchSKU(
    key: 'addProc.searchSKU',
    defaultValue: 'Search SKU',
  ),
  addProcSearchSkuHint(
    key: 'addProc.startTyping',
    defaultValue: 'Start Typing',
  ),
  addProcSelectedLabel(
    key: 'addProc.selected',
    defaultValue: 'Selected',
  ),
  addProcSelectSkuDoneBtn(
    key: 'addProc.done',
    defaultValue: 'Done',
  ),
  //end of add procurement

  //carry forward
  cfEmptyMessage(
    key: 'cf.emptyMessage',
    defaultValue: 'No Carry Forward!',
  ),
  cfEmptyDescription(
    key: 'cf.emptyDescription',
    defaultValue:
        'No carry forward was found for the SMO. Please acknowledge it by submitting it.',
  ),
  addCarryForwardTitle(
    key: 'cf.addCarryForwardTitle',
    defaultValue: 'Add Carry Forward',
  ),
  cfSubmitBtn(
    key: 'cf.submitBtn',
    defaultValue: 'Submit',
  ),
  cfConfirmationAlertMsg(
    key: 'cf.confirmationAlertMessage',
    defaultValue:
        "Please Confirm before submitting carry Forward Inventory.\n\n You won't be able to edit it later.",
  ),
  cfConfirmationAlertTitle(
    key: 'cf.confirmationAlertTitle',
    defaultValue: 'Confirmation',
  ),
  cfExcessShortageTitle(
      key: 'cf.excessShortageTitle', defaultValue: 'Excess/Shortage'),
  cfShortageTitle(key: 'cf.shortageTitle', defaultValue: 'Shortage'),
  cfExcessTitle(key: 'cf.excessTitle', defaultValue: 'Excess'),
  // carryForwardConfirmationAlertCancelBtn(
  //   key: 'cf.confirmationAlertCancelBtn',
  //   defaultValue: 'Cancel',
  // ),
  // carryForwardConfirmationAlertConfirmBtn(
  //   key: 'cf.confirmationAlertConfirm',
  //   defaultValue: 'Confirm',
  // ),
  //end of carry forward

  // add Loss
  addLossMarkingLossMessage(
    key: 'addLoss.markLossMessage',
    defaultValue: '##maxLoss## ##unit## of loss to be marked!',
    isTemplated: true,
  ),
  addLossMaxLossMessage(
    key: 'addLoss.maxLossMessage',
    defaultValue: 'Total loss can not be more than ##maxLoss## ##unit##',
    isTemplated: true,
  ),
  addLossCtaTitle(
    key: 'addLoss.ctaTitle',
    defaultValue: 'Done',
  ),
  addLossTotalLabel(
    key: 'addLoss.totalLabel',
    defaultValue: 'Total',
  ),
  addLossCommentHint(
    key: 'addLoss.commentHint',
    defaultValue: 'Comments',
  ),
  addLossLossesLabel(
    key: 'addLoss.lossesLabel',
    defaultValue: 'Losses',
  ),
  // add Field Charges
  // fieldChargesSelectTxt(
  //   key: 'addFieldCharges.select',
  //   defaultValue: 'Select',
  // ),
  fieldChargesAddBtn(
    key: 'addFieldCharges.add',
    defaultValue: 'Add',
  ),
  fieldChargesTotal(
    key: 'addFieldCharges.totalLabel',
    defaultValue: 'Total',
  ),
  fieldChargesUploadSlipsTitle(
    key: 'addFieldCharges.uploadSlipsTitle',
    defaultValue: 'Upload Slips',
  ),
  fieldChargesUploadSlipsButton(
    key: 'addFieldCharges.uploadSlipsButton',
    defaultValue: 'Upload Slips',
  ),
  // fieldChargesComments(
  //   key: 'addFieldCharges.comments',
  //   defaultValue: 'Comments',
  // ),
  fieldChargesCommentHint(
    key: 'addFieldCharges.commentHint',
    defaultValue: 'Type comment here',
  ),
  // fieldChargesTypeHere(
  //   key: 'addFieldCharges.typeHere',
  //   defaultValue: 'Type here',
  // ),
  // fieldChargesSaveAsDraftBtn(
  //   key: 'addFieldCharges.saveAsDraftBtn',
  //   defaultValue: 'Save as Draft',
  // ),
  fieldChargesSubmitBtn(
    key: 'addFieldCharges.submitBtn',
    defaultValue: 'Submit',
  ),
  // fieldChargesEmptyImagesError(
  //   key: 'addFieldCharges.emptyImagesError',
  //   defaultValue: 'Nothing\'s here',
  // ),
  // fieldChargesUploadFileBtn(
  //   key: 'addFieldCharges.uploadFileBtn',
  //   defaultValue: 'Upload File',
  // ),
  fieldChargesUploadSlipsSuccessMessage(
      key: 'addFieldCharges.successMessage',
      defaultValue: 'Slips uploaded successfully!'),
  // end of add field charges

  //recieve inventory
  recieveInventoryEmptyMessage(
    key: 'recieveInventory.emptyMessage',
    defaultValue: 'No Incoming Inventory Found',
  ),
  recieveInventoryErrorMessage(
    key: 'recieveInventory.errorMessage',
    defaultValue: 'Failed to Fetch Incoming Inventory',
  ),
  recieveInventoryFromLabel(
    key: 'recieveInventory.fromLabel',
    defaultValue: 'From: ##mandiName##',
    isTemplated: true,
  ),
  recieveInventoryAcceptLabel(
    key: 'recieveInventory.acceptLabel',
    defaultValue: 'Accept',
  ),
  recieveInventoryTitle(
    key: 'recieveInventory.title',
    defaultValue: 'Recieve Inventory',
  ),
  // acceptTxt(
  //   key: 'recieveInventory.acceptTxt',
  //   defaultValue: 'Accept',
  // ),
  // recieveInventorySelect(
  //   key: 'recieveInventory.selectTxt',
  //   defaultValue: 'Select',
  // ),
  // recieveInventorySKU(
  //   key: 'recieveInventory.sku',
  //   defaultValue: 'SKU',
  // ),
  // recieveInventoryTxtNoOfLots(
  //   key: 'recieveInventory.noOfLots',
  //   defaultValue: 'No. of Lots',
  // ),
  recieveInventoryTxtRecieved(
    key: 'recieveInventory.recieved',
    defaultValue: 'Recieved',
  ),
  // recieveInventoryTxtGoodInventory(
  //   key: 'recieveInventory.goodInventory',
  //   defaultValue: 'Good Inventory',
  // ),
  recieveInventoryTxtLosses(
    key: 'recieveInventory.losses',
    defaultValue: 'Losses',
  ),
  recieveInventoryComments(
    key: 'recieveInventory.commentsLabel',
    defaultValue: 'Comments*',
  ),
  recieveInventoryCommentBoxHint(
    key: 'recieveInventory.commentBoxHint',
    defaultValue: 'Please Enter Here',
  ),
  recieveInventoryConsignmentLabel(
    key: 'recieveInventory.consignmentLabel',
    defaultValue: 'Consignment ##cId##',
    isTemplated: true,
  ),
  recieveInventorySubmitBtn(
    key: 'recieveInventory.submitBtnTitle',
    defaultValue: 'Submit',
  ),
  recieveInventoryConfirmationAlertTitle(
    key: 'recieveInventory.confirmationAlertTitle',
    defaultValue: 'Confirmation',
  ),
  recieveInventoryConfirmationAlertText(
    key: 'recieveInventory.confirmationAlertDesc',
    defaultValue:
        "Are you sure you want to submit ?\nYou can't change it later.",
  ),
  // recieveInventoryConfirmationAlertCancelBtn(
  //   key: 'recieveInventory.confirmationAlertCancelBtn',
  //   defaultValue: 'No',
  // ),
  // recieveInventoryConfirmationAlertOKBtn(
  //   key: 'recieveInventory.confirmationAlertOKBtn',
  //   defaultValue: 'Yes',
  // ),
  recieveInventorySuccessTitle(
    key: 'recieveInventory.successTitle',
    defaultValue: 'Success',
  ),
  recieveInventorySuccessMessage(
    key: 'recieveInventory.successMessage',
    defaultValue: 'Inventory Recieved Successfully',
  ),

  //end of recieve inventory

  // close inventory
  closeInventoryTitle(
    key: 'closeInventory.title',
    defaultValue: 'Close Inventory',
  ),
  // closeInventorySelectTxt(
  //   key: 'closeInventory.select',
  //   defaultValue: 'Select',
  // ),
  // closeInventorySKUTxt(
  //   key: 'closeInventory.sku',
  //   defaultValue: 'SKU',
  // ),
  // closeInventoryUnitTxt(
  //   key: 'closeInventory.unit',
  //   defaultValue: 'Unit',
  // ),
  // closeInventoryCINTxt(
  //   key: 'closeInventory.cin',
  //   defaultValue: 'CIN',
  // ),
  // closeInventoryLossTxt(
  //   key: 'closeInventory.loss',
  //   defaultValue: 'Loss',
  // ),
  // closeInventoryTotalTxt(
  //   key: 'closeInventory.total',
  //   defaultValue: 'Total',
  // ),
  // closeInventoryCommentsTxt(
  //   key: 'closeInventory.comments',
  //   defaultValue: 'Comments',
  // ),
  closeInventorySubmitBtn(
    key: 'closeInventory.submitBtn',
    defaultValue: 'Submit',
  ),
  // closeInventoryLossesTxt(
  //   key: 'closeInventory.losses',
  //   defaultValue: 'Losses',
  // ),
  // closeInventoryLostItemsTxt(
  //   key: 'closeInventory.lostItems',
  //   defaultValue: 'Lost Items',
  // ),
  // closeInventoryDamagedTxt(
  //   key: 'closeInventory.damaged',
  //   defaultValue: 'Damaged',
  // ),
  // closeInventoryShrinkageTxt(
  //   key: 'closeInventory.shrinkage',
  //   defaultValue: 'Shrinkage',
  // ),
  // closeInventoryRottenTxt(
  //   key: 'closeInventory.rotten',
  //   defaultValue: 'Rotten',
  // ),
  // closeInventoryGradeB(
  //   key: 'closeInventory.gradeB',
  //   defaultValue: 'Grade B',
  // ),
  closeInventoryDoneBtn(
    key: 'closeInventory.doneBtn',
    defaultValue: 'Done',
  ),
  closeInventoryConfirmAlertTitle(
    key: 'closeInventory.confirmAlertTitle',
    defaultValue: 'Confirmation!',
  ),
  closeInventoryConfirmAlertMessage(
    key: 'closeInventory.confirmAlertMessage',
    defaultValue:
        "Please confirm before submitting close inventory.\n\n You won't be able to edit it later.",
  ),
  closeInventoryEmptyTitle(
    key: 'closeInventory.emptyTitle',
    defaultValue: 'No Inventory Found!',
  ),
  closeInventoryEmptyMessage(
    key: 'closeInventory.emptyMessage',
    defaultValue: 'Please submit it!',
  ),
  closeInventorySuccessMessage(
    key: 'closeInventory.successMessage',
    defaultValue: 'Inventory Closed Successfully!',
  ),
  //end of close inventory

  // Mandi inventory
  mandiInventoryUpdatedSuccess(
    key: 'mandiInventory.updateSucess',
    defaultValue: 'Inventory updated successfully',
  ),
  mandiInventoryNoInventoryMessage(
    key: 'mandiInventory.updateSucess',
    defaultValue: 'Inventory updated successfully',
  ),
  mandiInventoryTitle(
    key: 'mandiInventory.title',
    defaultValue: '##mandiName## - Inventory',
    isTemplated: true,
  ),

  // sku allocation
  allocateInventoryTitle(
    key: 'allocateInventory.title',
    defaultValue: 'Allocate Inventory',
  ),
  allocateInventorySelectDestinationTxt(
    key: 'allocateInventory.selectDestination',
    defaultValue: 'Select Destination',
  ),
  // allocateInventorySelectTxt(
  //   key: 'allocateInventory.select',
  //   defaultValue: 'Select',
  // ),
  allocateInventoryAllotTxt(
    key: 'allocateInventory.allot',
    defaultValue: 'Allot',
  ),
  // allocateInventoryAllocationSumary(
  //   key: 'allocateInventory.allocationSummary',
  //   defaultValue: 'Allocation Summary',
  // ),
  allocateInventoryAllocations(
    key: 'allocateInventory.allocations',
    defaultValue: 'Allocations',
  ),
  // allocateInventoryDraft(
  //   key: 'allocateInventory.draft',
  //   defaultValue: 'Draft',
  // ),
  // allocateInventoryAllocate(
  //   key: 'allocateInventory.allocate',
  //   defaultValue: 'Allocate',
  // ),
  // allocateInventorySelect(
  //   key: 'allocateInventory.select',
  //   defaultValue: 'Select',
  // ),
  // allocateInventorySKU(
  //   key: 'allocateInventory.sku',
  //   defaultValue: 'SKU',
  // ),
  // allocateInventoryTotal(
  //   key: 'allocateInventory.total',
  //   defaultValue: 'Total',
  // ),
  allocateInventoryAllocated(
    key: 'allocateInventory.allocated',
    defaultValue: 'Allocated',
  ),
  // allocateInventorySaveAsDraftBtn(
  //   key: 'allocateInventory.saveAsDraftBtn',
  //   defaultValue: 'Save as Draft',
  // ),
  // allocateInventoryDispatchBtn(
  //   key: 'allocateInventory.dispatch',
  //   defaultValue: 'Dispatch',
  // ),
  allocateInventoryDeliveryDateLabel(
    key: 'allocateInventory.deliveryDateLabel',
    defaultValue: 'Delivery Date',
  ),
  allocateInventoryDestinationSlotLabel(
    key: 'allocateInventory.destinationSlotLabel',
    defaultValue: 'Destination Slot',
  ),
  allocateInventoryDestinationLabel(
    key: 'allocateInventory.destinationLabel',
    defaultValue: 'Destination',
  ),
  allocateInventoryDestinationTypeLabel(
    key: 'allocateInventory.destinationTypeLabel',
    defaultValue: 'Destination Type',
  ),
  allocateInventoryCancelAlertTitle(
    key: 'allocateInventory.cancelAlertTitle',
    defaultValue: 'Cancel Allocation?',
  ),
  allocateInventoryCancelAlertMessage(
    key: 'allocateInventory.cancelAlertMessage',
    defaultValue: 'This will cancel the allocation!',
  ),
  allocateInventoryDownloadDeliveryMemo(
    key: 'allocateInventory.downloadDeliveryMemo',
    defaultValue: 'Download Delivery Memo',
  ),
  allocateInventoryDeliveryMemoStatus(
    key: 'allocateInventory.deliveryMemoStatus',
    defaultValue: 'Delivery Memo Status',
  ),
  allocateInventoryCancelAllocation(
    key: 'allocateInventory.cancelAllocationLabel',
    defaultValue: 'Cancel Allocation',
  ),
  allocateInventoryAllocateTitle(
    key: 'allocateInventory.allocateTitle',
    defaultValue: 'Allocate - ##name##',
    isTemplated: true,
  ),
  allocateInventoryAllocateDraftLabel(
    key: 'allocateInventory.allocateDraftLabel',
    defaultValue: 'Save as Draft',
  ),
  allocateInventoryAllocateCopyInventoryLabel(
    key: 'allocateInventory.allocateCopyInventoryLabel',
    defaultValue: 'Copy as Inventory',
  ),
  allocateInventoryAllocateSubmit(
    key: 'allocateInventory.allocateSubmit',
    defaultValue: 'Submit',
  ),
  allocateInventoryAllocateEmptyMessage(
    key: 'allocateInventory.allocateEmptyMessage',
    defaultValue: 'No items to allocate',
  ),
  allocateInventoryTripsTitle(
    key: 'allocateInventory.tripsTitle',
    defaultValue: 'Trips',
  ),
  allocateInventoryTripsVehicleNumberLabel(
    key: 'allocateInventory.tripsVehicleNumberLabel',
    defaultValue: 'Vehicle Number',
  ),
  allocateInventoryTripsMobilePhoneLabel(
    key: 'allocateInventory.tripsMobilePhoneLabel',
    defaultValue: 'Mobile Phone',
  ),
  allocateInventoryTripsDriverNameLabel(
    key: 'allocateInventory.tripsDriverNameLabel',
    defaultValue: 'Driver Name',
  ),
  allocateInventoryTripsDispatchLabel(
    key: 'allocateInventory.tripsDispatchLabel',
    defaultValue: 'Dispatch Trip',
  ),
  allocateInventoryTripsStatus(
    key: 'allocateInventory.tripsStatus',
    defaultValue: 'Trip Status: ##status##',
    isTemplated: true,
  ),
  allocateInventoryTripsRoutIdLabel(
    key: 'allocateInventory.tripsRoutIdLabel',
    defaultValue: 'Route ID: ##routeId##',
    isTemplated: true,
  ),
  allocateInventoryTripsEmptyMessage(
    key: 'allocateInventory.tripsEmptyMessage',
    defaultValue: 'There are no trips associated with this allotment!',
  ),
  allocateInventoryTripsEmptyTitle(
    key: 'allocateInventory.tripsEmptyTitle',
    defaultValue: 'No Trip!',
  ),
  // end of sku allocation

  // close Ops
  closeOpsCtaLabel(
    key: 'closeOps.ctaLabel',
    defaultValue: 'Done',
  ),
  closeOpsSuccessMessage(
    key: 'closeOps.successMessage',
    defaultValue: 'Smo Closed Succesfully',
  ),

  // lotting
  lottingTitle(
    key: 'lotting.title',
    defaultValue: 'Lotting',
  ),
  deLottingTitle(
    key: 'lotting.delottingTitle',
    defaultValue: 'De-Lotting',
  ),
  lottingEmptyTitle(
    key: 'lotting.emptyTitle',
    defaultValue: 'No Sku!',
  ),
  lottingEmptyMessage(
    key: 'lotting.emptyMessage',
    defaultValue: 'There are no sku!',
  ),
  lottingSelectSkuCtaLabel(
    key: 'lotting.selectSkuCtaLabel',
    defaultValue: 'Done',
  ),
  lottingSelectLabel(
    key: 'lotting.selectLabel',
    defaultValue: 'Select',
  ),
  lottingSelectSkuLabel(
    key: 'lotting.selectSkuLabel',
    defaultValue: 'Select Sku',
  ),
  // lottingSKU(
  //   key: 'lotting.sku',
  //   defaultValue: 'SKU',
  // ),
  lottingAddLotLabel(
    key: 'lotting.addLotLabel',
    defaultValue: 'Add Lot',
  ),
  lottingToLabel(
    key: 'lotting.toLabel',
    defaultValue: 'To',
  ),
  lottingFromLabel(
    key: 'lotting.fromLabel',
    defaultValue: 'From',
  ),
  lottingAlertTitle(
    key: 'lotting.alertTitle',
    defaultValue: 'Losses Detected!',
  ),
  lottingAlertMessage(
    key: 'lotting.alertMessage',
    defaultValue:
        'We have detected ##loss## ##unit## losses. Please provide details to continue.',
    isTemplated: true,
  ),
  // lottingQty(
  //   key: 'lotting.qty',
  //   defaultValue: 'Qty',
  // ),
  // lottingLottingLoss(
  //   key: 'lotting.lottingLoss',
  //   defaultValue: 'Lotting Loss',
  // ),
  // lottingLotSize(
  //   key: 'lotting.lotSize',
  //   defaultValue: 'Lot Size',
  // ),
  // lottingNoOfLots(
  //   key: 'lotting.noOfLots',
  //   defaultValue: 'No of lots',
  // ),
  // lottingLoss(
  //   key: 'lotting.loss',
  //   defaultValue: 'Loss',
  // ),
  // lottingWastage(
  //   key: 'lotting.wastage',
  //   defaultValue: 'Wastage',
  // ),
  // lottingEdit(
  //   key: 'lotting.edit',
  //   defaultValue: 'Edit',
  // ),
  // lottingTotalQuantity(
  //   key: 'lotting.totalQty',
  //   defaultValue: 'Total Quantity',
  // ),
  // lottingConversion(
  //   key: 'lotting.conversion',
  //   defaultValue: 'Conversion',
  // ),
  // lottingLottingSize(
  //   key: 'lotting.lottingSize',
  //   defaultValue: 'Lotting Size',
  // ),
  // lottingNoOfActualLotsCreated(
  //   key: 'lotting.NoOfActualLotsCreated',
  //   defaultValue: 'No of Actual Lots Created',
  // ),
  lottingSubmit(key: 'lotting.submit', defaultValue: 'Submit'),

  // Smo History

  smoHistoryProcHistoryFieldChargesTitle(
    key: 'smoHistory.fieldChargesTitle',
    defaultValue: 'Field Charges',
  ),
  smoHistoryFieldChargesFileTitle(
    key: 'smoHistory.fieldChargesFileTitle',
    defaultValue: 'File ##index##',
    isTemplated: true,
  ),
  smoHistoryFieldChargesDownloadLabel(
    key: 'smoHistory.fieldChargesDownloadLabel',
    defaultValue: 'Download',
  ),
  smoHistoryFieldChargesChargeTypeLabel(
    key: 'smoHistory.fieldChargesChargeTypeLabel',
    defaultValue: 'Charge Types:',
  ),
  smoHistoryFieldChargesAmountLabel(
    key: 'smoHistory.fieldChargesAmountLabel',
    defaultValue: 'Amount:',
  ),
  smoHistoryFieldChargesUserNameLabel(
    key: 'smoHistory.fieldChargesUserLabel',
    defaultValue: 'User:',
  ),
  smoHistoryFieldChargesCommentsLabel(
    key: 'smoHistory.fieldChargesCommentsLabel',
    defaultValue: 'Comments:',
  ),

  smoHistoryProcHistoryTitle(
    key: 'smoHistory.procHistoryTitle',
    defaultValue: 'Procurement History',
  ),
  smoHistoryProcHistoryEmptyTitle(
    key: 'smoHistory.procHistoryEmptyTitle',
    defaultValue: 'No Procurement!',
  ),
  smoHistoryProcHistoryEmptyMessage(
    key: 'smoHistory.procHistoryEmptyMessage',
    defaultValue: 'No procurement found for the selected SMO.',
  ),
  smoHistoryUpdatePriceTitle(
    key: 'smoHistory.updatePriceTitle',
    defaultValue: 'Update Prices',
  ),
  smoHistoryUpdatePriceUpdateCtaLabel(
    key: 'smoHistory.updatePriceUpdateCtaLabel',
    defaultValue: 'Save as Draft',
  ),
  smoHistoryUpdatePriceSubmitCtaLabel(
    key: 'smoHistory.updatePriceSubmitCtaLabel',
    defaultValue: 'Submit',
  ),
  smoHistoryUpdatePriceAlertTitle(
    key: 'smoHistory.updatePriceAlertTitle',
    defaultValue: 'Confirmation',
  ),
  smoHistoryUpdatePriceAlertMessage(
    key: 'smoHistory.updatePriceAlertMessage',
    defaultValue:
        "Please confirm before submitting prices.\n\n You won't be able to edit it later.",
  ),
  dateAndTime(
    key: 'dateAndTime',
    defaultValue: 'Date & Time',
  ),
  addProcAddedSlipsMessage(
    key: 'addProc.addedSlipsMessage',
    defaultValue: 'Added ##count## slips',
    isTemplated: true,
  ),
  addProcSkuSelectNumberOfLotsLabel(
    key: 'addProc.selectSkuNumberOfLotsLabel',
    defaultValue: 'Number of Lots*',
  ),
  lottingLoss(key: 'lottingLoss', defaultValue: 'Lotting Loss'),
  lottingLotSizeAlreadyExists(
    key: 'lotting.lotSizeAlreadyExists',
    defaultValue: 'Lot size already exists',
  ),
  addCarryForwardInventoryClosedByMessage(
    key: 'cf.inventoryClosedByMessage',
    defaultValue: 'Inventory closed by ##name##',
    isTemplated: true,
  ),
  allocateInventoryRegenerateDeliveryMemo(
    key: 'allocateInventory.regenerateDeliveryMemo',
    defaultValue: 'Regenerate Delivery Memo',
  ),
  allocateInventoryRegenerateDeliveryMemoMessage(
    key: 'allocateInventory.regenerateDeliveryMemoMessage',
    defaultValue: 'We are regenerating delivery memo! Please wait!',
  ),
  ;

  const LanguageEnum({
    required this.key,
    required this.defaultValue,
    this.isTemplated = false,
  });

  final String key;
  final String defaultValue;
  final bool isTemplated;
}
