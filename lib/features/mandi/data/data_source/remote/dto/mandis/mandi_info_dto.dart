import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';

part 'mandi_info_dto.freezed.dart';
part 'mandi_info_dto.g.dart';

@freezed
class MandiInfoDto with _$MandiInfoDto {
  const factory MandiInfoDto({
    required int id,
    required String name,
    @Default(MandiType.mandi) MandiType type,
    required String key,
  }) = _MandiInfoDto;

  @override
  factory MandiInfoDto.fromJson(Map<String, dynamic> json) =>
      _$MandiInfoDtoFromJson(json);
}
