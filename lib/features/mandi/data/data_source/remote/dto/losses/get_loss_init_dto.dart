import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_loss_init_dto.freezed.dart';
part 'get_loss_init_dto.g.dart';

@freezed
class GetLossInitDto with _$GetLossInitDto {
  const factory GetLossInitDto({
    required List<String> lossType,
    required List<String> opsStage,
  }) = _GetLossInitDto;

  @override
  factory GetLossInitDto.fromJson(Map<String, dynamic> json) =>
      _$GetLossInitDtoFromJson(json);
}
