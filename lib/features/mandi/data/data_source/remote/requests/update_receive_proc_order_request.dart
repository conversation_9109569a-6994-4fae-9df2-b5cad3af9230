import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';

class UpdateReceiveProcOrderRequest extends BaseRequest<dynamic, String> {
  final int procId;
  final int smoId;
  final List<SkuInputData> items;
  final String vendorName;
  final int vendorLocationId;
  final List<String> images;
  final List<ProcFieldCharge> fieldCharges;
  final String comments;

  UpdateReceiveProcOrderRequest({
    required this.procId,
    required this.smoId,
    required this.items,
    required this.vendorName,
    required this.images,
    required this.vendorLocationId,
    required this.fieldCharges,
    required this.comments,
  });
  @override
  String getPath() => 'procurements/receiveOrder';

  @override
  Map<String, dynamic> getBody() {
    final mp = {
      'procurementId': procId,
      'smoId': smoId,
      'comments': comments,
      'items': items
          .where((e) => e.procItemId != -1 && (e.hasChanged || e.newlyAdded))
          .map(
            (e) => {
              'id': e.procItemId,
              'receivedQuantity': e.totalQty,
              'weight': e.weight?.toDouble() ?? 0,
              'amount': e.amount?.toDouble() ?? 0,
              'finalSubmit': e.isReceived || e.isFinal,
              'weighingSource': e.weighingSource,
              'billedCostPrice':
                  e.totalQty > 0 ? (e.amount?.toDouble() ?? 0) / e.totalQty : 0,
            },
          )
          .toList(),
      'newSkus': items
          .where((e) => e.procItemId == -1)
          .map(
            (e) => {
              "skuId": e.sku.id,
              "type": e.procurementType.value.toUpperCase(),
              "unit": e.procurementUnit,
              "quantity": e.totalQty,
              "lotSize": e.lotSize,
              "weight": e.weight?.toDouble() ?? 0,
              "amount": e.amount?.toDouble() ?? 0,
              "orderedQuantity": e.orderedQuantity?.toDouble() ?? 0,
              "finalSubmit": e.isFinal,
              'billedCostPrice':
                  e.totalQty > 0 ? (e.amount?.toDouble() ?? 0) / e.totalQty : 0,
              'orderedCostPrice':
                  e.totalQty > 0 ? (e.amount?.toDouble() ?? 0) / e.totalQty : 0,
            },
          )
          .toList(),
      'images': images,
      'charges': fieldCharges
          .map(
            (e) => {
              'type': e.type,
              'amount': e.amount.toDouble(),
              'comment': e.comment,
              'images': e.images.map((e) => e.uploadKey).toList(),
            },
          )
          .toList(),
    };
    if (vendorLocationId != -1) {
      mp['vendorLocationId'] = vendorLocationId;
    } else {
      mp['vendorName'] = vendorName;
    }
    return mp;
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid data';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
