import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/core/presentation/vendor/view/vendor_drop_down.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/mandi_dropdown.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/search_proc/cubit/search_proc_cubit.dart';

class SearchProcScreen extends StatelessWidget {
  const SearchProcScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MandiBloc, MandiState>(
      builder: (context, state) {
        return state.map(
          loading: (_) => Center(
            child: CircularProgressIndicator(),
          ),
          success: (s) => SafeArea(
            child: Scaffold(
              appBar: AppBar(
                title: Text('selectProc.title'.tr('Select Procurement')),
                centerTitle: false,
              ),
              body: BlocBuilder<SearchProcCubit, SearchProcState>(
                builder: (context, state) {
                  final isCtaActive = state.mandi != MandiInfo.empty &&
                      state.selectedDate != null &&
                      state.selectedVendor != null;
                  return Center(
                    child: Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: MediaQuery.of(context).size.width,
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(0),
                            ),
                            child: IntrinsicHeight(
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 300,
                                    child: MandiDropdown(
                                      selectedMandi: state.mandi,
                                      onChange: (val) {
                                        if (val == null) return;
                                        final ops = context
                                            .read<MandiBloc>()
                                            .state
                                            .mapOrNull(success: (s) {
                                          return s.liveOps ?? <SmoOps>[];
                                        });
                                        final smoId = ops
                                            ?.where((e) => e.mandiId == val.id)
                                            .firstOrNull
                                            ?.smoId;
                                        if (smoId == null) return;
                                        context
                                            .read<SearchProcCubit>()
                                            .setMandi(val);
                                      },
                                    ), // Select Mandi
                                  ),
                                  const SizedBox(width: 20),
                                  SizedBox(
                                    width: 150,
                                    child: ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green.shade100,
                                        foregroundColor: Colors.black,
                                      ),
                                      onPressed: () async {
                                        // Open select date popup
                                        final selectedDate =
                                            await showDatePicker(
                                          context: context,
                                          initialDate: state.selectedDate ??
                                              DateTime.now(),
                                          firstDate: DateTime(2023, 4, 1),
                                          lastDate: DateTime.now(),
                                        );
                                        context
                                            .read<SearchProcCubit>()
                                            .setDate(selectedDate);
                                      },
                                      child: Text(
                                        state.selectedDate == null
                                            ? 'selectDate'.tr('Select Date')
                                            : DateFormat('dd MMM, yyyy')
                                                .format(state.selectedDate!),
                                      ), // Select Date
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  SizedBox(
                                    width: 600,
                                    child: VendorDropDown(
                                      isEnabled: true,
                                      enableNewCreation: false,
                                      selectedVendorLocation:
                                          state.selectedVendor,
                                      onChanged: (value) {
                                        context
                                            .read<SearchProcCubit>()
                                            .setVendor(value);
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  ElevatedButton(
                                    onPressed: !isCtaActive || state.isLoading
                                        ? null
                                        : () {
                                            context
                                                .read<SearchProcCubit>()
                                                .getProcurements();
                                          },
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue),
                                    child: state.isLoading
                                        ? SizedBox(
                                            height: 18,
                                            width: 18,
                                            child: CircularProgressIndicator(),
                                          )
                                        : Text(
                                            'apply'.tr('Apply'),
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (state.procurements == null)
                            Expanded(
                              child: Center(
                                child: Text(
                                  'searchProc.notSearchMessage'
                                      .tr('No data to Show!'),
                                ),
                              ),
                            ),
                          if (state.procurements != null &&
                              state.procurements!.isEmpty)
                            Expanded(
                              child: Center(
                                child: Text(
                                  'searchProc.noProcurements'
                                      .tr('No procurements found!'),
                                ),
                              ),
                            ),
                          if (state.procurements != null &&
                              state.procurements!.isNotEmpty)
                            Builder(builder: (context) {
                              return Expanded(
                                child: ListView.builder(
                                  itemCount: state.procurements!.length,
                                  itemBuilder: (context, index) {
                                    final myProcDetail =
                                        state.procurements![index];

                                    return Container(
                                      margin: EdgeInsets.symmetric(
                                          vertical: 8.0, horizontal: 16.0),
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 16.0, vertical: 8),
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(12.0),
                                        color: Colors.white,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.withOpacity(0),
                                            spreadRadius: 1,
                                            blurRadius: 5,
                                            offset: Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if ((myProcDetail.vendorName !=
                                                      null &&
                                                  myProcDetail.vendorName!
                                                      .isNotEmpty) ||
                                              (myProcDetail.vendorLocationId !=
                                                  null))
                                            Container(
                                              width: double.infinity,
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  vertical: 4,
                                                  horizontal: 8,
                                                ),
                                                child: Text(
                                                  di
                                                      .get<VendorCubit>()
                                                      .getVendorLocation(
                                                          myProcDetail
                                                              .vendorLocationId,
                                                          myProcDetail
                                                              .vendorName)
                                                      .vendorName,
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                              decoration: BoxDecoration(
                                                color: Colors.green.shade50,
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                              ),
                                            ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 8),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                  flex: 2,
                                                  child: LangText(
                                                    'dateAndTime',
                                                    'Date & Time',
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Expanded(
                                                  flex: 2,
                                                  child: LangText(
                                                    'amount',
                                                    'Amount',
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Expanded(
                                                  flex: 2,
                                                  child: LangText(
                                                    'numberOfItems',
                                                    '# of Items',
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: Colors.black87,
                                                    ),
                                                    textAlign: TextAlign.end,
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                              ],
                                            ),
                                          ),
                                          Divider(
                                            height: 1,
                                            color: Colors.grey[300],
                                          ),
                                          SizedBox(
                                            height: 8,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 0,
                                              horizontal: 0,
                                            ),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                  flex: 2,
                                                  child: Text(
                                                    myProcDetail.createdAt
                                                        .toDate(
                                                      'dd MMM - hh:mm a',
                                                    ),
                                                    style: TextStyle(
                                                      color: Colors.black87,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Expanded(
                                                  flex: 2,
                                                  child: Text(
                                                    '₹ ' +
                                                        myProcDetail
                                                            .totalAmount()
                                                            .asString(
                                                                maxDecimalDigits:
                                                                    2),
                                                    style: TextStyle(
                                                      color: Colors.black87,
                                                    ),
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                                Expanded(
                                                  flex: 2,
                                                  child: Text(
                                                    myProcDetail.items.length
                                                        .toString(),
                                                    style: TextStyle(
                                                      color: Colors.black87,
                                                    ),
                                                    textAlign: TextAlign.end,
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 8,
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            height: 4,
                                          ),
                                          ...[
                                            Divider(),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: OutlinedButton.icon(
                                                    onPressed: () async {
                                                      await context.push(
                                                          context.namedLocation(
                                                            'editProcurementAdmin',
                                                            pathParameters: {
                                                              'smoId':
                                                                  myProcDetail
                                                                      .smoId
                                                                      .toString(),
                                                              'mandiId': state
                                                                  .mandi!.id
                                                                  .toString(),
                                                            },
                                                          ),
                                                          extra: SmoDetail
                                                              .fromProcDetail(
                                                                  myProcDetail)
                                                          // extra: SmoDetail.fromMyProc(),
                                                          );
                                                      context
                                                          .read<
                                                              SearchProcCubit>()
                                                          .getProcurements();
                                                    },
                                                    icon: Icon(Icons
                                                        .cloud_upload_outlined),
                                                    label: LangText(
                                                      'myProcDetails.updatePrice',
                                                      'Update Price',
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 16,
                                                ),
                                                Expanded(
                                                  child: ElevatedButton.icon(
                                                    onPressed: () async {
                                                      context.pushNamed(
                                                        'procItem',
                                                        extra: myProcDetail,
                                                      );
                                                    },
                                                    icon: Icon(Icons
                                                        .navigate_next_rounded),
                                                    label: LangText(
                                                      'myProcDetails.view',
                                                      'View',
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ]
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              );
                            }),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
