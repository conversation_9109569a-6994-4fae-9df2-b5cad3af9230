import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';
import 'package:proc2/features/mandi/presentation/liquidation/cubit/update_liquidation_cubit.dart';

class UpdateLiquidationOrderRequest extends BaseRequest<dynamic, String> {
  final LiquidationOrder order;
  final int smoId;
  final List<LiquidationInputModel> inputOrders;

  UpdateLiquidationOrderRequest({
    required this.order,
    required this.smoId,
    required this.inputOrders,
  });
  @override
  String getPath() => 'liquidation/update-order';

  @override
  Map<String, dynamic> getBody() {
    return {
      "smoId": smoId,
      "liquidationId": order.liquidationId,
      "items": inputOrders
          .map(
            (e) => {
              "id": e.item!.id,
              "smoId": smoId,
              "receivedAmount": e.receiveAmountNow.toDouble(),
              "receivedQuantity": e.liquidationQtyNow.toDouble(),
              "isFinalized": e.closeLiquidationOrder,
              "Comments": "",
              "dumpedQuantity": e.dumpQtyNow.toDouble(),
              "images": e.files.map((e) => e.uploadKey ?? '').toList(),
            },
          )
          .toList(),
      "vendor": {
        "name": order.vendor.name,
      },
      "images": order.images,
      "comments": ""
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
