import 'dart:convert';
import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:proc2/app/app.dart';
import 'package:proc2/core/lang/language_enum.dart';

void main() {
  group('App', () {
    testWidgets('renders CounterPage', (tester) async {
      await tester.pumpWidget(App());
      expect(find.byType(Column), findsOneWidget);
    });
  });
  group('Code', () {
    test('lang', () async {
      final map = <String, dynamic>{};
      for (final x in LanguageEnum.values) {
        if (x.key.contains('.')) {
          final splits = x.key.split('.');
          final group = splits[0];
          final key = splits[1];
          if (!map.containsKey(group)) {
            map[group] = <String, dynamic>{};
          }
          (map[group] as Map<String, dynamic>)[key] = x.defaultValue;
        } else {
          map[x.key] = x.defaultValue;
        }
      }

      final jsonOutput = json.encode(map);
      const filename = 'lang.json';
      final file = await File(filename).writeAsString(jsonOutput);
    });
  });

  test('exportLangGroup', () async {
    final groups = <String>{};
    for (final x in LanguageEnum.values) {
      if (x.key.contains('.')) {
        final splits = x.key.split('.');
        final group = splits[0];
        groups.add(group);
      }
    }
    final out = StringBuffer();
    for (final group in groups) {
      out
        ..write(group)
        ..write('\n');
    }
    const filename = 'group.txt';
    final file = await File(filename).writeAsString(out.toString());
  });

  test('exportLang', () async {
    final groups = <String>[];
    final keys = <String>[];
    final defaultValues = <String>[];

    for (final x in LanguageEnum.values) {
      if (x.key.contains('.')) {
        final splits = x.key.split('.');
        final group = splits[0];
        final key = splits[1];
        groups.add(group);
        keys.add(key);
        defaultValues.add(x.defaultValue.replaceAll('\n', '\\n'));
      } else {
        groups.add('default');
        keys.add(x.key);
        defaultValues.add(x.defaultValue.replaceAll('\n', '\\n'));
      }
    }
    final out = StringBuffer();
    for (final group in groups) {
      out
        ..write(group)
        ..write('\n');
    }
    out.write('\n\n');
    for (final key in keys) {
      out
        ..write(key)
        ..write('\n');
    }
    out.write('\n\n');
    for (final val in defaultValues) {
      out
        ..write(val)
        ..write('\n');
    }
    const filename = 'lang_export.txt';
    final file = await File(filename).writeAsString(out.toString());
  });
}
