import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/grading_conversion_request.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/domain/use_case/get_inventory_usecase.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/inventory_type.dart';

part 'grading_conversion_cubit.freezed.dart';
part 'grading_conversion_state.dart';

@injectable
class GradingConversionCubit extends Cubit<GradingConversionState> {
  final GetInventoryUseCase _getInventory;
  final MandiRepository _mandiRepository;

  GradingConversionCubit(this._getInventory, this._mandiRepository)
      : super(GradingConversionState.initial());

  void loadDeviation() async {
    emit(state.copyWith(
      gradingConversionDeviation: null,
      gradingInventoryDeviation: null,
    ));
    final result = await _mandiRepository.getLottingDeviation();
    final newState = result.fold(
      (left) => state.copyWith(
          gradingConversionDeviation: -1,
          gradingInventoryDeviation: -1,
          message: left.message),
      (right) => state.copyWith(
          gradingInventoryDeviation: right.gradingInventoryDeviation,
          gradingConversionDeviation: right.gradingConversionDeviation),
    );
    emit(newState);
  }

  void loadInventory(int mandiId, InventoryType inventoryType) async {
    final result = await _getInventory.call(
      mandiId,
      type: inventoryType.key,
    );
    final newState = result.fold(
      (left) => state.copyWith(allInventory: [], message: left.message),
      (right) {
        final inv = List<InventoryItem>.from(right.skus)
            .where((element) => element.quantity > 0)
            .toList();
        inv.sort((a, b) => a.skuId.compareTo(b.skuId));
        return state.copyWith(
          allInventory: inv,
        );
      },
    );
    emit(newState);
  }

  void fromSkuSelected(InventoryItem? fromSku) {
    final newState = fromSku == null
        ? state.copyWith(message: null, toInventory: [], dumpFiles: [])
        : state;
    emit(newState.copyWith(
      fromInventory: fromSku,
    ));
  }

  void updateDumpSelectedFiles(List<PickedFile> files) {
    emit(state.copyWith(dumpFiles: files));
  }

  void updateToInventory(GradingInput result, int i) {
    final newList = List<GradingInput>.from(state.toInventory);
    newList[i] = result;
    emit(state.copyWith(toInventory: newList));
  }

  void deleteToInventory(int i) {
    final newList = List<GradingInput>.from(state.toInventory);
    newList.removeAt(i);
    emit(state.copyWith(toInventory: newList));
  }

  void addToInventory(GradingInput result) {
    final newList = List<GradingInput>.from(state.toInventory);
    newList.add(result);
    emit(state.copyWith(toInventory: newList));
  }

  void submit({
    required int mandiId,
    required int smoId,
    required InventoryType inventoryType,
    required WeighingQuantity dumpQty,
    required WeighingQuantity qty,
  }) async {
    emit(state.copyWith(isCtaLoading: true));
    final result = await GradingConversionRequest(
      inventoryType: inventoryType.key,
      fromInventory: [(state.fromInventory!, qty)],
      dumpQty: dumpQty,
      toInventory: state.toInventory,
      smoId: smoId,
      dumpFiles: state.dumpFiles,
      gradingInventoryDeviation: state.gradingInventoryDeviation!,
      gradingConversionDeviation: state.gradingConversionDeviation!,
    ).execute();
    final newState = result.fold(
        (left) => state.copyWith(isCtaLoading: false, message: left.message),
        (right) => state.copyWith(
              message: right,
              shouldPop: true,
              isCtaLoading: false,
            ));
    emit(newState);
  }

  void clearMessage() {
    emit(state.copyWith(message: null, shouldPop: false));
  }
}
