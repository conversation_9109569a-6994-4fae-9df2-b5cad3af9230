import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_sku_types_dto.freezed.dart';
part 'get_sku_types_dto.g.dart';

@freezed
class GetSkuTypesDto with _$GetSkuTypesDto {
  const factory GetSkuTypesDto({
    required List<String> quantityTypes,
    required List<String> unitTypes
  }) = _GetSkuTypesDto;

@override
factory GetSkuTypesDto.fromJson(Map<String, dynamic> json) =>_$GetSkuTypesDtoFromJson(json);
}
