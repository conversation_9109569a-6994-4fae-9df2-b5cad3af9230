import 'dart:async';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/mandatory_permission/mandatory_permission.dart';
import 'package:proc2/core/presentation/mandatory_permission/mandatory_permission_screen.dart';
import 'package:proc2/core/utils/permission_manager.dart';

/// Manages the mandatory permissions required by the app.
class MandatoryPermissionManager {
  MandatoryPermissionManager._();

  /// Mandatory Permissions List, which are required to be granted by the user.
  ///
  /// Make sure you add the permissions which are mandatory needed for the app
  /// to function.
  ///
  /// Below are the permissions:
  /// [Permission.location] - Location is required to get the current location
  static final List<MandatoryPermission> _mandatoryPermissions = [
    _storagePermission,
    _locationPermission,
  ];

  /// Must be called at the app start.
  static Future<void> init() async {
    final androidInfo = await DeviceInfoPlugin().androidInfo;
    final sdkVersion = androidInfo.version.sdkInt;
    if (sdkVersion > 32) {
      _mandatoryPermissions[0] = _photosPermission;
    }
  }

  static List<MandatoryPermission> get mandatoryPermissions =>
      _mandatoryPermissions;

  static final MandatoryPermission _photosPermission = MandatoryPermission(
    permission: Permission.photos,
    imagePath: const ImagePathProvider(
      defaultPath: 'assets/images/mandatory_permissions/file_permission.png',
      grantedPath:
          'assets/images/mandatory_permissions/file_permission_granted.png',
    ),
    overviewText: () => 'storage_photos_permission_text'
        .tr('Storage Permission is required to store the data.'),
    subText: () =>
        'storage_photos_permission_sub_text'.tr('Select: \'Allow all\'.'),
  );

  static final MandatoryPermission _storagePermission = MandatoryPermission(
    permission: Permission.storage,
    imagePath: const ImagePathProvider(
      defaultPath: 'assets/images/mandatory_permissions/file_permission.png',
      grantedPath:
          'assets/images/mandatory_permissions/file_permission_granted.png',
    ),
    overviewText: () => 'storage_photos_permission_text'
        .tr('Storage Permission is required to store the data.'),
    subText: () =>
        'storage_photos_permission_sub_text'.tr('Select: \'Allow all\'.'),
  );

  static final MandatoryPermission _locationPermission = MandatoryPermission(
    permission: Permission.location,
    imagePath: const ImagePathProvider(
      defaultPath:
          'assets/images/mandatory_permissions/location_permission.png',
      grantedPath:
          'assets/images/mandatory_permissions/location_permission_granted.png',
    ),
    overviewText: () => 'location_permission_text'
        .tr('Location Permission is required to get the current location.'),
    subText: () => 'location_permission_sub_text'
        .tr('Select: \'Precise\' and \'While using the app\'.'),
  );

  // static final MandatoryPermission _locationAlwaysPermission =
  //     MandatoryPermission(
  //   permission: Permission.locationAlways,
  //   imagePath: const ImagePathProvider(
  //     defaultPath:
  //         'assets/images/mandatory_permissions/location_background_permission.png',
  //     grantedPath:
  //         'assets/images/mandatory_permissions/location_background_permission_granted.png',
  //   ),
  //   overviewText: () => 'background_location_permission_text'.tr(
  //       'Background Location Permission is required to sync the data in the background.'),
  //   dependsOnPermission: Permission.location,
  //   subText: () => 'background_location_permission_sub_text'
  //       .tr('Select: \'Allow all the time\'.'),
  // );

  static Future<List<PermissionStatus>> getAllPermissionStatus() async {
    return Future.wait(
      _mandatoryPermissions.map((permission) => permission.permission.status),
    );
  }

  static Future<bool> isAllPermissionGranted() async {
    final permissionsStatus = await getAllPermissionStatus();
    return permissionsStatus.every((element) => element.isGranted);
  }

  static Future<bool> shouldOpenSettings() async {
    final permissionsStatus = await getAllPermissionStatus();
    bool isAllPermanentlyDenied = true;
    for (int i = 0; i < mandatoryPermissions.length; i++) {
      if (permissionsStatus[i].isGranted) {
        continue;
      } else {
        if (mandatoryPermissions[i].dependsOnPermission != null) {
          final dependsOnPermissionStatus =
              await mandatoryPermissions[i].dependsOnPermission!.status;
          if (dependsOnPermissionStatus.isPermanentlyDenied) {
            isAllPermanentlyDenied = isAllPermanentlyDenied &&
                (permissionsStatus[i].isPermanentlyDenied ||
                    permissionsStatus[i].isDenied);
          } else {
            isAllPermanentlyDenied = isAllPermanentlyDenied &&
                permissionsStatus[i].isPermanentlyDenied;
          }
        } else {
          isAllPermanentlyDenied = isAllPermanentlyDenied &&
              permissionsStatus[i].isPermanentlyDenied;
        }
      }
    }
    return isAllPermanentlyDenied;
  }

  static Future<bool> check(BuildContext context) async {
    final hasAllPermissionGranted = await isAllPermissionGranted();
    if (hasAllPermissionGranted) {
      return true;
    }
    if (!context.mounted) return false;

    return await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const MandatoryPermissionScreen(),
          ),
        ) ??
        false;
  }

  static Future<PermissionGrantStatus> startGrantingProcess(
      BuildContext context, FutureOr<void> Function() onGrant) async {
    final permanentlyDeniedPermissions = <MandatoryPermission>{};
    final deniedPermissions = <MandatoryPermission>{};

    await Future.forEach(_mandatoryPermissions, (mandatoryPermission) async {
      final status = await mandatoryPermission.permission.status;
      if (status.isGranted) {
        return true;
      }
      if (status.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(mandatoryPermission);
        return false;
      }

      // Check if there is any dependency on other permission
      if (mandatoryPermission.dependsOnPermission != null) {
        final dependsOnPermissionStatus =
            await mandatoryPermission.dependsOnPermission!.status;
        if (!dependsOnPermissionStatus.isGranted) {
          deniedPermissions.add(mandatoryPermission);
          return false;
        }
      }

      final permissionResult =
          await mandatoryPermission.permission.requestSerial();
      if (permissionResult == null) {
        deniedPermissions.add(mandatoryPermission);
        return false;
      }
      if (permissionResult.isGranted) {
        await onGrant();
        return true;
      }
      if (permissionResult.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(mandatoryPermission);
        return false;
      }
      deniedPermissions.add(mandatoryPermission);
      return false;
    });

    if (deniedPermissions.isNotEmpty) {
      return PermissionGrantStatus.denied;
    }
    if (permanentlyDeniedPermissions.isNotEmpty) {
      return PermissionGrantStatus.permanentlyDenied;
    }
    return PermissionGrantStatus.allGranted;
  }
}
