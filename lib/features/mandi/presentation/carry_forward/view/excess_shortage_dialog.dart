import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_loss.dart';

class ExcessShortageDialog extends StatefulWidget {
  const ExcessShortageDialog(
      {super.key, required this.unit, required this.losses});
  final String unit;
  final List<CarryForwardLoss> losses;

  @override
  State<ExcessShortageDialog> createState() => _ExcessShortageDialogState();
}

class _ExcessShortageDialogState extends State<ExcessShortageDialog> {
  int selectedItem = 0; // Shortage 0, Excess 1
  String _excessVal = '';
  String _excessComment = '';
  String _shortageVal = '';
  String _shortageComment = '';
  bool isCtaEnabled = false;

  @override
  void initState() {
    if (widget.losses.isNotEmpty) {
      final loss = widget.losses[0];
      if (loss.lossType == 'EXCESS') {
        _excessVal = loss.lossValue;
        _excessComment = loss.comment;
        selectedItem = 1;
      } else if (loss.lossType == 'SHORTAGE') {
        _shortageVal = loss.lossValue;
        _shortageComment = loss.comment;
        selectedItem = 0;
      }
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      backgroundColor: Colors.transparent,
      body: InkWell(
        onTap: () => context.pop(<CarryForwardLoss>[]),
        child: Container(
          height: 700,
          width: MediaQuery.of(context).size.width,
          padding:
              const EdgeInsets.only(left: 20, right: 20, bottom: 50, top: 50),
          child: InkWell(
            onTap: () {},
            child: Card(
                elevation: 4,
                child: SizedBox(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width,
                        height: 50,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Config.primaryColor,
                          boxShadow: const <BoxShadow>[
                            BoxShadow(
                                color: Colors.black54,
                                blurRadius: 15,
                                offset: Offset(0, 0.75))
                          ],
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(5),
                            topRight: Radius.circular(5),
                          ),
                        ),
                        child: Row(
                          children: [
                            InkWell(
                                onTap: () => Navigator.pop(context),
                                child: const Icon(Icons.arrow_back,
                                    color: Colors.white)),
                            const SizedBox(width: 16),
                            Text(
                              LanguageEnum.cfExcessShortageTitle.localized(),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 18),
                            )
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Scrollbar(
                          child: ListView(
                            children: [
                              lossItem(
                                isActive: selectedItem == 0,
                                title: LanguageEnum.cfShortageTitle.localized(),
                                comment: _shortageComment,
                                value: _shortageVal,
                                unit: widget.unit,
                                onValueChange: (value) {
                                  setState(() {
                                    _shortageVal = value;
                                    isCtaEnabled = value.isNotEmpty;
                                  });
                                },
                                onCommentChange: (value) {
                                  setState(() {
                                    _shortageComment = value;
                                  });
                                },
                                onTap: () {
                                  setState(() {
                                    selectedItem = 0;
                                    isCtaEnabled = _shortageVal.isNotEmpty;
                                  });
                                },
                              ),
                              const SizedBox(
                                height: 48,
                              ),
                              lossItem(
                                isActive: selectedItem == 1,
                                title: LanguageEnum.cfExcessTitle.localized(),
                                comment: _excessComment,
                                value: _excessVal,
                                unit: widget.unit,
                                onValueChange: (value) {
                                  setState(() {
                                    _excessVal = value;
                                    isCtaEnabled = value.isNotEmpty;
                                  });
                                },
                                onCommentChange: (value) {
                                  setState(() {
                                    _excessComment = value;
                                  });
                                },
                                onTap: () {
                                  setState(() {
                                    selectedItem = 1;
                                    isCtaEnabled = _excessVal.isNotEmpty;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      ElevatedButton(
                        style: ButtonStyle(
                          fixedSize: MaterialStateProperty.all(
                            Size(MediaQuery.of(context).size.width, 40),
                          ),
                        ),
                        onPressed: !isCtaEnabled
                            ? null
                            : () {
                                final loss = CarryForwardLoss(
                                  lossType:
                                      selectedItem == 0 ? 'SHORTAGE' : 'EXCESS',
                                  lossValue: selectedItem == 0
                                      ? _shortageVal
                                      : _excessVal,
                                  comment: selectedItem == 0
                                      ? _shortageComment
                                      : _excessComment,
                                  unit: widget.unit,
                                );
                                context.pop([loss]);
                              },
                        child: Text(
                          LanguageEnum.addLossCtaTitle.localized(),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      )
                    ],
                  ),
                )),
          ),
        ),
      ),
    ));
  }

  Widget lossItem({
    required bool isActive,
    required String title,
    required String value,
    required String unit,
    required String comment,
    required void Function(String value) onValueChange,
    required void Function(String value) onCommentChange,
    required void Function() onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: InkWell(
        onTap: onTap,
        child: Card(
          color: isActive ? Colors.white : Colors.grey[200],
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      isActive
                          ? Icons.radio_button_checked_rounded
                          : Icons.radio_button_off_rounded,
                      color: isActive ? Colors.green : Colors.white,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 16,
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    left: 40,
                    right: 8,
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        height: 40,
                        width: 80,
                        child: TextFormField(
                          enabled: isActive,
                          onChanged: onValueChange,
                          keyboardType: TextInputType.number,
                          maxLength: 10,
                          initialValue: value,
                          inputFormatters: Config.numberInputFilters,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            hintText: '0',
                            contentPadding: EdgeInsets.only(
                              left: 4,
                              right: 4,
                            ),
                            counterStyle: TextStyle(
                              height: double.minPositive,
                            ),
                            counterText: '',
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text(
                        unit.localized(),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )
                    ],
                  ),
                ),

                const SizedBox(
                  height: 16,
                ),
                // Comment
                Padding(
                  padding: const EdgeInsets.only(
                    left: 40,
                    right: 8,
                  ),
                  child: SizedBox(
                    height: 40,
                    child: TextFormField(
                      enabled: isActive,
                      initialValue: comment,
                      onChanged: onCommentChange,
                      decoration: InputDecoration(
                        border: const OutlineInputBorder(),
                        hintText: LanguageEnum.addLossCommentHint.localized(),
                        contentPadding:
                            const EdgeInsets.only(left: 8, right: 8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
