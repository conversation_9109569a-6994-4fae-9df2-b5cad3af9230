part of 'mandi_inventory_bloc.dart';

@freezed
class MandiInventoryState with _$MandiInventoryState {
  const MandiInventoryState._();
  factory MandiInventoryState.initial({
    required InventoryState mandi,
    required InventoryState returns,
    required InventoryState wastageReturns,
    @Default(false) bool isGradeD1TransferLoading,
    @Default(false) bool isGradeCTransferLoading,
    @Default(false) bool isGradeBTransferLoading,
    @Default(false) bool isGradeATransferLoading,
    @Default(null) String? message,
  }) = _Initial;

  static MandiInventoryState get empty => MandiInventoryState.initial(
        mandi: InventoryState(
          items: [],
          isLoading: false,
          errorMessage: null,
        ),
        returns: InventoryState(
          items: [],
          isLoading: false,
          errorMessage: null,
        ),
        wastageReturns: InventoryState(
          items: [],
          isLoading: false,
          errorMessage: null,
        ),
      );
}

@freezed
class InventoryState with _$InventoryState {
  const factory InventoryState({
    required List<List<InventoryItem>> items,
    required bool isLoading,
    required String? errorMessage,
  }) = _InventoryState;
}
