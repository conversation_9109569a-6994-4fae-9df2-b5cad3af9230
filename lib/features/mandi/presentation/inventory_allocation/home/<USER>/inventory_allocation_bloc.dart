import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_init.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_v2.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/destination_info.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/domain/use_case/cancel_allocation_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/create_allotment_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_allotment_init_usecase.dart';

part 'inventory_allocation_bloc.freezed.dart';
part 'inventory_allocation_event.dart';
part 'inventory_allocation_state.dart';

@injectable
class InventoryAllocationBloc
    extends Bloc<InventoryAllocationEvent, InventoryAllocationState> {
  InventoryAllocationBloc(
    this._allotmentInitUseCase,
    this._createAllotmentUseCase,
    this._cancelAllocationUseCase,
    this._mandiRepository,
    this._language,
  ) : super(const _Initial()) {
    on<InventoryAllocationEvent>((event, emit) async {
      await event.when(started: (smoId) async {
        await _allotmentInitUseCase(smoId).then(
          (value) => value.fold(
            (l) => emit(InventoryAllocationState.error(l)),
            (r) {
              if (state is Input) {
                emit((state as Input).copyWith(
                  initData: r,
                  selectedDestinationType: 'mandi',
                ));
              } else {
                emit(
                  InventoryAllocationState.input(
                    initData: r,
                    selectedDestinationType: 'mandi',
                  ),
                );
              }
            },
          ),
        );
      }, destinationTypeChanged: (String? destinationType) {
        final currentState = state;
        if (currentState is Input) {
          final customerGroup =
              destinationType == 'mandi' ? null : currentState.customerGroup;
          final newState = (state as Input).copyWith(
            selectedDestinationType: destinationType,
            selectedDestination: null,
            customerGroup: customerGroup,
          );
          emit(newState);
        }
      }, destinationChanged: (String? destination) {
        if (state is Input) {
          final currentState = state as Input;
          final selectedDestinationType = currentState.selectedDestinationType;
          final destinations =
              currentState.initData.destinations[selectedDestinationType] ?? [];
          final selectedInfo = destinations.firstWhere(
            (e) => e.name.toLowerCase() == destination!.toLowerCase(),
          );
          final customerGroup =
              selectedDestinationType == 'mandi' ? null : destination;
          emit(currentState.copyWith(
            selectedDestination: selectedInfo,
            customerGroup: customerGroup,
          ));
        }
      }, deliverySlotChanged: (String? deliverySlot) {
        if (state is Input) {
          final newState =
              (state as Input).copyWith(deliverySlot: deliverySlot);
          emit(newState);
        }
      }, deliveryDateChanged: (DateTime? deliveryDate) {
        if (state is Input) {
          final newState =
              (state as Input).copyWith(deliveryDate: deliveryDate);
          emit(newState);
        }
      }, createAllotment: (
        int smoId,
        String mandiName,
      ) async {
        if (state is Input) {
          final currentState = state as Input;
          if (currentState.isCtaLoading) {
            return;
          }
          final deliverySlot = currentState.deliverySlot == null
              ? currentState.initData.deliverySlot[0]
              : currentState.deliverySlot!;
          final deliveryDate = currentState.deliveryDate == null
              ? 0
              : currentState.deliveryDate!.toIstUtcEpoch() ~/ 1000;
          emit(currentState.copyWith(isCtaLoading: true));
          await _createAllotmentUseCase(
            smoId: smoId,
            deliveryDate: deliveryDate,
            deliverySlot: deliverySlot,
            destinationId: currentState.selectedDestination!.id,
            destinationType: currentState.selectedDestinationType!,
            customerGroup: currentState.customerGroup,
          ).then(
            (value) => value.fold(
              (l) => emit(
                currentState.copyWith(
                  isCtaLoading: false,
                  message: l.message,
                ),
              ),
              (r) {
                final queryParams = {
                  'customerGroup': currentState.customerGroup,
                  'deliverySlot': deliverySlot.toString(),
                  'status': 'IN_PROGRESS'
                };
                if (deliveryDate != 0) {
                  queryParams['deliveryDate'] = deliveryDate.toString();
                }
                emit(
                  currentState.copyWith(
                    isCtaLoading: false,
                    message: null,
                    selectedDestination: null,
                    deliveryDate: null,
                    deliverySlot: null,
                    allotmentIdToOpen: r,
                    allotmentDestinationName: mandiName,
                    allotmentQueryParams: queryParams,
                  ),
                );
                add(InventoryAllocationEvent.getAllotments(smoId));
              },
            ),
          );
        }
      }, getAllotments: (int smoId) async {
        final currentState = state;
        if (currentState is Input) {
          final result = await _mandiRepository.getAllotmentsV2(
            smoId,
            currentState.initData,
          );
          emit(
            result.fold(
              (left) => currentState.copyWith(
                message: left.message,
                allotmentIdToOpen: null,
                mandiAllotments: null,
                customerAllotments: null,
                hasErrorInGetAllotments: true,
              ),
              (right) => currentState.copyWith(
                mandiAllotments: right[0],
                customerAllotments: right[1],
                allotmentIdToOpen: null,
                hasErrorInGetAllotments: false,
              ),
            ),
          );
        }
      }, openAllotment: (
        int allotmentId,
        String destinationName,
        Map<String, dynamic> allotmentQueryParams,
      ) {
        if (state is Input) {
          final currentState = state as Input;
          emit(
            currentState.copyWith(
              allotmentIdToOpen: allotmentId,
              allotmentDestinationName: destinationName,
              allotmentQueryParams: allotmentQueryParams,
            ),
          );
        }
      }, clearMessage: () {
        if (state is Input) {
          final currentState = state as Input;
          emit(currentState.copyWith(message: null, allotmentIdToOpen: null));
        }
      }, clearOpenAllotment: () {
        if (state is Input) {
          final currentState = state as Input;
          emit(
            currentState.copyWith(
              allotmentIdToOpen: null,
              allotmentDestinationName: null,
              allotmentQueryParams: null,
            ),
          );
        }
      }, cancelAllocation: (allocationId, smoId) async {
        await _cancelAllocationUseCase(allocationId);
        add(InventoryAllocationEvent.getAllotments(smoId));
      });
    });
  }

  final GetAllotmentInitUseCase _allotmentInitUseCase;
  final CreateAllotmentUseCase _createAllotmentUseCase;
  // final GetAllotmentsUseCase _getAllotmentsUseCase;
  final CancelAllocationUseCase _cancelAllocationUseCase;
  final MandiRepository _mandiRepository;
  final Language _language;
}
