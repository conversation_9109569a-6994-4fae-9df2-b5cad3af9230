import 'dart:io' as io;

import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/file_downloader/file_downloader.dart';

class FileDownloaderImpl implements FileDownloader {
  @override
  Future<void> downloadText(String fileName, String data) async {
    final file = io.File(fileName);
    await file.writeAsString(data);
  }

  @override
  Future<void> downloadBlob(String fileName, List<int>? bytes) async {
    if (bytes == null) {
      return;
    }
    final file = io.File(fileName);
    await file.writeAsBytes(bytes);
  }
}
