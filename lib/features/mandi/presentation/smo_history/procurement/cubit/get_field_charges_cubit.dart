import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/charges/field_charges.dart';
import 'package:proc2/features/mandi/domain/use_case/get_field_charges_usecase.dart';

part 'get_field_charges_state.dart';
part 'get_field_charges_cubit.freezed.dart';

@injectable
class GetFieldChargesCubit extends Cubit<GetFieldChargesState> {
  GetFieldChargesCubit(this._getFieldChargesUseCase)
      : super(const GetFieldChargesState.initial());

  final GetFieldChargesUseCase _getFieldChargesUseCase;

  int _smoId = -1;

  void init(int smoId) {
    _smoId = smoId;
  }

  Future<void> fetch() async {
    return await _getFieldChargesUseCase(_smoId).then(
      (value) => emit(
        value.fold(
          GetFieldChargesState.error,
          GetFieldChargesState.success,
        ),
      ),
    );
  }
}
