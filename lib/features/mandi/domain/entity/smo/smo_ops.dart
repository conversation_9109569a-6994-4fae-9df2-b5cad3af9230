import 'package:freezed_annotation/freezed_annotation.dart';

part 'smo_ops.freezed.dart';
part 'smo_ops.g.dart';

@freezed
class SmoOps with _$SmoOps {
  const factory SmoOps({
    required int smoId,
    required int mandiId,
    required String currentStatus,
    required int smoStartTime,
    required int? smoEndTime,
  }) = _SmoOps;

  factory SmoOps.fromJson(Map<String, dynamic> json) => _$SmoOpsFromJson(json);
}
