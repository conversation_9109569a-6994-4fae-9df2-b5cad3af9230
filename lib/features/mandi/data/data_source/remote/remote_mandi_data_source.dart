import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/data/network/extensions/chopper_either_extension.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/data/data_source/mandi_data_source.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/accept_incoming_stocks_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/to_closing_stocks_req_body_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/update_mandi_inventory_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/alloted_item_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/allotment_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/allotment_init_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/trip_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/carry_forward/carry_forward_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/charges/get_charge_types_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/create/create_smo_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/inventory/get_inventory_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/losses/get_loss_init_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/mandi_inventory/mandi_inventory_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/mandis/mandi_info_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/consignment_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/sku/sku_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/sku_types/get_sku_types_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo/smo_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/smo_detail_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/smo_history_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/user_mandi/user_mandi_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/service/mandi_service.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/input_model/mandi_inventory_input_model.dart';

import '../../../domain/entity/lotting/lotting_deviation.dart';

@Injectable(as: MandiDataSource)
class RemoteMandiDataSource implements MandiDataSource {
  RemoteMandiDataSource(this._mandiService);

  final MandiService _mandiService;

  @override
  Future<Either<ErrorResult<dynamic>, CreateSmoDto>> createSmo(String mandiId) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.createSmo(mandiId),
      transform: (data) {
        if (data is Map<String, dynamic>) {
          return CreateSmoDto.fromJson(data);
        }
        throw Exception();
      },
      errorTransform: (data) {},
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<MandiInfoDto>>> getAllMandis() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getAllMandis,
      transform: (data) {
        if (data is List) {
          return data.map((e) {
            if (e is Map<String, dynamic>) {
              return MandiInfoDto.fromJson(e);
            }
            throw Exception();
          }).toList();
        } else {
          throw Exception(
              'GetAllMandis: Expected a json array. Received ${data.runtimeType}');
        }
      },
      errorTransform: (data) {
        return null;
      },
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, UserMandiDto>> getUserMandis() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getUserMandis,
      transform: (data) {
        if (data is Map<String, dynamic>) {
          return UserMandiDto.fromJson(data);
        }
        throw Exception(
            'GetMandi: Expected a json object. Received ${data.runtimeType}');
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, SmoDto>> getSmo(String mandiId) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getSmo(mandiId),
      transform: (data) {
        if (data is Map<String, dynamic>) {
          return SmoDto.fromJson(data);
        }
        throw Exception();
      },
      errorTransform: (data) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<SkuDto>>> getSku(String language) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getSku(lang: language),
      transform: (data) {
        if (data is Map<String, dynamic>) {
          final skus = data['skus'] as List<dynamic>? ?? [];
          return skus
              .map((e) => e as Map<String, dynamic>)
              .map(SkuDto.fromJson)
              .toList();
        }
        throw Exception();
      },
      errorTransform: (e) {
        return null;
      },
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> addProcurement(
    Map<String, dynamic> body,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.addProcurement(body),
      transform: (d) {
        if (d is String) {
          return d;
        }
        return 'Added Procurement!';
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<ConsignmentDto>>>
      getIncomingStockList(int mandiId) async {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getIncomingStocks(mandiId),
      transform: (d) {
        if (d is List) {
          return d
              .map((e) => e as Map<String, dynamic>)
              // ignore: unnecessary_lambdas
              .map((e) => ConsignmentDto.fromJson(e))
              .toList();
        }
        return [];
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, CarryForwardDto>> getCarryForward({
    required int smoId,
  }) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getCarryForward(smoId),
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return CarryForwardDto.fromJson(d);
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, GetChargeTypesDto>> getChargesType() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getChargesType,
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return GetChargeTypesDto.fromJson(d);
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getFieldCharges(
      int smoId) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getFieldCharges(smoId),
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, GetLossInitDto>> getLossInit() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getLossInit,
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return GetLossInitDto.fromJson(d);
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, GetSkuTypesDto>> getSkuTypes() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getSkuTypes,
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return GetSkuTypesDto.fromJson(d);
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> acceptIncomingStocks(
    int smoId,
    InventoryRecievingInputModel inventoryRecievingInp,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.acceptIncomingStocks(
        toAcceptIncomingStocksBody(smoId, inventoryRecievingInp),
      ),
      errorTransform: (e) => null,
      transform: (d) {
        if (d is Map<String, dynamic>) {
          if (d['status'] == false) {
            throw Exception(d['message']);
          }
        }
        return;
      },
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> addCharges(
    Map<String, dynamic> body,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.addCharges(body),
      transform: (d) {
        if (d is String) {
          return d;
        }
        return '';
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, MandiInventoryDto>> getMandiInventory(
    int mandiId,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getMandiInventory(mandiId),
      transform: (d) {
        if (d is List<dynamic>) {
          return MandiInventoryDto.fromJson({'skuList': d});
        }
        throw Exception('Failed to get Mandi Inventory');
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> updateMandiInventory(
    MandiInventoryInputModel inpModel,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService
          .updateMandiInventory(toUpdateMandiInventoryRequestBody(inpModel)),
      transform: (d) {
        if (d is Map<String, dynamic>) {
          if (d['status'] == false) {
            throw Exception(d['message']);
          }
        }
        return;
      },
      errorTransform: (data) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> acceptCarryForward(
    Map<String, dynamic> body,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.acceptCarryForward(body),
      transform: (d) {
        if (d is String) {
          return d;
        }
        return '';
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, GetInventoryDto>> getInventory(
    int mandiId,
    String type,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getInventory(
        mandiId,
        type,
      ),
      transform: (d) {
        if (d is List<dynamic>) {
          return GetInventoryDto.fromJson({'skus': d});
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> closeInventory(
    int smoId,
    int mandiId,
    List<SkuInputWithLoss> data,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService
          .closeStocks(toClosingStocksReqBody(smoId, mandiId, data)),
      transform: (e) {},
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, AllotmentInitDto>> getAllotmentInit(
      int smoId) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getInitAllotment(smoId),
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return AllotmentInitDto.fromJson(d);
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, int>> createAllotment({
    required int smoId,
    required int destinationId,
    required String destinationType,
    required int deliveryDate,
    required String deliverySlot,
    required String? customerGroup,
  }) {
    final data = {
      'smoId': smoId,
      'destinationId': destinationId,
      'destinationType': destinationType.toUpperCase(),
      'deliveryDate': deliveryDate,
      'deliverySlot': deliverySlot,
    };
    if (customerGroup != null) {
      data['customerGroup'] = customerGroup;
    }
    return _mandiService.safeCall(
      apiCall: () => _mandiService.createAllotment(data),
      transform: (d) {
        if (d is int) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<AllotmentDto>>> getAllotments(
    int smoId,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getAllotments(smoId),
      transform: (d) {
        if (d is List<dynamic>) {
          return d
              .map((e) => e as Map<String, dynamic>)
              .map(AllotmentDto.fromJson)
              .toList();
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<AllotedItemDto>>> getAllotedItems(
    int allotmentId,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getAllotedItems(allotmentId),
      transform: (d) {
        if (d is List<dynamic>) {
          return d
              .map((e) => e as Map<String, dynamic>)
              .map(AllotedItemDto.fromJson)
              .toList();
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> submitAllotment(
    Map<String, dynamic> body,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.submitAllotment(body),
      transform: (d) {
        if (d is String) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<SmoHistoryDto>>> getSmoHistory() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getSmoHistory,
      transform: (d) {
        if (d is List<dynamic>) {
          return d
              .map((e) => e as Map<String, dynamic>)
              .map(SmoHistoryDto.fromJson)
              .toList();
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<SmoDetailDto>>> getSmoDetails(
    int smoId,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getSmoDetails(smoId),
      transform: (d) {
        if (d is List<dynamic>) {
          return d
              .map((e) => e as Map<String, dynamic>)
              .map(SmoDetailDto.fromJson)
              .toList();
        }

        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> updateOrSubmitEditProcurement(
    Map<String, dynamic> body, {
    bool isSubmit = false,
  }) {
    return _mandiService.safeCall(
      apiCall: () => isSubmit
          ? _mandiService.submitProcurement(body)
          : _mandiService.updateProcurementAmount(body),
      transform: (d) {
        if (d is String) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> submitConversion(
    Map<String, dynamic> body,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.submitConversion(body),
      transform: (d) {
        if (d is String) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  Future<Either<ErrorResult<dynamic>, void>> closeSmoOps(int smoId) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.closeOps(smoId),
      transform: (d) {
        if (d is Map<String, dynamic>) {
          if (d['status'] == false) {
            throw Exception(d['error']['message']);
          }
        }
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, List<TripDto>>> getTripsForAllocation(
    int allocationId,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getTripsForAllocation(allocationId),
      transform: (d) {
        if (d is List<dynamic>) {
          return d
              .map((e) => e as Map<String, dynamic>)
              .map(TripDto.fromJson)
              .toList();
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, String>> dispatchTrip({
    required int tripId,
    required String driverName,
    required String driverPhone,
    required String vehicleNumber,
    required int smoId,
  }) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.dispatchTrip({
        'tripId': tripId,
        'driverName': driverName,
        'driverMobile': driverPhone,
        'vehicleNumber': vehicleNumber,
        'smoId': smoId,
        'tripStartedAt': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      }),
      transform: (d) {
        if (d is String) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, void>> cancelAllocation(
    int allocationId,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.cancelAllocation(allocationId),
      transform: (d) {
        return;
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getLanguages() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getLanguages,
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getLanguageFile(
    String code,
  ) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.getLanguageFile(code),
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return d;
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, LottingDeviation>> getLottingDeviation() {
    return _mandiService.safeCall(
      apiCall: _mandiService.getLottingDeviations,
      transform: (d) {
        if (d is Map<String, dynamic>) {
          return LottingDeviation(
            lottingDeviation: d['lottingDeviation'] as double,
            deLottingDeviation: d['delottingDeviation'] as double,
            gradingInventoryDeviation: d['gradingInventoryDeviation'] as double,
            gradingConversionDeviation:
                d['gradingConversionDeviation'] as double,
          );
        }
        throw Exception();
      },
      errorTransform: (e) => null,
    );
  }

  @override
  Future<Either<ErrorResult<dynamic>, bool>> regenerateDeliveryMemo(
      int allotmentId, String langCode) {
    return _mandiService.safeCall(
      apiCall: () => _mandiService.regenerateDeliveryMem(allotmentId, langCode),
      transform: (d) {
        return true;
      },
      errorTransform: (e) => null,
    );
  }
}
