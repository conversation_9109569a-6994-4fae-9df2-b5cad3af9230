import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';

extension CancelDialog on BuildContext {
  Future<bool?> showAlertDialog({
    required String title,
    required String message,
    bool showActions = true,
  }) {
    return showDialog<bool>(
      context: this,
      builder: (_) => AlertDialog(
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(message),
        actions: !showActions
            ? null
            : [
                TextButton(
                  onPressed: () {
                    this.pop(false);
                  },
                  child: Text(LanguageEnum.falseButton.localized()),
                ),
                TextButton(
                  onPressed: () {
                    this.pop(true);
                  },
                  child: Text(LanguageEnum.trueButton.localized()),
                ),
              ],
      ),
    );
  }
}
