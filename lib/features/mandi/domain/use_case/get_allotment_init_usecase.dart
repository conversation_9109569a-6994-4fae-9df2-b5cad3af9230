import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_init.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetAllotmentInitUseCase {
  GetAllotmentInitUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, AllotmentInit>> call(int smoId) async {
    return _mandiRepository.getAllotmentInit(smoId);
  }
}
