import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/allotment_dto.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_init.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_v2.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/delivery_memo.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/process_action.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/process_stage.dart';

class GetAllotmentV2Request
    extends BaseRequest<dynamic, List<List<AllotmentV2>>> {
  final int smoId;
  final AllotmentInit init;

  GetAllotmentV2Request({required this.smoId, required this.init});

  @override
  String getPath() => 'allotments/smo/v2/$smoId';

  @override
  List<List<AllotmentV2>> mapper(data) {
    if (data is List<dynamic>) {
      final mandiMap = Map.fromEntries(
          init.destinations['mandi']!.map((e) => MapEntry(e.id, e.name)));
      final mandiList = <AllotmentV2>[];
      final customerList = <AllotmentV2>[];

      data
          .map((e) => e as Map<String, dynamic>)
          .map((e) => AllotmentDto.fromJson(e))
          .forEach((e) {
        final item = AllotmentV2(
          id: e.id,
          smoId: e.smoId,
          destinationId: e.destinationId,
          customerGroup: e.customerGroup ?? '',
          deliveryDate: e.deliveryDate,
          deliverySlot: e.deliverySlot,
          destinationName: e.destinationId == -1
              ? e.customerGroup ?? ''
              : mandiMap[e.destinationId] ?? '',
          status: ProcessStage.from(e.status),
          consignmentId: e.consignmentId,
          actions: e.actions
              .map((e) => ProcessAction.from(e))
              .where((e) => e.key != 'unknown')
              .toList(),
          deliveryMemo: DeliveryMemo(
            errorCode: e.deliveryMemo?.errorCode,
            errorMessage: e.deliveryMemo?.errorMessage,
            status: e.deliveryMemo?.status,
            file: e.deliveryMemo?.file,
          ),
          processStage:
              e.processStages.map((e) => ProcessStage.from(e)).toList(),
        );
        if (mandiMap[item.destinationId] != null) {
          mandiList.add(item);
        } else {
          customerList.add(item);
        }
      });
      return [mandiList, customerList];
    }
    throw 'Invalid json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
