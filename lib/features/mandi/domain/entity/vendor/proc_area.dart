import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';

part 'proc_area.freezed.dart';
part 'proc_area.g.dart';

@freezed
class ProcArea with _$ProcArea implements DropDownItem {
  const ProcArea._();
  const factory ProcArea({
    required int id,
    required String name,
    required String stateCode,
  }) = _ProcArea;

  @override
  factory ProcArea.fromJson(Map<String, dynamic> json) =>
      _$ProcAreaFromJson(json);

  @override
  String get displayText => name;
}
