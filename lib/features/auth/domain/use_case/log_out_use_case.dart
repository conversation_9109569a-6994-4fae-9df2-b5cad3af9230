import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/auth/domain/repository/auth_repository.dart';

@injectable
class LogOutUseCase {
  LogOutUseCase(this._authRepository);

  final AuthRepository _authRepository;

  Future<Either<ErrorResult<void>, void>> call() async {
    return _authRepository.logOut();
  }
}
