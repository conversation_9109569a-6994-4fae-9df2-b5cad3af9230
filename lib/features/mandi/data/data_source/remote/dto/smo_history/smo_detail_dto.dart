import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/sku_history_dto.dart';

part 'smo_detail_dto.freezed.dart';
part 'smo_detail_dto.g.dart';

@freezed
class SmoDetailDto with _$SmoDetailDto {
  factory SmoDetailDto({
    required int id,
    required int createdAt,
    required String status,
    required int smoId,
    @Default([]) List<String>? images,
    required List<SkuHistoryDto> items,
    @Default(null) String? vendorName,
  }) = _SmoDetailDto;

  factory SmoDetailDto.fromJson(Map<String, dynamic> json) =>
      _$SmoDetailDtoFromJson(json);
}
