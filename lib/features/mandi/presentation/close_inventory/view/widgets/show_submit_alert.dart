import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

void showSubmitAlert(
  BuildContext context, {
  required int mandiId,
  required int smoId,
}) {
  Alert(
    context: context,
    type: AlertType.warning,
    title: LanguageEnum.closeInventoryConfirmAlertTitle.localized(),
    desc: LanguageEnum.closeInventoryConfirmAlertMessage.localized(),
    buttons: [
      DialogButton(
        onPressed: () => Navigator.pop(context),
        width: 120,
        color: Colors.red,
        child: Text(
          LanguageEnum.cancelButton.localized(),
          style: const TextStyle(color: Colors.white, fontSize: 20),
        ),
      ),
      DialogButton(
        onPressed: () {
          Navigator.pop(context);

          context.read<CloseInventoryBloc>().add(
                CloseInventoryEvent.submitCloseInventory(
                  smoId: smoId,
                  mandiId: mandiId,
                ),
              );
        },
        width: 120,
        child: Text(
          LanguageEnum.confirmButton.localized(),
          style: const TextStyle(color: Colors.white, fontSize: 20),
        ),
      ),
    ],
  ).show();
}
