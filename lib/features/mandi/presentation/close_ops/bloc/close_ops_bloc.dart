import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/domain/use_case/close_smo_ops_usecase.dart';

part 'close_ops_event.dart';
part 'close_ops_state.dart';
part 'close_ops_bloc.freezed.dart';

@injectable
class CloseOpsBloc extends Bloc<CloseOpsEvent, CloseOpsState> {
  CloseOpsBloc(this._closeSmoOpsUseCase) : super(const _Initial()) {
    on<CloseOpsEvent>((event, emit) async {
      await event.when(
        started: (smoId) async {
          emit(const CloseOpsState.loading());
          // ignore: avoid_dynamic_calls
          await _closeSmoOpsUseCase(smoId: smoId).then(
            (value) => value.fold(
              (l) {
                emit(CloseOpsState.failure(
                  l.code,
                  l.message,
                ));
              },
              (r) => emit(
                const CloseOpsState.success(),
              ),
            ),
          );
        },
      );
    });
  }

  final CloseSmoOpsUseCase _closeSmoOpsUseCase;
}
