{"retry": "Retry", "uploadingLogFile": "Uploading log file....", "reportingBugMessage": "Reporting bug....", "bugReported": "Bug Reported!", "commentCopied": "Comment copied!", "reportBug": {"title": "Report a bug", "description": "Description", "attachments": "Attachments", "addAttachment": "Add attachment"}, "submit": "Submit", "upload": {"deleteImageMessage": "Are you sure you want to delete it?", "updatedMessage": "Updated the files", "clearedMessage": "Cleared the uploaded files", "exitPopupTitle": "Go back?", "exitPopupMessage": "Images which are not uploaded will be removed. Are you sure?", "maxSelectionMessage": "You can select maximum of ##maxSelection## images", "deleteImageTitle": "Delete?", "ctaTitle": "Upload", "erorUploadingFiles": "Error while uploading files!", "chooseOption": "Choose option", "chooseOptionGallery": "Gallery", "chooseOptionCamera": "Camera"}, "vendorManagement": {"create": "Create", "createLocation": "Create Location", "createVendor": "Create <PERSON><PERSON><PERSON>", "vendorName": "Vendor Name*", "vendorPhone": "Vendor Phone*", "pocName": "POC Name", "pocPhone": "POC Phone", "addNewVendor": "Add New Vendor", "addNewLocation": "Add New Location", "noAreaMapped": "No Area Mapped"}, "vendorName": "Vendor Name*", "vendorLocation": "Vendor Location*", "searchSku": {"selectSku": "Select Sku"}, "unit": "Unit", "count": "Count", "qty": "Qty", "startedAt": "Started at", "closedAt": "Closed at", "reportBugMessage": "Found a bug? Report it!", "reportNow": "Report Now", "info": "Info", "searchingIfDeviceNearbyMessage": "Searching if device is nearby...", "error": "Error", "weighingMachineNotFoundMessage": "Failed to find weighing machine!", "success": "Success", "weighingMachineSucessMessage": "Weighing machine connected successfully!", "weighingMachineFailedMessage": "Failed to connect weighing machine!", "bluetoothPermissionMessage": "Bluetooth permission is required!", "weighingMachine": "Weighing Machine", "cancel": "Cancel", "update": "Update", "emptyQuantityMessage": "Please fill the previous quantity!", "weighingMachineStatus": "Weighing Scale Status", "weights": "Weights", "total": "Total", "captureWeight": "Capture Weight", "updateWeight": "Update Weight", "terminalId": "Terminal Id", "reportBugAdmin": {"title": "Active Bug Reports"}, "noBugReportFound": "No active bug reports", "comment": "Comment", "attachments": "Attachments", "downloadLogFile": "Download Log File", "resolveABug": "Resolve Bug", "resolveABugMessage": "Are you sure you want to resolve this bug?", "markResolve": "<PERSON>", "adminMenu": {"adminMenu": "Admin Panel", "procAdmin": "Proc Admin", "procOrder": "Proc Order", "procOrderUpdate": "Proc Order Update", "manageUsers": "Manage Users", "weighingMachine": "Weighing Machine", "bugReports": "Bug Reports", "supplyOrders": "Supply Orders"}, "procurementUpdate": {"deleteSkuMessage": "Are you sure you want to delete this SKU? It can not be reverted!", "title": "Procurement Update", "noOrderMessage": "No procurement order found for this date!"}, "updateProcurement": {"title": "Update Procurement", "errorLoadingChildOrders": "Error while loading child orders. Please try again."}, "pitstops": "Pitstops", "serialNumber": "S.No.", "createdAt": "Created At", "createdBy": "Created By", "skus": "Skus", "status": "Status", "weighingMachineBypass": {"submit": "Submit"}, "searchUsers": "Search Users", "name": "Name", "emailorPhone": "Email/Phone", "invalidUser": "Invalid User", "loginFailed": "Login Failed", "wheelocityFailedLogin": "Login Failed by Wheelocity", "waitingForOTPToBeSent": "Waiting For OTP To be sent", "failedOTPVerification": "Failed OTP Verification", "enter6DigitsOTP": "Enter Six Digits OTP", "invalidOTP": "Invalid OTP", "pleaseEnter10Digit": "Please Enter 10 Digits Number", "loginViaPhone": "Login with Phone", "phoneNumber": "Phone number", "otp": "OTP", "changePhoneNumber": "Change Number", "verifyOTP": "Verify OTP", "sendOTP": "Get OTP", "amount": "Amount", "addProc": {"placeProcOrder": "Place Procurement Order", "placeProcOrderSubmit": "Submit", "receivedQuantity": "Received Quantity", "receivedLots": "Received Lots", "orderedQuantity": "Ordered Quantity", "orderedLots": "Ordered Lots", "submitPopupTitle": "Are you sure?", "submitPopupMessage": "The procurements will submit.", "deletePopupTitle": "Are you sure?", "deletePopupMessage": "The procurement will be deleted.", "productCost": "Product Cost", "fieldCharges": "Field Charges", "addComment": "Add Comment", "editComment": "Edit Comment", "selectSkuNumberOfLotsLabel": "Number of Lots*", "addedSlipsMessage": "Added ##count## slips", "submitSuccessMessage": "Procurement Submitted Successfully!", "searchBoxHint": "Search SKU", "selectSkuCtaTitle": "Done", "selectSkuAmountLabel": "Amount", "selectSkuQuantityLabel": "Quantity*", "selectSkuWeightLabel": "Weight(Kg)", "selectSkuUnitLabel": "Unit", "selectSkuLotSizeLabel": "Lot Size", "selectSkuProcTypeLabel": "Type", "selectSkuHeadingEdit": "Edit SKU", "selectSkuHeadingAdd": "Add SKU", "addSKUBtn": "Add SKU", "uploadSplitsBtnName": "Upload Slips", "procSummaryBtnName": "Proc Summary", "emptySkuDescription": "Please add SKUs to submit for procurement!", "exitPopupTitle": "Are you sure?", "exitPopupMessage": "You will lose all the data if you go back!", "submitBtn": "Submit", "searchSKU": "Search SKU", "startTyping": "Start Typing", "selected": "Selected", "done": "Done"}, "addProcAdmin": {"selectPitStops": "Select Pit Stops"}, "procFieldCharges": {"addNew": "Add New", "fieldCharges": "Field Charges"}, "placeOrder": {"orderedQuantity": "Ordered Quantity", "previousReceive": "Already Received", "newReceived": "<PERSON>ly Received", "totalReceive": "Total Received", "finalSubmit": "Received All?"}, "orderedQty": "<PERSON><PERSON>", "received": "Received", "childOpenOrders": "Child Open Orders", "assignUser": "Assign User", "attendance": {"noUserAssigned": "No User Assigned"}, "phone": "Phone Number", "refresh": "Refresh", "lotSize": "Lot Size", "noOfLots": "No. of Lots", "losses": "Losses", "closeInventory": {"noSkuMessage": "Please add SKUs for closing inventory.", "successMessage": "Inventory Closed Successfully!", "title": "Close Inventory", "submitBtn": "Submit", "doneBtn": "Done", "confirmAlertTitle": "Confirmation!", "confirmAlertMessage": "Please confirm before submitting close inventory.\n\n You won't be able to edit it later.", "emptyTitle": "No Inventory Found!", "emptyMessage": "Please submit it!"}, "receiveInventory": {"damaged": "Damaged", "damagedLot": "Damaged Lots", "addDamages": "Add", "expectedQty": "Expected", "expectedLots": "Expected Lots", "received": "Received", "receivedLots": "Received Lots", "damagedLots": "Damaged Lots", "totalDamages": "Total Damage", "goodQty": "Good Qty", "goodLots": "Good Lots", "editUpdateCta": "Update", "infoTitle": "Trip Info", "from": "From", "submit": "Submit", "damages": "Damages", "missing": "Missing"}, "closeInv": {"quantity": "Quantity", "numberOfLots": "Number of Lots"}, "mandiMenu": {"procurements": "Procurements", "liquidation": "Liquidation", "addProcurement": "Add Procurement", "addCarryForward": "Add <PERSON>y Forward", "addFieldCharges": "Add Field Charges", "recieveInventory": "Receive Inventory", "closeInventory": "Close Inventory", "mandiInventory": "Mandi Inventory", "skuAllocation": "SKU Allocation", "mandiProcSummary": "Mandi Procurement Summary", "Lotting": "Lotting", "smoHistory": "Smo History", "closeOps": "Close Ops"}, "noAccess": "No Access", "gradingConversion": {"selectSku": "Select Sku", "qtyLabel": "Qty", "title": "Grading Conversion", "configLoadError": "Error while loading configuration!", "clearSkuPopupTitle": "Clear Select Sku?", "clearSkuPopupMessage": "Are you sure you want to clear selected Sku? All data will be cleared!", "availableQty": "Available Qty", "availableLot": "Available Lot", "qtyToGrade": "Qty to Grade*", "lotsToGrade": "No. of Lots to Grade*", "addSku": "Add Sku", "submitCta": "Submit", "dumpQty": "Dump Quantity($unit)"}, "gradingPopup": {"submitCta": "Submit"}, "quantity": "Quantity", "grading": {"title": "Grading", "selectSourceInventory": "Select Source Inventory", "mandiInventory": "Mandi Inventory", "returnInventory": "Return Inventory", "wastageReturnsInventory": "Wastage Returns Inventory"}, "smoHistory": {"procHistoryTitle": "Procurement History", "procHistoryEmptyTitle": "No Procurement!", "procHistoryEmptyMessage": "No procurement found for the selected SMO.", "updatePriceTitle": "Update Prices", "updatePriceUpdateCtaLabel": "Update", "updatePriceSubmitCtaLabel": "Submit", "updatePriceAlertTitle": "Confirmation", "updatePriceAlertMessage": "Please confirm before submitting prices.\n\n You won't be able to edit it later."}, "home": {"noOpsFound": "No operations found!", "welcomeBack": "Welcome Back", "selectAMandi": "Select a Mandi", "signout": "Signout", "updateLanguageMenu": "Update Language"}, "adminPanel": "Admin Panel", "allocateInventory": {"customerAllocationEmpty": "No Supply Orders Found!", "regenerateDeliveryMemoMessage": "We are regenerating delivery memo! Please wait!", "regenerateDeliveryMemo": "Regenerate Delivery Memo", "title": "Allocate Inventory", "selectDestination": "Select Destination", "allot": "Allot", "allocations": "Allocations", "allocated": "Allocated", "deliveryDateLabel": "Delivery Date", "destinationSlotLabel": "Destination Slot", "destinationLabel": "Destination", "destinationTypeLabel": "Destination Type", "cancelAlertTitle": "Cancel Allocation?", "cancelAlertMessage": "This will cancel the allocation!", "downloadDeliveryMemo": "Download Delivery Memo", "deliveryMemoStatus": "Delivery Memo Status", "cancelAllocationLabel": "Cancel Allocation", "allocateTitle": "Allocate - ##name##", "allocateDraftLabel": "Save as Draft", "allocateCopyInventoryLabel": "<PERSON><PERSON> as In<PERSON>ory", "allocateSubmit": "Submit", "allocateEmptyMessage": "No items to allocate", "tripsTitle": "Trips", "tripsVehicleNumberLabel": "Vehicle Number", "tripsMobilePhoneLabel": "Mobile Phone", "tripsDriverNameLabel": "Driver Name", "tripsDispatchLabel": "Dispatch Trip", "tripsStatus": "Trip Status: ##status##", "tripsRoutIdLabel": "Route ID: ##routeId##", "tripsEmptyMessage": "There are no trips associated with this allotment!", "tripsEmptyTitle": "No Trip!"}, "allocationInventory": {"excessMessage": "Excess inventory allocation is allowed for this customer group", "noExcessMessage": "Allocation can be done upto the supply order quantity for this customer group", "excessSubmitTitle": "Allow Excess?", "excessSubmitMessage": {"": "Any excess inventory quantity would be allocated based on proportion of product in the supply orders"}, "noExcessSubmitMessage": {"": "Allocation would be done based on the available inventory in the mandi."}, "excessCopyTitle": "Allow Excess?", "excessCopyMessage": {"": "Allow all available mandi inventory to be distributed for allocation"}, "noExcessCopyMessage": {"": "Allow all available mandi inventory to be distributed for allocation"}, "excessCopySupplyMessage": {"": "Allow supply order quantity to be distributed for allocation"}, "noExcessCopySupplyMessage": {"": "Allow supply order quantity to be distributed for allocation"}}, "mandiQty": "Mandi\\nQty", "allocatedQty": "Allocated\\nQty", "allocation": {"supplyOrders": "SupplyOrders: ", "serialNumber": "S.No", "terminal": "Terminal", "allocated": "Allocated", "finalAllocation": "Final Allocation", "sno": "S.No", "terminals": "Terminals", "mandi": "Mandi", "customer": "Customers", "allocateCta": "Allocate", "noAllocationFoundTitle": "No Allocation Found!", "noAllocationrDescription": "Please create new allocation!", "totalOrders": "Total Orders", "groups": "Groups", "groupCount": "Group Count", "group": "Group", "addGroups": "Add Groups", "startAllocation": "Start Allocation"}, "autoAllocationDisabled": "Auto Allocation Disabled!", "autoAllocationDisabledMessage": "Auto allocation is disabled for this allocation. Please contact your admin to enable it.", "allocationError": "Allocation Error", "allocationManagement": {"ttile": "Allocation Management", "cancelOrderMessage": "Are you sure you want to cancel the selected orders?"}, "updateAllocation": "Update Allocation", "viewAllocation": "View Allocation", "allocationSummary": "Allocation Summary", "distribution": {"goBackTitle": "Are you sure?", "goBackMessage": "You have not submitted the distribution. Your data will be lost if you do not submit."}, "draft": "Draft", "groupAllocations": "Group Allocations", "groupingPopupSubmitTitle": "Are you sure?", "allocationLoading": "Allocation Loading...", "exit": "Exit?", "exitMessage": "Are you sure you want to exit?", "sku": "S<PERSON>", "ordered": "Ordered", "mandi": "Mandi", "copyFrom": "Copy From", "mandiInventory": {"updateSucess": "Inventory updated successfully", "title": "##mandiName## - Inventory"}, "supplyOrders": "Supply Orders", "copy": "Copy", "customerGroup": "Customer Group:", "delivery": "Delivery:", "lockedSkuPopup": {"title": "Locked <PERSON>", "noLockedSkus": "No locked skus found"}, "lockedBy": "Locked By", "wastage": "Wastage", "allocationQtyLessThanDistribution": "Allocation Qty can not be less than ##qty##. To change please update distribution first", "expected": "Expected", "Allocated": "Total", "mandiQtyError": "Qty can not be greater than mandi qty", "deliverySlot": "Delivery Slot", "deliveryDate": "Delivery Date", "driverName": "Driver Name", "driverPhone": "Driver Phone", "vehicleNumber": "Vehicle Number", "eta": "ETA", "liquidation": {"title": "Liquidation", "activeTabKey": "Active", "pastTabKey": "Past", "orderedDate": "Ordered Date", "skuAge": "Age(Days)", "orderBy": "By", "vendor": "<PERSON><PERSON><PERSON>", "receivedAmount": "Received Amount"}, "liqQty": "<PERSON><PERSON>", "pending": "Pending", "vendor": "<PERSON><PERSON><PERSON>", "updateLiquidation": {"uploadSlipsTitle": "Upload Slips", "vendorMobile": "Vendor Mobile*"}, "totalQty": "Total Qty", "amt": "Amt", "receivedAmount": "<PERSON><PERSON>", "liquidationPopup": {"searchSku": "Search Sku", "submitCta": "Submit", "agreedAmount": "Agreed Amount", "liquidationQty": "Liquidation Qty", "previousLiquidationQty": "Previous Liquidation Qty", "previousDumpedQty": "Previous Dumped <PERSON><PERSON>", "availableForLiquidateQty": "Available for Liquidation Qty", "liquidatingQtyNow": "Liquidating Qty Now", "dumpingQtyNot": "Dumping Qty Now", "previousReceivedAmount": "Already Received Amount", "amount": "Amount", "totalAmount": "Total Amount"}, "all": "All", "gradeA": "Grade A", "gradeD1": "Grade D1", "gradeC": "Grade C", "transferToC": "Transfer to C", "transferToDump": "Transfer to Dump", "transferTitle": "Are you sure", "transferBody": "Do you want to transfer the inventory?", "transferCtaTitle": "Transfer", "returns": {"copyAll": "Copy All"}, "wastageReturns": "Wastage Returns", "ungraded": "Ungraded", "myProcDetail": {"title": "My Procurements", "noData": "No Procurements Found!"}, "dateAndTime": "Date & Time", "numberOfItems": "# of Items", "myProcDetails": {"updateOrder": "Update Order", "receiveOrder": "Receive Order", "updatePrice": "Update Price", "view": "View"}, "myProc": {"vendorSummary": "<PERSON><PERSON><PERSON> Summary: ", "title": "My Procurement Summary"}, "detail": "Details", "myProcSummary": {"noData": "No Procurements Found!", "noDataFilter": "No procurements for this search!"}, "pendingQty": "Pending", "closedOpsAt": "Ops Closed at", "closingInventoryClosedAt": "Inventory Closed at", "endAt": "Ended at", "opsSummary": {"myOpenProc": "My Open Procurements", "otherOpenProc": "Other Open Procurements", "operationsClosed": "Operations Closed!"}, "procurementSummary": {"managerTitle": "Manager", "pendingTitle": "Pending Count", "detailCta": "Details", "title": "Procurement Summary", "myProcTitle": "My Procurements", "otherProcTitle": "Other Procurements", "noProcFoundTitle": "No Procurement Found!"}, "procItem": {"title": "Procurement Details"}, "receivedQty": "<PERSON><PERSON>", "createUser": {"firstName": "First Name*", "lastName": "Last Name", "email": "Email*", "phone": "Phone*", "create": "Create"}, "userCreationSuccess": "User created successfully!", "search": "Search", "clearAll": "Clear All", "mandiList": "Mandi List", "selectRoles": "Select Roles", "userMandiRoleUpdateSuccess": "User mandi role updated successfully!", "selectProc": {"title": "Select Procurement"}, "selectMandi": "Select Mandi", "selectDate": "Select Date", "apply": "Apply", "receiveQty": "<PERSON><PERSON>", "createReturnsScreen": {"submitPopupMessage": "These inventory will be added to the returns inventory!"}, "returnsPopup": {"receiveQty": "Receive Qty"}, "returnedQty": "Returned <PERSON><PERSON>", "availableQty": "Avl. <PERSON><PERSON>", "acceptedQty": "Accepted <PERSON>ty", "recQty": "<PERSON><PERSON>", "returnsAcceptScreen": {"ctaLabel": "Submit", "title": "Accept Returns", "noItemsFoundMessage": "No Return Items Found!", "submitPopupMessage": "Are you sure you want to submit?"}, "returnsAccept": {"validationVariationPopup": "Are you sure?"}, "returnsHistoryScreen": {"title": "Returns: History"}, "wastageReturnsListScreen": {"title": "Wastage Returns"}, "returnsListScreen": {"title": "Returns", "emptyMessage": "No Returns Found!"}, "editProc": {"editPrice": "Edit Price", "addImage": "Add Image${fileCountText}", "deleteImagePoolMessage": "Are you sure you want to delete it? This will also delete from any sku if you have selected it."}, "updatePrice": {"ctaTitle": "Update", "update": "Update", "submit": "Submit"}, "editPrice": {"orderedLots": "Ord Lots", "receivedLots": "Rec Lots"}, "procurementHistory": {"procuredBy": "ProcuredBy"}, "noOfItems": "# of Items", "editSupplyOrder": {"addSkuDialogEnableQuantityCheckboxTitle": "Enable quantity update?", "addSkuDialogEnableQuantityCheckboxSubTitle": "Updating the quantity will update the quantity for the entire row for this sku!", "addSkuDialogCtaGoBack": "Go Back", "addSkuDialogCtaStage0": "Next", "addSkuDialogCtaStage1": "Add to Supply Order", "title": "Edit Supply Order", "productLabel": "Product", "copyAllDialogTitle": "Are you sure?"}, "viewSupplyOrder": {"title": "View Supply Order", "deliveryDateLabel": "Delivery Date", "deliverySlotLabel": "Delivery Slot", "customerGroupsLabel": "Customers Group", "searchCta": "Search", "editCta": "Edit Supply Order", "terminal": "Terminal", "orderDate": "Order Date", "orderNumber": "Order Number", "user": "User", "deliveryDate": "Delivery Date", "deliverySlot": "Delivery Slot", "customerGroup": "Customer Group"}, "ViewSupplyOrderDetail": {"title": "View Supply Order"}, "lossPick": {"addCta": "Add Image", "deleteTitle": "Delete?", "deleteMessage": "Are you sure to delete the file?"}, "hi": "Welcome", "login": {"hi": "Please Login!"}, "mandiName": "Mandi Name", "tripId": "Trip Id", "slNo": "Sl No", "descriptionOfGoods": "Description of Goods", "lotsBulkQuantity": "Quantity", "lotsBulkQuantityPdf": "Qty", "customerId": "Customer ID", "customerName": "Customer Name", "customerPhone": "Customer Phone", "orderMemo": "Order Memo", "original": "Original", "invoiceNo": "Invoice No", "invoiceDate": "Invoice Date", "customerSignature": "Customer Signature", "billTo": "Bill <PERSON>", "amountInWords": "Amount In Words", "orderedPrice": "Ordered Price", "allocatedPrice": "Allocated Price", "wheelocityName": "Wheelocity Fresh Pvt Ltd", "wheelocityAddress": "A-19 & 20, 3rd Floor, IndiQube Ocean, Guindy Industrial Estate - Chennai -32", "Morning": "Morning", "Replenishment": "Replenishment", "KG": "KG", "PIECES": "PIECES", "BUNCH": "BUNCH", "logoutError": "Error while logging out!", "jsonParseError": "Error while parsing the json.", "appGreetMessage": "WELCOME TO FLOW", "appCompanyName": "WHEELOCITY", "selectLangHint": "Select a language", "loginButtonTitle": "Login with Wheelocity", "signInAborted": "SignIn Aborted", "firebaseUserNotFound": "Unable to get firebase user info.", "firebaseSignInRequired": "User must be sign-in via firebase.", "falseButton": "No", "trueButton": "Yes", "cancelButton": "Cancel", "confirmButton": "Confirm", "lots": "Lots", "weightKg": "Weight(Kg)", "weight": "Weight", "totalAmount": "Total Amount", "none": "None", "bulk": "Bulk", "homeLabel": "Home", "price": "Price", "remove": "Remove", "invalid": "Invalid", "type": "Type", "loss": "Loss", "back": "Back", "lang": {"alertTitle": "Update Language", "alertCtaLabel": "Update"}, "uploadFile": {"ctaLabel": "Add File", "emptyMessage": "Nothing's here", "ctaUploadLabel": "Upload Files"}, "cf": {"emptyMessage": "No Carry Forward!", "emptyDescription": "No carry forward was found for the SMO. Please acknowledge it by submitting it.", "addCarryForwardTitle": "Add <PERSON>y Forward", "submitBtn": "Submit", "confirmationAlertMessage": "Please Confirm before submitting carry Forward Inventory.\n\n You won't be able to edit it later.", "confirmationAlertTitle": "Confirmation", "inventoryClosedByMessage": "Inventory closed by ##name##"}, "addLoss": {"markLossMessage": "##maxLoss## ##unit## of loss to be marked!", "maxLossMessage": "Total loss can not be more than ##maxLoss## ##unit##", "ctaTitle": "Done", "totalLabel": "Total", "commentHint": "Comments", "lossesLabel": "Losses"}, "addFieldCharges": {"add": "Add", "totalLabel": "Total", "uploadSlipsTitle": "Upload Slips", "uploadSlipsButton": "Upload Slips", "commentHint": "Type comment here", "submitBtn": "Submit", "successMessage": "Slips uploaded successfully!"}, "recieveInventory": {"emptyMessage": "No Incoming Inventory Found", "errorMessage": "Failed to Fetch Incoming Inventory", "fromLabel": "From: ##mandiName##", "acceptLabel": "Accept", "title": "Receive Inventory", "recieved": "Received", "losses": "Losses", "commentsLabel": "Comments*", "commentBoxHint": "Please Enter Here", "consignmentLabel": "Consignment ##cId##", "submitBtnTitle": "Submit", "confirmationAlertTitle": "Confirmation", "confirmationAlertDesc": "Are you sure you want to submit ?\nYou can't change it later.\n Are you sure you want to submit ?\nPlease add a comment mandatorily.", "successTitle": "Success", "successMessage": "Inventory Received Successfully"}, "closeOps": {"ctaLabel": "Done", "successMessage": "Smo Closed Successfully"}, "lotting": {"title": "Lotting", "delottingTitle": "De-Lotting", "emptyTitle": "No Sku!", "emptyMessage": "There are no sku!", "selectSkuCtaLabel": "Done", "selectLabel": "Select", "selectSkuLabel": "Select Sku", "addLotLabel": "Add Lot", "toLabel": "To", "fromLabel": "From", "alertTitle": "Losses Detected!", "alertMessage": "We have detected ##loss## ##unit## losses. Please provide details to continue.", "submit": "Submit", "lotSizeAlreadyExists": "Lot size already exists"}, "ERROR_PDF_GENERATION": "Something went wrong in PDF generation", "ERROR_AUTH": "User doesn't have authorization", "ERROR_AUTH_USER_NOT_FOUND": "User not found", "ERROR_ACCESS_DENIED": "Access Denied", "AUTHENTICATION_FAILED": "Authentication failed", "ERROR_LANG_ISSUE": "Error while translating the message", "ERROR_CARRY_FORWARD_NOT_ACKNOWLEDGE": "Please close yesterday's stock", "ERROR_SKU_NOT_FOUND": "Item not found.", "ERROR_INVALID_ID": "Invalid ID", "ERROR_ALLOTMENT_DISPATCHED": "Allocations has already been dispatched", "ERROR_ALLOTMENT_NOT_IN_PROGRESS": "Allocation not in progress", "ERROR_ALLOTMENT_NOT_FOUND": "Allocation not found", "ERROR_WRONG_ALLOTMENT_STATUS": "The status of allocation is wrong", "ERROR_SMO_CLOSED": "Today's operation is closed", "ERROR_INVENTORY": "Something went wrong in inventory", "ERROR_NO_CARRY_FORWARD": "There is no closing stock", "ERROR_SMO_NOT_CLOSED_OPS": "Please close the operation", "ERROR_INCOMING_STOCK_EMPTY": "There is no incoming stock", "ERROR_CONSIGNMENT_EMPTY": "There is an error in creating consignment ID", "ERROR_DESTINATION_ID_EMPTY": "Please enter the destination place", "ERROR_INCOMING_STOCK_ACCEPTED": "Incoming stock already accepted", "ERROR_INSUFFICIENT_INVENTORY": "There is shortage in inventory goods", "ERROR_CREATING_MANDI": "There is an error in creating new Mandi", "ERROR_PROC_NOT_FOUND": "There is no procurement found", "ERROR_PROC_ITEM_NOT_FOUND": "The item not found", "SMO_NOT_FOUND": "There is no running operation found", "ERROR_FETCHING_OPERATIONAL_MANDI": "None of the mandi is operating", "ERROR_OPS_CANNOT_BE_CLOSED": "Operation cannot be closed because of pending Allocation", "ALL_PROC_NOT_COMPLETED": "Please complete the procuring items", "CLOSING_STOCK_NOT_UPDATED": "Please update the closing stock", "ERROR_SUPPLY_ORDER_NOT_FOUND": "Supply order not found", "ERROR_TRIP_NOT_FOUND": "There is no delivery trip", "ERROR_USER_NOT_FOUND": "The user is not found", "ERROR_PRESIGNED_URL_GENERATION_FAILED": "pre-signed url not found", "ERROR_FILE_UPLOAD_FAILED": "Uploading file failed", "ERROR_MULTIPART_CONVERSION_FAILED": "Multipart conversion failed", "ERROR_INVALID_SKU_TYPE": "The item type is invalid", "ERROR_CLEARING_TEMP_FILE": "There was an error in clearing files", "ERROR_OPS_CANNOT_BE_CLOSED_PROC": "There are still open Procurements Orders", "ERR_ObseleteVersion": "Please Update App", "ERR_HttpRequestBodyCommonPayloadNotReceived": "Please Update App", "damage": "Damages", "lottingLoss": "Lotting Loss", "MISSING": "Missing", "DAMAGED_AND_ROTTEN": "Damaged and Rotten", "BAD_QUALITY": "Bad Quality", "MOISTURE_LOSS": "Moisture loss", "UNTRACKED": "Untracked", "GRADE_B": "Grade B", "LOTTING": "Lotting", "LIQUIDATION": "Liquidation", "HONEYBEE_OR_PENTAGON": "HoneyBee and Pentagon", "routeId": "Route ID", "delivery_time": "Delivery Time", "customerAlternatePhone": "Alternate Phone", "supplyOrderId": "Supply Order ID", "deliveryNotes": "Delivery Notes", "returnCrates": "Return Crates", "procHistory": {"procuredBy": "Procured By", "numberOfItems": "# of Items"}, "OPEN": "Open", "CARRY_FORWARD_ACKNOWLEDGED": "Carry Forward Acknowledged", "CLOSED_OPS": "Operation Closed", "CLOSING_STOCK_UPDATED": "Closing Stock Updated", "CLOSED": "Closed"}