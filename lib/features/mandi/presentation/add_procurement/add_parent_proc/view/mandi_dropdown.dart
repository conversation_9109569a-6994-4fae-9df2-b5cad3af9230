import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';

class MandiDropdown extends StatefulWidget {
  final MandiInfo? selectedMandi;
  final Function(MandiInfo?) onChange;
  final MandiType filterOnMandi;
  final String noMandiMessage;
  final String hintText;
  final String selectedLabel;
  const MandiDropdown({
    required this.selectedMandi,
    required this.onChange,
    this.filterOnMandi = MandiType.corporate,
    this.noMandiMessage = 'No corporate mandis found',
    this.hintText = 'Select Corporate Office',
    this.selectedLabel = 'Selected Corporate Office',
  });
  @override
  State<MandiDropdown> createState() => _MandiDropdown();
}

class _MandiDropdown extends State<MandiDropdown> {
  @override
  Widget build(BuildContext context) {
    return dropdown();
  }

  Widget dropdown() {
    return BlocConsumer<MandiBloc, MandiState>(
      listener: (context, state) {},
      builder: (context, state) {
        return state.map(
          loading: (_) => SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(),
          ),
          success: (success) {
            if (success.error != null) {
              return Text(success.error!.message);
            }
            final allMandisToShow = <MandiInfo>[];
            final liveOps = success.liveOps;

            if (liveOps != null) {
              liveOps.forEach(
                (op) {
                  final mandi = success.allMandis
                      .where((e) => e.id == op.mandiId)
                      .firstOrNull;
                  if (mandi != null && mandi.type == widget.filterOnMandi) {
                    allMandisToShow.add(mandi);
                  }
                },
              );
            }

            if (allMandisToShow.isEmpty) {
              return Text(widget.noMandiMessage);
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.selectedMandi != null)
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 16,
                    ),
                    child: Text(
                      widget.selectedLabel,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                Expanded(
                  child: CustomDropdownV2<MandiInfo>.search(
                    items: allMandisToShow,
                    selectedItem: widget.selectedMandi,
                    onItemSelected: (value) {
                      widget.onChange(value);
                    },
                    hintText: widget.hintText,
                    selectedItemBuilder: (context, result) {
                      return Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    result.name,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 8,
                            ),
                            Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
