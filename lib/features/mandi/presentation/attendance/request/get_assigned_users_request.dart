import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/presentation/attendance/model/assigned_user.dart';

class GetAssignedUsersRequest extends BaseRequest<dynamic, List<AssignedUser>> {
  final int smoId;

  GetAssignedUsersRequest({required this.smoId});

  @override
  String getPath() => 'attendance/smo/$smoId';

  @override
  List<AssignedUser> mapper(data) {
    if (data is List<dynamic>) {
      final List<AssignedUser> users = [];
      for (var i = 0; i < data.length; i++) {
        try {
          final user = AssignedUser.fromMap(data[i]);
          users.add(user);
        } catch (e, s) {
          talker.handle(e, s);
        }
      }
      return users;
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
