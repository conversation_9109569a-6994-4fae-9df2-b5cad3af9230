import 'package:flutter/material.dart';
import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/proc_user.dart';

class SearchUserDropdown extends StatelessWidget {
  const SearchUserDropdown(
      {super.key,
      required this.selectedUser,
      required this.onChanged,
      required this.userList});

  final ProcUser? selectedUser;
  final ValueChanged<ProcUser?> onChanged;
  final List<ProcUser> userList;

  Future<List<ProcUser>> _futureRequest(String query) async {
    final lowercaseQuery = query.toLowerCase();
    final itemsToSend = userList.where((e) => e.match(lowercaseQuery)).toList();
    return Future.value(itemsToSend);
  }

  @override
  Widget build(BuildContext context) {
    return CustomDropdownV2<ProcUser>.searchRequest(
      borderSide: BorderSide(color: Colors.grey.shade200),
      hintText: "Select User",
      items: userList,
      futureRequest: _futureRequest,
      onItemSelected: (value) {
        onChanged.call(value);
      },
      selectedItem: selectedUser,
      listItemBuilder: (context, result) {
        final user = result;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${user.firstName} ${user.lastName}",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            Text(
              user.email ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        );
      },
    );
  }
}
