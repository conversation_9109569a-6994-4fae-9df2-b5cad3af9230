import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class UpdateLanguageUseCase {
  UpdateLanguageUseCase(this._mandiRepository, this._language);

  final MandiRepository _mandiRepository;
  final Language _language;

  Future<Either<ErrorResult<void>, bool>> call(String languageCode) async {
    return _mandiRepository.getLangFile(languageCode).mapRight((right) async {
      final result1 = await _language.updateCurrentLanguage(
        language: languageCode,
      );
      await _language.updateLangDb(language: languageCode, json: right);

      return result1;
    });
  }
}
