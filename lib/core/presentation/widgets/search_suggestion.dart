import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';

abstract class SearchKey {
  String searchKey();
  String searchTitle();
}

class SearchSkuWidget extends StatelessWidget implements PreferredSizeWidget {
  const SearchSkuWidget({
    super.key,
    this.initialValue,
    required this.skus,
    required this.onSelected,
    this.padding = const EdgeInsets.symmetric(
      horizontal: 16,
      vertical: 2,
    ),
    this.hintText,
  });
  final Sku? initialValue;
  final List<Sku> skus;
  final Function(Sku?) onSelected;
  final EdgeInsets padding;
  final String? hintText;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: SearchSuggestion<Sku>(
        initialValue: initialValue,
        suggestions: skus,
        onSelected: onSelected,
        shouldInclude: (item, key) => item.contains(key),
        hintText: hintText ?? 'searchSku.selectSku'.tr('Select Sku'),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(48);
}

class SearchSuggestion<T extends SearchKey> extends StatefulWidget
    implements PreferredSizeWidget {
  final List<T> suggestions;
  final Function(T?) onSelected;
  final bool Function(T item, String key) shouldInclude;
  final T? initialValue;
  final String? hintText;

  const SearchSuggestion({
    Key? key,
    this.initialValue,
    required this.suggestions,
    required this.onSelected,
    required this.shouldInclude,
    this.hintText,
  }) : super(key: key);

  @override
  _SearchSuggestionState<T> createState() => _SearchSuggestionState<T>();

  @override
  Size get preferredSize => Size.fromHeight(48);
}

class _SearchSuggestionState<T extends SearchKey>
    extends State<SearchSuggestion<T>> {
  Map<String, T> _filteredSuggestions = <String, T>{};
  final TextEditingController _searchController = TextEditingController();
  T? _selectedValue;

  @override
  void initState() {
    _selectedValue = widget.initialValue;
    if (_selectedValue != null) {
      _searchController.text = _selectedValue!.searchTitle();
    }
    _filteredSuggestions = Map.fromEntries(
        widget.suggestions.map((e) => MapEntry(e.searchKey(), e)));
    super.initState();
  }

  @override
  void didUpdateWidget(covariant SearchSuggestion<T> oldWidget) {
    _selectedValue = widget.initialValue;
    if (_selectedValue != null) {
      _searchController.text = _selectedValue!.searchTitle();
      _searchController.selection = TextSelection.fromPosition(
        TextPosition(offset: _searchController.text.length),
      );
    }
    _filteredSuggestions = Map.fromEntries(
        widget.suggestions.map((e) => MapEntry(e.searchKey(), e)));
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomDropdown.searchRequest(
      items: widget.suggestions.map((e) => e.searchKey()).toList(),
      futureRequest: (userInput) {
        return Future.value(
          userInput.isNotEmpty
              ? widget.suggestions
                  .where((element) => widget.shouldInclude(element, userInput))
                  .map((e) => e.searchKey())
                  .toList()
              : widget.suggestions.map((e) => e.searchKey()).toList(),
        );
      },
      fieldSuffixIcon: _selectedValue != null
          ? InkWell(
              onTap: () {
                setState(() {
                  _searchController.clear();
                });
                widget.onSelected(null);
              },
              child: Icon(
                Icons.close,
                color: Colors.black,
              ),
            )
          : null,
      controller: _searchController,
      hintText: widget.hintText,
      listItemBuilder: (context, key) {
        return Text(
          _filteredSuggestions[key]!.searchTitle(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontSize: 16,
          ),
        );
      },
      onChanged: (key) {
        if (widget.suggestions.contains(_filteredSuggestions[key])) {
          widget.onSelected(_filteredSuggestions[key]!);
          _searchController.text = _filteredSuggestions[key]!.searchTitle();
        }
      },
    );
  }
}
