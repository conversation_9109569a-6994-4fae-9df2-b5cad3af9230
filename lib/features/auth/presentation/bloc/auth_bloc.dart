import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/auth/domain/entity/user.dart';
import 'package:proc2/features/auth/domain/use_case/current_user_use_case.dart';
import 'package:proc2/features/auth/domain/use_case/log_out_use_case.dart';
import 'package:proc2/features/auth/domain/use_case/login_use_case.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/bloc/add_proc_db.dart';

part 'auth_event.dart';
part 'auth_state.dart';
part 'auth_bloc.freezed.dart';

@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc(
    this._loginUseCase,
    this._currentUserUseCase,
    this._logOutUseCase,
    this._addProcDb,
  ) : super(const Initial()) {
    on<AuthEvent>((event, emit) async {
      await event.when(
        started: () async {
          emit(const AuthState.loading());
          await _listenCurrentUser(emit);
        },
        performLogin: () async {
          emit(const AuthState.loading());
          final loginResult = await _loginUseCase();
          if (loginResult.isLeft) {
            emit(AuthState.unauthenticated(loginResult.left));
          } else {
            emit(AuthState.authenticated(loginResult.right));
          }
        },
        logout: () async {
          await _addProcDb.clearSkus();
          await _logOutUseCase().then((result) {
            result.fold(
              (error) => emit(AuthState.unauthenticated(error)),
              (_) => emit(const AuthState.unauthenticated(null)),
            );
          });
        },
      );
    });
  }

  final LoginUseCase _loginUseCase;
  final CurrentUserUseCase _currentUserUseCase;
  final LogOutUseCase _logOutUseCase;
  final AddProcDb _addProcDb;

  Future<void> _listenCurrentUser(Emitter<AuthState> emit) async {
    return emit.forEach(
      _currentUserUseCase().asBroadcastStream(),
      onData: (User? user) {
        if (user == null) {
          return const AuthState.unauthenticated(null);
        }
        return AuthState.authenticated(user);
      },
    );
  }
}
