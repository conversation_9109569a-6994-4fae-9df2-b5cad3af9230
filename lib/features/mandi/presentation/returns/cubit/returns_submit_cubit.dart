import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/presentation/widgets/weight_summary_card.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_return_detail_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/submit_returns_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

part 'returns_submit_state.dart';
part 'returns_submit_cubit.freezed.dart';

@injectable
class ReturnsSubmitCubit extends Cubit<ReturnsSubmitState> {
  ReturnsSubmitCubit() : super(ReturnsSubmitState.initial());

  void updateReturnsOrHistory(
    Object object,
    int smoId,
  ) async {
    if (object is Returns) {
      emit(state.copyWith(
        isHistory: false,
        isLoading: true,
      ));
      await _updateReturns(
        object,
        smoId,
      );
    } else if (object is ReturnsHistory) {
      emit(state.copyWith(isHistory: true));
      _updateReturnsHistory(object);
    }
  }

  Future<void> _updateReturns(
    Returns returns,
    int smoId,
  ) async {
    emit(
      state.copyWith(
        returnOrderId: returns.returnOrderId,
        isLoading: true,
        driverDetails: returns.driversDetails,
        returnType: returns.returnType,
        createdAt: returns.createdAt,
        subHeading: returns.terminals.join(', '),
      ),
    );
    await getReturnDetails(
      returns.returnOrderId,
      smoId,
    );
  }

  void _updateReturnsHistory(ReturnsHistory history) async {
    final items = _groupBySkuId(history.returnItems);
    final summaryItems = getSummaryItems(items.expand((e) => e).toList());
    emit(
      state.copyWith(
        returnOrderId: history.returnOrderId,
        isLoading: false,
        driverDetails: DriversDetails(
          driverMobile: history.driverPhone,
          driverName: history.driverName,
          vehicleNumber: history.driverVehicleNumber,
        ),
        returnType: history.returnType,
        createdAt: history.returnAt,
        items: items,
        summaryItems: summaryItems,
      ),
    );
    // await getReturnDetails(history.returnOrderId);
  }

  Future<void> getReturnDetails(String returnId, int smoId) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    final result =
        await GetReturnDetailRequest(returnOrderId: returnId, smoId: smoId)
            .execute();
    if (result.isRight) {
      final items = result.fold(
        (l) => <ReturnItem>[],
        (r) => r,
      );
      final updatedItems = _groupBySkuId(items);
      final summaryItems =
          getSummaryItems(updatedItems.expand((e) => e).toList());
      emit(
        state.copyWith(
          items: updatedItems,
          summaryItems: summaryItems,
          isLoading: false,
        ),
      );
    } else {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: result.left.message,
      ));
    }
  }

  List<List<ReturnItem>> _groupBySkuId(
    List<ReturnItem> items,
  ) {
    final grouped = <int, List<ReturnItem>>{};
    for (final item in items) {
      final skuId = item.skuQuantity.skuId;
      if (!grouped.containsKey(skuId)) {
        grouped[skuId] = [];
      }
      grouped[skuId]!.add(
        item.copyWith(
          acceptedQuantity: item.skuQuantity.quantity.toString(),
        ),
      );
    }
    // Sort by all deviated items first
    return grouped.values.toList()
      ..sort((a, b) {
        final aDeviated = a.any((item) => item.isDeviated);
        final bDeviated = b.any((item) => item.isDeviated);
        return (aDeviated == bDeviated) ? 0 : (aDeviated ? -1 : 1);
      });
  }

  void updateQty(int index, int i, String val, String? source) {
    final items = state.items.indexed
        .map((e) => e.$1 == index
            ? e.$2.indexed
                .map((e) => e.$1 == i
                    ? e.$2.copyWith(
                        acceptedQuantity: val,
                        weighingSource: source,
                      )
                    : e.$2)
                .toList()
            : e.$2)
        .toList();
    emit(state.copyWith(
      items: items,
      summaryItems: getSummaryItems(items.expand((e) => e).toList()),
    ));
  }

  List<SummaryItem> getSummaryItems(List<ReturnItem> items) {
    final isHistory = state.isHistory;
    final Map<String, double> quantity = {};
    final Map<String, int> count = {};
    for (final item in items) {
      final sku = item.skuQuantity;
      if (sku.isBulk) {
        final key = sku.unit.toUpperCase();
        quantity[key] = (quantity[key] ?? 0) +
            (isHistory
                ? item.receivedQuantity
                : item.acceptedQuantity.toDouble());
        count[key] = (count[key] ?? 0) + 1;
      }
    }

    return quantity.entries
        .map((e) => SummaryItem(
              unit: e.key.capitalize(),
              quantity: e.value,
              count: count[e.key] ?? 0,
            ))
        .toList();
  }

  void clearMessage() {
    emit(state.copyWith(message: null, shouldPop: false));
  }

  void submit(int smoId, {required bool isWastageReturns}) async {
    emit(state.copyWith(isCtaLoading: true));
    final result = await SubmitReturnsRequest(
      returnOrderId: state.returnOrderId,
      returnItems: state.items.expand((e) => e).toList(),
      smoId: smoId,
      returnType: state.returnType,
      driversDetails: state.driverDetails,
      isWastageReturns: isWastageReturns,
      createdAt: state.createdAt,
    ).execute();

    final newState = result.fold(
      (left) => state.copyWith(
        message: left.message,
        isCtaLoading: false,
      ),
      (right) => state.copyWith(
        message: right,
        shouldPop: true,
        isCtaLoading: false,
      ),
    );
    emit(newState);
  }

  void copyAll() async {
    final items = state.items
        .map((e) => e
            .map((e) => e.copyWith(
                  acceptedQuantity: e.skuQuantity.quantity?.asString() ?? '',
                  weighingSource: 'auto',
                ))
            .toList())
        .toList();

    emit(
      state.copyWith(
        items: items,
        isLoading: true,
        summaryItems: getSummaryItems(
          items.expand((e) => e).toList(),
        ),
      ),
    );
    await Future.delayed(Duration(milliseconds: 500));
    emit(state.copyWith(isLoading: false));
  }
}
