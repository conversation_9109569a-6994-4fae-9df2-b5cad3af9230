import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_history.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetSmoHistoryUseCase {
  GetSmoHistoryUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;
  Future<Either<ErrorResult<void>, List<SmoHistory>>> call() async {
    return _mandiRepository.getSmoHistory();
  }
}
