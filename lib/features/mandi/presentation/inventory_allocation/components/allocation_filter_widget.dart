import 'package:flutter/material.dart';
import 'package:proc2/core/presentation/widgets/w_text_form_field.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class AllocationFilterWidget extends StatelessWidget {
  const AllocationFilterWidget({
    super.key,
    required this.filterData,
    required this.onFilterDataChanged,
    this.padding,
    this.margin,
    required this.filters,
    required this.groupAllocationFilters,
  });
  final FilterData filterData;
  final Function(FilterData) onFilterDataChanged;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final List<AllocationFilter> filters;
  final List<GroupAllocationFilter> groupAllocationFilters;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade100,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                    child: WTextFormField(
                      initialValue: filterData.searchQuery,
                      decoration: InputDecoration(
                        hintText: 'Search sku',
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8)),
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 8,
                        ),
                        fillColor: Colors.white,
                      ),
                      style: TextStyles.heading,
                      maxLines: 1,
                      onChanged: (value) {
                        onFilterDataChanged(
                          filterData.copyWith(
                            searchQuery: value,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.grey.shade500,
                        )),
                    child: DropdownButton(
                      dropdownColor: Colors.white,
                      isExpanded: true,
                      isDense: true,
                      padding: EdgeInsets.all(0),
                      hint: Text(
                        'Filter By',
                        style: TextStyles.heading,
                      ),
                      underline: SizedBox(),
                      value: filterData.allocationFilter,
                      items: filters
                          .map(
                            (e) => DropdownMenuItem(
                              child: Text(
                                e.displayText,
                                style: TextStyles.heading,
                              ),
                              value: e,
                            ),
                          )
                          .toList(),
                      onChanged: (v) {
                        onFilterDataChanged(
                          filterData.copyWith(
                            allocationFilter: v,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                if (groupAllocationFilters.isNotEmpty) ...[
                  SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.white,
                          border: Border.all(
                            color: Colors.grey.shade500,
                          )),
                      child: DropdownButton(
                        dropdownColor: Colors.white,
                        isExpanded: true,
                        isDense: true,
                        padding: EdgeInsets.all(0),
                        menuMaxHeight: 300,
                        hint: Text(
                          'Category',
                          style: TextStyles.heading,
                        ),
                        underline: SizedBox(),
                        value: filterData.selectedGroupAllocationFilter,
                        items: groupAllocationFilters
                            .map(
                              (e) => DropdownMenuItem(
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 2),
                                  child: Text(
                                    e.displayText,
                                    style: TextStyles.body,
                                    maxLines: 3,
                                    softWrap: true,
                                  ),
                                ),
                                value: e,
                              ),
                            )
                            .toList(),
                        onChanged: (v) {
                          onFilterDataChanged(
                            filterData.copyWith(
                              selectedGroupAllocationFilter: v,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          SizedBox(
            width: 4,
          ),
          IconButton(
            onPressed: () {
              onFilterDataChanged(FilterData());
            },
            icon: Icon(
              Icons.clear,
            ),
            color: Colors.blue,
          )
        ],
      ),
    );
  }
}
