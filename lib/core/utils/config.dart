import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class Config {
  // static Color primaryColor = Colors.green;
  static Color primaryColor = const Color(0xFF4CB050);
  static String logoImagePath = 'assets/images/logo.jpg';
  static Color halfWhite = const Color(0xFFf2f2f2);

  static Color secondaryColor = const Color(0XFF01BAEF);

  // static LinearGradient gradientContainer = LinearGradient(
  //     colors: [primaryColor, primaryColor],
  //     begin: const FractionalOffset(0.0, 0.0),
  //     end: const FractionalOffset(1.0, 0.0),
  //     stops: const [0.0, 1.0],
  //     tileMode: TileMode.clamp);
  static LinearGradient gradientContainer = const LinearGradient(
    colors: [Color(0xFF006837), Color(0xFF4CB050)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  static final numberInputFilters = [
    // r'^(0|[1-9]\d*)?\.?\d{0,2}'
    FilteringTextInputFormatter.allow(RegExp(r'^(0|[1-9]\d*)?(\.\d{0,3})?')),
    // FilteringTextInputFormatter.allow(RegExp(r'^[0-9]*\.?[0-9]*$'))
  ];
  static final numberInputFiltersInt = [
    // r'^(0|[1-9]\d*)?\.?\d{0,2}'
    FilteringTextInputFormatter.allow(RegExp(r'^(0|[1-9]\d*)?')),
    // FilteringTextInputFormatter.allow(RegExp(r'^[0-9]*\.?[0-9]*$'))
  ];

  static List<FilteringTextInputFormatter> getNumberFilter(
      {required bool isBulk, required String unit}) {
    if (isBulk && unit.toLowerCase() == 'kg') {
      return numberInputFilters;
    } else {
      return numberInputFiltersInt;
    }
  }
}
