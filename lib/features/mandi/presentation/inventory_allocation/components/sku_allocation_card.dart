import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_configuration.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/total_allocation_popup.dart';

class SkuAllocationCard extends StatelessWidget {
  const SkuAllocationCard({
    super.key,
    required this.skuName,
    required this.skuImage,
    required this.unitString,
    required this.orderQty,
    required this.mandiQty,
    required this.qty,
    required this.showContextMenu,
    required this.onQtyChanged,
    required this.showOrderColumn,
    required this.isUpdateUi,
    this.padding,
    required this.status,
    required this.isCustomerOrder,
    required this.openManualDistribution,
    required this.onSelected,
    required this.isSelected,
    required this.isSelectionActive,
    required this.isManuallyEdited,
    required this.isReadOnly,
    required this.allowExpectation,
    required this.isBulkKg,
    required this.item,
    required this.config,
  })  : showMandiColumn = isUpdateUi,
        showStatusColumn = !isUpdateUi && isCustomerOrder;
  final String skuName;
  final String skuImage;
  final String unitString;
  final double? orderQty;
  final double? mandiQty;
  final AllocationQty qty;
  final bool showContextMenu;
  final EdgeInsets? padding;
  final Function(AllocationQty) onQtyChanged;
  final bool showOrderColumn;
  final bool showMandiColumn;
  final bool showStatusColumn;
  final bool isUpdateUi;
  final String status;
  final bool isCustomerOrder;
  final Function openManualDistribution;
  final Function() onSelected;
  final bool isSelected;
  final bool isSelectionActive;
  final bool isManuallyEdited;
  final bool isReadOnly;
  final bool allowExpectation;
  final bool isBulkKg;
  final AllocationSkuItem item;
  final AllocationConfiguration? config;

  @override
  Widget build(BuildContext context) {
    final isEditOnlyFromWeighingMachine =
        context.read<SmoBloc>().state.mapOrNull(
                  success: (s) =>
                      s.config.allocation.isEditOnlyFromWeighingMachine,
                ) ??
            false;
    final bool hasExpectedQty = qty.expectedQty.toDouble() > 0;
    return InkWell(
      onLongPress: isSelectionActive
          ? null
          : () {
              onSelected();
            },
      onTap: isSelectionActive ? () => onSelected() : null,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Colors.blue.shade100 : Colors.grey.shade100,
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Image.network(
                        skuImage,
                        width: 48,
                        height: 48,
                      ),
                      SizedBox(
                        height: 16,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          if (!isUpdateUi && hasExpectedQty)
                            Text(
                              '! ',
                              style: TextStyle(color: Colors.red)
                                  .merge(TextStyles.body),
                            ),
                          Expanded(
                            child: Text(
                              skuName,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: !isUpdateUi && hasExpectedQty
                                    ? Colors.red
                                    : null,
                              ).merge(TextStyles.body),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4),
                      Text(unitString,
                          style: TextStyle(
                                  fontSize: 12, color: Colors.grey.shade600)
                              .merge(TextStyles.body)),
                    ],
                  ),
                ),
                if (showOrderColumn) ...[
                  SizedBox(
                    width: 4,
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      orderQty?.asString() ?? '-',
                      style:
                          TextStyle(fontSize: 14, fontWeight: FontWeight.w600)
                              .merge(TextStyles.body),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                if (showMandiColumn) ...[
                  SizedBox(
                    width: 4,
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      mandiQty?.asString() ?? '-',
                      style:
                          TextStyle(fontSize: 14, fontWeight: FontWeight.w600)
                              .merge(TextStyles.body),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                SizedBox(
                  width: 4,
                ),
                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: isUpdateUi
                        ? CrossAxisAlignment.end
                        : CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 80,
                        child: isUpdateUi
                            ? WeighingQuantityButton(
                                isReadOnly: isReadOnly,
                                label: qty.totalQty,
                                popupBuilder: () {
                                  return WeightCapturePopupProcurementReceive(
                                    isBulkKg: isBulkKg,
                                    isManualEditAllowed:
                                        !isEditOnlyFromWeighingMachine,
                                    initialWeight: qty.totalQty
                                        .toDouble()
                                        .asString()
                                        .toDouble(),
                                    skuName: skuName,
                                    unitInfo: unitString,
                                  );
                                },
                                onChange: (quantity) {
                                  if (quantity == null) return;
                                  onQtyChanged(
                                    AllocationQty.fromTotalQty(
                                      qty: quantity.value ?? '',
                                      mandiQty: mandiQty ?? 0,
                                      allowExpectation: allowExpectation,
                                      weighingSource: quantity.source,
                                    ),
                                  );
                                },
                              )
                            : Text(
                                qty.totalQty.isEmpty ? '0' : qty.totalQty,
                                style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600)
                                    .merge(TextStyles.body),
                                textAlign: TextAlign.center,
                              ),
                      ),
                      Visibility(
                        visible: isUpdateUi ? qty.totalQty.isNotEmpty : true,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: InkWell(
                            onTap: !isUpdateUi
                                ? null
                                : () async {
                                    final updatedQty =
                                        await showDialog<AllocationQty>(
                                      context: context,
                                      builder: (_) => TotalAllocationPopup(
                                        qty: qty,
                                        mandiQty: mandiQty ?? 0,
                                        skuName: skuName,
                                        allowExpectation: allowExpectation,
                                      ),
                                    );

                                    if (updatedQty != null) {
                                      onQtyChanged(updatedQty);
                                    }
                                  },
                            child: Text(
                              qty.mandiAndExpectedText,
                              style: TextStyle(
                                color: isUpdateUi ? Colors.blue : null,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                decoration: TextDecoration.underline,
                              ).merge(TextStyles.body),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                if (showStatusColumn) ...[
                  SizedBox(
                    width: 4,
                  ),
                  Expanded(
                    child: !item.isValidId
                        ? SizedBox()
                        : InkWell(
                            onTap: () {
                              openManualDistribution();
                            },
                            child: Container(
                              padding: EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.grey.shade600,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  status == 'PENDING'
                                      ? Icon(
                                          Icons.warning,
                                          color: Colors.amber,
                                          size: 18,
                                        )
                                      : Icon(
                                          Icons.check_circle,
                                          color: Colors.green,
                                          size: 18,
                                        ),
                                  if (isManuallyEdited)
                                    Text(
                                      ' M',
                                      style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.blue,
                                          fontWeight: FontWeight.bold),
                                    )
                                ],
                              ),
                            ),
                          ),
                  ),
                ],
                SizedBox(
                  width: 8,
                ),
              ],
            ),
            if (qty.hasError)
              Text(
                qty.errorMessage!,
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }
}
