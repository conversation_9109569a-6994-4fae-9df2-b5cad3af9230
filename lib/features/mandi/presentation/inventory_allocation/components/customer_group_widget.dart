import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/customer_group_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class CustomerGroupWidget extends StatelessWidget {
  const CustomerGroupWidget({
    super.key,
    this.padding,
    this.margin,
    required this.data,
    required this.activeTerminalCount,
    required this.supplyOrderCount,
  });
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final CustomerGroupData data;
  final int activeTerminalCount;
  final int supplyOrderCount;

  @override
  Widget build(BuildContext context) {
    final dateTime = data.deliveryDateEpochSeconds.toDate('dd MMM');
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                'customerGroup'.tr('Customer Group:'),
                style: TextStyles.heading,
              ),
              SizedBox(
                width: 4,
              ),
              Text(
                data.customerGroup,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ).merge(TextStyles.heading),
              ),
            ],
          ),
          SizedBox(
            height: 4,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                'delivery'.tr('Delivery:'),
                style: TextStyles.heading,
              ),
              SizedBox(
                width: 4,
              ),
              Text(
                '$dateTime | ${data.deliverySlot}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ).merge(TextStyles.heading),
              ),
            ],
          ),
          SizedBox(
            height: 4,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                'allocation.supplyOrders'.tr('SupplyOrders: '),
                style: TextStyles.heading,
              ),
              SizedBox(
                width: 2,
              ),
              Text(
                supplyOrderCount.toString(),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ).merge(TextStyles.heading),
              ),
            ],
          ),
          SizedBox(
            height: 4,
          ),
          Text(
            data.canAllocateExcess
                ? getLangText('allocationInventory.excessMessage',
                    'Excess inventory allocation is allowed for this customer group')
                : getLangText('allocationInventory.noExcessMessage',
                    'Allocation can be done upto the supply order quantity for this customer group'),
            textAlign: TextAlign.left,
            style: TextStyle(color: Colors.blue),
          ),
        ],
      ),
    );
  }
}
