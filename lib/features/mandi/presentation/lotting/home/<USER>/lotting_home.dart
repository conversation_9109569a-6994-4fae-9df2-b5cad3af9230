// ignore_for_file: inference_failure_on_instance_creation

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/config.dart';

class LottingHome extends StatefulWidget {
  const LottingHome({super.key, required this.mandiId});
  final int mandiId;
  @override
  State<LottingHome> createState() => _LottingHome();
}

class _LottingHome extends State<LottingHome> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: WAppBar.getAppBar(
        title: Text(LanguageEnum.lottingTitle.localized()),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            const SizedBox(height: 30),
            lotButton(
              name: LanguageEnum.lottingTitle.localized(),
              isLotting: true,
            ),
            const SizedBox(height: 10),
            lotButton(
              name: LanguageEnum.deLottingTitle.localized(),
              isLotting: false,
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Widget lotButton({required String name, required bool isLotting}) {
    return InkWell(
      onTap: () {
        context.push(
          context.namedLocation(
            isLotting ? 'lottingSku' : 'dellotingSku',
            pathParameters: {
              'mandiId': widget.mandiId.toString(),
            },
          ),
          extra: context,
        );
      },
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
            border: Border.all(color: Config.primaryColor, width: 2),
          ),
          child: Text(
            name,
            style: TextStyle(
              color: Config.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
