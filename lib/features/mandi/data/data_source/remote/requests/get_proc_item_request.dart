import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

class GetProcItemRequest extends BaseRequest<dynamic, List<List<ProcItem>>> {
  final int procId;
  final int smoId;

  GetProcItemRequest(this.procId, this.smoId);

  @override
  String getPath() => 'procurements/$procId/items/smo/$smoId';

  @override
  List<List<ProcItem>> mapper(data) {
    if (data is List<dynamic>) {
      return _groupProcItemsBySkuId(data
          .map((e) => e as Map<String, dynamic>)
          .map((e) => ProcItem.fromJson(e))
          .toList());
    }
    throw Exception('Invalid data type');
  }

  List<List<ProcItem>> _groupProcItemsBySkuId(List<ProcItem> procItems) {
    final skuIdMap = <int, List<ProcItem>>{};

    for (final procItem in procItems) {
      final skuId = procItem.skuQuantity.skuId;
      if (!skuIdMap.containsKey(skuId)) {
        skuIdMap[skuId] = [];
      }
      skuIdMap[skuId]!.add(procItem);
    }

    return skuIdMap.values.toList();
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
