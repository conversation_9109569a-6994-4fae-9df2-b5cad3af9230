import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/row_item_value.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class AddGroupPopup extends StatefulWidget {
  const AddGroupPopup({
    super.key,
    required this.orders,
    required this.groupCount,
  });
  final int orders;
  final int? groupCount;

  @override
  State<AddGroupPopup> createState() => _AddGroupPopupState();
}

class GroupItem implements DropDownItem {
  final int value;

  GroupItem({required this.value});

  @override
  String get displayText => value.toString();
}

class _AddGroupPopupState extends State<AddGroupPopup> {
  GroupItem? _selectedGroup;
  final List<GroupItem> items = [];

  @override
  void initState() {
    items.clear();
    items.addAll(
        List.generate(widget.orders, (index) => GroupItem(value: index + 1)));
    if (widget.groupCount != null) {
      _selectedGroup = GroupItem(value: widget.groupCount!);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Popup(
      title: 'allocation.addGroup'.tr('Add Group'),
      children: [
        Expanded(
          child: ListView(
            shrinkWrap: true,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                ),
                child: RowItemValue(
                    label: 'allocation.totalOrders'.tr('Total Orders'),
                    value: widget.orders.toString()),
              ),
              SizedBox(
                height: 16,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'allocation.groups'.tr('Groups'),
                      style: TextStyles.heading,
                    ),
                    Spacer(),
                    SizedBox(
                      width: 100,
                      child: CustomDropdownV2<GroupItem>(
                        items: items,
                        onItemSelected: (item) {
                          setState(() {
                            _selectedGroup = item;
                          });
                        },
                        fillColor: Colors.grey.shade100,
                        hintText: 'allocation.groupCount'.tr('Group Count'),
                        selectedItem: _selectedGroup,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 8,
              ),
              Divider(
                color: Colors.grey.shade600,
              ),
              if (_selectedGroup != null)
                for (var i = 1; i <= _selectedGroup!.value; i++)
                  Builder(
                    builder: (context) {
                      int count = widget.orders ~/ _selectedGroup!.value;
                      if (i <= widget.orders % _selectedGroup!.value) {
                        count++;
                      }

                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 4,
                          horizontal: 16,
                        ),
                        child: Container(
                          padding: EdgeInsets.only(
                            bottom: 8,
                          ),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                          ),
                          child: RowItemValue(
                            label: 'allocation.group'.tr('Group') + ' $i:',
                            value: count.toString(),
                          ),
                        ),
                      );
                    },
                  ),
            ],
          ),
        ),
        WStickyBottomCta(
          icon: Icons.check,
          label: Text('allocation.addGroups'.tr('Add Groups')),
          onPressed: _selectedGroup == null
              ? null
              : () {
                  context.pop(_selectedGroup!.value);
                },
        )
      ],
    );
  }
}
