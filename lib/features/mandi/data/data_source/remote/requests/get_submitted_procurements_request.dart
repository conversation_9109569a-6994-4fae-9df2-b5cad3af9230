import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';

class GetSubmittedProcurementsRequest
    extends BaseRequest<dynamic, List<ProcDetail>> {
  final int mandiId;
  final int timestamp;
  final int vendorLocationId;

  GetSubmittedProcurementsRequest(
      {required this.mandiId,
      required this.timestamp,
      required this.vendorLocationId});

  @override
  String getPath() => 'procurements/adminProcurements';

  @override
  Map<String, dynamic> getQuery() {
    return {
      'mandiId': mandiId,
      'timeStamp': timestamp,
      'vendorLocationId': vendorLocationId,
    };
  }

  @override
  List<ProcDetail> mapper(data) {
    if (data is List<dynamic>) {
      return data
          .map((e) => e as Map<String, dynamic>)
          .map((e) => ProcDetail.fromJson(e))
          .toList();
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
