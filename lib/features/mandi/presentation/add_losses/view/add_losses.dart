// ignore_for_file: omit_local_variable_types

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/mandi/presentation/add_losses/bloc/add_losses_bloc.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/loss_types/bloc/loss_types_bloc.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class AddLosses extends StatelessWidget {
  const AddLosses({
    super.key,
    required this.moduleKey,
    required this.unit,
    required this.losses,
    this.maxLoss = double.infinity,
    this.remainingLossLabel,
    required this.title,
    this.shouldAddHardcodeLosses = false,
    this.showImagePicker = true,
  });

  final String moduleKey;
  final String unit;
  final List<LossInputModel>? losses;
  final double maxLoss;
  final String? remainingLossLabel;
  final String title;
  final bool shouldAddHardcodeLosses;
  final bool showImagePicker;

  @override
  Widget build(BuildContext context) {
    final bloc = di.get<AddLossesBloc>();

    return BlocProvider(
      create: (context) => bloc..add(AddLossesEvent.prefill(losses, maxLoss)),
      child: _AddLosses(
        unit: unit,
        remainingLossLabel: remainingLossLabel,
        title: title,
        shouldAddHardcodeLosses: shouldAddHardcodeLosses,
        moduleKey: moduleKey,
      ),
    );
  }
}

class _AddLosses extends StatefulWidget {
  const _AddLosses({
    required this.unit,
    this.remainingLossLabel,
    required this.title,
    required this.shouldAddHardcodeLosses,
    required this.moduleKey,
  });
  final String moduleKey;
  final String unit;
  final String? remainingLossLabel;
  final String title;
  final bool shouldAddHardcodeLosses;
  @override
  State<_AddLosses> createState() => __AddLosses();
}

class __AddLosses extends State<_AddLosses> {
  @override
  void initState() {
    super.initState();
    final lossState = context.read<LossTypesBloc>().state;
    lossState.maybeMap(
      orElse: () {},
      success: (s) {
        final list = s.losses[widget.moduleKey];
        if (list != null) {
          context.read<AddLossesBloc>().add(
                AddLossesEvent.started(
                  list,
                  widget.unit,
                  widget.shouldAddHardcodeLosses,
                ),
              );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = context
        .watch<LossTypesBloc>()
        .state
        .maybeWhen(orElse: () => false, loading: () => true);

    return BlocListener<LossTypesBloc, LossTypesState>(
      listenWhen: (previous, current) => previous != current,
      listener: (context, state) {
        state.maybeWhen(
          success: (lossTypes) {
            final list = lossTypes[widget.moduleKey];
            if (list != null) {
              context.read<AddLossesBloc>().add(
                    AddLossesEvent.started(
                      list,
                      widget.unit,
                      widget.shouldAddHardcodeLosses,
                    ),
                  );
            }
          },
          orElse: () {},
        );
      },
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.transparent,
          body: InkWell(
            onTap: () => context.pop(),
            child: Container(
              height: 700,
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                  left: 20, right: 20, bottom: 50, top: 50),
              child: InkWell(
                onTap: () {},
                child: Card(
                  elevation: 4,
                  child: Builder(
                    builder: (context) {
                      if (isLoading) {
                        return loading();
                      }
                      return content();
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget loading() {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [CircularProgressIndicator()],
      ),
    );
  }

  bool uploadNeeded = false;

  Widget content() {
    return BlocConsumer<AddLossesBloc, AddLossesState>(
      listener: (context, state) {
        setState(() {
          uploadNeeded = state.losses
              .where((element) =>
                  element.lossValue.isNotEmpty &&
                  element.files.length < element.minFileUploadCount)
              .isNotEmpty;
        });
      },
      builder: (context, state) {
        final totalLoss = state.getTotalLoss();
        final isTotalMoreThanMax = totalLoss > state.maxLoss;

        return SizedBox(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              appBar(),
              const SizedBox(height: 8),
              if (state.maxLoss != double.infinity)
                Center(
                  child: Text(
                    LanguageEnum.addLossMarkingLossMessage.localized(
                      params: {
                        'maxLoss': state.maxLoss,
                        'unit': widget.unit.localized()
                      },
                    ),
                    style: const TextStyle(
                      color: Colors.blue,
                    ),
                  ),
                ),
              const SizedBox(height: 8),
              Expanded(
                child: Scrollbar(
                  child: ListView(
                    children: [
                      const SizedBox(height: 8),
                      if (state.maxLoss != double.infinity &&
                          widget.remainingLossLabel != null)
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 16, right: 16, bottom: 16),
                          child: Row(
                            children: [
                              Text(
                                widget.remainingLossLabel!,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                '${(state.maxLoss - totalLoss).toStringAsFixed(2)} ${widget.unit}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 8),
                      for (int i = 0; i < state.losses.length; i++)
                        lossItem(i, state.losses[i]),
                      const Divider(),
                      total(totalLoss),
                      const Divider(),
                      const SizedBox(height: 12),
                    ],
                  ),
                ),
              ),
              if (isTotalMoreThanMax) ...[
                const SizedBox(height: 8),
                Center(
                  child: Text(
                    LanguageEnum.addLossMaxLossMessage.localized(
                      params: {
                        'maxLoss': state.maxLoss,
                        'unit': widget.unit.localized()
                      },
                    ),
                    style: const TextStyle(
                      color: Colors.red,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
              ],
              ElevatedButton(
                style: ButtonStyle(
                  fixedSize: MaterialStateProperty.all(
                    Size(MediaQuery.of(context).size.width, 40),
                  ),
                ),
                onPressed: isTotalMoreThanMax || uploadNeeded
                    ? null
                    : () {
                        context.pop(state.losses);
                      },
                child: Text(
                  LanguageEnum.addLossCtaTitle.localized(),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget total(double total) {
    return Container(
      height: 30,
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(left: 12, right: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            LanguageEnum.addLossTotalLabel.localized(),
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Text(
            '$total ${widget.unit.localized()}',
            style: const TextStyle(fontSize: 18),
          )
        ],
      ),
    );
  }

  Widget lossItem(int index, LossInputModel input) {
    final bloc = context.read<AddLossesBloc>();
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(left: 12, right: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            input.lossType.localized(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Row(
                children: [
                  SizedBox(
                    height: 40,
                    width: 80,
                    child: TextFormField(
                      onChanged: (val) {
                        bloc.add(
                          AddLossesEvent.lossChanged(
                            index,
                            val,
                            input.comment,
                            input.files,
                          ),
                        );
                      },
                      keyboardType: TextInputType.number,
                      maxLength: 10,
                      initialValue:
                          (input.lossValue == '0' || input.lossValue == '0.0')
                              ? null
                              : input.lossValue,
                      inputFormatters: Config.numberInputFilters,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: '0',
                        contentPadding: EdgeInsets.only(
                          left: 4,
                          right: 4,
                        ),
                        counterStyle: TextStyle(
                          height: double.minPositive,
                        ),
                        counterText: '',
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Text(
                    widget.unit.localized(),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  )
                ],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextFormField(
                    initialValue: input.comment,
                    onChanged: (txt) {
                      bloc.add(
                        AddLossesEvent.lossChanged(
                            index, input.lossValue, txt, input.files),
                      );
                    },
                    decoration: InputDecoration(
                      border: const OutlineInputBorder(),
                      hintText: LanguageEnum.addLossCommentHint.localized(),
                      contentPadding: const EdgeInsets.only(left: 4, right: 4),
                    ),
                  ),
                ),
              )
            ],
          ),
          if (input.lossValue.isNotEmpty &&
              input.files.length < input.minFileUploadCount)
            Text(
              'Please upload at least ${input.minFileUploadCount} file.',
              style: TextStyle(color: Colors.red),
            ),
          InlineImagePicker(
              files: input.files,
              allowMultiple: true,
              minFileAllowed: input.minFileUploadCount,
              maxFileAllowed: input.maxFileUploadCount,
              updateFile: (files) {
                bloc.add(
                  AddLossesEvent.lossChanged(
                    index,
                    input.lossValue,
                    input.comment,
                    files,
                  ),
                );
              }),
          Divider(),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget appBar() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
          color: Config.primaryColor,
          boxShadow: const <BoxShadow>[
            BoxShadow(
                color: Colors.black54, blurRadius: 15, offset: Offset(0, 0.75))
          ],
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(5), topRight: Radius.circular(5))),
      child: Row(
        children: [
          InkWell(
              onTap: () => Navigator.pop(context),
              child: const Icon(Icons.arrow_back, color: Colors.white)),
          const SizedBox(width: 16),
          Text(
            widget.title,
            style: const TextStyle(color: Colors.white, fontSize: 18),
          )
        ],
      ),
    );
  }
}
