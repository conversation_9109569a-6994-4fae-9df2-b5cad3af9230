import 'package:proc2/features/mandi/presentation/mandi_inventory/input_model/mandi_inventory_input_model.dart';

Map<String, dynamic> toUpdateMandiInventoryRequestBody(
    MandiInventoryInputModel inputModel,) {
  final skus = <Map<String, dynamic>>[];

  for (final skuInp in inputModel.skuList.toList()) {
    if (skuInp.quantity != skuInp.sku.quantity ||
        skuInp.lotSize != skuInp.sku.lotSize) {
      skus.add({
        'skuId': skuInp.sku.skuId,
        'type': skuInp.sku.type.toUpperCase(),
        'unit': skuInp.sku.unit.toUpperCase(),
        'quantity': skuInp.quantity,
        'lotSize': skuInp.lotSize
      });
    }
  }

  return {'mandiId': inputModel.mandiId, 'operation': 'UPDATE', 'skus': skus};
}
