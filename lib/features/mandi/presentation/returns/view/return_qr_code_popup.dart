import 'package:flutter/material.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/features/mandi/presentation/returns/request/get_return_qr_code_request.dart';
import 'dart:async';

class ReturnQrCodePopup extends StatefulWidget {
  const ReturnQrCodePopup({
    super.key,
    required this.smoId,
  });
  final int smoId;

  @override
  State<ReturnQrCodePopup> createState() => _ReturnQrCodePopupState();
}

class _ReturnQrCodePopupState extends State<ReturnQrCodePopup> {
  String? _data;
  String? _errorMessage;
  Timer? _refreshTimer;
  DateTime? _lastRefreshTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getQrCode();
      _startRefreshTimer();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startRefreshTimer() {
    // Cancel existing timer if any
    _refreshTimer?.cancel();

    // Start a new timer that refreshes every 5 minutes
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _getQrCode();
      } else {
        timer.cancel();
      }
    });
  }

  void _getQrCode() async {
    _errorMessage = null;
    _data = null;
    setState(() {});

    final request = await GetReturnQrCodeRequest(widget.smoId).execute();

    request.fold(
      (error) {
        if (mounted) {
          setState(() {
            _errorMessage = error.message;
          });
        }
      },
      (data) {
        if (mounted) {
          setState(() {
            _data =
                '${data}/${DateTime.now().add(const Duration(seconds: 40)).millisecondsSinceEpoch}';
            _lastRefreshTime = DateTime.now();
          });
        }
      },
    );
  }

  String _getTimeUntilNextRefresh() {
    if (_lastRefreshTime == null) return '';

    final nextRefresh = _lastRefreshTime!.add(const Duration(seconds: 30));
    final now = DateTime.now();
    final difference = nextRefresh.difference(now);

    if (difference.isNegative) return 'Refreshing...';

    final minutes = difference.inMinutes;
    final seconds = difference.inSeconds % 60;
    return 'Next auto refresh in ${minutes}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    print("_data: $_data");
    return Popup(
      height: 0.6,
      title: 'returns.qrCodePopupTitle'.tr('QR Code'),
      children: [
        if (_errorMessage != null)
          Expanded(
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(_errorMessage!),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _getQrCode,
                    icon: Icon(Icons.refresh),
                    label: Text('retry'.tr('Retry')),
                  )
                ],
              ),
            ),
          ),
        if (_data == null && _errorMessage == null)
          Expanded(
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                ],
              ),
            ),
          ),
        if (_data != null && _errorMessage == null)
          Expanded(
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 8,
                    ),
                    child: PrettyQrView.data(
                      data: _data!,
                      decoration: const PrettyQrDecoration(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Add refresh timer display
                  StreamBuilder(
                    stream: Stream.periodic(const Duration(seconds: 1)),
                    builder: (context, snapshot) {
                      return Text(
                        _getTimeUntilNextRefresh(),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
