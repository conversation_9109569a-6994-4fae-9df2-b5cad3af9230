part of 'lotting_conversion_bloc.dart';

@freezed
class LottingConversionEvent with _$LottingConversionEvent {
  const factory LottingConversionEvent.started({
    required bool isLotting,
    required int smoId,
  }) = _Started;
  const factory LottingConversionEvent.addFrom({
    required List<InventoryItemInputModel> items,
    required bool clearPrevious,
  }) = _AddFrom;
  const factory LottingConversionEvent.addTo({
    required List<InventoryItemInputModel> items,
    required bool clearPrevious,
  }) = _AddTo;

  const factory LottingConversionEvent.removeFrom(
    int index,
  ) = _RemoveFrom;
  const factory LottingConversionEvent.removeTo(
    int index,
  ) = _RemoveTo;
  const factory LottingConversionEvent.addLot() = _AddLot;
  const factory LottingConversionEvent.updateFromQuantity({
    required String value,
    required int index,
  }) = _UpdateFromQuantity;
  const factory LottingConversionEvent.updateToQuantityAndLotSize({
    required String quantity,
    required String lotSize,
    required int index,
  }) = _UpdateToQuantity;

  const factory LottingConversionEvent.updateLoss({
    required List<LossInputModel> losses,
  }) = _AddLosses;

  const factory LottingConversionEvent.submit() = _Submit;
  const factory LottingConversionEvent.clearMessage() = _ClearMessage;
}
