import 'package:freezed_annotation/freezed_annotation.dart';

part 'get_charge_types_dto.freezed.dart';
part 'get_charge_types_dto.g.dart';

@freezed
class GetChargeTypesDto with _$GetChargeTypesDto {
  const factory GetChargeTypesDto({
    required List<String> chargeType,
    required Map<String, dynamic> proc,
  }) = _GetChargeTypesDto;

  @override
  factory GetChargeTypesDto.fromJson(Map<String, dynamic> json) =>
      _$GetChargeTypesDtoFromJson(json);
}
