import 'package:flutter/material.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/delayed_button.dart';

class AllocationDispatchPopup extends StatelessWidget {
  const AllocationDispatchPopup({super.key, required this.onPressed});
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Popup(
      title: 'Are you sure',
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Do you want to dispatch the allocation?'),
              Text(
                'You can not edit any quantities after dispatching.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        Spacer(),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: DelayedButton(
            label: Text(
              'Dispatch',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            delayInSeconds: 5,
            onPressed: onPressed,
          ),
        ),
      ],
      height: 0.4,
    );
  }
}
