import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/use_case/get_sku_usecase.dart';

part 'sku_event.dart';
part 'sku_state.dart';
part 'sku_bloc.freezed.dart';

@injectable
class SkuBloc extends Bloc<SkuEvent, SkuState> {
  SkuBloc(this._getSkuUseCase) : super(const SkuState.initial()) {
    on<SkuEvent>((event, emit) async {
      await event.map(
        fetch: (f) async {
          await _getSkuUseCase().then(
            (value) => value.fold(
              (left) {
                showRetrySnackBar(
                    'Failed to fetch SKU List. Error : ${left.message}', () {
                  add(const SkuEvent.fetch());
                });
                emit(SkuState.error(left));
              },
              (right) => emit(
                SkuState.success(
                  skus: right,
                  searchTerm: '',
                  filteredSku: [],
                ),
              ),
            ),
          );
        },
        selectSku: (value) {
          if (state is Success) {
            emit((state as Success).copyWith(selectedSku: value.sku));
          }
        },
        updateSearch: (_UpdateSearch value) {
          if (state is Success) {
            final currentState = state as Success;
            final searchTerm = value.searchTerm.toLowerCase();
            final filteredSku = searchTerm == ''
                ? <Sku>[]
                : currentState.skus.where(
                    (element) {
                      final sku = element;
                      final skuName = sku.name.toLowerCase();
                      return skuName.contains(searchTerm);
                    },
                  ).toList();
            emit(
              currentState.copyWith(
                searchTerm: searchTerm,
                filteredSku: filteredSku,
              ),
            );
          }
        },
      );
    });
  }

  final GetSkuUseCase _getSkuUseCase;
}
