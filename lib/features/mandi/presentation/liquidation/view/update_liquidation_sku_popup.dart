import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_base.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/liquidation/cubit/update_liquidation_cubit.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku_types/bloc/sku_types_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class UpdateLiquidationSkuPopup extends StatefulWidget {
  const UpdateLiquidationSkuPopup({
    super.key,
    required this.showDelete,
    required this.isCreating,
    this.onDelete = null,
    required this.notAllowedSkus,
    this.inputModel,
    required this.smoId,
    required this.isEditOnlyFromWeighingMachine,
  }) : isReceiving = inputModel != null;
  final bool showDelete;
  final bool isCreating;
  final VoidCallback? onDelete;
  final List<String> notAllowedSkus;
  final LiquidationInputModel? inputModel;
  final bool isReceiving;
  final int smoId;
  final bool isEditOnlyFromWeighingMachine;

  @override
  State<UpdateLiquidationSkuPopup> createState() =>
      _UpdateLiquidationSkuPopupState();
}

class _UpdateLiquidationSkuPopupState extends State<UpdateLiquidationSkuPopup> {
  final TextEditingController _skuSearchController = TextEditingController();
  final TextEditingController _liquidationQtyController =
      TextEditingController();
  final TextEditingController _agreedAmountController = TextEditingController();
  ProcurementType procurementType = ProcurementType.bulk;
  String? procurementUnit = null;
  double? lotSize = null;
  Sku? sku;
  List<PickedFile> skuImages = [];
  bool isCtaActive = false;
  bool isDuplicate = false;

  String dumpQtyNow = '';
  String liquidQtyNow = '';
  String receivedAmountNow = '';
  bool closeLiquidationOrder = false;
  String? weighingSource;

  @override
  void initState() {
    final model = widget.inputModel;
    if (model != null) {
      procurementType = model.procurementType;
      procurementUnit = model.procurementUnit;
      lotSize = model.lotSize;
      sku = model.sku;
      _liquidationQtyController.text = model.quantity;
      _agreedAmountController.text = model.agreedPrice ?? '';
      skuImages = model.files;
      isCtaActive = true;
      dumpQtyNow = model.dumpQtyNow;
      liquidQtyNow = model.liquidationQtyNow;
      receivedAmountNow = model.receiveAmountNow;
      closeLiquidationOrder = model.closeLiquidationOrder;
    }
    _skuSearchController.addListener(_skuSearchListener);
    super.initState();
  }

  @override
  void setState(VoidCallback fn) {
    super.setState(fn);
    validate();
  }

  String _compositeKey() {
    if (procurementType == ProcurementType.bulk) {
      return '${sku?.id}-BULK-$procurementUnit';
    } else {
      return '${sku?.id}-LOTS-$procurementUnit-$lotSize';
    }
  }

  void validate() {
    bool _isCtaActive = false;
    bool _isDuplicate = false;
    if (sku == null ||
        procurementUnit == null ||
        (procurementType == ProcurementType.lots && lotSize == null)) {
      _isCtaActive = false;
    } else if (widget.notAllowedSkus.contains(_compositeKey())) {
      _isDuplicate = true;
      _isCtaActive = false;
    } else if (_liquidationQtyController.text.isEmpty) {
      _isCtaActive = false;
    } else {
      _isCtaActive = true;
    }

    if (_isCtaActive != isCtaActive || _isDuplicate != isDuplicate) {
      setState(() {
        isCtaActive = _isCtaActive;
        isDuplicate = _isDuplicate;
      });
    }
  }

  @override
  void dispose() {
    _skuSearchController.removeListener(_skuSearchListener);
    _skuSearchController.dispose();
    _liquidationQtyController.dispose();
    _agreedAmountController.dispose();
    super.dispose();
  }

  void _skuSearchListener() {
    try {
      final skuName = _skuSearchController.text;
      if (skuName == sku?.name) return;
      if (skuName.isNotEmpty) {
        final foundSku = context.read<SkuBloc>().state.mapOrNull(
              success: (s) => s.skus.firstWhere(
                (element) => element.name == skuName,
              ),
            );
        if (foundSku != null) {
          setState(() {
            sku = foundSku;
          });
        }
      }
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.6,
            color: Colors.white,
            margin: const EdgeInsets.only(
              left: 20,
              right: 20,
            ),
            child: BlocBuilder<SkuTypesBloc, SkuTypesState>(
              builder: (context, skuTypesState) {
                return skuTypesState.map(
                  initial: (_) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  loading: (_) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  failure: (failure) {
                    return ErrorScreen(
                      onPressed: () {
                        context.read<SkuTypesBloc>().add(SkuTypesEvent.fetch());
                      },
                      message: failure.message,
                    );
                  },
                  success: (success) {
                    return BlocBuilder<SkuBloc, SkuState>(
                      builder: (context, skuState) {
                        return skuState.map(
                          initial: (_) => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          success: (skuBlockState) {
                            final quantityTypes = success.quantityTypes;
                            final unitTypes = success.unitTypes;
                            final skus = skuBlockState.skus.toList();
                            return widget.isCreating
                                ? _createScreen(
                                    skus: skus,
                                    quantityTypes: quantityTypes,
                                    unitTypes: unitTypes,
                                  )
                                : _updateScreen();
                          },
                          error: (e) => ErrorScreen(
                            onPressed: () {
                              context
                                  .read<SkuBloc>()
                                  .add(const SkuEvent.fetch());
                            },
                            message: e.errorr.message,
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _createScreen({
    required List<Sku> skus,
    required List<String> quantityTypes,
    required List<String> unitTypes,
  }) {
    bool isBulk = procurementType == ProcurementType.bulk;
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Container(
          color: Colors.green,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Add Sku',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                if (widget.showDelete)
                  InkWell(
                    onTap: () {
                      context.pop();
                      widget.onDelete?.call();
                    },
                    child: Icon(
                      Icons.delete,
                      color: Colors.white,
                    ),
                  ),
                SizedBox(
                  width: 16,
                ),
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        Expanded(
          child: Scrollbar(
            child: ListView(
              children: [
                widget.isReceiving
                    ? Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                        ),
                        child: Text(
                          getSKU(context, skuID: sku!.id).name,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                    : CustomDropdown.search(
                        hintText: getLangText(
                            'liquidationPopup.searchSku', 'Search Sku'),
                        items: skus.map((e) => e.name).toList(),
                        controller: _skuSearchController,
                      ),
                const SizedBox(height: 12),
                if (sku != null) ...[
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 16.0,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: procurementTypeField(
                            procurementType,
                            true,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Expanded(
                          child: unitField(
                            unit: procurementUnit,
                            isBulk: procurementType == ProcurementType.bulk,
                            bulkUnits: sku?.bulkUnitTypes ?? [],
                            lotSizesUnits: sku?.lotSizes.keys.toList() ?? [],
                            allowEditing: true,
                          ),
                        ),
                        if (!isBulk && procurementUnit != null)
                          Expanded(
                            child: lotSizeField(
                              lotSize: lotSize,
                              lotSizes: sku?.lotSizes[
                                      procurementUnit?.toUpperCase() ?? ''] ??
                                  [],
                              allowEditing: true,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 12.0,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: liquidationQtyField(),
                        ),
                        Expanded(
                          child: agreedAmountField(),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 16.0,
                      right: 16,
                    ),
                    child: InlineImagePicker(
                      files: skuImages,
                      minFileAllowed: 0,
                      maxFileAllowed: 4,
                      allowMultiple: true,
                      uploadAlso: true,
                      module: UploadFileModule.liquidationOrder,
                      smoId: widget.smoId,
                      updateFile: (files) {
                        setState(() {
                          skuImages = files;
                        });
                      },
                    ),
                  ),
                ]
              ],
            ),
          ),
        ),
        if (isDuplicate)
          Align(
              alignment: Alignment.center,
              child: Text('Sku already added!',
                  style: TextStyle(color: Colors.red, fontSize: 14))),
        WStickyBottomCta(
          isEnabled: isCtaActive,
          icon: Icons.check,
          label: LangText('liquidationPopup.submitCta', 'Submit'),
          onPressed: () {
            context.pop(LiquidationInputModel(
              sku: sku!,
              procurementType: procurementType,
              procurementUnit: procurementUnit!,
              lotSize: lotSize,
              quantity: _liquidationQtyController.text,
              agreedPrice: _agreedAmountController.text.isEmpty
                  ? null
                  : _agreedAmountController.text,
              weighingSource: weighingSource,
              files: skuImages,
            ));
          },
        ),
      ],
    );
  }

  Widget agreedAmountField() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(getLangText('liquidationPopup.agreedAmount', 'Agreed Amount'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              )),
          const SizedBox(height: 8),
          TextFormField(
            controller: _agreedAmountController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
              counter: SizedBox(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.numberInputFilters,
            onChanged: (val) {
              validate();
            },
          )
        ],
      ),
    );
  }

  Widget liquidationQtyField() {
    final isBulkKg = procurementType == ProcurementType.bulk &&
        procurementUnit?.toLowerCase() == 'kg';
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              getLangText('liquidationPopup.liquidationQty', 'Liquidation Qty'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              )),
          const SizedBox(height: 8),
          SizedBox(
            width: 120,
            height: 48,
            child: WeighingQuantityButton(
              isReadOnly: false,
              label: _liquidationQtyController.text,
              popupBuilder: () {
                return WeighingCapturePopupBase(
                  title: 'captureWeight'.tr('Capture Weight'),
                  initalWeight: _liquidationQtyController.text.toDouble(),
                  allowManualEdit:
                      isBulkKg ? !widget.isEditOnlyFromWeighingMachine : true,
                  inputFormatters: isBulkKg
                      ? Config.numberInputFilters
                      : Config.numberInputFiltersInt,
                  enableWeighingMachine: isBulkKg,
                );
              },
              onChange: (quantity) {
                if (quantity == null) return;
                _liquidationQtyController.text = quantity.value ?? '';
                weighingSource = quantity.source;

                validate();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget unitField({
    required String? unit,
    required bool isBulk,
    required List<String> bulkUnits,
    required List<String> lotSizesUnits,
    required bool allowEditing,
  }) {
    // final skuEditBloc = context.read<SkuEditBloc>();
    // final state = skuEditBloc.state;
    final items = isBulk ? bulkUnits : lotSizesUnits;
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectUnitLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: unit,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((String item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (String? newValue) {
                          setState(
                            () {
                              this.procurementUnit = newValue;
                              this.lotSize = null;
                            },
                          );
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    unit?.capitalize().localized() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget lotSizeField({
    required double? lotSize,
    required dynamic lotSizes,
    required bool allowEditing,
  }) {
    // final selectedLotSize =
    //     state.sku?.lotSizes[state.procurementUnit?.toUpperCase() ?? ''] ??
    //         <String>[];
    final items = <double>[];
    if (lotSizes is List) {
      for (final element in lotSizes) {
        items.add(double.parse(element.toString()));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectLotSizeLabel.localized(),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: lotSize,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((double item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.toString()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (double? newValue) {
                          setState(() {
                            this.lotSize = newValue;
                          });
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    lotSize.toString() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget procurementTypeField(
    ProcurementType procurementType,
    bool allowEditing,
  ) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectProcTypeLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton<ProcurementType>(
                  // Initial Value
                  value: procurementType,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: ProcurementType.values.map((ProcurementType item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.value.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (ProcurementType? newValue) {
                          setState(() {
                            this.procurementType = newValue!;
                            this.procurementUnit = null;
                            this.lotSize = null;
                          });
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    procurementType.value.capitalize().localized(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget _updateScreen() {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Container(
          color: Colors.green,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    getSKU(context, skuID: sku!.id).name,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            bottom: 16.0,
          ),
          child: Row(
            children: [
              Expanded(
                child: procurementTypeField(
                  procurementType,
                  false,
                ),
              ),
              const SizedBox(height: 12),
              Expanded(
                child: unitField(
                  unit: procurementUnit,
                  isBulk: procurementType == ProcurementType.bulk,
                  bulkUnits: sku?.bulkUnitTypes ?? [],
                  lotSizesUnits: sku?.lotSizes.keys.toList() ?? [],
                  allowEditing: false,
                ),
              ),
              if (procurementType == ProcurementType.lots)
                Expanded(
                  child: lotSizeField(
                    lotSize: lotSize,
                    lotSizes:
                        sku?.lotSizes[procurementUnit?.toUpperCase() ?? ''] ??
                            [],
                    allowEditing: false,
                  ),
                ),
            ],
          ),
        ),
        Divider(),
        Expanded(
          child: Scrollbar(
            child: ListView(
              padding: EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              children: [
                _headingAndValue(
                  title: getLangText(
                    'liquidationPopup.liquidationQty',
                    'Liquidation Qty',
                  ),
                  value: widget.inputModel!.quantity,
                ),
                _headingAndValue(
                  title: getLangText(
                    'liquidationPopup.previousLiquidationQty',
                    'Previous Liquidation Qty',
                  ),
                  value:
                      widget.inputModel!.item!.receivedQuantity?.toString() ??
                          '-',
                ),
                _headingAndValue(
                  title: getLangText(
                    'liquidationPopup.previousDumpedQty',
                    'Previous Dumped Qty',
                  ),
                  value: widget.inputModel!.item!.dumpedQuantity?.toString() ??
                      '-',
                ),
                _headingAndValue(
                  title: getLangText(
                    'liquidationPopup.availableForLiquidateQty',
                    'Available for Liquidation Qty',
                  ),
                  value: widget.inputModel!.item!.availableForLiquidation
                      .toString(),
                ),
                Divider(),
                SizedBox(
                  height: 8,
                ),
                _headingAndInput(
                    title: getLangText(
                      'liquidationPopup.liquidatingQtyNow',
                      'Liquidating Qty Now',
                    ),
                    value: liquidQtyNow,
                    onChanged: (value) {
                      setState(() {
                        liquidQtyNow = value;
                      });
                    }),
                _headingAndInput(
                    title: getLangText(
                      'liquidationPopup.dumpingQtyNot',
                      'Dumping Qty Now',
                    ),
                    value: dumpQtyNow,
                    onChanged: (value) {
                      dumpQtyNow = value;
                    }),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Divider(
                    thickness: 2,
                  ),
                ),
                _headingAndValue(
                  title: getLangText(
                    'liquidationPopup.agreedAmount',
                    'Agreed Amount',
                  ),
                  value:
                      widget.inputModel!.item!.agreedAmount?.toString() ?? '-',
                ),
                SizedBox(
                  height: 16,
                ),
                _headingAndValue(
                  title: getLangText(
                    'liquidationPopup.previousReceivedAmount',
                    'Already Received Amount',
                  ),
                  value: widget.inputModel!.item!.receivedAmount?.toString() ??
                      '-',
                ),
                _headingAndInput(
                  title: getLangText(
                    'liquidationPopup.amount',
                    'Amount',
                  ),
                  value: receivedAmountNow,
                  onChanged: (value) {
                    setState(() {
                      receivedAmountNow = value;
                    });
                  },
                ),
                _headingAndValue(
                  title: getLangText(
                    'liquidationPopup.totalAmount',
                    'Total Amount',
                  ),
                  value: (receivedAmountNow.toDouble() +
                          (widget.inputModel!.item!.receivedAmount ?? 0))
                      .toString(),
                ),
                Divider(),
                Padding(
                  padding: const EdgeInsets.only(left: 0.0),
                  child: InlineImagePicker(
                    files: skuImages,
                    minFileAllowed: 0,
                    maxFileAllowed: 4,
                    uploadAlso: true,
                    module: UploadFileModule.liquidationOrder,
                    smoId: widget.smoId,
                    allowMultiple: true,
                    updateFile: (files) {
                      setState(() {
                        skuImages = files;
                      });
                    },
                  ),
                ),
                CheckboxListTile(
                  contentPadding: EdgeInsets.zero,
                  value: closeLiquidationOrder,
                  onChanged: (value) {
                    setState(() {
                      closeLiquidationOrder = value ?? false;
                    });
                  },
                  title: Text(
                    'Close Liquidation Order?',
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        WStickyBottomCta(
          icon: Icons.check,
          label: LangText('liquidationPopup.submitCta', 'Update'),
          onPressed: () {
            context.pop(widget.inputModel!.copyWith(
              liquidationQtyNow: liquidQtyNow,
              dumpQtyNow: dumpQtyNow,
              receiveAmountNow: receivedAmountNow,
              closeLiquidationOrder: closeLiquidationOrder,
              files: skuImages,
              weighingSource: weighingSource,
            ));
          },
        ),
      ],
    );
  }

  Widget _headingAndValue({
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 8,
        right: 12,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _headingAndInput({
    required String title,
    required String value,
    required ValueChanged<String>? onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 8,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(
            width: 80,
            child: TextFormField(
              initialValue: value,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
              onChanged: onChanged,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                contentPadding: EdgeInsets.symmetric(
                  vertical: 4,
                  horizontal: 8,
                ),
                isDense: true,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
