import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart' as picker;
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/ui/view_full_image.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';

class ImagePickerScreen extends StatefulWidget {
  final List<PickedFile>? initialImages;
  final List<PickedFile>? selectionFiles;
  final Map<String, dynamic> reqBody;
  final bool allowMultiple;
  final String pageTitle;
  final FileType fileType;
  final UploadFileModule module;
  final bool enableSelection;
  final int maxSelection;
  final String deleteMessage;

  ImagePickerScreen({
    this.initialImages,
    required this.reqBody,
    required this.allowMultiple,
    required this.pageTitle,
    this.fileType = FileType.image,
    required this.module,
    this.enableSelection = false,
    this.maxSelection = 4,
    this.selectionFiles,
    String? deleteMessage,
  }) : this.deleteMessage = deleteMessage ??
            getLangText(
              'upload.deleteImageMessage',
              'Are you sure you want to delete it?',
            );

  @override
  _ImagePickerScreenState createState() => _ImagePickerScreenState();
}

class PickerResult {
  final List<PickedFile> files;
  final String message;
  final List<PickedFile> selectionFiles;

  PickerResult(
      {required this.files,
      required this.message,
      required this.selectionFiles});
}

class _ImagePickerScreenState extends State<ImagePickerScreen> {
  List<PickedFile> _images = [];
  bool isLoading = false;
  bool hasDeleted = false;
  bool hasAdded = false;
  Set<int> selectedIndexes = {};

  void onBack(BuildContext context) {
    var message = '';
    if (hasDeleted || hasAdded) {
      message = getLangText('upload.updatedMessage', 'Updated the files');
    }

    if (_images.isEmpty &&
        widget.initialImages != null &&
        widget.initialImages!.isNotEmpty) {
      message =
          getLangText('upload.clearedMessage', 'Cleared the uploaded files');
    }

    List<PickedFile> selectedFiles = [];
    for (int i = 0; i < _images.length; i++) {
      if (selectedIndexes.contains(i)) {
        selectedFiles.add(_images[i]);
      }
    }

    context.pop(
      PickerResult(
        files: _images.where((element) => element.isUploaded).toList(),
        message: message,
        selectionFiles: selectedFiles,
      ),
    );
  }

  void setLoading(bool value) {
    setState(() {
      isLoading = value;
    });
  }

  Future<bool> shouldPopIfNotUploaded() async {
    var nonUploadedFiles =
        _images.where((e) => !e.isUploaded && e.file != null).toList();
    if (nonUploadedFiles.isEmpty) return true;

    return await context.showAlertDialog(
          title: getLangText('upload.exitPopupTitle', 'Go back?'),
          message: getLangText('upload.exitPopupMessage',
              'Images which are not uploaded will be removed. Are you sure?'),
        ) ??
        false;
  }

  Future<void> uploadFiles() async {
    try {
      setLoading(true);

      final uploader = di.get<FileUpload>();

      var nonUploadedFiles =
          _images.where((e) => !e.isUploaded && e.file != null).toList();

      if (nonUploadedFiles.isEmpty) {
        onBack(context);
        return;
      }

      final fileUrls = await uploader.uploadFile(
        widget.module,
        widget.reqBody,
        files: nonUploadedFiles.map((e) => e.file!).toList(),
      );

      if (fileUrls.length != nonUploadedFiles.length) {
        throw Exception(
            'upload.erorUploadingFiles'.tr('Error while uploading files!'));
      }

      for (var i = 0; i < nonUploadedFiles.length; i++) {
        nonUploadedFiles[i] = nonUploadedFiles[i]
            .copyWith(isUploaded: true, uploadKey: fileUrls[i]);
      }

      int index = 0;
      for (int i = 0; i < _images.length; i++) {
        if (index < nonUploadedFiles.length &&
            _images[i].uuid == nonUploadedFiles[index].uuid) {
          _images[i] = nonUploadedFiles[index];
          index++;
        }
      }
      setState(() {});
      onBack(context);

      setLoading(false);
    } catch (e) {
      showSnackBar(e.toString());
      setLoading(false);
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.initialImages != null) {
      _images = [...widget.initialImages!];
      if (widget.selectionFiles != null) {
        for (int i = 0; i < _images.length; i++) {
          if (widget.selectionFiles!.contains(_images[i])) {
            selectedIndexes.add(i);
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        final shouldPop = await shouldPopIfNotUploaded();
        if (shouldPop) {
          onBack(context);
        }
        return shouldPop;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.pageTitle),
          centerTitle: false,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () async {
              final shouldPop = await shouldPopIfNotUploaded();
              if (shouldPop) {
                onBack(context);
              }
            },
          ),
        ),
        body: Center(
          child: Container(
            color: Colors.grey.shade100,
            width: context.maxScreenWidth,
            child: Column(
              children: [
                Expanded(
                  child: GridView.builder(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: kIsWeb ? 6 : 3,
                    ),
                    padding: EdgeInsets.only(left: 16, top: 16),
                    itemCount: _images.length + 1,
                    itemBuilder: (context, index) {
                      if (index == _images.length) {
                        return InkWell(
                          onTap: _pickImage,
                          child: Padding(
                            padding:
                                const EdgeInsets.only(right: 16, bottom: 16),
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey[400]!,
                                      blurRadius: 10,
                                      offset: Offset(0, 10),
                                    ),
                                  ]),
                              child: Icon(
                                Icons.add_photo_alternate_rounded,
                                size: 32,
                                color: Colors.green,
                              ),
                            ),
                          ),
                        );
                      } else {
                        return Padding(
                          padding: const EdgeInsets.only(right: 16, bottom: 16),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Stack(
                                fit: StackFit.expand,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => ViewFullImage(
                                            image: _images[index].image,
                                          ),
                                        ),
                                      );
                                    },
                                    child: Image(
                                      image: _images[index].image,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  Positioned(
                                    right: 0,
                                    child: ClipOval(
                                      child: Container(
                                        color: widget.enableSelection
                                            ? Colors.black.withOpacity(0.5)
                                            : Colors.white.withOpacity(0.4),
                                        child: widget.enableSelection
                                            ? IconButton(
                                                onPressed: () {
                                                  if (selectedIndexes
                                                      .contains(index)) {
                                                    selectedIndexes
                                                        .remove(index);
                                                  } else {
                                                    if (selectedIndexes
                                                            .length >=
                                                        widget.maxSelection) {
                                                      showSnackBar(
                                                        getLangText(
                                                          'upload.maxSelectionMessage',
                                                          'You can select maximum of ##maxSelection## images',
                                                          params: {
                                                            'maxSelection':
                                                                widget
                                                                    .maxSelection
                                                                    .toString(),
                                                          },
                                                        ),
                                                      );

                                                      return;
                                                    }
                                                    selectedIndexes.add(index);
                                                  }
                                                  setState(() {});
                                                },
                                                icon: selectedIndexes
                                                        .contains(index)
                                                    ? Icon(
                                                        Icons.check_box,
                                                        color: Colors.white,
                                                      )
                                                    : Icon(
                                                        Icons
                                                            .check_box_outline_blank_outlined,
                                                        color: Colors.white,
                                                      ))
                                            : IconButton(
                                                icon: Icon(Icons.delete,
                                                    color: Colors.red),
                                                onPressed: () async {
                                                  final shouldDelete =
                                                      await context
                                                          .showAlertDialog(
                                                    title: getLangText(
                                                      'upload.deleteImageTitle',
                                                      'Delete?',
                                                    ),
                                                    message:
                                                        widget.deleteMessage,
                                                  );
                                                  if (shouldDelete == true) {
                                                    setState(() {
                                                      hasDeleted = true;
                                                      _images.removeAt(index);
                                                    });
                                                  }
                                                },
                                              ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }
                    },
                  ),
                ),
                WStickyBottomCta(
                  icon: Icons.upload,
                  label: LangText('upload.ctaTitle', 'Upload'),
                  onPressed: _images.isEmpty ? null : uploadFiles,
                  isEnabled: !isLoading,
                  isLoading: isLoading,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    if (isLoading) return;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('upload.chooseOption'.tr('Choose option')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Icon(Icons.photo_library),
              title: Text('upload.chooseOptionGallery'.tr('Gallery')),
              onTap: () async {
                context.pop();
                FilePickerResult? result = await FilePicker.platform.pickFiles(
                  type: widget.fileType,
                  allowMultiple: widget.allowMultiple,
                );

                if (result != null) {
                  final newImages =
                      result.files.map((e) => PickedFile.fromFile(e));
                  setState(() {
                    _images = [..._images, ...newImages];
                    hasAdded = true;
                    if (widget.enableSelection) {
                      selectedIndexes.add(_images.length - 1);
                    }
                  });
                }
              },
            ),
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Icon(Icons.add_a_photo),
              title: Text('upload.chooseOptionCamera'.tr('Camera')),
              onTap: () async {
                context.pop();

                final picture = await picker.ImagePicker().pickImage(
                  source: picker.ImageSource.camera,
                  imageQuality: 70,
                );
                if (picture != null) {
                  final data = await picture.readAsBytes();
                  setState(() {
                    _images.add(PickedFile.fromData(data));
                    hasAdded = true;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
