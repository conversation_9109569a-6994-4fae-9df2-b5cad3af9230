part of 'add_losses_bloc.dart';

@freezed
class AddLossesState with _$AddLossesState {
  const AddLossesState._();
  const factory AddLossesState.input({
    required List<LossInputModel> losses,
    required bool isPrefilledData,
    @Default(double.infinity) double maxLoss,
  }) = Input;

  double getTotalLoss() {
    return losses.fold(0, (previousValue, element) {
      return previousValue + (double.tryParse(element.lossValue) ?? 0.0);
    });
  }
}
