import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/ui/view_full_image.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';

class InlineImagePicker extends StatefulWidget {
  InlineImagePicker({
    super.key,
    required this.files,
    required this.allowMultiple,
    this.fileType = FileType.image,
    required this.updateFile,
    this.minFileAllowed = 0,
    this.maxFileAllowed = 4,
    this.uploadAlso = false,
    this.module = UploadFileModule.liquidationOrder,
    this.smoId = -1,
    this.isEnabled = true,
  });
  final Function(List<PickedFile> files) updateFile;
  final List<PickedFile> files;
  final bool allowMultiple;
  final FileType fileType;
  final int maxFileAllowed;
  final int minFileAllowed;
  final bool uploadAlso;
  final UploadFileModule module;
  final int smoId;
  final bool isEnabled;

  @override
  State<InlineImagePicker> createState() => _InlineImagePickerState();
}

class _InlineImagePickerState extends State<InlineImagePicker> {
  bool isUploading = false;
  @override
  Widget build(BuildContext context) {
    if (widget.maxFileAllowed == 0) return SizedBox();
    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: kIsWeb ? 4 : 2,
        childAspectRatio: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 8,
      ),
      padding: EdgeInsets.only(top: 8),
      itemCount: widget.files.length >= widget.maxFileAllowed
          ? widget.files.length
          : widget.files.length + (widget.isEnabled ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == widget.files.length) {
          return !widget.isEnabled
              ? SizedBox()
              : Padding(
                  padding: const EdgeInsets.only(
                    right: 16,
                    bottom: 8,
                  ),
                  child: ElevatedButton(
                    onPressed: widget.uploadAlso && isUploading
                        ? null
                        : () async {
                            if (!widget.isEnabled) return;
                            FilePickerResult? result =
                                await FilePicker.platform.pickFiles(
                              type: widget.fileType,
                              allowMultiple: widget.allowMultiple,
                            );

                            if (result != null) {
                              if (result.files.length >
                                  (widget.maxFileAllowed -
                                      widget.files.length)) {
                                showSnackBar(
                                    'Only ${widget.maxFileAllowed} files allowed for this loss');
                              }
                              final max =
                                  widget.maxFileAllowed - widget.files.length;
                              var newImages = result.files
                                  .sublist(
                                      0,
                                      result.files.length < max
                                          ? result.files.length
                                          : max)
                                  .map((e) => PickedFile.fromFile(e))
                                  .toList();
                              if (widget.uploadAlso) {
                                setState(() {
                                  isUploading = true;
                                });
                                final imageToUpload = result.files.sublist(
                                    0,
                                    result.files.length < max
                                        ? result.files.length
                                        : max);
                                final uploader = di.get<FileUpload>();
                                final uploadResult = await uploader.uploadFile(
                                    widget.module, {'smoId': widget.smoId},
                                    files: imageToUpload);
                                newImages = newImages.indexed
                                    .map((e) => e.$2.copyWith(
                                        uploadKey: uploadResult[e.$1],
                                        isUploaded: true))
                                    .toList();
                                setState(() {
                                  isUploading = false;
                                });
                              }
                              widget.updateFile(widget.files + newImages);
                            }
                          },
                    // icon: Icon(Icons.add),
                    child: isUploading
                        ? SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(),
                          )
                        : LangText(
                            'lossPick.addCta',
                            'Add Image',
                          ),
                  ),
                );
        } else {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all()),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: InkWell(
                      onTap: () async {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ViewFullImage(
                              image: widget.files[index].image,
                            ),
                          ),
                        );
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Text(
                            'File ${index + 1}',
                            style: TextStyle(color: Colors.black, fontSize: 14),
                          ),
                          InkWell(
                            onTap: () async {
                              final result = await context.showAlertDialog(
                                  title: getLangText(
                                      'lossPick.deleteTitle', 'Delete?'),
                                  message: getLangText('lossPick.deleteMessage',
                                      'Are you sure to delete the file?'));
                              if (result == true) {
                                widget.updateFile(
                                    widget.files.sublist(0, index) +
                                        widget.files.sublist(index + 1));
                                // Delete here
                              }
                            },
                            child: !widget.isEnabled
                                ? SizedBox()
                                : Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                    ),
                                    child: Icon(
                                      Icons.close,
                                      color: Colors.red,
                                      size: 20,
                                    ),
                                  ),
                          ),
                        ],
                      ),
                    )),
              ),
            ),
          );
        }
      },
    );
  }
}
