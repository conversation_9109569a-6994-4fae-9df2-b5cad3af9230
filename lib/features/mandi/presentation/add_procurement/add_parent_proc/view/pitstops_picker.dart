import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';

class PitStopPicker extends StatefulWidget {
  const PitStopPicker({super.key});

  @override
  State<PitStopPicker> createState() => _PitStopPickerState();
}

class _PitStopPickerState extends State<PitStopPicker> {
  final List<MandiInfo> _pitStops = [];
  final List<MandiInfo> _allMandis = [];
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _allMandis.clear();
      final allMandis = context
          .read<MandiBloc>()
          .state
          .maybeMap(
            orElse: () => <MandiInfo>[],
            success: (s) => s.allMandis,
          )
          .where((e) => e.type != MandiType.corporate)
          .toList();
      allMandis.sort((a, b) => a.key.compareTo(b.key));
      setState(() {
        _allMandis.addAll(allMandis);
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 200,
      child: Popup(
          title: 'addProcAdmin.selectPitStops'.tr('Select Pit Stops'),
          children: [
            SizedBox(
              height: 8,
            ),
            Expanded(
              child: ListView(
                shrinkWrap: true,
                children: [
                  ..._allMandis.map(
                    (e) => CheckboxListTile(
                      value: _pitStops.contains(e),
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            _pitStops.add(e);
                          } else {
                            _pitStops.remove(e);
                          }
                        });
                      },
                      title: Text(e.name),
                      subtitle: Text(e.key),
                    ),
                  ),
                ],
              ),
            ),
            WStickyBottomCta(
              icon: Icons.check,
              label: Text('Submit'),
              onPressed: _pitStops.isEmpty
                  ? null
                  : () {
                      context.pop(_pitStops);
                    },
            ),
          ]),
    );
  }
}
