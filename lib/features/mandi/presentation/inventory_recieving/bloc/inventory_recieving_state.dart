part of 'inventory_recieving_bloc.dart';

@freezed
class InventoryRecievingState with _$InventoryRecievingState {
  const factory InventoryRecievingState.initial() = _Initial;
  const factory InventoryRecievingState.data({
    required List<Consignment> consignmentList,
    @Default(null) InventoryRecievingInputModel? inputModel,
    @Default(false) bool isLoading,
    @Default(false) bool isInputModelValid,
    @Default(null) String? error,
    @Default(null) Consignment? selectedConsignment,
  }) = InventoryRecievingData;

  const factory InventoryRecievingState.error(ErrorResult<dynamic> error) =
      _Error;
  const factory InventoryRecievingState.recievedInventory() =
      _RecievedInventory;
}
