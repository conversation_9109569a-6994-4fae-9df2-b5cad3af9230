import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';

class GetLossInitV2Request
    extends BaseRequest<dynamic, Map<String, List<LossTypeV2>>> {
  @override
  String getPath() => 'losses/v2/getLossInit';

  @override
  Map<String, List<LossTypeV2>> mapper(data) {
    if (data is Map<String, dynamic>) {
      return data
          .map((key, value) => MapEntry(key, value as List<dynamic>))
          .map(
            (key, value) => MapEntry(
              key,
              value.map((e) => LossTypeV2.fromJson(e)).toList(),
            ),
          );
    }
    throw Exception('Error while parsing losses');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
