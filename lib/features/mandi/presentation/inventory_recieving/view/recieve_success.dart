import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';

class RecieveSuccess extends StatefulWidget {
  const RecieveSuccess({super.key});
  @override
  State<RecieveSuccess> createState() => _RecieveSuccessState();
}

class _RecieveSuccessState extends State<RecieveSuccess> {
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          leading: Container(),
          title: Text(LanguageEnum.recieveInventorySuccessTitle.localized()),
        ),
        body: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              const SizedBox(height: 80),
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 100,
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                LanguageEnum.recieveInventorySuccessMessage.localized(),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(
                height: 80,
              ),
              ElevatedButton(
                onPressed: () {
                  context.pop();
                },
                child: Text(LanguageEnum.back.localized()),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
