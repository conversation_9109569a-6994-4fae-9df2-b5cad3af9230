import 'package:flutter/material.dart';
import 'package:proc2/core/utils/config.dart';

class SupplyOrderCell extends StatefulWidget {
  SupplyOrderCell({
    required this.text,
    required this.showBottomBorder,
    required this.showRightBorder,
    required this.onChanged,
    required this.hasValueChanged,
    required this.isInputDisabled,
    required this.suffix,
    required this.lotSize,
  });
  final String text;
  final bool showBottomBorder;
  final bool showRightBorder;
  final ValueChanged<String> onChanged;
  final bool hasValueChanged;
  final bool isInputDisabled;
  final String suffix;
  final String lotSize;

  @override
  State<SupplyOrderCell> createState() => _SupplyOrderCellState();
}

class _SupplyOrderCellState extends State<SupplyOrderCell> {
  bool hasMouseOver = false;
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant SupplyOrderCell oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (e) async {
        if (!widget.isInputDisabled) {
          if (hasMouseOver != true) {
            setState(() {
              hasMouseOver = true;
            });
            if (!focusNode.hasFocus) {
              await Future.delayed(Duration(milliseconds: 60));
              focusNode.requestFocus();
            }
          }
        }
      },
      onExit: (e) {
        if (!widget.isInputDisabled) {
          setState(() {
            hasMouseOver = false;
          });
        }
      },
      child: Container(
        width: 100,
        height: 80,
        decoration: BoxDecoration(
          color: widget.isInputDisabled
              ? Colors.grey.shade100
              : widget.hasValueChanged
                  ? Colors.blue.shade50
                  : Colors.white,
          border: Border(
            top: BorderSide(),
            left: BorderSide(),
            bottom: widget.showBottomBorder ? BorderSide() : BorderSide.none,
            right: widget.showRightBorder ? BorderSide() : BorderSide.none,
          ),
          boxShadow: hasMouseOver
              ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ]
              : [],
        ),
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Text(
                hasMouseOver ? widget.lotSize : '',
                style: TextStyle(
                  color: Colors.black54,
                  fontSize: 12,
                ),
              ),
            ),
            Center(
              child: hasMouseOver
                  ? TextFormField(
                      // focusNode: focusNode,
                      initialValue: widget.text,
                      style: TextStyle(fontSize: 14),
                      decoration: InputDecoration(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 8,
                          ),
                          border: InputBorder.none,
                          suffix: Text(
                            widget.text.isEmpty ? '' : widget.suffix,
                          )),
                      textAlign: TextAlign.center,
                      onChanged: (value) {
                        widget.onChanged(value);
                      },
                      inputFormatters: Config.numberInputFilters,
                    )
                  : Text(
                      widget.text.isEmpty
                          ? ''
                          : widget.text + ' ${widget.suffix}',
                      style: TextStyle(fontSize: 14),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
