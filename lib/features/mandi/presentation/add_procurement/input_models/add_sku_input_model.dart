import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_sku_input_model.freezed.dart';

@freezed
class AddSkuInputModel with _$AddSkuInputModel {
  const factory AddSkuInputModel({
    required String name,
    required String skuId,
    required bool isLot,
    required String lotSize,
    required String lotQuantiy,
    required String unitOfMeasurement,
    required String totalAmount,
  }) = _AddSkuInputModel;
}
