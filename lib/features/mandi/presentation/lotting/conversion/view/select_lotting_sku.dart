import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/add_losses/view/add_losses.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/bloc/lotting_conversion_bloc.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/input_model/inventory_item_input_model.dart';
import 'package:proc2/features/mandi/presentation/lotting/select_sku/view/select_sku.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';
import 'package:rflutter_alert/rflutter_alert.dart';

class SelectLottingSku extends StatefulWidget {
  const SelectLottingSku({
    super.key,
    required this.isLotting,
    required this.mandiId,
  });

  final bool isLotting;
  final int mandiId;
  @override
  State<SelectLottingSku> createState() => _SelectLottingSku();
}

class _SelectLottingSku extends State<SelectLottingSku> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          title: Text(
            widget.isLotting
                ? LanguageEnum.lottingTitle.localized()
                : LanguageEnum.deLottingTitle.localized(),
          ),
        ),
        body: BlocBuilder<SkuBloc, SkuState>(
          builder: (context, state) {
            return BlocConsumer<LottingConversionBloc, LottingConversionState>(
              listener: (context, state) {
                state.maybeMap(
                  input: (state) {
                    if (state.shouldGoBack) {
                      context.pop();
                    }
                    if (state.message != null) {
                      showSnackBar(state.message!);
                      context
                          .read<LottingConversionBloc>()
                          .add(const LottingConversionEvent.clearMessage());
                    }
                  },
                  orElse: () {},
                );
              },
              builder: (context, state) {
                return state.map(
                  loading: (_) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  },
                  error: (e) {
                    return ErrorScreen(
                      onPressed: () {
                        context.read<LottingConversionBloc>().add(
                              LottingConversionEvent.started(
                                isLotting: e.isLotting,
                                smoId: e.smoId,
                              ),
                            );
                      },
                      message: e.error.message,
                    );
                  },
                  input: (state) {
                    return Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      padding: const EdgeInsets.all(8),
                      child: Scrollbar(
                        child: ListView(
                          children: [
                            const SizedBox(height: 8),
                            fromSKU(
                              isBulk: widget.isLotting,
                              canMultiSelect: !widget.isLotting,
                              from: state.from,
                            ),
                            const SizedBox(height: 10),
                            toSku(
                              isBulk: !widget.isLotting,
                              canMultiSelect: widget.isLotting,
                              to: state.to,
                              unit: state.unit,
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            ElevatedButton(
                              onPressed: (state.isSubmitActive ||
                                      state.isSubmitLoading)
                                  ? () {
                                      if (context
                                          .read<LottingConversionBloc>()
                                          .shouldShowLossDialog()) {
                                        showAlertDialog(
                                            state.calculatedLoss, state.unit);
                                      } else {
                                        context
                                            .read<LottingConversionBloc>()
                                            .add(
                                              const LottingConversionEvent
                                                  .submit(),
                                            );
                                      }
                                    }
                                  : null,
                              style: ButtonStyle(
                                fixedSize: MaterialStateProperty.all(
                                  Size(MediaQuery.of(context).size.width, 50),
                                ),
                              ),
                              child: state.isSubmitLoading
                                  ? const SizedBox(
                                      height: 18,
                                      width: 18,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    )
                                  : Text(
                                      LanguageEnum.lottingSubmit.localized()),
                            )
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  void showAlertDialog(
    double calculatedLoss,
    String unit,
  ) {
    Alert(
        context: context,
        type: AlertType.warning,
        title: LanguageEnum.lottingAlertTitle.localized(),
        desc: LanguageEnum.lottingAlertMessage.localized(
          params: {'loss': calculatedLoss, 'unit': unit},
        ),
        buttons: [
          DialogButton(
            onPressed: () => context.pop(),
            width: 120,
            color: Colors.red,
            child: Text(
              LanguageEnum.cancelButton.localized(),
              style: TextStyle(color: Colors.white, fontSize: 20),
            ),
          ),
          DialogButton(
              onPressed: () async {
                context.pop();
                await showLoss(calculatedLoss, unit);
              },
              width: 120,
              child: Text(
                LanguageEnum.confirmButton.localized(),
                style: TextStyle(color: Colors.white, fontSize: 20),
              )),
        ]).show();
  }

  Future<void> showLoss(
    double calculatedLoss,
    String unit,
  ) async {
    var result = await showDialog<List<LossInputModel>>(
      context: context,
      builder: (context) => AddLosses(
        moduleKey: 'CONVERSION',
        unit: unit,
        losses: null,
        maxLoss: calculatedLoss,
        title: LanguageEnum.addLossLossesLabel.localized(),
        remainingLossLabel: LanguageEnum.lottingLoss.localized(),
      ),
    );

    if (result == null) return;

    final allLosses = result.fold(
      0.0,
      (previousValue, element) => previousValue + element.lossValue.toDouble(),
    );

    if (allLosses < calculatedLoss) {
      // ignore: use_build_context_synchronously
      result = [
        ...result,
        LossInputModel(
          lossValue: (calculatedLoss - allLosses).toString(),
          lossType: 'LOTTING',
          unit: unit,
          comment: 'Lotting loss marked acutomatically!',
        ),
      ];
    }

    // ignore: use_build_context_synchronously
    context.read<LottingConversionBloc>().add(
          LottingConversionEvent.updateLoss(
            losses: result,
          ),
        );

    // ignore: use_build_context_synchronously
    context
        .read<LottingConversionBloc>()
        .add(const LottingConversionEvent.submit());
  }

  Widget fromSKU({
    required bool isBulk,
    required bool canMultiSelect,
    required List<InventoryItemInputModel> from,
  }) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 4,
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LanguageEnum.lottingFromLabel.localized(),
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              // ignore: inference_failure_on_function_invocation
              onPressed: () async {
                final items = await showDialog<List<InventoryItemInputModel>?>(
                  context: context,
                  builder: (context) => SelectSku(
                    isLotting: widget.isLotting,
                    mandiId: widget.mandiId,
                  ),
                );
                if (items != null && items.isNotEmpty) {
                  // ignore: use_build_context_synchronously
                  context.read<LottingConversionBloc>().add(
                        LottingConversionEvent.addFrom(
                          items: items,
                          clearPrevious: true,
                        ),
                      );
                }
              },

              child: Text(
                LanguageEnum.lottingSelectSkuLabel.localized(),
              ),
            ),
            const SizedBox(height: 10),
            const Divider(),
            const SizedBox(height: 10),
            if (from.isNotEmpty)
              Container(
                width: MediaQuery.of(context).size.width,
                padding: const EdgeInsets.only(left: 4, right: 4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    fromSkuLabels(),
                    const Divider(),
                    const SizedBox(height: 4),
                    for (var i = 0; i < from.length; i++)
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 8,
                          right: 8,
                          top: 4,
                          bottom: 4,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 4,
                              child: Text(
                                getSKU(context, skuID: from[i].skuId).name,
                              ),
                            ),
                            if (!isBulk)
                              Expanded(
                                flex: 4,
                                child: Text(
                                  '${from[i].lotSize} ${from[i].unit.localized()}',
                                ),
                              ),
                            Expanded(
                              flex: 4,
                              child: Text(
                                isBulk
                                    ? '${from[i].quantity} ${from[i].unit.localized()}'
                                    : '${from[i].quantity}',
                              ),
                            ),
                            Expanded(
                              flex: 4,
                              child: TextFormField(
                                initialValue: from[i].quantityInput,
                                decoration: const InputDecoration(
                                  border: OutlineInputBorder(),
                                  hintText: '0',
                                  isDense: false,
                                  contentPadding: EdgeInsets.only(
                                    left: 8,
                                    right: 4,
                                    top: 4,
                                    bottom: 4,
                                  ),
                                  counterStyle: TextStyle(
                                    height: double.minPositive,
                                  ),
                                  counterText: '',
                                ),
                                keyboardType: TextInputType.number,
                                maxLength: 10,
                                inputFormatters: Config.numberInputFilters,
                                onChanged: (val) {
                                  context.read<LottingConversionBloc>().add(
                                        LottingConversionEvent
                                            .updateFromQuantity(
                                          index: i,
                                          value: val,
                                        ),
                                      );
                                },
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Align(
                                alignment: AlignmentDirectional.centerEnd,
                                child: InkWell(
                                  onTap: () {
                                    context.read<LottingConversionBloc>().add(
                                          LottingConversionEvent.removeFrom(i),
                                        );
                                  },
                                  child: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget fromSkuLabels() {
    return Container(
      padding: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
      child: Row(
        children: [
          Expanded(flex: 4, child: Text(LanguageEnum.sku.localized())),
          if (!widget.isLotting)
            Expanded(flex: 4, child: Text(LanguageEnum.lotSize.localized())),
          Expanded(flex: 4, child: Text(LanguageEnum.availableQty.localized())),
          Expanded(
              flex: 4,
              child: Text(
                LanguageEnum.qty.localized(),
                textAlign: TextAlign.center,
              )),
          Expanded(
            flex: 2,
            child: Text(
              LanguageEnum.remove.localized(),
              style: TextStyle(
                fontSize: 10,
              ),
              textAlign: TextAlign.end,
            ),
          )
        ],
      ),
    );
  }

  Widget toSkuLabels(String unit) {
    return Container(
      padding: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
      child: Row(
        children: [
          Expanded(flex: 3, child: Text(LanguageEnum.sku.localized())),
          const SizedBox(width: 8),
          if (widget.isLotting) ...[
            Expanded(
              flex: 2,
              child: Text(
                '${LanguageEnum.lotSize.localized()} (${unit.localized()})',
                softWrap: true,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            flex: 2,
            child: Text(
              widget.isLotting
                  ? LanguageEnum.qty.localized()
                  : '${LanguageEnum.qty.localized()} (${unit.localized()})',
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 16),
          if (widget.isLotting)
            Expanded(
              flex: 2,
              child: Text(
                LanguageEnum.remove.localized(),
                textAlign: TextAlign.end,
                style: TextStyle(fontSize: 12),
              ),
            )
        ],
      ),
    );
  }

  Widget toSku({
    required bool isBulk,
    required bool canMultiSelect,
    required List<InventoryItemInputModel> to,
    required String unit,
  }) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 4,
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LanguageEnum.lottingToLabel.localized(),
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            if (widget.isLotting)
              ElevatedButton(
                onPressed: () async {
                  context
                      .read<LottingConversionBloc>()
                      .add(const LottingConversionEvent.addLot());
                },
                child: Text(LanguageEnum.lottingAddLotLabel.localized()),
              ),
            const SizedBox(height: 10),
            const Divider(),
            const SizedBox(height: 10),
            if (!to.isEmpty) ...[
              toSkuLabels(unit),
              const Divider(),
              const SizedBox(height: 4),
              for (var i = 0; i < to.length; i++)
                Padding(
                  padding: const EdgeInsets.only(
                    left: 8,
                    right: 8,
                    top: 4,
                    bottom: 4,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(getSKU(context, skuID: to[i].skuId).name),
                      ),
                      const SizedBox(width: 8),
                      if (!isBulk) ...[
                        Expanded(
                          flex: 3,
                          child: SizedBox(
                            child: DropdownButton(
                              value: to[i].lotSizeInput.isEmpty
                                  ? null
                                  : to[i].lotSizeInput.toDouble(),
                              items: to[i]
                                  .lots
                                  .map(
                                    (e) => DropdownMenuItem(
                                      value: e,
                                      child: Text(
                                        '$e ${to[i].unit}',
                                      ),
                                    ),
                                  )
                                  .toList(),
                              onChanged: (value) {
                                final hasSameLot = to.any((element) =>
                                    element.lotSizeInput.toDouble() == value);

                                if (hasSameLot) {
                                  showSnackBar(LanguageEnum
                                      .lottingLotSizeAlreadyExists
                                      .localized());
                                  return;
                                }

                                context.read<LottingConversionBloc>().add(
                                      LottingConversionEvent
                                          .updateToQuantityAndLotSize(
                                        index: i,
                                        lotSize: value.toString(),
                                        quantity: to[i].quantityInput,
                                      ),
                                    );
                              },
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 2,
                        child: SizedBox(
                          child: TextFormField(
                            initialValue: to[i].quantityInput,
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              hintText: '0',
                              isDense: false,
                              contentPadding: EdgeInsets.only(
                                left: 8,
                                right: 4,
                                top: 4,
                                bottom: 4,
                              ),
                              counterStyle: TextStyle(
                                height: double.minPositive,
                              ),
                              counterText: '',
                            ),
                            keyboardType: TextInputType.number,
                            maxLength: 10,
                            inputFormatters: Config.numberInputFilters,
                            onChanged: (val) {
                              context.read<LottingConversionBloc>().add(
                                    LottingConversionEvent
                                        .updateToQuantityAndLotSize(
                                      index: i,
                                      quantity: val,
                                      lotSize: to[i].lotSizeInput,
                                    ),
                                  );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      if (widget.isLotting)
                        Expanded(
                          flex: 2,
                          child: i == 0
                              ? Container()
                              : Align(
                                  alignment: AlignmentDirectional.centerEnd,
                                  child: InkWell(
                                    onTap: () {
                                      context.read<LottingConversionBloc>().add(
                                            LottingConversionEvent.removeTo(i),
                                          );
                                    },
                                    child: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                        )
                    ],
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }
}
