import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/auth/domain/entity/user.dart';
import 'package:proc2/features/auth/domain/repository/auth_repository.dart';

@injectable
class LoginUseCase {
  LoginUseCase(this._authRepository);
  final AuthRepository _authRepository;

  Future<Either<ErrorResult<void>, User>> call() async {
    try {
      final firebaseResult = await _authRepository.signInWithGoogle();

      return firebaseResult.thenAsync((user) async {
        final idToken = await user.getIdToken();
        return _authRepository.login(idToken: idToken ?? '');
      });
    } catch (e) {
      return Future.value(
        Left(
          ErrorResult(
            message: 'Please signin with Wheelocity account only!',
            code: 'ERROR_GOOGLE_SIGNIN',
            timestamp: DateTime.now().millisecondsSinceEpoch,
          ),
        ),
      );
    }
  }
}
