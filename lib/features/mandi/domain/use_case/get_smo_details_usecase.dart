import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetSmoDetailsUseCase {
  GetSmoDetailsUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, List<SmoDetail>>> call(
    int smoId,
  ) async {
    return _mandiRepository.getSmoDetails(smoId);
  }
}
