import 'package:proc2/core/data/network/base_request.dart';

class GetUserNameByPhoneRequest extends BaseRequest<dynamic, String> {
  final String phoneNumber;

  GetUserNameByPhoneRequest({required this.phoneNumber});

  @override
  String getPath() => 'users/getByMobile/$phoneNumber';

  @override
  String mapper(data) {
    if (data is Map<String, dynamic>) {
      final firstName = data['firstName']?.toString() ?? '';
      final lastName = data['lastName']?.toString() ?? '';
      return (firstName + ' ' + lastName).trim();
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
