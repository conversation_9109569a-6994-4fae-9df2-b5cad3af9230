import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/user_role.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/cubit/manage_users_cubit.dart';

class MultiSelectRolePopup extends StatefulWidget {
  final Map<UserRole, bool> mappedItem;
  final List<UserRole> items;
  final Widget question;
  final Function(List<UserRole>) onSubmit;

  const MultiSelectRolePopup({
    super.key,
    required this.mappedItem,
    required this.items,
    required this.question,
    required this.onSubmit,
  });

  @override
  State<MultiSelectRolePopup> createState() => _MultiSelectRolePopupState();
}

class _MultiSelectRolePopupState extends State<MultiSelectRolePopup> {
  List<UserRole> items = [];
  @override
  void initState() {
    items = List<UserRole>.from(widget.items);
    super.initState();
  }

  void onSearch(String? query) {
    if (query == null) {
      setState(() {
        items.clear();
        items.addAll(widget.items);
      });
      return;
    }

    setState(() {
      final lowercase = query.toLowerCase();
      items.clear();
      items = widget.items
          .where((e) => e.roleName.toLowerCase().contains(lowercase))
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ManageUsersCubit, ManageUsersState>(
      builder: (context, state) {
        return AlertDialog(
          title: widget.question,
          content: SizedBox(
            width: 350,
            child: Column(
              children: [
                TextFormField(
                  decoration: InputDecoration(
                    hintText: getLangText('search', 'Search'),
                    suffixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20)),
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 0,
                      horizontal: 16,
                    ),
                  ),
                  onChanged: onSearch,
                ),
                const SizedBox(
                  height: 8,
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: items.map((UserRole key) {
                        double tileHeight = 70 + key.roleName.length * 0.1;
                        return StatefulBuilder(
                          builder: (_, StateSetter setState) => SizedBox(
                            height: tileHeight,
                            width: 350,
                            child: CheckboxListTile(
                              title: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    key.roleName.toString(),
                                    style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              value: widget.mappedItem[key]!,
                              controlAffinity: ListTileControlAffinity.platform,
                              onChanged: (value) => setState(
                                  () => widget.mappedItem[key] = value!),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            OutlinedButton(
              onPressed: () {
                setState(() {
                  for (var element in widget.mappedItem.keys) {
                    widget.mappedItem[element] = false;
                  }
                });
              },
              child: Text(getLangText('clearAll', 'Clear All')),
            ),
            ElevatedButton(
              onPressed: () {
                final selectedItems = widget.mappedItem.entries
                    .where((entry) => entry.value)
                    .map((entry) => entry.key)
                    .toList();
                widget.onSubmit(selectedItems);
                Navigator.pop(context, selectedItems);
              },
              child: Text(getLangText('submit', 'Submit')),
            ),
          ],
        );
      },
    );
  }
}
