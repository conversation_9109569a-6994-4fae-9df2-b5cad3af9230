import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/transfer_mandi_inventory_request.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/inventory_type.dart';

part 'mandi_inventory_bloc.freezed.dart';
part 'mandi_inventory_event.dart';
part 'mandi_inventory_state.dart';

@injectable
class MandiInventoryBloc
    extends Bloc<MandiInventoryEvent, MandiInventoryState> {
  MandiInventoryBloc(
    this._mandiRepository,
  ) : super(MandiInventoryState.empty) {
    on<MandiInventoryEvent>((event, emit) async {
      await event.when(
        fetchAll: (mandiId) {
          add(MandiInventoryEvent.fetchMandiInventory(mandiId,
              type: InventoryType.primary));
          add(MandiInventoryEvent.fetchMandiInventory(mandiId,
              type: InventoryType.returns));
          add(MandiInventoryEvent.fetchMandiInventory(mandiId,
              type: InventoryType.gradeC));
        },
        fetchMandiInventory: (int mandiId, InventoryType type) async {
          final beforeState = type == InventoryType.returns
              ? state.copyWith(returns: state.returns.copyWith(isLoading: true))
              : type == InventoryType.primary
                  ? state.copyWith(mandi: state.mandi.copyWith(isLoading: true))
                  : state.copyWith(
                      wastageReturns:
                          state.wastageReturns.copyWith(isLoading: true));
          emit(beforeState);
          final result =
              await _mandiRepository.getInventory(mandiId, type: type.key);
          final afterState = result.fold(
            (left) {
              if (type == InventoryType.returns) {
                return state.copyWith(
                    returns: state.returns.copyWith(
                        isLoading: false, errorMessage: left.message));
              } else if (type == InventoryType.primary) {
                return state.copyWith(
                    mandi: state.mandi.copyWith(
                        isLoading: false, errorMessage: left.message));
              } else {
                return state.copyWith(
                    wastageReturns: state.wastageReturns.copyWith(
                        isLoading: false, errorMessage: left.message));
              }
            },
            (right) {
              if (type == InventoryType.returns) {
                return state.copyWith(
                  returns: state.returns.copyWith(
                    isLoading: false,
                    errorMessage: null,
                    items: _groupBySkuId(right.skus),
                  ),
                );
              } else if (type == InventoryType.primary) {
                return state.copyWith(
                  mandi: state.mandi.copyWith(
                    isLoading: false,
                    errorMessage: null,
                    items: _groupBySkuId(right.skus),
                  ),
                );
              } else {
                return state.copyWith(
                  wastageReturns: state.wastageReturns.copyWith(
                    isLoading: false,
                    errorMessage: null,
                    items: _groupBySkuId(right.skus),
                  ),
                );
              }
            },
          );
          emit(afterState);
        },
        transferGradeB: (
          int smoId,
          List<InventoryItem> skus,
          int mandiId,
        ) async {
          emit(state.copyWith(isGradeBTransferLoading: true));

          final result = await TransferMandiInventoryRequest.toGradeD1(
            smoId: smoId,
            skus: skus
                .map((e) => SkuQuantity(
                    skuId: e.skuId,
                    type: e.type,
                    unit: e.unit,
                    lotSize: e.lotSize,
                    quantity: e.quantity))
                .toList(),
          ).execute();
          final newState = result.fold(
              (left) => state.copyWith(
                    message: left.message,
                    isGradeBTransferLoading: false,
                  ),
              (right) => state.copyWith(
                  message: right,
                  isGradeBTransferLoading: false,
                  mandi: InventoryState(
                      isLoading: true, items: [], errorMessage: null)));
          if (result.isRight) {
            add(MandiInventoryEvent.fetchMandiInventory(mandiId,
                type: InventoryType.primary));
          }
          emit(newState);
        },
        transferGradeA: (
          int smoId,
          List<InventoryItem> skus,
          int mandiId,
        ) async {
          emit(state.copyWith(isGradeATransferLoading: true));

          final result = await TransferMandiInventoryRequest.toGradeD1(
            smoId: smoId,
            skus: skus
                .map((e) => SkuQuantity(
                    skuId: e.skuId,
                    type: e.type,
                    unit: e.unit,
                    lotSize: e.lotSize,
                    quantity: e.quantity))
                .toList(),
          ).execute();
          final newState = result.fold(
              (left) => state.copyWith(
                    message: left.message,
                    isGradeATransferLoading: false,
                  ),
              (right) => state.copyWith(
                  message: right,
                  isGradeATransferLoading: false,
                  mandi: InventoryState(
                      isLoading: true, items: [], errorMessage: null)));
          if (result.isRight) {
            add(MandiInventoryEvent.fetchMandiInventory(mandiId,
                type: InventoryType.primary));
          }
          emit(newState);
        },
        transferGradeD1: (
          int smoId,
          List<InventoryItem> skus,
          int mandiId,
        ) async {
          emit(state.copyWith(isGradeD1TransferLoading: true));

          final result = await TransferMandiInventoryRequest.toGradeC(
            smoId: smoId,
            skus: skus
                .map((e) => SkuQuantity(
                    skuId: e.skuId,
                    type: e.type,
                    unit: e.unit,
                    lotSize: e.lotSize,
                    quantity: e.quantity))
                .toList(),
          ).execute();
          final newState = result.fold(
              (left) => state.copyWith(
                    message: left.message,
                    isGradeD1TransferLoading: false,
                  ),
              (right) => state.copyWith(
                  message: right,
                  isGradeD1TransferLoading: false,
                  mandi: InventoryState(
                      isLoading: true, items: [], errorMessage: null)));
          if (result.isRight) {
            add(MandiInventoryEvent.fetchMandiInventory(mandiId,
                type: InventoryType.primary));
          }
          emit(newState);
        },
        transferGradeC: (
          int smoId,
          List<InventoryItem> skus,
          int mandiId,
        ) async {
          emit(state.copyWith(isGradeCTransferLoading: true));

          final result = await TransferMandiInventoryRequest.toDump(
            smoId: smoId,
            skus: skus
                .map((e) => SkuQuantity(
                    skuId: e.skuId,
                    type: e.type,
                    unit: e.unit,
                    lotSize: e.lotSize,
                    quantity: e.quantity))
                .toList(),
          ).execute();
          final newState = result.fold(
              (left) => state.copyWith(
                    message: left.message,
                    isGradeCTransferLoading: false,
                  ),
              (right) => state.copyWith(
                  message: right,
                  isGradeCTransferLoading: false,
                  mandi: InventoryState(
                      isLoading: true, items: [], errorMessage: null)));
          if (result.isRight) {
            add(MandiInventoryEvent.fetchMandiInventory(mandiId,
                type: InventoryType.primary));
          }
          emit(newState);
        },
        clearMessage: () {
          emit(state.copyWith(message: null));
        },
      );
    });
  }

  List<List<InventoryItem>> _groupBySkuId(List<InventoryItem> allItems) {
    final grouped = <int, List<InventoryItem>>{};
    final items = allItems.where((e) => e.quantity > 0);
    for (final item in items) {
      final skuId = item.skuId;
      if (!grouped.containsKey(skuId)) {
        grouped[skuId] = [];
      }
      grouped[skuId]!.add(item);
    }
    return grouped.values.toList();
  }

  final MandiRepository _mandiRepository;
}
