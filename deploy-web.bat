@echo off

:: Check if the environment parameter is provided
if "%~1"=="" (
    echo Please provide the environment parameter: prod or dev
    exit /b 1
)

set NO_CLEAN=0

:: Check for --no-clean flag
for %%i in (%*) do (
    if "%%i"=="--no-clean" (
        set NO_CLEAN=1
    )
)

:: Check if the environment parameter is valid
if NOT "%~1"=="prod" if NOT "%~1"=="dev" (
    echo Invalid environment parameter. Allowed values: prod or dev
    exit /b 1
)

:: Set the correct entry point file based on the environment
if "%~1"=="prod" (
    set entry_point=lib\main_production.dart
    set file_to_copy=firebase-prod.json
    set project_id=signintest-84632
    set site=flow-woc
) else (
    set entry_point=lib\main_development.dart
    set file_to_copy=firebase-dev.json
    set project_id=wowfirebaseapps-test
    set site=flow-woc-test
)

:: Clean and get dependencies if NO_CLEAN is not set
if "%NO_CLEAN%"=="0" (
    fvm flutter clean
    fvm flutter pub get
    fvm dart run build_runner build --delete-conflicting-outputs
)

:: Invoke the flutter build apk command
fvm flutter build web -t %entry_point% --no-tree-shake-icons

:: Copy file_to_copy as firebase.json
del /f /q firebase.json >nul 2>&1
copy /y %file_to_copy% firebase.json >nul 2>&1

:: Use Firebase project
firebase use %project_id%

:: Deploy to Firebase hosting
firebase deploy --only hosting:%site%

:: Clean up
if exist firebase.json del /f /q firebase.json >nul 2>&1
