import 'package:freezed_annotation/freezed_annotation.dart';

part 'proc_item.freezed.dart';
part 'proc_item.g.dart';

@freezed
class ProcItem with _$ProcItem {
  const factory ProcItem({
    required int id,
    required int procurementId,
    required SkuQuantity skuQuantity,
    required double? weight,
    required double? amount,
    required double? orderedQuantity,
  }) = _ProcItem;

  factory ProcItem.fromJson(Map<String, dynamic> json) =>
      _$ProcItemFromJson(json);
}

@freezed
class SkuQuantity with _$SkuQuantity {
  const SkuQuantity._();
  const factory SkuQuantity({
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double? quantity,
  }) = _SkuQuantity;

  factory SkuQuantity.fromJson(Map<String, dynamic> json) =>
      _$SkuQuantityFromJson(json);

  bool get isBulk => type.toLowerCase() == 'bulk';
  String get compositeKey =>
      isBulk ? '$skuId-$type-$unit' : '$skuId-$type-$unit-$lotSize';

  String getUnitString() {
    if (isBulk) {
      return 'Bulk - $unit';
    }
    return 'Lots - $lotSize $unit';
  }

  String get displayLotSize => isBulk ? '-' : lotSize.toString();
  String get displayNumberOfLots => isBulk ? '-' : quantity.toString();
  String get displayQuantity => isBulk
      ? quantity.toString()
      : ((lotSize ?? 0) * (quantity ?? 0)).toString();

  String get unitInfo => isBulk ? 'BULK - $unit' : 'LOTS - $lotSize $unit';
  bool get isBulkKg => isBulk && unit.toLowerCase() == 'kg';
}
