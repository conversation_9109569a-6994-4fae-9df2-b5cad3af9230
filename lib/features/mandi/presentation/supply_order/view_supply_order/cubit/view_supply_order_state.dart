part of 'view_supply_order_cubit.dart';

@freezed
class ViewSupplyOrderState with _$ViewSupplyOrderState {
  const ViewSupplyOrderState._();
  const factory ViewSupplyOrderState({
    @Default(null) List<String>? customerGroups,
    @Default([]) List<String> deliverySlots,
    @Default(null) String? selectedCustomerGroup,
    @Default(false) bool allSelected,
    @Default(false) bool showEdit,
    @Default(null) List<SupplyOrder>? supplyOrders,
    @Default(null) String? message,
    @Default(null) DateTime? deliveryDate,
    @Default(null) String? deliverySlot,
    @Default(false) bool isSearchLoading,
  }) = _Input;
}
