import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/update_receive_proc_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/procurement_data.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';
import 'package:proc2/features/mandi/domain/use_case/add_procurement_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/bloc/add_proc_db.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';

part 'add_procurement_event.dart';
part 'add_procurement_state.dart';
part 'add_procurement_bloc.freezed.dart';

@injectable
class AddProcurementBloc
    extends Bloc<AddProcurementEvent, AddProcurementState> {
  AddProcurementBloc(this._addProcurementUseCase, this._addProcDb)
      : super(AddProcurementState.empty) {
    on<AddProcurementEvent>(
      (event, emit) async {
        await event.when(
          add: (SkuInputData inputData) async {
            final previousList = state.inputSku[inputData.sku.id] ?? [];

            final newList = List<SkuInputData>.from(previousList)
              ..add(inputData);
            final map = Map.fromEntries(state.inputSku.entries);
            map[inputData.sku.id] = newList;
            emit(
              state.copyWith(
                inputSku: map,
                isSubmitCtaActive: true,
                skuCount: state.skuCount + 1,
              ),
            );
            await _addProcDb.updateSkus(newList);
          },
          delete: (SkuInputData value) async {
            final previousList = state.inputSku[value.sku.id] ?? [];

            final newList = List<SkuInputData>.from(previousList)
              ..removeWhere((element) => element.uuid == value.uuid);
            final map = Map.fromEntries(state.inputSku.entries);
            if (newList.isEmpty) {
              map.remove(value.sku.id);
            } else {
              map[value.sku.id] = newList;
            }
            emit(
              state.copyWith(
                  inputSku: map,
                  isSubmitCtaActive: state.inputSku.length > 1,
                  skuCount: state.skuCount - 1),
            );
            await _addProcDb.updateSkus(newList);
          },
          updateComment: (String comment) {
            emit(state.copyWith(comment: comment));
          },
          update: (SkuInputData value) async {
            final previousList = state.inputSku[value.sku.id] ?? [];
            final newList = previousList
                .map(
                  (e) => e.uuid == value.uuid
                      ? value.copyWith(hasChanged: true)
                      : e,
                )
                .toList();
            final map = Map.fromEntries(state.inputSku.entries);
            map[value.sku.id] = newList;
            emit(
              state.copyWith(
                inputSku: map,
                isSubmitCtaActive: true,
              ),
            );
            await _addProcDb.updateSkus(newList);
          },
          updateProcCharge: (
            charge,
            index,
          ) {
            final newCharges =
                List<ProcFieldCharge>.from(state.procFieldCharges);
            newCharges[index] = charge;
            emit(state.copyWith(procFieldCharges: newCharges));
          },
          addProcCharge: (charge) {
            final newCharges =
                List<ProcFieldCharge>.from(state.procFieldCharges);
            newCharges.add(charge);
            emit(state.copyWith(procFieldCharges: newCharges));
          },
          deleteProcCharge: (index) {
            final newCharges =
                List<ProcFieldCharge>.from(state.procFieldCharges);
            newCharges.removeAt(index);
            emit(state.copyWith(procFieldCharges: newCharges));
          },
          submit: () async {
            final allInputs = state.inputSku.values.fold(<SkuInputData>[],
                (previousValue, element) => [...previousValue, ...element]);
            emit(state.copyWith(isSubmitLoading: true));
            final futureToExecute = state.procDetail != null
                ? UpdateReceiveProcOrderRequest(
                    procId: state.procDetail!.id,
                    smoId: state.data.smoId,
                    items: state.inputSku.values.expand((e) => e).toList(),
                    vendorName: state.vendorLocation!.vendorName,
                    vendorLocationId: state.vendorLocation!.locationId,
                    images: state.files.map((e) => e.uploadKey ?? '').toList(),
                    fieldCharges: state.procFieldCharges,
                    comments: state.comment,
                  ).execute()
                : _addProcurementUseCase(
                    procurementData: state.data.copyWith(
                        images:
                            state.files.map((e) => e.uploadKey ?? '').toList()),
                    skuInputData: allInputs,
                    vendorLocationId: state.vendorLocation!.locationId,
                    fieldCharges: state.procFieldCharges,
                    comments: state.comment,
                    supplyDate: state.selectedSupplyDate,
                  );
            await futureToExecute.then(
              (value) => value.fold(
                (left) => emit(
                  state.copyWith(
                    isSubmitLoading: false,
                    message: left.message,
                  ),
                ),
                (right) async {
                  emit(
                    state.copyWith(
                      isSubmitLoading: false,
                      inputSku: {},
                      isSubmitCtaActive: false,
                      data: state.data.copyWith(images: []),
                      files: [],
                      skuCount: 0,
                      shouldPop: true,
                      message:
                          LanguageEnum.addProcSubmitSuccessMessage.localized(),
                    ),
                  );
                  //await _addProcDb.clearSkus();
                },
              ),
            );
          },
          clearMessage: () async => emit(state.copyWith(
            message: '',
            shouldPop: false,
          )),
          init: (userName, smoId, mandiName, procDetail, skus) async {
            // final input = await _addProcDb.getSkus();
            // var isCtaActive = false;
            // if (input.isNotEmpty) {
            //   isCtaActive = true;
            // }
            final slips = procDetail?.images
                    .map((e) => PickedFile.fromUploadPath(e,
                        uploadUrl: procDetail.urls[e]))
                    .toList() ??
                [];
            final newData = state.data.copyWith(
              procuredBy: userName,
              smoId: smoId,
              mandiName: mandiName,
            );
            final fieldCharges = procDetail?.charges.map(
                  (e) {
                    final charge =
                        ProcFieldCharge.fromJson(e as Map<String, dynamic>);
                    return charge.copyWith(
                      images: charge.images
                          .map(
                            (e) => PickedFile.fromUploadPath(
                              e.uploadKey ?? '',
                              uploadUrl:
                                  procDetail.urls[e.uploadKey].toString(),
                            ),
                          )
                          .toList(),
                    );
                  },
                ).toList() ??
                [];
            emit(
              state.copyWith(
                data: newData,
                isSubmitCtaActive: procDetail != null,
                files: slips,
                isOpenToReceive: procDetail != null,
                // vendorName: procDetail?.vendorName ?? '',
                procDetail: procDetail,
                vendorLocation: di.get<VendorCubit>().getVendorLocation(
                      procDetail?.vendorLocationId,
                      procDetail?.vendorName,
                    ),
                // inputSku: input,
                // isSubmitCtaActive: isCtaActive,
                procFieldCharges: fieldCharges,
                comment: procDetail?.comments ?? '',
                selectedSupplyDate: procDetail?.supplyDate == null
                    ? null
                    : DateTime.fromMillisecondsSinceEpoch(
                        procDetail!.supplyDate! * 1000),
              ),
            );
            procDetail?.items.forEach((element) {
              final skuInput = SkuInputData.from(element, skus);
              add(AddProcurementEvent.add(skuInput));
            });
          },
          addSlips: (List<PickedFile> slips) {
            emit(
              state.copyWith(
                files: slips,
              ),
            );
          },
          clearSlips: () {
            emit(
              state.copyWith(
                files: [],
              ),
            );
          },
          updateVendor: (vendor) {
            emit(state.copyWith(vendorLocation: vendor));
          },
          updateSupplyDate: (date) {
            emit(state.copyWith(selectedSupplyDate: date));
          },
        );
      },
    );
  }

  final AddProcurementUseCase _addProcurementUseCase;
  final AddProcDb _addProcDb;
}
