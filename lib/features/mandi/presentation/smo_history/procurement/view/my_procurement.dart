import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/sku_history.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/procurement/bloc/procurement_history_bloc.dart';

class MyProcurement extends StatefulWidget {
  const MyProcurement({
    super.key,
    required this.smoId,
    required this.mandiId,
  });
  final int smoId;
  final int mandiId;

  @override
  State<MyProcurement> createState() => _MyProcurementState();
}

class _MyProcurementState extends State<MyProcurement> {
  var bulkItems = <SkuHistory>[];
  var lotItems = <SkuHistory>[];
  bool isBulkExpanded = true;
  bool isLotExpanded = true;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(LanguageEnum.addProcMyProcyBtn.localized()),
        ),
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          padding: const EdgeInsets.only(
            left: 5,
            right: 5,
            top: 10,
          ),
          child: BlocConsumer<ProcurementHistoryBloc, ProcurementHistoryState>(
            listener: (context, state) {
              state.maybeMap(
                success: (s) {
                  // final allItems = <SkuHistory>[];
                  // for (final element in s.data) {
                  //   allItems.addAll(element.items);
                  // }
                  // final bulk = allItems
                  //     .where((element) => element.isBulk())
                  //     .combineByKey(
                  //       (el) => el.getCompositeKey(),
                  //       (first, second) => first.copyWith(
                  //         quantity: first.quantity + second.quantity,
                  //       ),
                  //     )
                  //     .toList();
                  // final lots = allItems
                  //     .where((element) => !element.isBulk())
                  //     .combineByKey(
                  //       (el) => el.getCompositeKey(),
                  //       (first, second) => first.copyWith(
                  //         quantity: first.quantity + second.quantity,
                  //       ),
                  //     )
                  //     .toList();
                  // setState(() {
                  //   bulkItems = bulk;
                  //   lotItems = lots;
                  // });
                },
                orElse: () {},
              );
            },
            builder: (context, smoState) {
              return smoState.when(
                initial: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                success: (success) {
                  return success.isEmpty
                      ? EmptyScreen(
                          message: LanguageEnum.smoHistoryProcHistoryEmptyTitle
                              .localized(),
                          description: LanguageEnum
                              .smoHistoryProcHistoryEmptyMessage
                              .localized(),
                        )
                      : RefreshIndicator(
                          onRefresh: () async {
                            context.read<ProcurementHistoryBloc>().add(
                                  ProcurementHistoryEvent.started(
                                    widget.smoId,
                                  ),
                                );
                          },
                          child: onSuccessWidget(),
                        );
                },
                error: (error) {
                  return ErrorScreen(
                    onPressed: () {
                      context.read<ProcurementHistoryBloc>().add(
                            ProcurementHistoryEvent.started(widget.smoId),
                          );
                    },
                    message: error.message,
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget onSuccessWidget() {
    return Scrollbar(
      child: ListView(
        shrinkWrap: true,
        children: [
          ExpansionPanelList(
            expansionCallback: (int index, bool isExpanded) {
              setState(() {
                if (index == 0) {
                  isBulkExpanded = !isExpanded;
                } else {
                  isLotExpanded = !isExpanded;
                }
              });
            },
            children: [
              if (bulkItems.isNotEmpty)
                ExpansionPanel(
                  headerBuilder: (BuildContext context, bool isExpanded) {
                    return ListTile(
                      title: Text(
                        LanguageEnum.bulk.localized(),
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  },
                  body: Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    child: Column(
                      // Add 16 px padding to left and right of column
                      children: [
                        bulkHeadingWidget(),
                        const SizedBox(
                          height: 8,
                        ),
                        for (int i = 0; i < bulkItems.length; i++)
                          bulkInputWidget(bulkItems[i], i),
                        const SizedBox(
                          height: 8,
                        ),
                      ],
                    ),
                  ),
                  isExpanded: isBulkExpanded,
                  canTapOnHeader: true,
                ),
              if (lotItems.isNotEmpty)
                ExpansionPanel(
                  headerBuilder: (BuildContext context, bool isExpanded) {
                    return ListTile(
                      title: Text(
                        LanguageEnum.lots.localized(),
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  },
                  body: Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    child: Column(
                      // Add 16 px padding to left and right of column
                      children: [
                        lotHeadingWidget(),
                        const SizedBox(
                          height: 8,
                        ),
                        for (int i = 0; i < lotItems.length; i++)
                          lotInputWidget(lotItems[i], i),
                        const SizedBox(
                          height: 8,
                        ),
                      ],
                    ),
                  ),
                  isExpanded: isLotExpanded,
                  canTapOnHeader: true,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget bulkHeadingWidget() {
    return Row(
      children: [
        Expanded(
          flex: 4,
          child: Text(
            LanguageEnum.sku.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 4,
          child: Text(
            LanguageEnum.totalQty.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget bulkInputWidget(
    SkuHistory inputModel,
    int index,
  ) {
    final skuBloc = context.watch<SkuBloc>();
    return Padding(
      padding: const EdgeInsets.only(
        top: 8,
        bottom: 8,
      ),
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 4,
              child: Text(
                skuBloc.state.getSkuName(inputModel.skuId) ?? '-',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Text(
                '${inputModel.quantity}  ${inputModel.unit.localized()}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.end,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget lotHeadingWidget() {
    return Row(
      children: [
        Expanded(
          flex: 4,
          child: Text(
            LanguageEnum.sku.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 4,
          child: Text(
            LanguageEnum.lotSize.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            LanguageEnum.qty.localized(),
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget lotInputWidget(
    SkuHistory inputModel,
    int index,
  ) {
    final skuBloc = context.watch<SkuBloc>();
    return Padding(
      padding: const EdgeInsets.only(
        top: 8,
        bottom: 8,
      ),
      child: Container(
        height: 80,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade300),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 4,
              child: Text(
                skuBloc.state.getSkuName(inputModel.skuId) ?? '-',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Text(
                '${inputModel.lotSize}  ${inputModel.unit.localized()}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(
                  '${inputModel.quantity}',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

extension IterableCombine<T> on Iterable<T> {
  Iterable<T> combineByKey(
    String Function(T el) getKey,
    T Function(T first, T second) combine,
  ) {
    final mp = <String, T>{};
    for (final el in this) {
      final key = getKey(el);
      if (mp.containsKey(key)) {
        mp[key] = combine(mp[key]!, el);
      } else {
        mp[key] = el;
      }
    }
    return mp.values;
  }
}
