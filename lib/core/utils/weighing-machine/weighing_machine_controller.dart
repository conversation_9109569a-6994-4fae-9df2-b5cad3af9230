import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_machine_scale_configuration_request.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_scale_device_storage.dart';
import 'package:weighing_machine/weighing_machine.dart';

class WeighingMachineController extends IWeighingMachineController {
  WeighingMachineController._();
  static final instance = WeighingMachineController._();

  @override
  IWeighingScaleDeviceStorageInterface get deviceStorageInterface =>
      WeighingScaleDeviceStorage();

  @override
  void log(dynamic message) {
    talker.info(message);
  }

  @override
  Future<List<ScaleConfiguration>> getScaleConfigurations() async {
    final response = await WeighingMachineScaleConfigurationRequest().execute();
    if (response.isLeft) {
      throw response.left;
    }
    return response.right;
  }

  SnackbarController? _snackbarController;
  void _closeSnackbar() {
    try {
      _snackbarController?.close();
    } catch (_) {
    } finally {
      _snackbarController = null;
    }
  }

  SnackbarController? snackBarHandler(SnackbarController Function() init) {
    try {
      // return init();
    } catch (e, s) {
      talker.handle(e, s);
    }
    return null;
  }

  @override
  void onEvent(WeighingMachineScaleEvent event) {
    talker.info(event.toString());

    switch (event) {
      case WeighingMachineScaleEvent.searchingDevice:
        {
          _closeSnackbar();
          _snackbarController = snackBarHandler(() => Get.snackbar(
                getLangText('info', 'Info'),
                getLangText('searchingIfDeviceNearbyMessage',
                    'Searching if device is nearby...'),
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.blue,
                colorText: Colors.white,
                margin: const EdgeInsets.all(8),
                isDismissible: true,
                instantInit: false,
                showProgressIndicator: true,
                progressIndicatorValueColor:
                    const AlwaysStoppedAnimation(Colors.blue),
                progressIndicatorBackgroundColor: Colors.white,
                duration: const Duration(seconds: 60),
              ));
          break;
        }
      case WeighingMachineScaleEvent.deviceNotFound:
        {
          _closeSnackbar();
          _snackbarController = snackBarHandler(() => Get.snackbar(
                getLangText('error', 'Error'),
                getLangText('weighingMachineNotFoundMessage',
                    'Failed to find weighing machine!'),
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.red,
                colorText: Colors.white,
                instantInit: false,
                margin: const EdgeInsets.all(8),
              ));
          break;
        }
      case WeighingMachineScaleEvent.deviceConnected:
        {
          _closeSnackbar();
          _snackbarController = snackBarHandler(() => Get.snackbar(
                getLangText('success', 'Success'),
                getLangText('weighingMachineSucessMessage',
                    'Weighing machine connected successfully!'),
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
                margin: const EdgeInsets.all(8),
                instantInit: false,
              ));
          break;
        }
      case WeighingMachineScaleEvent.deviceConnectionFailed:
        {
          _closeSnackbar();
          _snackbarController = snackBarHandler(() => Get.snackbar(
                getLangText('error', 'Error'),
                getLangText('weighingMachineFailedMessage',
                    'Failed to connect weighing machine!'),
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.red,
                margin: const EdgeInsets.all(8),
                instantInit: false,
              ));
          break;
        }
      case WeighingMachineScaleEvent.none:
        _closeSnackbar();
        break;
    }
  }

  @override
  Future<bool> requestPermission() async {
    final result = await [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.location,
    ].request();
    bool notGranted =
        result.values.any((element) => element != PermissionStatus.granted);
    if (notGranted) {
      talker.info('Bluetooth permission not Granted');
      _closeSnackbar();
      _snackbarController = snackBarHandler(() => Get.snackbar(
            getLangText('error', 'Error'),
            getLangText('bluetoothPermissionMessage',
                'Bluetooth permission is required!'),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
            margin: const EdgeInsets.all(8),
            instantInit: false,
          ));
    }
    return !notGranted;
  }
}
