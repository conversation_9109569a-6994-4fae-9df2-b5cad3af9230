class ProcTableItem {
  final int itemId;
  final String lastOrderedQuantity;
  final String orderedQuantity;
  final double? receivedQuantity;

  ProcTableItem({
    required this.itemId,
    required this.lastOrderedQuantity,
    required this.orderedQuantity,
    required this.receivedQuantity,
  });

  bool get hasChanged => lastOrderedQuantity != orderedQuantity;

  ProcTableItem copyWith({
    int? itemId,
    String? lastOrderedQuantity,
    String? orderedQuantity,
    double? receivedQuantity,
  }) {
    return ProcTableItem(
      itemId: itemId ?? this.itemId,
      lastOrderedQuantity: lastOrderedQuantity ?? this.lastOrderedQuantity,
      orderedQuantity: orderedQuantity ?? this.orderedQuantity,
      receivedQuantity: receivedQuantity ?? this.receivedQuantity,
    );
  }
}
