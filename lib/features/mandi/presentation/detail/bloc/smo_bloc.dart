import 'package:bloc/bloc.dart';
import 'package:either_dart/either.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/location_repository_utils.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_smo_config_request.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/domain/use_case/get_smo_usecase.dart';
import 'package:proc2/features/mandi/presentation/attendance/request/assign_user_request.dart';

part 'smo_event.dart';
part 'smo_state.dart';
part 'smo_bloc.freezed.dart';

@injectable
class SmoBloc extends Bloc<SmoEvent, SmoState> {
  SmoBloc(this._getSmoUseCase) : super(const SmoState.initial()) {
    on<SmoEvent>((event, emit) async {
      await event.when(
        fetchSmo: (mandiId, smoId) async {
          final smoRequest = _getSmoUseCase(mandiId.toString());
          final smoConfigRequest = GetSmoConfigRequest(smoId: smoId).execute();
          final allAwait = await Future.wait([smoRequest, smoConfigRequest]);
          final smoResponse = allAwait[0] as Either<ErrorResult<void>, Smo>;
          final smoConfigResponse =
              allAwait[1] as Either<ErrorResult<dynamic>, SmoConfig>;

          if (smoResponse.isLeft) {
            emit(SmoState.error(smoResponse.left));
          } else if (smoConfigResponse.isLeft) {
            emit(SmoState.error(smoConfigResponse.left));
          } else {
            emit(SmoState.success(
              smo: smoResponse.right,
              config: smoConfigResponse.right,
            ));
          }
        },
        markSelfAttendance: (smoId) async {
          final location = await getLocation(Duration(minutes: 5))
              .timeout(Duration(seconds: 10), onTimeout: () {
            return null;
          });
          await AssignUserRequest.selfMark(
            smoId: smoId,
            location: location,
          ).execute();
          // No need to check on the result.
        },
      );
    });
  }

  final GetSmoUseCase _getSmoUseCase;
}
