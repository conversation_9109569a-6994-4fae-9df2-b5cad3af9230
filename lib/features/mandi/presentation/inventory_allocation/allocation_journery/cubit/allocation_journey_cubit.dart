import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/customer_journey_manager.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/journey_manager.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/mandi_journey_manager.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_configuration.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_error_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_journey_stage.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_terminal.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/customer_group_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/group_info.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/terminal_distribution.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/cancel_allocation_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_allocation_pdf_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_sku_distribution.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/submit_final_allocation_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/unlock_locked_sku_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/update_allocation_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/update_sku_distribution.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/total_allocation_popup.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

part 'allocation_journey_cubit.freezed.dart';
part 'allocation_journey_state.dart';

@injectable
class AllocationJourneyCubit extends Cubit<AllocationJourneyState> {
  AllocationJourneyCubit() : super(AllocationJourneyState.data());
  JourneyManager? _journeyManager;

  void startCustomerGroupJourney({
    required int mandiId,
    required int smoId,
    required int allocationId,
    required String customerGroup,
    required String deliverySlot,
    required int deliveryDate,
    required bool isInProgress,
    required bool canAllocateExcess,
    required String? status,
  }) async {
    emit(
      state.copyWith(
        customerGroupData: CustomerGroupData(
          customerGroup: customerGroup,
          deliverySlot: deliverySlot,
          deliveryDateEpochSeconds: deliveryDate,
          canAllocateExcess: canAllocateExcess,
        ),
        mandiId: mandiId,
        allocationId: allocationId,
        smoId: smoId,
        isInProgress: isInProgress,
        status: status,
      ),
    );
    _journeyManager = CustomerJourneyManager(stream: this, emmiter: this);
    _journeyManager!.initialize();
  }

  void startMandiJourney({
    required int mandiId,
    required int smoId,
    required int allocationId,
    required bool isInProgress,
    required String? status,
  }) async {
    emit(
      state.copyWith(
        stage: AllocationJourneyStage.loading,
        mandiId: mandiId,
        allocationId: allocationId,
        smoId: smoId,
        isInProgress: isInProgress,
        status: status,
      ),
    );
    _journeyManager = MandiJourneyManager(stream: this, emmiter: this);
    _journeyManager!.initialize();
  }

  void updateSkuMap({required List<Sku> skus}) async {
    final skuMap = Map<int, Sku>.fromIterable(
      skus,
      key: (sku) => sku.id,
      value: (sku) => sku,
    );
    emit(
      state.copyWith(
        skuMap: skuMap,
      ),
    );
  }

  void refreshSku({required SkuBloc skuBloc}) async {
    skuBloc.add(const SkuEvent.fetch());
  }

  void setGroupCount(int groupCount) {
    _journeyManager?.addGroupCount(groupCount);
  }

  void createUpdateGroup() {
    _journeyManager?.createUpdateGroup();
  }

  void copyFromSupplyOrders() {
    final selectedIds = state.allocationFilterData.selectedSkuIds;
    // Change in both and change in supply order
    final items = state.items.map((e) {
      if (selectedIds.isEmpty || selectedIds.contains(e.skuQuantity.skuId)) {
        final qty = e.orderedQty?.asString() ?? '';
        return e.copyWith(
            updateAllocationQty: AllocationQty.fromTotalQty(
          qty: qty,
          mandiQty: e.mandiQty,
          allowExpectation: state.allocationConfig?.allocation.updateAllocation
                  .allowExpectation ??
              false,
        ));
      } else {
        return e;
      }
    }).toList();
    final itemsInSupplyOrder = state.itemsInSupplyOrder.map((e) {
      if (selectedIds.isEmpty || selectedIds.contains(e.skuQuantity.skuId)) {
        final qty = e.orderedQty?.asString() ?? '';

        return e.copyWith(
            updateAllocationQty: AllocationQty.fromTotalQty(
          qty: qty,
          mandiQty: e.mandiQty,
          allowExpectation: state.allocationConfig?.allocation.updateAllocation
                  .allowExpectation ??
              false,
        ));
      } else {
        return e;
      }
    }).toList();
    final itemsInMandi = state.itemsInMandi.map((e) {
      if (selectedIds.isEmpty || selectedIds.contains(e.skuQuantity.skuId)) {
        return e.copyWith(
            updateAllocationQty: AllocationQty.fromTotalQty(
          qty: '',
          mandiQty: e.mandiQty,
          allowExpectation: state.allocationConfig?.allocation.updateAllocation
                  .allowExpectation ??
              false,
        ));
      } else {
        return e;
      }
    }).toList();
    emit(state.copyWith(
      items: items,
      itemsInSupplyOrder: itemsInSupplyOrder,
      itemsInMandi: itemsInMandi,
    ));
  }

  void copyFromMandiInventory() {
    final selectedIds = state.allocationFilterData.selectedSkuIds;
    // Change in both and change in mandi inventory
    final items = state.items.map((e) {
      if (selectedIds.isEmpty || selectedIds.contains(e.skuQuantity.skuId)) {
        final qty = e.mandiQty.asString();
        return e.copyWith(
          updateAllocationQty: AllocationQty.fromTotalQty(
            qty: qty,
            mandiQty: e.mandiQty,
            allowExpectation: state.allocationConfig?.allocation
                    .updateAllocation.allowExpectation ??
                false,
          ),
        );
      } else {
        return e;
      }
    }).toList();
    final itemsInMandi = state.itemsInMandi.map((e) {
      if (selectedIds.isEmpty || selectedIds.contains(e.skuQuantity.skuId)) {
        final qty = e.mandiQty.asString();
        return e.copyWith(
          updateAllocationQty: AllocationQty.fromTotalQty(
            qty: qty,
            mandiQty: e.mandiQty,
            allowExpectation: state.allocationConfig?.allocation
                    .updateAllocation.allowExpectation ??
                false,
          ),
        );
      } else {
        return e;
      }
    }).toList();
    final itemsInSupplyOrder = state.itemsInSupplyOrder.map((e) {
      if (selectedIds.isEmpty || selectedIds.contains(e.skuQuantity.skuId)) {
        return e.copyWith(
          updateAllocationQty: AllocationQty.fromTotalQty(
            qty: '',
            mandiQty: e.mandiQty,
            allowExpectation: state.allocationConfig?.allocation
                    .updateAllocation.allowExpectation ??
                false,
          ),
        );
      } else {
        return e;
      }
    }).toList();
    emit(state.copyWith(
        items: items,
        itemsInMandi: itemsInMandi,
        itemsInSupplyOrder: itemsInSupplyOrder));
  }

  void updateDistribution(TerminalDistribution distribution) {
    emit(state.copyWith(terminalDistribution: distribution));
  }

  void submitDistribution({required bool isDraft}) async {
    final terminalDistribution = state.terminalDistribution;
    if (terminalDistribution == null) return;
    emit(state.copyWith(
      stage: AllocationJourneyStage.loading,
      previousStage: null,
      loadingMessage: 'Updating distribution...',
    ));
    final result = await UpdateSkuDistribution(
      allocationId: state.allocationId,
      items: terminalDistribution.allDistributionItems,
      lossQuantity: terminalDistribution.wastage.toDouble(),
      isDraft: isDraft,
      skuQuantity: terminalDistribution.skuQuantity,
    ).execute();
    if (result.isLeft) {
      emit(
        state.copyWith(
          loadingMessage: null,
          stage: AllocationJourneyStage.manualDistribution,
          previousStage: AllocationJourneyStage.view,
          errorData: null,
          message: result.left.message.isEmpty
              ? 'Error while updating distribution!'
              : result.left.message,
        ),
      );
      return;
    }
    emit(state.copyWith(
      message: result.right.message,
      previousStage: null,
      terminalDistribution: null,
    ));
    if (result.isRight && result.right.refreshSupplyOrder) {
      _journeyManager?.initialize();
    } else {
      _journeyManager?.reloadAfterAllocationUpdate();
    }
  }

  void retry() {
    _journeyManager?.retry();
  }

  void clearMessage() {
    emit(state.copyWith(message: null, shouldPop: false));
  }

  void updateQtyBoth({required int index, required AllocationQty qty}) {
    final updated = state.items.indexed
        .map((e) =>
            e.$1 == index ? e.$2.copyWith(updateAllocationQty: qty) : e.$2)
        .toList();
    emit(state.copyWith(items: updated));
  }

  void updateQtyPartial({required int index, required AllocationQty qty}) {
    final updated = state.itemsPartial.indexed
        .map((e) =>
            e.$1 == index ? e.$2.copyWith(updateAllocationQty: qty) : e.$2)
        .toList();
    emit(state.copyWith(itemsPartial: updated));
  }

  void updateQtyMandi({
    required int index,
    required AllocationQty qty,
    required bool isValidIdOnly,
  }) {
    final updated = state.itemsInMandi
        .where((e) => e.isValidId == isValidIdOnly)
        .indexed
        .map((e) =>
            e.$1 == index ? e.$2.copyWith(updateAllocationQty: qty) : e.$2)
        .toList();
    emit(state.copyWith(itemsInMandi: updated));
  }

  void updateQtySupply({required int index, required AllocationQty qty}) {
    final updated = state.itemsInSupplyOrder.indexed
        .map((e) =>
            e.$1 == index ? e.$2.copyWith(updateAllocationQty: qty) : e.$2)
        .toList();
    emit(state.copyWith(itemsInSupplyOrder: updated));
  }

  void updateAllocation() async {
    emit(state.copyWith(
      stage: AllocationJourneyStage.loading,
      previousStage: null,
      loadingMessage: 'Updating allocation...',
    ));
    // Here call backend api
    await _updateAllocation(
      items: state.items,
      itemsPartial: state.itemsPartial,
      itemsInMandi: state.itemsInMandi,
      itemsInSupplyOrder: state.itemsInSupplyOrder,
    );
  }

  Future<void> _updateAllocation({
    required List<AllocationSkuItem> items,
    required List<AllocationSkuItem> itemsPartial,
    required List<AllocationSkuItem> itemsInMandi,
    required List<AllocationSkuItem> itemsInSupplyOrder,
  }) async {
    final result = await UpdateAllocationRequest(
      allocationId: state.allocationId,
      items: [...items, ...itemsPartial],
      mandiItems: itemsInMandi,
      supplyItems: itemsInSupplyOrder,
    ).execute();

    final newState = result.fold(
      (left) => state.copyWith(
          loadingMessage: null,
          stage: AllocationJourneyStage.allocation,
          errorData: null,
          message: left.message.isEmpty
              ? 'Error while updating allocation!'
              : left.message),
      (right) => state.copyWith(message: right.message),
    );
    emit(newState);

    if (result.isRight) {
      if (result.isRight && result.right.refreshSupplyOrder) {
        _journeyManager?.initialize();
      } else {
        _journeyManager?.reloadAfterAllocationUpdate();
      }
    }
  }

  Future<void> refreshAllocationAndInventory(
      {AllocationJourneyStage? stage}) async {
    await _journeyManager?.reloadAfterAllocationUpdate(stage: stage);
  }

  void updateFilterData({
    required FilterData data,
    required bool isAllocationFilter,
  }) {
    final newState = isAllocationFilter
        ? state.copyWith(allocationFilterData: data)
        : state.copyWith(viewFilterData: data);
    emit(newState);
  }

  void openManualDistribution(AllocationSkuItem item) async {
    // bool isEditingAllowed =
    //     state.allocationConfig?.distribution.canEditDistribution ?? false;
    // if (!isEditingAllowed) return;
    emit(state.copyWith(
      stage: AllocationJourneyStage.loading,
      loadingMessage: 'Fetching distribution data...',
      previousStage: AllocationJourneyStage.view,
    ));
    final result = await GetSkuDistribution(
      allocationId: state.allocationId,
      skuQuantity: item.skuQuantity,
      isAutoCopyDistributionQtyEnabled:
          (state.allocationConfig?.distribution.allowAutoCopyFromDistributed ??
                  false) &&
              item.backendAllocationQty.expectedQty.toDouble() == 0,
      supplyOrders: state.supplyOrders!,
    ).execute();
    final newState = result.fold(
      (left) => state.copyWith(
          stage: AllocationJourneyStage.view,
          previousStage: null,
          message: left.message),
      (right) => state.stage != AllocationJourneyStage.loading
          ? state.copyWith(
              stage: AllocationJourneyStage.view,
              previousStage: null,
            )
          : state.copyWith(
              stage: AllocationJourneyStage.manualDistribution,
              previousStage: AllocationJourneyStage.view,
              loadingMessage: null,
              errorData: null,
              terminalDistribution: TerminalDistribution(
                skuName: item.skuName,
                distributionInfo: right,
                wastage: '',
                totalAllocated: item.totalQuantityBackend,
                mandiQty: item.mandiQty,
                expectationQty:
                    item.backendAllocationQty.expectedQty.toDouble(),
                skuQuantity: item.skuQuantity,
              ),
            ),
    );
    emit(newState);
  }

  void openAllocationScreenWithSelectedItems(List<String> selectedSkus) {
    emit(state.copyWith(
      stage: AllocationJourneyStage.allocation,
      previousStage: AllocationJourneyStage.view,
      allocationFilterData: FilterData(
        selectedSkuIds: selectedSkus,
      ),
    ));
  }

  bool canMoveBack() {
    return state.previousStage != null && state.previousStage != state.stage;
  }

  void moveBack() {
    if (state.previousStage != null && state.previousStage != state.stage) {
      emit(state.copyWith(stage: state.previousStage!, previousStage: null));
    }
  }

  void showPdf(List<String> selectedSkus) async {
    emit(
      state.copyWith(
        stage: AllocationJourneyStage.loading,
        loadingMessage: 'Generating PDF...',
        previousStage: AllocationJourneyStage.view,
      ),
    );
    final request = await GetAllocationPdfRequest(
      allocationId: state.allocationId,
      customerGroup: state.customerGroupData!.customerGroup,
      pdfType: selectedSkus.isEmpty ? 'GROUP' : 'SKU',
      selectedSkus: selectedSkus,
    ).execute();

    final newState = request.fold(
      (left) => state.copyWith(
        stage: AllocationJourneyStage.view,
        loadingMessage: null,
        message:
            left.message.isEmpty ? 'Error while generating PDF!' : left.message,
        previousStage: null,
      ),
      (right) => state.stage == AllocationJourneyStage.loading
          ? state.copyWith(
              stage: AllocationJourneyStage.pdf,
              loadingMessage: null,
              message: null,
              pdfs: right,
              previousStage: AllocationJourneyStage.view,
            )
          : state,
    );
    emit(newState);
  }

  void autoResolve() async {
    emit(state.copyWith(
      stage: AllocationJourneyStage.loading,
      loadingMessage: 'Trying to auto resolve...',
      previousStage: null,
    ));
    final items = _ResolveResult.fromItems(state.items);
    final itemsPartial = _ResolveResult.fromItems(state.itemsPartial);
    final itemsInMandi = _ResolveResult.fromItems(state.itemsInMandi);
    final itemsInSupplyOrder =
        _ResolveResult.fromItems(state.itemsInSupplyOrder);
    final count = items.resolveCount +
        itemsPartial.resolveCount +
        itemsInMandi.resolveCount +
        itemsInSupplyOrder.resolveCount;
    final partialResolveCount = items.partialResolveCount +
        itemsPartial.partialResolveCount +
        itemsInMandi.partialResolveCount +
        itemsInSupplyOrder.partialResolveCount;

    if (count == 0 && partialResolveCount == 0) {
      emit(state.copyWith(
        stage: AllocationJourneyStage.view,
        loadingMessage: null,
        message: 'Auto resolving failed. No items to auto resolve!',
        previousStage: null,
      ));
      return;
    }
    String message = (count > 0 ? 'Resolving $count items' : 'Resolving ') +
        (partialResolveCount > 0
            ? (count > 0 ? ' and ' : '') + '$partialResolveCount partially...'
            : '...');

    emit(state.copyWith(loadingMessage: message));

    await _updateAllocation(
      items: items.items,
      itemsPartial: itemsPartial.items,
      itemsInMandi: itemsInMandi.items,
      itemsInSupplyOrder: itemsInSupplyOrder.items,
    );
  }

  void dispatchAllocation() async {
    emit(state.copyWith(
      stage: AllocationJourneyStage.loading,
      loadingMessage: 'Dispatching allocation...',
      previousStage: null,
    ));
    final result =
        await SubmitFinalAllocationRequest(allocationId: state.allocationId)
            .execute();
    final newState = result.fold(
      (left) => state.copyWith(
        stage: AllocationJourneyStage.view,
        loadingMessage: null,
        message:
            left.message.isEmpty ? 'Error while creating trips' : left.message,
        previousStage: null,
      ),
      (right) => state.copyWith(
        stage: AllocationJourneyStage.view,
        loadingMessage: null,
        message: right.message,
        shouldPop: !right.refreshSupplyOrder,
        previousStage: null,
      ),
    );
    if (result.isRight && result.right.refreshSupplyOrder) {
      _journeyManager?.initialize();
    }
    emit(newState);
  }

  void updatePendingTerminals(List<AllocationTerminal> terminals) {
    emit(state.copyWith(pendingAllocationTerminals: terminals));
  }

  void unlockDistributionSku() async {
    final sku = state.terminalDistribution?.skuQuantity;
    if (sku == null) return;
    unlockSku(sku);
  }

  void unlockSku(SkuQuantity sku, {bool showResult = false}) async {
    final result = await UnlockLockedSkuRequest(
      allocationId: state.allocationId,
      sku: sku,
    ).execute();
    if (showResult) {
      final newState = result.fold(
        (left) => state.copyWith(
          message: left.message.isEmpty
              ? 'Error while unlocking SKU!'
              : left.message,
        ),
        (right) => state.copyWith(
          message: right,
        ),
      );
      emit(newState);
    }
  }

  void autoAllocateRemainingSku() {
    _journeyManager?.autoAllocateRemainingSku();
  }

  Future<void> cancelSelectedOrders({required List<String> orders}) async {
    emit(state.copyWith(
      stage: AllocationJourneyStage.loading,
      previousStage: null,
      loadingMessage:
          'allocation.cancellingSupplyOrders'.tr('Cancelling supply orders...'),
    ));
    final result = await CancelAllocationRequest(
      supplyOrderIds: orders,
      allocationId: state.allocationId,
    ).execute();
    final newState = result.fold(
      (left) => state.copyWith(
        message: left.message.isEmpty
            ? 'Error while cancelling supply orders!'
            : left.message,
        loadingMessage: null,
      ),
      (right) => state.copyWith(
        message: right,
        previousStage: null,
        stage: AllocationJourneyStage.view,
        loadingMessage: null,
      ),
    );
    emit(newState);
    await _journeyManager?.loadSupplyOrders();
  }

  void openAllocationManagementScreen() {
    emit(state.copyWith(
      stage: AllocationJourneyStage.management,
      previousStage: state.stage,
    ));
  }

  void copyMandiAllocation() {
    final items = state.items.map((e) {
      return e.copyWith(
        updateAllocationQty: AllocationQty.fromTotalQty(
          qty: e.mandiQty.asString(),
          mandiQty: e.mandiQty,
          allowExpectation: false,
        ),
      );
    }).toList();
    emit(state.copyWith(items: items));
  }
}

class _ResolveResult {
  final List<AllocationSkuItem> items;
  final int resolveCount;
  final int partialResolveCount;

  _ResolveResult._(
    this.items,
    this.resolveCount,
    this.partialResolveCount,
  );

  factory _ResolveResult.fromItems(
    List<AllocationSkuItem> items,
  ) {
    int resolveCount = 0;
    int partialResolveCount = 0;
    final updatedItems = items.map((e) {
      final remainingMandiQty =
          e.mandiQty - e.backendAllocationQty.mandiQty.toDouble();
      if (e.backendAllocationQty.expectedQty.toDouble() > 0) {
        if (e.mandiQty >= e.totalQuantityBackend) {
          resolveCount++;
          return e.copyWith(
              updateAllocationQty: AllocationQty.fromTotalQty(
            qty: e.totalQuantityBackend.asString(),
            mandiQty: e.mandiQty,
            allowExpectation: false,
          ));
        } else if (remainingMandiQty > 0) {
          partialResolveCount++;
          return e.copyWith(
            mandiQty: 0,
            updateAllocationQty: AllocationQty.fromTotalQty(
              qty: e.backendAllocationQty.totalQty,
              mandiQty: e.mandiQty,
              allowExpectation: false,
            ),
          );
        } else {
          return e;
        }
      } else {
        return e;
      }
    }).toList();
    return _ResolveResult._(updatedItems, resolveCount, partialResolveCount);
  }
}
