import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/admin/weighing_machine_bypass/model/pitstop_user.dart';
import 'package:proc2/features/admin/weighing_machine_bypass/request/get_pitstop_users_request.dart';
import 'package:proc2/features/admin/weighing_machine_bypass/request/update_weighing_machine_bypass_request.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';

part 'weighing_machine_bypass_cubit.freezed.dart';
part 'weighing_machine_bypass_state.dart';

@injectable
class WeighingMachineBypassCubit extends Cubit<WeighingMachineBypassState> {
  WeighingMachineBypassCubit() : super(WeighingMachineBypassState.initial());

  void updateSelectedMandi({required MandiInfo mandi, required int smoId}) {
    emit(state.copyWith(
      selectedMandi: mandi,
      smoId: smoId,
      assignedUsers: null,
      isLoading: false,
      hasAnyUpdateActionDone: false,
      message: null,
    ));
  }

  void clearMessage() {
    emit(state.copyWith(
      message: null,
    ));
  }

  Future<void> searchUsers() async {
    emit(state.copyWith(
      isSearchLoading: true,
      message: null,
    ));
    final smoId = state.smoId;
    if (smoId == null) return;
    final result = await GetPitstopUsersRequest(smoId: smoId).execute();
    result.fold(
      (l) {
        emit(state.copyWith(
          isSearchLoading: false,
          message: l.message,
        ));
      },
      (r) {
        emit(state.copyWith(
          isSearchLoading: false,
          assignedUsers: r,
        ));
      },
    );
  }

  Future<bool> submit({required int userId, required bool access}) async {
    // emit(state.copyWith(
    //   isLoading: true,
    //   message: null,
    // ));
    final smoId = state.smoId;
    final assignedUsers = state.assignedUsers;
    if (smoId == null || assignedUsers == null) return false;
    final result = await UpdateWeighingMachineBypassRequest(
      smoId: smoId,
      userId: userId,
      access: access,
    ).execute();
    result.fold(
      (l) {
        emit(state.copyWith(
          // isLoading: false,
          message: l.message,
        ));
      },
      (r) {
        emit(state.copyWith(
          // isLoading: false,
          message: r,
          hasAnyUpdateActionDone: false,
        ));
      },
    );
    return result.isRight;
  }

  void toggleBypass({required int id}) async {
    final assignedUsers = state.assignedUsers;
    if (assignedUsers == null) return;
    final updatedUsers = assignedUsers.map((user) {
      if (user.id == id) {
        return user.copyWith(
          hasWeighingMachineByPass: !user.hasWeighingMachineByPass,
        );
      }
      return user;
    }).toList();
    final user = updatedUsers.where((e) => e.id == id).first;
    final result = await submit(
        userId: user.user.id, access: user.hasWeighingMachineByPass);
    if (result) {
      emit(state.copyWith(
        assignedUsers: updatedUsers,
        hasAnyUpdateActionDone: true,
      ));
    }
  }
}
