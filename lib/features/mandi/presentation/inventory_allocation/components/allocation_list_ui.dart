import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/allocation_list_common_header.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/sliver_allocation_list.dart';

class AllocationListUi extends StatefulWidget {
  const AllocationListUi({
    super.key,
    required this.state,
    required this.isUpdateUi,
    required this.selectedItems,
    required this.onSelectedItemChange,
    required this.isSelectionActive,
    required this.isEditingAllowed,
  });
  final AllocationJourneyState state;
  final bool isUpdateUi;
  final List<String> selectedItems;
  final Function(String) onSelectedItemChange;
  final bool isSelectionActive;
  final bool isEditingAllowed;

  @override
  State<AllocationListUi> createState() => _AllocationListUiState();
}

class _AllocationListUiState extends State<AllocationListUi> {
  @override
  Widget build(BuildContext context) {
    bool isEmpty = widget.state.items.isEmpty &&
        widget.state.itemsPartial.isEmpty &&
        widget.state.itemsInMandi.isEmpty &&
        widget.state.itemsInSupplyOrder.isEmpty;
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(left: 4, right: 4, top: 8),
        child: isEmpty
            ? Center(
                child: Text(
                  widget.state.isCustomerGroup
                      ? 'Mandi inventory is empty & no allocation data found!'
                      : 'Mandi inventory is empty!',
                  style: TextStyle(
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              )
            : RefreshIndicator(
                onRefresh: () async {
                  await context
                      .read<AllocationJourneyCubit>()
                      .refreshAllocationAndInventory(stage: widget.state.stage);
                },
                child: CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(
                      child: AllocationListCommonHeader(
                        state: widget.state,
                        showCopy: widget.isUpdateUi,
                      ),
                    ),
                    SliverAllocationList(
                      state: widget.state,
                      isUpdateUi: widget.isUpdateUi,
                      isSelectionActive: widget.isSelectionActive,
                      selectedItems: widget.selectedItems,
                      onSelectedItemChange: widget.onSelectedItemChange,
                      isEditingAllowed: widget.isEditingAllowed,
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}
