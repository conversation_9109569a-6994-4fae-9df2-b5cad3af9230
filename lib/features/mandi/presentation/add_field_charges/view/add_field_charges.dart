// ignore_for_file: inference_failure_on_instance_creation

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/uploader/image_picker_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/features/mandi/presentation/add_field_charges/bloc/add_field_charge_bloc.dart';
import 'package:proc2/features/mandi/presentation/add_field_charges/input_models/charge_input_model.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';

class AddFieldCharges extends StatefulWidget {
  const AddFieldCharges({super.key, required this.smoId});

  final int smoId;

  @override
  State<AddFieldCharges> createState() => _AddFieldCharges();
}

class _AddFieldCharges extends State<AddFieldCharges> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          title: Text(LanguageEnum.addFieldCharges.localized()),
        ),
        body: BlocConsumer<AddFieldChargeBloc, AddFieldChargeState>(
          listener: (context, state) {
            state.maybeMap(
              orElse: () {},
              error: (error) {
                showSnackBar(error.error.toString());
                context
                    .read<AddFieldChargeBloc>()
                    .add(const AddFieldChargeEvent.clearMessage());
              },
              input: (input) {
                if (input.message != null) {
                  showSnackBar(input.message!);
                  context
                      .read<AddFieldChargeBloc>()
                      .add(const AddFieldChargeEvent.clearMessage());
                }
              },
            );
          },
          builder: (context, state) {
            return state.map(
              initial: (initial) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              },
              error: (error) {
                return ErrorScreen(
                  onPressed: () {
                    context.read<AddFieldChargeBloc>().add(
                          const AddFieldChargeEvent.fetchChargesType(),
                        );
                  },
                  message: error.error.message,
                );
              },
              input: (input) {
                return Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  padding: const EdgeInsets.only(left: 8, right: 8, top: 10),
                  child: Scrollbar(
                    child: ListView(
                      children: [
                        for (int i = 0; i < input.charges.length; i++)
                          chargesWidget(
                            chargesType: input.chargeTypes,
                            index: i,
                            chargeInputModel: input.charges[i],
                          ),
                        const SizedBox(height: 10),
                        addChargesButton(),
                        Divider(color: Colors.grey.shade400),
                        const SizedBox(height: 4),
                        totalWidget(input),
                        const SizedBox(height: 4),
                        Divider(color: Colors.grey.shade400),
                        const SizedBox(height: 10),
                        uploadSlipsButton(input.files),
                        const SizedBox(height: 15),
                        // commentsField(input),
                        // saveAsDraftButton(),
                        submitButton(input)
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget addChargesButton() {
    return ElevatedButton.icon(
      onPressed: () {
        context.read<AddFieldChargeBloc>().add(
              const AddFieldChargeEvent.addCharge(),
            );
      },
      style:
          ButtonStyle(fixedSize: MaterialStateProperty.all(const Size(80, 40))),
      label: Text(LanguageEnum.fieldChargesAddBtn.localized()),
      icon: const Icon(Icons.add),
    );
  }

  Widget submitButton(InputChargeState state) {
    final isDisabled = state.charges.isEmpty ||
        state.charges.any(
          (element) => element.amount.isEmpty || element.amount.isNotDouble(),
        );
    final smoId = context.read<SmoBloc>().state.maybeMap(
          orElse: () => -1,
          success: (success) => success.smo.smoId,
        );
    return Padding(
      padding: const EdgeInsets.only(
        left: 8,
        right: 8,
        top: 4,
        bottom: 10,
      ),
      child: ElevatedButton(
        style: ButtonStyle(
          padding: MaterialStateProperty.all(
            const EdgeInsets.all(4),
          ),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(
              side: BorderSide(
                color: Config.primaryColor,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          fixedSize: MaterialStateProperty.all(
            Size(
              MediaQuery.of(context).size.width,
              40,
            ),
          ),
        ),
        onPressed: isDisabled
            ? null
            : () {
                context
                    .read<AddFieldChargeBloc>()
                    .add(AddFieldChargeEvent.submit(smoId));
              },
        child: state.isSubmitLoading
            ? const CircularProgressIndicator(
                color: Colors.white,
              )
            : Text(
                LanguageEnum.fieldChargesSubmitBtn.localized(),
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget uploadSlipsButton(List<PickedFile> files) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ElevatedButton.icon(
          onPressed: () async {
            final result = await Navigator.push<PickerResult?>(
              context,
              MaterialPageRoute(
                builder: (context) => ImagePickerScreen(
                  pageTitle:
                      LanguageEnum.fieldChargesUploadSlipsTitle.localized(),
                  reqBody: {'smoId': widget.smoId},
                  module: UploadFileModule.smoCharges,
                  allowMultiple: true,
                  initialImages: files,
                ),
              ),
            );
            if (result != null) {
              if (result.message.isNotEmpty) {
                showSnackBar(result.message);
              }

              // ignore: use_build_context_synchronously
              context
                  .read<AddFieldChargeBloc>()
                  .add(AddFieldChargeEvent.addSlips(result.files));
            }
          },
          label: Text(LanguageEnum.fieldChargesUploadSlipsButton.localized() +
              (files.isEmpty ? '' : '(${files.length})')),
          icon: const Icon(Icons.upload_file),
        )
      ],
    );
  }

  Widget totalWidget(InputChargeState state) {
    var amount = 0.0;
    for (final element in state.charges) {
      amount += double.tryParse(element.amount) ?? 0.0;
    }
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(right: 10, left: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            LanguageEnum.fieldChargesTotal.localized(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '₹ $amount',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget chargesWidget({
    required List<String> chargesType,
    required int index,
    required ChargeInputModel chargeInputModel,
  }) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(
        left: 8,
        right: 8,
        top: 16,
        bottom: 10,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade300,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade500),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.only(left: 4, right: 4),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton(
                      isDense: true,
                      padding: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 4,
                      ),
                      // Initial Value
                      value: chargeInputModel.chargeType.isEmpty
                          ? null
                          : chargeInputModel.chargeType,

                      // Down Arrow Icon
                      icon: const Icon(Icons.keyboard_arrow_down),

                      // Array list of items
                      items: chargesType.map((String items) {
                        return DropdownMenuItem(
                          value: items,
                          child: Text(
                            items.replaceAll('_', ' ').capitalize(),
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }).toList(),

                      // After selecting the desired option,it will
                      // change button value to selected value
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          context.read<AddFieldChargeBloc>().add(
                                AddFieldChargeEvent.updateChargeType(
                                  index,
                                  newValue,
                                ),
                              );
                        }
                      },
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              InkWell(
                onTap: () {
                  context
                      .read<AddFieldChargeBloc>()
                      .add(AddFieldChargeEvent.removeCharge(index));
                },
                child: const Icon(Icons.delete, color: Colors.red),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  initialValue: chargeInputModel.comment,
                  maxLines: 1,
                  decoration: InputDecoration(
                    counterText: '',
                    hintText: LanguageEnum.fieldChargesCommentHint.localized(),
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (String? value) {
                    context.read<AddFieldChargeBloc>().add(
                          AddFieldChargeEvent.updateComment(index, value ?? ''),
                        );
                  },
                ),
              ),
              SizedBox(
                width: 16,
              ),
              SizedBox(
                  height: 48,
                  width: 100,
                  child: _TextFormFieldCharge(
                      amount: chargeInputModel.amount,
                      onChanged: (value) {
                        context.read<AddFieldChargeBloc>().add(
                              AddFieldChargeEvent.updateAmount(
                                  index, value ?? ''),
                            );
                      })),
            ],
          ),
        ],
      ),
    );
  }
}

class _TextFormFieldCharge extends StatefulWidget {
  const _TextFormFieldCharge(
      {required this.amount, required this.onChanged});
  final String amount;
  final ValueChanged<String> onChanged;

  @override
  State<_TextFormFieldCharge> createState() => _TextFormFieldChargeState();
}

class _TextFormFieldChargeState extends State<_TextFormFieldCharge> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    _controller.text = widget.amount;
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant _TextFormFieldCharge oldWidget) {
    _controller.text = widget.amount;
    _controller.selection = TextSelection.fromPosition(
      TextPosition(offset: _controller.text.length),
    );
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.only(
          left: 8,
          right: 8,
          top: 8,
          bottom: 8,
        ),
        border: OutlineInputBorder(),
        hintText: '0',
        label: LangText('amount', 'Amount'),
        counterStyle: TextStyle(
          height: double.minPositive,
        ),
        isDense: false,
        counterText: '',
      ),
      keyboardType: TextInputType.number,
      maxLength: 10,
      inputFormatters: Config.numberInputFilters,
      onChanged: widget.onChanged,
    );
  }
}
