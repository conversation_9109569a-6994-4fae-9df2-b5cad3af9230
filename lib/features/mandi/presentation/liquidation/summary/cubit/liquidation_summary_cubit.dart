import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_liquidation_order_by_id_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order_details.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/use_case/liquidation_order_details_usecase.dart';

part 'liquidation_summary_state.dart';
part 'liquidation_summary_cubit.freezed.dart';

@injectable
class LiquidationSummaryCubit extends Cubit<LiquidationSummaryState> {
  final LiquidationOrderDetailsUseCase _orderDetailsUseCase;
  LiquidationSummaryCubit(this._orderDetailsUseCase)
      : super(LiquidationSummaryState.initial());

  void loadLiquidationSummary(
    int smoId,
    List<Sku> skus,
  ) async {
    emit(state.copyWith(isLoading: true));
    final result = await _orderDetailsUseCase.call(smoId: smoId, skus: skus);
    final newState = result.fold(
        (left) => state.copyWith(message: left.message, isLoading: false),
        (right) => state.copyWith(liquidationSummary: right, isLoading: false));
    emit(newState);
  }

  void clearMessage() {
    emit(state.copyWith(message: null, orderToOpen: null));
  }

  void getOrder(int orderId) async {
    final orderResult =
        await GetLiquidationOrderByIdRequest(itemId: orderId).execute();
    final newState = orderResult.fold(
        (left) => state.copyWith(message: left.message),
        (right) => state.copyWith(orderToOpen: right));
    emit(newState);
  }
}
