import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/alloted_item_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/allotment_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/allotment_init_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/delivery_date_range_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/delivery_memo_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/trip_dto.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/alloted_item.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment_init.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/delivery_date_range.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/delivery_memo.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/destination_info.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/trip.dart';

AllotmentInit fromAllotmentInitDto(AllotmentInitDto dto) {
  return AllotmentInit(
    destinations: fromDestinationInfoDto(dto.destinations),
    deliveryDateRange: fromDeliveryDateRangeDto(dto.deliveryDateRange),
    deliverySlot: dto.deliverySlot,
  );
}

Map<String, List<DestinationInfo>> fromDestinationInfoDto(
  Map<String, dynamic> dto,
) {
  final newMap = dto.map((key, value) {
    if (value is List<dynamic>) {
      final newList = value.map((e) {
        if (e is Map<String, dynamic>) {
          return DestinationInfo(
            id: (e['id'] as int?) ?? -1,
            name: e['name'] as String,
            canAllocateExcess: e['canAllocateExcess'] as bool? ?? false,
          );
        }
        throw Exception();
      }).toList();
      return MapEntry(key, newList);
    }
    return MapEntry(key, <DestinationInfo>[]);
  });
  return newMap;
}

DeliveryDateRange fromDeliveryDateRangeDto(DeliveryDateRangeDto dto) {
  return DeliveryDateRange(
    past: dto.past,
    future: dto.future,
  );
}

Allotment toAllotment(AllotmentDto dto) {
  return Allotment(
    id: dto.id,
    destinationId: dto.destinationId,
    destinationType: dto.destinationType,
    smoId: dto.smoId,
    status: dto.status,
    consignmentId: dto.consignmentId,
    canRetryDeliveryMemo: false,
    deliveryMemo: toDeliveryMemo(dto.deliveryMemo),
  );
}

DeliveryMemo? toDeliveryMemo(DeliveryMemoDto? dto) {
  if (dto == null) return null;

  return DeliveryMemo(
    errorCode: dto.errorCode,
    errorMessage: dto.errorMessage,
    file: dto.file,
    status: dto.status,
  );
}

AllotedItem toAllotedItem(AllotedItemDto dto) {
  return AllotedItem(
    id: dto.id,
    allotmentId: dto.allotmentId,
    availableInventory: dto.availableInventory,
    skuId: dto.skuQuantity.skuId,
    quantity: dto.skuQuantity.quantity,
    lotSize: dto.skuQuantity.lotSize ?? 0,
    unit: dto.skuQuantity.unit,
    type: dto.skuQuantity.type,
  );
}

Trip toTrip(TripDto tripDto) {
  return Trip(
    id: tripDto.id,
    routeId: tripDto.routeId,
    status: tripDto.status,
    canDispatch: tripDto.canDispatch,
    tripStartedAt: tripDto.tripStartedAt,
    driverDetails: DriverDetail(
      name: tripDto.driverDetails?.name ?? '',
      mobile: tripDto.driverDetails?.mobile ?? '',
    ),
    vehicleDetails: VehicleDetail(
      vehicleNumber: tripDto.vehicleDetails?.vehicleNumber ?? '',
    ),
  );
}
