import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_item.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_loss.dart';
import 'package:proc2/features/mandi/domain/use_case/accept_carryforward_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_carry_forward_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';

part 'add_carry_forward_bloc.freezed.dart';
part 'add_carry_forward_event.dart';
part 'add_carry_forward_state.dart';

@injectable
class AddCarryForwardBloc
    extends Bloc<AddCarryForwardEvent, AddCarryForwardState> {
  AddCarryForwardBloc(
      this._getCarryForwardUseCase, this._acceptCarryForwardUseCase)
      : super(const AddCarryForwardState.initial()) {
    on<AddCarryForwardEvent>((event, emit) async {
      await event.when(
        started: (smoId) async {
          await _getCarryForwardUseCase(smoId).then(
            (value) => value.fold(
              (left) {
                if (left.code == 'ERROR_NO_CARRY_FORWARD') {
                  emit(
                    const AddCarryForwardState.success(
                      CarryForward(
                        id: -1,
                        bulkItems: [],
                        lotItems: [],
                      ),
                      null,
                      false,
                    ),
                  );
                } else {
                  emit(AddCarryForwardState.error(left, smoId));
                }
              },
              (right) => emit(
                AddCarryForwardState.success(right, null, false),
              ),
            ),
          );
        },
        submitCarryForward: (int smoId) async {
          await state.whenOrNull(
            success: (carryForward, message, isCtaLoading) async {
              emit(AddCarryForwardState.success(carryForward, message, true));
              await _acceptCarryForwardUseCase(
                carryForward: carryForward,
                smoId: smoId,
              ).then(
                (value) => value.fold(
                  (left) => emit(
                    AddCarryForwardState.success(
                        carryForward, left.message, false),
                  ),
                  (right) => emit(
                    AddCarryForwardState.submitted(right),
                  ),
                ),
              );
            },
          );
        },
        updateLoss: (int index, bool isBulk, List<LossInputModel> loss) {
          final carryForwardLoss = loss.map(
            (e) => CarryForwardLoss(
              lossType: e.lossType,
              lossValue: e.lossValue,
              unit: e.unit,
              comment: e.comment,
              files: e.files,
              minFileAllowed: e.minFileUploadCount,
              maxFileAllowed: e.maxFileUploadCount,
            ),
          );

          if (state is SuccessCarryForward) {
            final currentState = state as SuccessCarryForward;
            if (isBulk) {
              final updatedItem =
                  currentState.carryForward.bulkItems[index].copyWith(
                loss: carryForwardLoss.toList(),
              );
              final newList = List<CarryForwardItem>.from(
                  currentState.carryForward.bulkItems)
                ..removeAt(index)
                ..insert(index, updatedItem);
              emit(
                currentState.copyWith(
                  carryForward:
                      currentState.carryForward.copyWith(bulkItems: newList),
                ),
              );
            } else {
              final updatedItem =
                  currentState.carryForward.lotItems[index].copyWith(
                loss: carryForwardLoss.toList(),
              );
              final newList = List<CarryForwardItem>.from(
                  currentState.carryForward.lotItems)
                ..removeAt(index)
                ..insert(index, updatedItem);
              emit(
                currentState.copyWith(
                  carryForward:
                      currentState.carryForward.copyWith(lotItems: newList),
                ),
              );
            }
          }
        },
        clearError: () {
          if (state is SuccessCarryForward) {
            final currentState = state as SuccessCarryForward;
            emit(currentState.copyWith(message: null));
          }
        },
        updateExcessOrShortage:
            (int index, bool isBulk, List<CarryForwardLoss> loss) {
          if (state is SuccessCarryForward) {
            final currentState = state as SuccessCarryForward;
            if (isBulk) {
              final updatedItem =
                  currentState.carryForward.bulkItems[index].copyWith(
                shortOrExcess: loss,
              );
              final newList = List<CarryForwardItem>.from(
                  currentState.carryForward.bulkItems)
                ..removeAt(index)
                ..insert(index, updatedItem);
              emit(
                currentState.copyWith(
                  carryForward:
                      currentState.carryForward.copyWith(bulkItems: newList),
                ),
              );
            } else {
              final updatedItem =
                  currentState.carryForward.lotItems[index].copyWith(
                shortOrExcess: loss,
              );
              final newList = List<CarryForwardItem>.from(
                  currentState.carryForward.lotItems)
                ..removeAt(index)
                ..insert(index, updatedItem);
              emit(
                currentState.copyWith(
                  carryForward:
                      currentState.carryForward.copyWith(lotItems: newList),
                ),
              );
            }
          }
        },
      );
    });
  }

  final GetCarryForwardUseCase _getCarryForwardUseCase;
  final AcceptCarryForwardUseCase _acceptCarryForwardUseCase;
}
