import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/features/admin/otp_dashboard/cubit/otp_dashboard_cubit.dart';

class OtpDashboardPage extends StatefulWidget {
  const OtpDashboardPage({super.key});

  @override
  State<OtpDashboardPage> createState() => _OtpDashboardPageState();
}

class _OtpDashboardPageState extends State<OtpDashboardPage> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OtpDashboardCubit, OtpDashboardState>(
        builder: (context, state) {
      return SafeArea(
          child: Scaffold(
        appBar: AppBar(
          title: Text("OTP Dashboard"),
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Visibility(
              visible: state.otp != null,
              child: Text(
                "OTP :   ${state.otp}",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 25),
              ),
            ),
            const SizedBox(height: 100),
            Padding(
              padding: const EdgeInsets.all(20),
              child: TextFormField(
                keyboardType: TextInputType.phone,
                maxLength: 10, // Adjust based on your requirement
                onChanged: (value) {
                  context.read<OtpDashboardCubit>().setMobile(value);
                },
                decoration: InputDecoration(
                  labelText: 'Phone Number',
                  prefixIcon: const Icon(Icons.phone_android),
                  hintText: 'Enter mobile number',
                  counterText: '', // hides the character counter below
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.blue),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            if (state.message != null)
              Text(
                state.message ?? "",
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            const SizedBox(height: 20),
            ElevatedButton(
                onPressed: state.isLoading
                    ? null
                    : () {
                        context.read<OtpDashboardCubit>().getOTP();
                      },
                child: state.isLoading
                    ? SizedBox(
                        height: 25,
                        width: 25,
                        child: Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2.5,
                          ),
                        ),
                      )
                    : Text("Submit")),
          ],
        ),
      ));
    });
  }
}
