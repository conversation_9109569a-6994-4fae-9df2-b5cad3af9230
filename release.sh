#!/bin/bash

# Check if the environment parameter is provided
if [ $# -eq 0 ]; then
  echo "Please provide the environment parameter: prod or dev"
  exit 1
fi

# Check if the environment parameter is valid
if [ "$1" != "prod" ] && [ "$1" != "dev" ]; then
  echo "Invalid environment parameter. Allowed values: prod or dev"
  exit 1
fi

current_date=$(date +"%Y-%m-%d-%H-%M-%S")

# Read the flutter version from the .fvmrc file
FLUTTER_VERSION=$(grep '"flutter"' .fvmrc | awk -F '"' '{print $4}')
echo "Flutter version: $FLUTTER_VERSION"

# Set build config based on environment
if [ "$1" == "prod" ]; then
  entry_point="lib/main_production.dart"
  flv="production"
  slack_env="PROD Flow"
  dirPath="./build/app/outputs/apk/production/release"
else
  entry_point="lib/main_development.dart"
  flv="development"
  slack_env="TEST Flow"
  dirPath="./build/app/outputs/apk/development/release"
fi

# Build APK
if [ "$2" == "--shorebird" ]; then
  echo "Releasing to shorebird"
  shorebird release android --target "$entry_point" --flavor "$flv" --flutter-version="$FLUTTER_VERSION"
else
  echo "Building apk"
  fvm flutter build apk --release --target "$entry_point" --flavor "$flv"
  # Rename APK
  echo "Build completed. Renaming the apk file"
  version=$(grep 'version:' pubspec.yaml | cut -d ' ' -f 2)
  apk_old="$dirPath/app-$flv-release.apk"
  apk_new="$dirPath/flow-$current_date-$flv-$version-release.apk"
  mv "$apk_old" "$apk_new"
fi


# Upload to Slack
echo "Uploading APK to Slack: $slack_env"
python3 auto_upload_apk_to_slack.py "$slack_env" "Flow"
