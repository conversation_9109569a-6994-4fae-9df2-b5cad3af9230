import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocating/input_model/allocate_item_input_model.dart';

@injectable
class SubmitAllotmentUseCase {
  SubmitAllotmentUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, String>> call({
    required List<AllocateItemInputModel> newItems,
    required List<AllocateItemInputModel> updatedItems,
    required List<int> deletedItems,
    required bool isSubmit,
    required int allotmentId,
  }) async {
    final bodyMap = {
      'allotmentId': allotmentId,
      'forSubmit': isSubmit,
      'itemsToBeDeleted': deletedItems,
      'itemsToBeUpdated': updatedItems
          .map(
            (e) => {
              'id': e.id!,
              'quantity': e.allotmentQuantity.toDouble(),
            },
          )
          .toList(),
      'itemToBeAdded': newItems
          .map(
            (e) => {
              'skuId': e.skuId,
              'quantity': e.allotmentQuantity.toDouble(),
              'type': e.type,
              'unit': e.unit,
              'lotSize': e.lotSize,
            },
          )
          .toList(),
    };

    return _mandiRepository.submitAllotment(bodyMap);
  }
}
