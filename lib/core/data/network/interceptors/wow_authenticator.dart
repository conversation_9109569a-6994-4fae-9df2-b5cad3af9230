import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:chopper/chopper.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/data/preference/preference.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/features/auth/domain/repository/auth_repository.dart';
import 'package:proc2/features/auth/domain/use_case/login_use_case.dart';
import 'package:synchronized/synchronized.dart';

@Singleton(as: Authenticator)
class WowAuthenticator extends Authenticator {
  final Preference preference;
  final Lock _lock = Lock();

  WowAuthenticator(this.preference);

  @override
  // ignore: strict_raw_type
  FutureOr<Request?> authenticate(
    Request request,
    Response<dynamic> response, [
    Request? originalRequest,
  ]) async {
    var isAuthExpired = false;
    try {
      final jsonBody =
          json.decode(response.body as String) as Map<String, dynamic>;
      final errorCode = (jsonBody['error'] as Map<String, dynamic>)['code'];
      isAuthExpired = errorCode == 'AUTHENTICATION_FAILED' ||
          errorCode == 'ERROR_AUTH_FAILED';
    } catch (_) {}

    if (response.statusCode == HttpStatus.unauthorized || isAuthExpired) {
      if (_shouldSkipRetry(request)) {
        return null;
      }

      final hasNewTokenGeneratedRequest =
          await _retryWithNewTokenIfExist(request);
      if (hasNewTokenGeneratedRequest != null) {
        return hasNewTokenGeneratedRequest;
      }

      // Ensure proper synchronization
      return await _retryLock(request);
    }

    return null;
  }

  bool _shouldSkipRetry(Request request) {
    final path = request.url.path;
    if (path.contains('login')) {
      return true;
    }
    return false;
  }

  Future<Request?> _retryLock(Request request) async {
    return await _lock.synchronized(() async {
      final hasNewTokenGeneratedRequest =
          await _retryWithNewTokenIfExist(request);
      if (hasNewTokenGeneratedRequest != null) {
        return hasNewTokenGeneratedRequest;
      }

      // Remove the token from the preference
      preference.remove(Preference.authTokenKey);

      // Generate new token
      final fireAuth = FirebaseAuth.instance;

      // Is anonymous user or user is not logged in (mostly for number users)
      if (fireAuth.currentUser == null ||
          fireAuth.currentUser!.isAnonymous == true ||
          fireAuth.currentUser!.email == null) {
        AuthRepository authRepository = di.get();
        await authRepository.logOut();
        return null;
      }
      final loginUsecase = di.get<LoginUseCase>();
      final result = await loginUsecase.call();

      return result.fold(
        (l) => null,
        (r) => request.copyWith(
          headers: {
            ...request.headers,
            HttpHeaders.authorizationHeader: 'Bearer ${r.jwt}',
          },
        ),
      );
    });
  }

  Future<Request?> _retryWithNewTokenIfExist(Request request) async {
    final token = preference.get<String>(Preference.authTokenKey);
    if (token == null) return null;
    final headerToken = request.headers[HttpHeaders.authorizationHeader]
        ?.split('Bearer')[1]
        .trim();
    if (headerToken == token) return null;
    return request.copyWith(headers: {
      ...request.headers,
      HttpHeaders.authorizationHeader: 'Bearer $token',
    });
  }
}
