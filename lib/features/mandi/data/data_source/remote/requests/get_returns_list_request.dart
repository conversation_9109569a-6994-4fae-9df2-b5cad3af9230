import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

class GetReturnsListRequest extends BaseRequest<dynamic, List<Returns>> {
  final int smoId;
  final bool isWastageReturns;

  GetReturnsListRequest({
    required this.smoId,
    required this.isWastageReturns,
  });

  @override
  String getPath() => 'returns/getAllReturnOrders';

  @override
  Map<String, dynamic>? getQuery() {
    return {'smoId': smoId};
  }

  @override
  List<Returns> mapper(data) {
    if (data is List) {
      return data
          .map((e) => e as Map<String, dynamic>)
          .where((e) =>
              e['inventoryType'] ==
              (isWastageReturns ? 'WASTAGE' : 'INVENTORY'))
          .map((e) => Returns.fromJson(e))
          .toList();
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
