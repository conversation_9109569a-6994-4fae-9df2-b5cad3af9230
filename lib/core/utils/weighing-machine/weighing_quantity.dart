import 'package:proc2/core/utils/extensions.dart';

class WeighingQuantity {
  final String? value;
  final String? source;

  double? get quantity => value?.toDouble().asString().toDouble();
  bool get isPositive => (quantity ?? 0) >= 0;

  WeighingQuantity._({required this.value, required this.source});
  factory WeighingQuantity.auto(String? value) {
    return WeighingQuantity._(value: value, source: 'auto');
  }

  factory WeighingQuantity.manual(String? value) {
    return WeighingQuantity._(value: value, source: 'manual');
  }

  factory WeighingQuantity.weighingMachine(String? value) {
    return WeighingQuantity._(value: value, source: 'weighingMachine');
  }

  factory WeighingQuantity.noSouce(String? value) {
    return WeighingQuantity._(
      value: value,
      source: null,
    );
  }

  WeighingQuantity copyWith({
    String? value,
    String? source,
  }) {
    return WeighingQuantity._(
      value: value ?? this.value,
      source: source ?? this.source,
    );
  }
}
