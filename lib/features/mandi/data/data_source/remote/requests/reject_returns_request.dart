import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

class RejectReturnsRequest extends BaseRequest<dynamic, String> {
  final String returnOrderId;
  final int smoId;
  final DriversDetails? driversDetails;
  final String returnType;
  final bool isWastageReturns;
  final int createdAt;

  RejectReturnsRequest({
    required this.returnOrderId,
    required this.smoId,
    this.driversDetails,
    required this.returnType,
    required this.isWastageReturns,
    required this.createdAt,
  });

  @override
  String getPath() => 'returns/submitReturnOrder';

  @override
  Map<String, dynamic> getBody() {
    return {
      'createdAt': createdAt,
      'inventoryType': isWastageReturns ? 'WASTAGE' : 'INVENTORY',
      'returnOrderId': returnOrderId,
      'smoId': smoId,
      'status': 'REJECTED',
      "driversDetails": {
        "driverName": driversDetails?.driverName,
        "driverMobile": driversDetails?.driverMobile,
        "vehicleNumber": driversDetails?.vehicleNumber,
      },
      'returnType': returnType,
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
