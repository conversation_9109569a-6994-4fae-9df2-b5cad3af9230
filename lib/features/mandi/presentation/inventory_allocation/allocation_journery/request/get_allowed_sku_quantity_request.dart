import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';

class GetAllowedSkuQuantityRequest
    extends BaseRequest<dynamic, List<InventoryItem>> {
  final int allocationId;

  GetAllowedSkuQuantityRequest(this.allocationId);

  @override
  String getPath() {
    return 'allotments/${this.allocationId}/allowed-skus';
  }

  @override
  List<InventoryItem> mapper(data) {
    if (data is Map<String, dynamic>) {
      return (data['allowedSkus'] as List<dynamic>)
          .map((e) => InventoryItem.fromJson(e))
          .toList();
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
