import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/utils/location_repository_utils.dart';
import 'package:proc2/features/mandi/presentation/attendance/cubit/phone_number_cache.dart';
import 'package:proc2/features/mandi/presentation/attendance/model/assigned_user.dart';
import 'package:proc2/features/mandi/presentation/attendance/request/assign_user_request.dart';
import 'package:proc2/features/mandi/presentation/attendance/request/get_assigned_users_request.dart';
import 'package:proc2/features/mandi/presentation/attendance/request/get_user_name_by_phone_request.dart';
import 'package:proc2/features/mandi/presentation/attendance/request/remove_assigned_user_request.dart';

part 'attendance_state.dart';

@injectable
class AttendanceCubit extends Cubit<AttendanceState> {
  AttendanceCubit(this._phoneNumberCache) : super(AttendanceState.initial());
  final PhoneNumberCache _phoneNumberCache;

  void init({required int smoId}) async {
    emit(state.copyWith(smoId: smoId));
    await getAssignedeUsers();
  }

  Future<void> getAssignedeUsers() async {
    emit(state.copyWith(isLoading: true));
    final result = await GetAssignedUsersRequest(smoId: state.smoId).execute();
    result.fold(
      (error) => emit(state.copyWith(isLoading: false, message: error.message)),
      (data) => emit(state.copyWith(isLoading: false, assignedUsers: data)),
    );
  }

  void clearMessage() {
    emit(state.copyWith(message: null));
  }

  bool _isAssigningUser = false;
  Future<bool> assignUser({
    required String phoneNumber,
    String? name,
  }) async {
    if (_isAssigningUser) return false;
    _isAssigningUser = true;
    final location = await getLocation(Duration(minutes: 5))
        .timeout(Duration(seconds: 10), onTimeout: () {
      return null;
    });
    final result = await (name == null
            ? AssignUserRequest.assignUser(
                smoId: state.smoId,
                phoneNumber: phoneNumber,
                location: location,
              )
            : AssignUserRequest.createAndAssignUser(
                smoId: state.smoId,
                phoneNumber: phoneNumber,
                name: name,
                location: location,
              ))
        .execute();
    _isAssigningUser = false;
    await _phoneNumberCache.addPhoneNumber(phoneNumber);
    return result.fold(
      (error) {
        emit(state.copyWith(message: error.message));
        return false;
      },
      (data) {
        getAssignedeUsers();
        return true;
      },
    );
  }

  Future<List<String>?> getPhoneNumberSuggestion({
    required String query,
  }) async {
    if (query.trim().isEmpty) return null;
    final allNumbers = await _phoneNumberCache.getPhoneNumbers();
    return allNumbers
        .where((element) => element.contains(query))
        .toList(growable: false);
  }

  void removeAssignedUser({
    required AssignedUser user,
  }) async {
    final originalUsers = state.assignedUsers;

    final updatedUsers = state.assignedUsers
        .where((element) => element.attendanceRoleId != user.attendanceRoleId)
        .toList();
    emit(state.copyWith(assignedUsers: updatedUsers));

    final result = await RemoveAssignedUserRequest(
      attendanceRoleId: user.attendanceRoleId,
    ).execute();

    result.fold(
      (error) => emit(state.copyWith(
        message: error.message,
        assignedUsers: originalUsers,
      )),
      (data) {
        emit(state.copyWith(
          message: data,
        ));
        getAssignedeUsers();
      },
    );
  }

  Future<void> getUserByPhone({required String phoneNumber}) async {
    final result =
        await GetUserNameByPhoneRequest(phoneNumber: phoneNumber).execute();
    result.fold(
      (error) => emit(state.copyWith(message: error.message)),
      (data) {
        emit(state.copyWith(
          message: data,
        ));
      },
    );
  }
}
