import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo/menu_config_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo/smo_detail_dto.dart';

part 'smo_dto.freezed.dart';
part 'smo_dto.g.dart';

@freezed
class SmoDto with _$SmoDto {
  const factory SmoDto({
    SmoDetailsDto? smoDetails,
    List<String>? roles,
    MenuConfigDto? menuConfig,
  }) = _SmoDto;

  @override
  factory SmoDto.fromJson(Map<String, dynamic> json) => _$SmoDtoFromJson(json);
}
