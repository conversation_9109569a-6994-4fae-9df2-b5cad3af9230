import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/return_download/return_download_entity.dart';

class GetReturnDownloadRequest
    extends BaseRequest<dynamic, List<ReturnDownloadEntity>> {
  final int mandiId;
  final int startDateTime;
  final int endDateTime;
  final String type;

  GetReturnDownloadRequest(
      {required this.mandiId,
      required this.startDateTime,
      required this.endDateTime,
      required this.type});

  @override
  String getPath() => 'returns/getAllReturnsInTimestamp';

  @override
  Map<String, dynamic> getBody() {
    return {
      "mandiId": mandiId,
      "startDateTime": startDateTime,
      "endDateTime": endDateTime,
      "type": type,
    };
  }

  @override
  List<ReturnDownloadEntity> mapper(data) {
    if (data == null) {
      throw Exception('Received null data');
    }

    if (data is! List) {
      throw Exception(
          'Invalid data type: Expected List, got ${data.runtimeType}');
    }

    final Map<int, ReturnDownloadEntity> mergedMap = {};

    for (final item in data) {
      if (item is! Map<String, dynamic>) continue;

      final entity = ReturnDownloadEntity.fromJson(item);
      if (mergedMap.containsKey(entity.skuId)) {
        final existing = mergedMap[entity.skuId]!;
        mergedMap[entity.skuId] = ReturnDownloadEntity(
          skuId: entity.skuId,
          receivedQuantity: existing.receivedQuantity + entity.receivedQuantity,
          returnOrderIds:
              {...existing.returnOrderIds, ...entity.returnOrderIds}.toList(),
        );
      } else {
        mergedMap[entity.skuId] = entity;
      }
    }

    return mergedMap.values.toList();
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
