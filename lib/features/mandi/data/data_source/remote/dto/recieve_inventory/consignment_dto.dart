import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/incoming_stock_item_dto.dart';

part 'consignment_dto.freezed.dart';
part 'consignment_dto.g.dart';

@freezed
class ConsignmentDto with _$ConsignmentDto {
  const factory ConsignmentDto({
    required String consignmentId,
    required int incomingStockId,
    required List<IncomingStockItemDto> incomingStockItems,
    required int? receivedFromMandi,
    @Default([]) List<TripDetailsDto> tripDetails,
  }) = _ConsignmentDto;

  @override
  factory ConsignmentDto.fromJson(Map<String, dynamic> json) =>
      _$ConsignmentDtoFromJson(json);
}

@freezed
class TripDetailsDto with _$TripDetailsDto {
  const factory TripDetailsDto({
    String? driverName,
    String? driverPhone,
    String? vehicleNo,
    int? expectedDeliveryTime,
  }) = _TripDetailsDto;
  @override
  factory TripDetailsDto.fromJson(Map<String, dynamic> json) =>
      _$TripDetailsDtoFromJson(json);
}
