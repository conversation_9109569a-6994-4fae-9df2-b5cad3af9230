import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_base.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/row_item_value.dart';

class WeightCapturePopupAllocationDistribution extends StatefulWidget {
  const WeightCapturePopupAllocationDistribution({
    super.key,
    required this.isBulkKg,
    required this.isManualEditAllowed,
    required this.initialWeight,
    required this.terminalId,
    required this.allocatedQuantity,
  });
  final bool isBulkKg;
  final bool isManualEditAllowed;
  final double initialWeight;
  final String terminalId;
  final double allocatedQuantity;

  @override
  State<WeightCapturePopupAllocationDistribution> createState() =>
      _WeightCapturePopupAllocationDistributionState();
}

class _WeightCapturePopupAllocationDistributionState
    extends State<WeightCapturePopupAllocationDistribution> {
  double totalWeight = 0;

  @override
  void initState() {
    totalWeight = widget.initialWeight;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WeighingCapturePopupBase(
      title: widget.isBulkKg
          ? 'captureWeight'.tr('Capture Weight')
          : 'updateWeight'.tr('Update Weight'),
      initalWeight: widget.initialWeight,
      allowManualEdit: widget.isBulkKg ? widget.isManualEditAllowed : true,
      inputFormatters: widget.isBulkKg
          ? Config.numberInputFilters
          : Config.numberInputFiltersInt,
      enableWeighingMachine: widget.isBulkKg,
      headers: [
        RowItemValue(
          label: 'terminalId'.tr('Terminal Id'),
          value: widget.terminalId,
        ),
        SizedBox(
          height: 8,
        ),
        Container(
          color: Colors.yellow.shade200,
          padding: EdgeInsets.all(8),
          child: RowItemValue(
            label: 'pendingQuantity'.tr('Pending Qty'),
            value: (widget.allocatedQuantity - totalWeight).asString(),
          ),
        ),
        SizedBox(
          height: 8,
        ),
      ],
      onTotalWeightChanged: (weight) {
        setState(() {
          totalWeight = weight.quantity ?? 0;
        });
      },
    );
  }
}

class WeightCapturePopupProcurementReceive extends StatelessWidget {
  const WeightCapturePopupProcurementReceive({
    super.key,
    required this.isBulkKg,
    required this.isManualEditAllowed,
    required this.initialWeight,
    required this.skuName,
    required this.unitInfo,
  });
  final bool isBulkKg;
  final bool isManualEditAllowed;
  final double initialWeight;
  final String skuName;
  final String unitInfo;

  @override
  Widget build(BuildContext context) {
    return WeighingCapturePopupBase(
      title: isBulkKg
          ? 'captureWeight'.tr('Capture Weight')
          : 'updateWeight'.tr('Update Weight'),
      initalWeight: initialWeight,
      allowManualEdit: isBulkKg ? isManualEditAllowed : true,
      inputFormatters:
          isBulkKg ? Config.numberInputFilters : Config.numberInputFiltersInt,
      enableWeighingMachine: isBulkKg,
      headers: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              skuName,
              style: TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
            ),
            SizedBox(
              width: 6,
            ),
            Text(
              unitInfo,
              style: TextStyle(
                fontSize: 13,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 8,
        ),
      ],
    );
  }
}

class WeightCapturePopupReturnsReceive extends StatelessWidget {
  const WeightCapturePopupReturnsReceive({
    super.key,
    required this.isBulkKg,
    required this.isManualEditAllowed,
    required this.initialWeight,
    required this.skuName,
    required this.unitInfo,
    required this.image,
  });
  final bool isBulkKg;
  final bool isManualEditAllowed;
  final double initialWeight;
  final String skuName;
  final String unitInfo;
  final String? image;

  @override
  Widget build(BuildContext context) {
    return WeighingCapturePopupBase(
      title: isBulkKg
          ? 'captureWeight'.tr('Capture Weight')
          : 'updateWeight'.tr('Update Weight'),
      initalWeight: initialWeight,
      allowManualEdit: isBulkKg ? isManualEditAllowed : true,
      inputFormatters:
          isBulkKg ? Config.numberInputFilters : Config.numberInputFiltersInt,
      enableWeighingMachine: isBulkKg,
      headers: [
        if (image != null) ...[
          Image.network(
            image!,
            height: 48,
          ),
          SizedBox(
            height: 16,
          ),
        ],
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: AutoSizeText(
                skuName,
                style: TextStyle(fontWeight: FontWeight.w500),
                maxFontSize: 15,
              ),
            ),
            SizedBox(
              width: 6,
            ),
            Text(
              unitInfo,
              style: TextStyle(
                fontSize: 13,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 8,
        ),
      ],
    );
  }
}
