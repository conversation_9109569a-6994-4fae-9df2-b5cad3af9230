import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/domain/use_case/get_allotment_init_usecase.dart';

part 'view_supply_order_state.dart';
part 'view_supply_order_cubit.freezed.dart';

@injectable
class ViewSupplyOrderCubit extends Cubit<ViewSupplyOrderState> {
  final MandiRepository _mandiRepository;
  final GetAllotmentInitUseCase _allotmentInitUseCase;
  ViewSupplyOrderCubit(this._mandiRepository, this._allotmentInitUseCase)
      : super(ViewSupplyOrderState());

  void init(int smoId) async {
    final result = await _allotmentInitUseCase(smoId);
    final newState =
        result.fold((left) => state.copyWith(message: left.message), (right) {
      final customerGroups =
          right.destinations['customers']?.map((e) => e.name).toList() ?? [];
      return state.copyWith(
        customerGroups: customerGroups,
        deliverySlots: right.deliverySlot,
      );
    });
    emit(newState);
  }

  void load({
    required DateTime deliveryDate,
    required String deliverySlot,
    required String customerGroup,
  }) async {
    emit(state.copyWith(
      supplyOrders: null,
      isSearchLoading: true,
    ));

    final result = await _mandiRepository.getSupplyOrders(
        deliveryDate: deliveryDate,
        deliverySlot: deliverySlot,
        customerGroup: customerGroup,
        forAdmin: true);
    final newState = result.fold(
      (left) => state.copyWith(
        message: left.message,
        isSearchLoading: false,
        supplyOrders: null,
      ),
      (right) => state.copyWith(
        supplyOrders: right.supplyOrders,
        isSearchLoading: false,
        allSelected: false,
        showEdit: false,
      ),
    );
    emit(newState);
  }

  void clearMessage() {
    emit((state as _Input).copyWith(message: null));
  }

  void toggleSelectAll(bool isSelected) {
    final currentState = state;
    if (currentState is _Input) {
      final updatedSupplyOrders = currentState.supplyOrders
          ?.map((e) => e.isPreOrder ? e : e.copyWith(isChecked: isSelected))
          .toList();
      final allNonPreorder = updatedSupplyOrders?.where((e) => !e.isPreOrder);
      final allSelected = allNonPreorder?.every((e) => e.isChecked) ?? false;
      emit(
        currentState.copyWith(
          allSelected: allSelected && (allNonPreorder?.isNotEmpty ?? false),
          supplyOrders: updatedSupplyOrders,
          showEdit:
              !isSelected ? isSelected : (allNonPreorder?.isNotEmpty ?? false),
        ),
      );
    }
  }

  void toggleSelect({
    required int index,
    required bool isSelected,
  }) {
    final currentState = state;
    if (currentState is _Input) {
      bool allSelected = true;
      bool anySelected = false;
      final updatedSupplyOrders = currentState.supplyOrders?.indexed.map((e) {
        final order =
            e.$1 == index ? e.$2.copyWith(isChecked: isSelected) : e.$2;
        allSelected = allSelected && order.isChecked;
        anySelected = anySelected || order.isChecked;
        return order;
      }).toList();
      emit(
        currentState.copyWith(
          allSelected: allSelected,
          supplyOrders: updatedSupplyOrders,
          showEdit: anySelected,
        ),
      );
    }
  }

  void updateDeliveryDate(DateTime? date) {
    if (date != null) {
      emit(state.copyWith(deliveryDate: date));
    }
  }

  void updateDeliverySlot(String? value) {
    emit(state.copyWith(deliverySlot: value));
  }

  void updateCustomerGroup(String? value) {
    emit(state.copyWith(selectedCustomerGroup: value));
  }
}
