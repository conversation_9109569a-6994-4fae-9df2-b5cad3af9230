import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';

class GetSmoConfigRequest extends BaseRequest<dynamic, SmoConfig> {
  final int smoId;

  GetSmoConfigRequest({required this.smoId});

  @override
  String getPath() => 'smo/getConfigs/smoId/$smoId';

  @override
  SmoConfig mapper(data) {
    if (data is Map<String, dynamic>) {
      return SmoConfig.fromJson(data);
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
