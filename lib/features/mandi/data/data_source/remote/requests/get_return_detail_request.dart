import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

class GetReturnDetailRequest extends BaseRequest<dynamic, List<ReturnItem>> {
  final String returnOrderId;
  final int smoId;

  GetReturnDetailRequest({
    required this.returnOrderId,
    required this.smoId,
  });

  @override
  Map<String, dynamic>? getQuery() {
    return {
      'returnOrderId': returnOrderId,
      'smoId': smoId,
    };
  }

  @override
  String getPath() => 'returns/getOrderDetails';

  @override
  List<ReturnItem> mapper(data) {
    if (data is Map<String, dynamic>) {
      final list = data['items'] as List<dynamic>;
      return list.map((e) => ReturnItem.fromJson(e)).toList();
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
