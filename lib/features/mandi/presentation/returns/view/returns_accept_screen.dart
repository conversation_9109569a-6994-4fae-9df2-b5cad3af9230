import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/presentation/widgets/weight_summary_card.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/returns/cubit/returns_submit_cubit.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class ReturnsAcceptScreen extends StatefulWidget {
  final int smoId;
  final int mandiId;
  final bool isHistory;
  final bool isWastageReturns;
  final bool isEditOnlyFromWeighingMachine;
  final bool hideSystemQuantity;
  final ReturnsConfig config;

  const ReturnsAcceptScreen({
    super.key,
    required this.smoId,
    required this.mandiId,
    required this.isHistory,
    required this.isWastageReturns,
    required this.isEditOnlyFromWeighingMachine,
    required this.hideSystemQuantity,
    required this.config,
  });

  @override
  State<ReturnsAcceptScreen> createState() => _ReturnsAcceptScreenState();
}

class _ReturnsAcceptScreenState extends State<ReturnsAcceptScreen> {
  int? _createdAtDate;
  String? _subHeading;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _createdAtDate = context.read<ReturnsSubmitCubit>().state.createdAt;
      _subHeading = context.read<ReturnsSubmitCubit>().state.subHeading;
      setState(() {});
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.isHistory
                    ? (widget.isWastageReturns
                        ? 'wastageReturnsHistoryAcceptScreen.title'
                            .tr('Wastage Return: History')
                        : 'returnsHistoryAcceptScreen.title'
                            .tr('Return: History'))
                    : (widget.isWastageReturns
                        ? 'wastageReturnsAcceptScreen.title'
                            .tr('Accept Wastage Returns')
                        : 'returnsAcceptScreen.title'.tr('Accept Returns')),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (_createdAtDate != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Text(
                        _createdAtDate!.toDate(
                          'dd MMM | hh:mm a',
                          skipTimezone: !widget.isHistory,
                        ),
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w400,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  if (_subHeading != null)
                    Text(
                      _subHeading!,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                      ),
                    ),
                ],
              )
            ],
          ),
          centerTitle: false,
        ),
        body: BlocConsumer<ReturnsSubmitCubit, ReturnsSubmitState>(
          listener: (context, state) {
            final shouldPop = state.shouldPop;
            final message = state.message;
            if (message != null) {
              showSnackBar(message);
              context.read<ReturnsSubmitCubit>().clearMessage();
            }
            if (shouldPop) {
              context.pop();
            }
          },
          builder: (context, state) {
            if (state.isLoading)
              return Center(
                child: CircularProgressIndicator(),
              );
            if (state.errorMessage != null) {
              return ErrorScreen(
                onPressed: () async {
                  await context.read<ReturnsSubmitCubit>().getReturnDetails(
                        state.returnOrderId,
                        widget.smoId,
                      );
                },
                message: state.errorMessage!,
              );
            }
            if (state.items.isEmpty)
              return EmptyScreen(
                message: 'returnsAcceptScreen.noItemsFoundMessage'.tr(
                  'No Return Items Found!',
                ),
              );

            final isCtaActive = !state.items
                .any((e) => e.any((e) => e.acceptedQuantity.isEmpty));
            return Column(
              children: [
                Visibility(
                  visible: !widget.isHistory && !widget.hideSystemQuantity,
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: ElevatedButton.icon(
                        onPressed: () {
                          context.read<ReturnsSubmitCubit>().copyAll();
                        },
                        icon: Icon(Icons.copy),
                        label: Text('returns.copyAll'.tr('Copy All')),
                      ),
                    ),
                  ),
                ),
                Container(
                  color: Colors.grey[300],
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8.0,
                      vertical: 4.0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 3,
                          child: LangText(
                            'unit',
                            'Unit',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 4,
                        ),
                        if (!widget.hideSystemQuantity) ...[
                          Expanded(
                            flex: 2,
                            child: Text(
                              widget.isHistory
                                  ? getLangText('returnedQty', 'Returned Qty')
                                  : getLangText(
                                      'availableQty',
                                      'Available Qty',
                                    ),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                        ],
                        Expanded(
                          flex: 2,
                          child: Text(
                            widget.isHistory
                                ? getLangText('acceptedQty', 'Accepted Qty')
                                : getLangText('recQty', 'Rec Qty'),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ),
                        SizedBox(width: 8),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Scrollbar(
                    child: ListView.builder(
                      itemCount: state.items.length,
                      itemBuilder: (context, index) {
                        return skuCard(
                          state.items[index],
                          index,
                          !state.isCtaLoading && widget.isWastageReturns,
                        );
                      },
                    ),
                  ),
                ),
                if (state.summaryItems.isNotEmpty)
                  WeightSummaryCard(
                    items: state.summaryItems,
                    padding: EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                  ),
                if (!widget.isHistory)
                  WStickyBottomCta(
                    isEnabled: isCtaActive && !state.isCtaLoading,
                    isLoading: state.isCtaLoading,
                    icon: Icons.check,
                    label: LangText('returnsAcceptScreen.ctaLabel', 'Submit'),
                    onPressed: () async {
                      final shouldSubmit = await context.showAlertDialog(
                            title: 'returnsAcceptScreen.submitPopupTitle'
                                .tr('Submit?'),
                            message:
                                'returnsAcceptScreen.submitPopupMessage'.tr(
                              'Are you sure you want to submit?\nYou can\'t change it later.',
                            ),
                          ) ??
                          false;
                      if (shouldSubmit) {
                        context.read<ReturnsSubmitCubit>().submit(
                              widget.smoId,
                              isWastageReturns: widget.isWastageReturns,
                            );
                      }
                    },
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  void showOutOfVariationPopup({
    required int index,
    required int i,
    required ReturnItem item,
    required String skuName,
    required String? skuImage,
  }) async {
    final shouldShowDialog = await context.showAlertDialog(
            title: 'returnsAccept.validationVariationPopup'.tr('Are you sure?'),
            message: 'returnsAccept.validationVariationMessage'
                .tr('The value is out of accepted range. Please confirm!')) ??
        false;

    if (!shouldShowDialog) {
      context.read<ReturnsSubmitCubit>().updateQty(
            index,
            i,
            '',
            null,
          );
      return;
    }
    final value = await showDialog<WeighingQuantity?>(
      context: context,
      builder: (_) => WeightCapturePopupReturnsReceive(
        isBulkKg: item.skuQuantity.isBulkKg,
        isManualEditAllowed: !widget.isEditOnlyFromWeighingMachine,
        initialWeight: item.acceptedQuantity.toDouble().asString().toDouble(),
        skuName: skuName,
        unitInfo: item.skuQuantity.unitInfo,
        image: skuImage,
      ),
    );
    if (value != null) {
      context.read<ReturnsSubmitCubit>().updateQty(
            index,
            i,
            value.value ?? '',
            value.source,
          );
    }
  }

  Widget skuCard(
    List<ReturnItem> items,
    int index,
    bool isEnabled,
  ) {
    final sku = getSKU(context, skuID: items.first.skuQuantity.skuId);
    final skuName = sku.name;
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 4,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Image.network(
                  sku.image,
                  width: 48,
                  height: 48,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 26,
                    );
                  },
                ),
                SizedBox(
                  width: 16,
                ),
                Text(
                  skuName,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            for (int i = 0; i < items.length; i++)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        // 'Lots - 2.5kg',
                        items[i].skuQuantity.getUnitString(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    if (!widget.hideSystemQuantity) ...[
                      Expanded(
                        flex: 2,
                        child: Text(
                          items[i].skuQuantity.quantity?.asString() ?? '-',
                          style: TextStyle(
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(
                        width: 4,
                      ),
                    ],
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          right: 8.0,
                          left: 16,
                        ),
                        child: widget.isHistory
                            ? Text(
                                items[i].receivedQuantity.asString(),
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              )
                            : SizedBox(
                                width: 100,
                                child: WeighingQuantityButton(
                                  isReadOnly: !isEnabled,
                                  label: items[i].acceptedQuantity,
                                  popupBuilder: () {
                                    return WeightCapturePopupReturnsReceive(
                                      isBulkKg: items[i].skuQuantity.isBulkKg,
                                      isManualEditAllowed:
                                          !widget.isEditOnlyFromWeighingMachine,
                                      initialWeight: items[i]
                                          .acceptedQuantity
                                          .toDouble()
                                          .asString()
                                          .toDouble(),
                                      skuName: skuName,
                                      unitInfo: items[i].skuQuantity.unitInfo,
                                      image: sku.image,
                                    );
                                  },
                                  onChange: (quantity) {
                                    if (quantity != null) {
                                      final actualValue =
                                          quantity.value?.toDouble() ?? 0;
                                      final availableQty = items[i]
                                              .skuQuantity
                                              .quantity
                                              ?.toDouble() ??
                                          actualValue;
                                      final variation = widget
                                          .config.validationVariation
                                          .getVariationValue(
                                              type: items[i].skuQuantity.type,
                                              unit: items[i].skuQuantity.unit);

                                      if (variation != null) {
                                        final minValue =
                                            availableQty - variation.minValue;
                                        final maxValue =
                                            availableQty + variation.maxValue;
                                        if (actualValue < minValue ||
                                            actualValue > maxValue) {
                                          showOutOfVariationPopup(
                                            index: index,
                                            i: i,
                                            item: items[i],
                                            skuName: skuName,
                                            skuImage: sku.image,
                                          );
                                          return;
                                        }
                                      }

                                      context
                                          .read<ReturnsSubmitCubit>()
                                          .updateQty(
                                            index,
                                            i,
                                            quantity.value ?? '',
                                            quantity.source,
                                          );
                                    }
                                  },
                                ),
                              ),
                      ),
                    ),
                    // SizedBox(width: 4),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
