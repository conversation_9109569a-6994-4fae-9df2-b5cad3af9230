part of 'search_proc_cubit.dart';

@freezed
class SearchProcState with _$SearchProcState {
  const factory SearchProcState.initial({
    @Default(null) MandiInfo? mandi,
    @Default(null) DateTime? selectedDate,
    @Default(null) VendorLocation? selectedVendor,
    @Default(null) String? message,
    @Default(null) List<ProcDetail>? procurements,
    @Default(false) bool isLoading,
  }) = _Initial;
}
