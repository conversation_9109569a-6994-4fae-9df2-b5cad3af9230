import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_liquidation_order_details_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order_details.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';

@injectable
class LiquidationOrderDetailsUseCase {
  LiquidationOrderDetailsUseCase();

  Future<Either<ErrorResult<dynamic>, List<List<LiquidationOrderSummary>>>>
      call({
    required int smoId,
    required List<Sku> skus,
  }) async {
    final orderDetailFuture =
        await GetLiquidationOrderDetailRequest(smoId: smoId).execute();

    return orderDetailFuture.fold(
        (left) => Left(left), (right) => Right(_processResult(right, skus)));
  }

  List<List<LiquidationOrderSummary>> _processResult(
    LiquidationOrderDetails details,
    List<Sku> skus,
  ) {
    final skuMap = Map.fromEntries(skus.map((e) => MapEntry(e.id, e)));
    final skuToLiquidationItemMap = <int, Map<int, LiquidationOrderSummary>>{};

    for (final newOrder in details.orders) {
      for (final newItem in newOrder.liquidationItems) {
        final skuId = newItem.skuQuantity.skuId;
        final skuName = skuMap[skuId]?.name ?? '';
        final liquidationMap = skuToLiquidationItemMap[skuId];
        if (liquidationMap == null) {
          skuToLiquidationItemMap[skuId] = {
            newItem.id: LiquidationOrderSummary(
              amount: 0,
              item: newItem,
              skuName: skuName,
              vendor: newOrder.vendor,
              createdAt: newOrder.createdAt ?? 0,
            ),
          };
        } else {
          final item = liquidationMap[newItem.id];
          if (item == null) {
            liquidationMap[newItem.id] = LiquidationOrderSummary(
              amount: 0,
              item: newItem,
              skuName: skuName,
              vendor: newOrder.vendor,
              createdAt: newOrder.createdAt ?? 0,
            );
          } else {
            liquidationMap[newItem.id] = item.copyWith(
              item: item.createdAt > (newOrder.createdAt ?? 0)
                  ? item.item
                  : newItem,
              createdAt: item.createdAt > (newOrder.createdAt ?? 0)
                  ? item.createdAt
                  : newOrder.createdAt ?? 0,
            );
          }
          skuToLiquidationItemMap[skuId] = liquidationMap;
        }
      }
    }

    for (final amountItem in details.amountLogs) {
      final skuId = amountItem.liquidationItem.skuQuantity.skuId;
      final skuName = skuMap[skuId]?.name ?? '';
      final liquidationMap = skuToLiquidationItemMap[skuId];
      if (liquidationMap == null) {
        skuToLiquidationItemMap[skuId] = {
          amountItem.liquidationItem.id: LiquidationOrderSummary(
            amount: amountItem.receivedAmount,
            item: amountItem.liquidationItem,
            skuName: skuName,
            vendor: amountItem.vendor,
            createdAt: amountItem.createdAt,
          ),
        };
      } else {
        final item = liquidationMap[amountItem.liquidationItem.id];
        if (item == null) {
          liquidationMap[amountItem.liquidationItem.id] =
              LiquidationOrderSummary(
            amount: amountItem.receivedAmount,
            item: amountItem.liquidationItem,
            skuName: skuName,
            vendor: amountItem.vendor,
            createdAt: amountItem.createdAt,
          );
        } else {
          liquidationMap[amountItem.liquidationItem.id] = item.copyWith(
            amount: item.amount + amountItem.receivedAmount,
            item: item.createdAt > amountItem.createdAt
                ? item.item
                : amountItem.liquidationItem,
            createdAt: item.createdAt > amountItem.createdAt
                ? item.createdAt
                : amountItem.createdAt,
          );
        }
        skuToLiquidationItemMap[skuId] = liquidationMap;
      }
    }

    for (final dumpItem in details.dumpLogs) {
      final skuId = dumpItem.liquidationItem.skuQuantity.skuId;
      final skuName = skuMap[skuId]?.name ?? '';
      final liquidationMap = skuToLiquidationItemMap[skuId];
      if (liquidationMap == null) {
        skuToLiquidationItemMap[skuId] = {
          dumpItem.liquidationItem.id: LiquidationOrderSummary(
            amount: 0,
            item: dumpItem.liquidationItem,
            skuName: skuName,
            vendor: dumpItem.vendor,
            createdAt: dumpItem.createdAt,
          ),
        };
      } else {
        final item = liquidationMap[dumpItem.liquidationItem.id];
        if (item == null) {
          liquidationMap[dumpItem.liquidationItem.id] = LiquidationOrderSummary(
            amount: 0,
            item: dumpItem.liquidationItem,
            skuName: skuName,
            vendor: dumpItem.vendor,
            createdAt: dumpItem.createdAt,
          );
        } else {
          liquidationMap[dumpItem.liquidationItem.id] = item.copyWith(
            item: item.createdAt > dumpItem.createdAt
                ? item.item
                : dumpItem.liquidationItem,
            createdAt: item.createdAt > dumpItem.createdAt
                ? item.createdAt
                : dumpItem.createdAt,
          );
        }
        skuToLiquidationItemMap[skuId] = liquidationMap;
      }
    }

    return skuToLiquidationItemMap.values
        .map((e) => e.values.toList())
        .toList();
  }
}
