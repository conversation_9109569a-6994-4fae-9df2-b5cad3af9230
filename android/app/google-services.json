{"project_info": {"project_number": "175834420930", "firebase_url": "https://signintest-84632-default-rtdb.firebaseio.com", "project_id": "signintest-84632", "storage_bucket": "signintest-84632.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:175834420930:android:62d5af66b2194b2a410d5b", "android_client_info": {"package_name": "com.example.farm_side_ops_flutter"}}, "oauth_client": [{"client_id": "175834420930-gupoq35okb27kh21kie0pev3r2qln1b0.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.farm_side_ops_flutter", "certificate_hash": "1e222589fd19a587f2517c85b6948d8a9094c6e1"}}, {"client_id": "175834420930-ortgfnggpnqiarqrciigd1klsbu70pnb.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.farm_side_ops_flutter", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-vt7mmkqeav03j0gansbunq1dbacjm4ct.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.farm_side_ops_flutter", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:936120573c7be032410d5b", "android_client_info": {"package_name": "com.example.farm_side_ops_flutter.dev.debug"}}, "oauth_client": [{"client_id": "175834420930-3h7f2a6pjntsj4122ii230fja6qk1ss6.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.farm_side_ops_flutter.dev.debug", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-v1e693g871gl5o18r1inhq8909f81v8q.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.farm_side_ops_flutter.dev.debug", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:f094668ae8f058cb410d5b", "android_client_info": {"package_name": "com.example.user_acquisition_app"}}, "oauth_client": [{"client_id": "175834420930-60836m57dncav1tud4dseomofg86g53j.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.user_acquisition_app", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-b6rg4dla6mkh2rgecd2m5cj05oioqnql.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.user_acquisition_app", "certificate_hash": "594aa4b8a52c7261211fe79573a6b7db5ff16886"}}, {"client_id": "175834420930-gg96djg8b0fntsu03kmnuocvbddlr56d.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.user_acquisition_app", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:71adcc1fa8f81453410d5b", "android_client_info": {"package_name": "com.example.user_acquisition_app.dev"}}, "oauth_client": [{"client_id": "175834420930-2fl8ucljm10lfhmog9s3uo32uob3rtr4.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.user_acquisition_app.dev", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-ab00ant8q35krma7qhgjnvmc366lkq7o.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.user_acquisition_app.dev", "certificate_hash": "594aa4b8a52c7261211fe79573a6b7db5ff16886"}}, {"client_id": "175834420930-umfeerfuuaa79n043dqfial1htqffmeb.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.user_acquisition_app.dev", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:6436ea52b8f6f1ed410d5b", "android_client_info": {"package_name": "com.example.wow_app_admin"}}, "oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:213ae6f1d4fd5e65410d5b", "android_client_info": {"package_name": "com.example.wow_ev_management"}}, "oauth_client": [{"client_id": "175834420930-2jn8a7m3n7g3t8vb0ste5vnqtqod7cue.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.wow_ev_management", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:13dddab305cf23d1410d5b", "android_client_info": {"package_name": "com.example.wow_procurement_app"}}, "oauth_client": [{"client_id": "175834420930-39cigl0e7h2m8scoclicu9dhvbra9n2i.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.wow_procurement_app", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-ksnvr1dnq9ipki59tspvnkajh55q97fv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.wow_procurement_app", "certificate_hash": "1bcdaa11a475734efae7d3c126115ee0aee2be79"}}, {"client_id": "175834420930-rld60jonkq0j6dhgqgkrg9vukbvqust7.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.wow_procurement_app", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:83905ada88714012410d5b", "android_client_info": {"package_name": "com.example.wow_procurement_app.dev"}}, "oauth_client": [{"client_id": "175834420930-agt615vg1j211pav2iu8ho7d47hdt6ie.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.wow_procurement_app.dev", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:1b8e5d42c68f6177410d5b", "android_client_info": {"package_name": "com.example.wow_sales_app"}}, "oauth_client": [{"client_id": "175834420930-v91u667t3un91b86um7juvespj21bcm6.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.wow_sales_app", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:bf3d0df245e1430a410d5b", "android_client_info": {"package_name": "com.wheelocity.proc"}}, "oauth_client": [{"client_id": "175834420930-t9fj09jqhsbqtakegrkf7su0l7gfov2r.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.proc", "certificate_hash": "573404c52bf06ca4b1dc66dbc37287f53feb84ee"}}, {"client_id": "175834420930-u318ehm3j4r1tq10or221r4tbj6gf8q2.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.proc", "certificate_hash": "373f424b8dede745703397a6d23e92773a2a2606"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:2e7039a3e3d69a5f410d5b", "android_client_info": {"package_name": "com.wheelocity.proc.dev"}}, "oauth_client": [{"client_id": "175834420930-qeb20ojl00h1ogg5i4eh4509lihrhggb.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.proc.dev", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-t0sphroh827765th0rpbjnrits7bio7d.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.proc.dev", "certificate_hash": "573404c52bf06ca4b1dc66dbc37287f53feb84ee"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:afff9e1ee8c36b1c410d5b", "android_client_info": {"package_name": "com.wheelocity.wow_customer_pos"}}, "oauth_client": [{"client_id": "175834420930-icsbpn7j5vt011ed072qt6gq8lhckjoo.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.wow_customer_pos", "certificate_hash": "2e20d58ceaad626e7d2bb60849a994b1ea5f50b1"}}, {"client_id": "175834420930-l44n6hr14uusk269h1ukil48i0ocsntg.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.wow_customer_pos", "certificate_hash": "573404c52bf06ca4b1dc66dbc37287f53feb84ee"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:5f8f9e9d5f7f12df410d5b", "android_client_info": {"package_name": "com.wheelocity.wow_customer_pos.dev.debug"}}, "oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:175834420930:android:4cf5280c8c9abad6410d5b", "android_client_info": {"package_name": "com.wheelocity.wow_user_sales"}}, "oauth_client": [{"client_id": "175834420930-2iqi5d0kj7bs5q09ekgintt72gtvljck.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.wow_user_sales", "certificate_hash": "e88ebcd768963a769338ca2299cdbc852dbddfd1"}}, {"client_id": "175834420930-9rmltq3uagc8fdh2dr8d8a0nm1purle1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.wow_user_sales", "certificate_hash": "573404c52bf06ca4b1dc66dbc37287f53feb84ee"}}, {"client_id": "175834420930-c9j0msld9o5a4qugmf85rbh6sfrbmgpj.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.wheelocity.wow_user_sales", "certificate_hash": "142b3f705e34719f7db4c05535490ff3753ba4d5"}}, {"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCjkkRZ9iwg_5LCF02oq2DTPoLpI9ZQblE"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}