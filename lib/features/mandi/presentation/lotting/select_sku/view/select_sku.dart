import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/input_model/inventory_item_input_model.dart';
import 'package:proc2/features/mandi/presentation/lotting/select_sku/bloc/select_sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class SelectSku extends StatelessWidget {
  const SelectSku({
    super.key,
    required this.isLotting,
    required this.mandiId,
  });

  final bool isLotting;
  final int mandiId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          di.get<SelectSkuBloc>()..add(SelectSkuEvent.started(mandiId)),
      child: _SelectSKU(
        isLotting: isLotting,
      ),
    );
  }
}

class _SelectSKU extends StatefulWidget {
  const _SelectSKU({
    required this.isLotting,
  });

  final bool isLotting;

  @override
  State<_SelectSKU> createState() => __SelectSKU();
}

class __SelectSKU extends State<_SelectSKU> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.width,
          padding: const EdgeInsets.all(20),
          child: BlocBuilder<SkuBloc, SkuState>(
            builder: (context, state) {
              return state.map(
                initial: (_) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                },
                error: (error) {
                  return ErrorScreen(
                    onPressed: () {},
                    message: error.errorr.message,
                  );
                },
                success: (s) {
                  return BlocBuilder<SelectSkuBloc, SelectSkuState>(
                    builder: (context, state) {
                      return state.map(
                        initial: (initial) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        },
                        error: (error) {
                          return ErrorScreen(
                            onPressed: () {},
                            message: error.errorResult.message,
                          );
                        },
                        success: (success) {
                          final bulkOrLotItems = widget.isLotting
                              ? success.bulkItems
                              : success.lotItems;
                          final items = bulkOrLotItems
                              .where((e) => e.quantity > 0)
                              .toList();
                          final selectedSku = success.selectedItems;
                          final selectedSkuIds = success.selectedSkuIds;

                          return Column(
                            children: [
                              Card(
                                elevation: 4,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Container(
                                  width: MediaQuery.of(context).size.width,
                                  height: 600,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    children: [
                                      appBar(),
                                      const SizedBox(height: 10),
                                      if (items.isEmpty)
                                        Expanded(
                                          child: EmptyScreen(
                                            message: LanguageEnum
                                                .lottingEmptyTitle
                                                .localized(),
                                            description: LanguageEnum
                                                .lottingEmptyMessage
                                                .localized(),
                                          ),
                                        ),
                                      if (items.isNotEmpty)
                                        Expanded(
                                          child: Scrollbar(
                                            child: ListView(
                                              children: [
                                                labels(),
                                                const Divider(),
                                                for (var i = 0;
                                                    i < items.length;
                                                    i++)
                                                  sku(
                                                    items[i],
                                                    selectedSku.contains(
                                                      items[i]
                                                          .getCompositeKey(),
                                                    ),
                                                    !widget.isLotting &&
                                                        (selectedSkuIds
                                                                .isEmpty ||
                                                            selectedSkuIds
                                                                .contains(
                                                              items[i].skuId,
                                                            )),
                                                    s.skus,
                                                  ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      if (!widget.isLotting && items.isNotEmpty)
                                        ElevatedButton.icon(
                                          onPressed: selectedSku.isEmpty
                                              ? null
                                              : () {
                                                  // pop back with the skus
                                                  final allItems = items
                                                      .where((e) =>
                                                          selectedSku.contains(e
                                                              .getCompositeKey()))
                                                      .map(
                                                        (e) => InventoryItemInputModel(
                                                            skuId: e.skuId,
                                                            type: e.type,
                                                            unit: e.unit,
                                                            lotSize:
                                                                e.lotSize ?? 0,
                                                            quantity:
                                                                e.quantity,
                                                            quantityInput: '',
                                                            lotSizeInput: '',
                                                            lots: []),
                                                      )
                                                      .toList();
                                                  context.pop(allItems);
                                                },
                                          style: ButtonStyle(
                                            elevation:
                                                MaterialStateProperty.all(4),
                                            fixedSize:
                                                MaterialStateProperty.all(
                                              Size(
                                                MediaQuery.of(context)
                                                    .size
                                                    .width,
                                                50,
                                              ),
                                            ),
                                          ),
                                          icon: const Icon(Icons.done),
                                          label: Text(LanguageEnum
                                              .lottingSelectSkuCtaLabel
                                              .localized()),
                                        )
                                    ],
                                  ),
                                ),
                              )
                            ],
                          );
                        },
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget labels() {
    return Container(
      padding: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
      child: Row(
        children: [
          Expanded(
            flex: 4,
            child: Text(
              LanguageEnum.sku.localized(),
            ),
          ),
          if (!widget.isLotting)
            Expanded(flex: 4, child: Text(LanguageEnum.lotSize.localized())),
          Expanded(flex: 4, child: Text(LanguageEnum.quantity.localized())),
          if (!widget.isLotting)
            Expanded(
              flex: 2,
              child: Text(
                LanguageEnum.lottingSelectLabel.localized(),
                textAlign: TextAlign.end,
              ),
            )
        ],
      ),
    );
  }

  Widget sku(
    InventoryItem inventoryItem,
    bool isSelected,
    bool isEnabled,
    List<Sku> skus,
  ) {
    final quantityUnit = widget.isLotting ? inventoryItem.unit : '';
    return Padding(
      padding: EdgeInsets.only(
        bottom: !widget.isLotting ? 0 : 8,
      ),
      child: Opacity(
        opacity: isEnabled ? 1 : 0.5,
        child: InkWell(
          onTap: !widget.isLotting
              ? () {
                  if (isEnabled) {
                    // add or remove from selected list
                    context.read<SelectSkuBloc>().add(
                          SelectSkuEvent.selectUnSelect(
                            skuKey: inventoryItem.getCompositeKey(),
                            isSelected: !isSelected ?? false,
                            skuId: inventoryItem.skuId,
                          ),
                        );
                  }
                }
              : () {
                  var lots = <double>[];
                  try {
                    lots = (skus
                            .firstWhere((e) => e.id == inventoryItem.skuId)
                            .lotSizes[inventoryItem.unit] as List<dynamic>)
                        .map((e) => e as double)
                        .toList();
                  } catch (_) {}

                  context.pop(
                    [
                      InventoryItemInputModel(
                        skuId: inventoryItem.skuId,
                        type: inventoryItem.type,
                        unit: inventoryItem.unit,
                        lotSize: inventoryItem.lotSize ?? 0,
                        quantity: inventoryItem.quantity,
                        quantityInput: '',
                        lotSizeInput: '',
                        lots: lots,
                      ),
                    ],
                  );
                },
          child: Container(
            padding: EdgeInsets.only(
              left: 8,
              right: 8,
              top: !widget.isLotting ? 0 : 8,
              bottom: !widget.isLotting ? 0 : 8,
            ),
            decoration: !widget.isLotting
                ? null
                : const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.grey,
                      ),
                    ),
                  ),
            child: Row(
              children: [
                Expanded(
                  flex: 4,
                  child: Text(
                    getSKU(context, skuID: inventoryItem.skuId).name,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                if (!widget.isLotting)
                  Expanded(
                    flex: 4,
                    child: Text(
                      '${inventoryItem.lotSize} ${inventoryItem.unit.localized()}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                Expanded(
                  flex: 4,
                  child: Text(
                    '${inventoryItem.quantity} ${quantityUnit}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                if (!widget.isLotting)
                  Expanded(
                    flex: 2,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Checkbox(
                          value: isSelected,
                          onChanged: (val) {
                            if (isEnabled) {
                              // add or remove from selected list
                              context.read<SelectSkuBloc>().add(
                                    SelectSkuEvent.selectUnSelect(
                                      skuKey: inventoryItem.getCompositeKey(),
                                      isSelected: val ?? false,
                                      skuId: inventoryItem.skuId,
                                    ),
                                  );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget appBar() {
    return Container(
      height: 50,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: Config.primaryColor,
      ),
      padding: const EdgeInsets.only(left: 4, right: 4),
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            child: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 15),
          Text(
            LanguageEnum.lottingSelectSkuLabel.localized(),
            style: const TextStyle(color: Colors.white, fontSize: 18),
          ),
        ],
      ),
    );
  }
}
