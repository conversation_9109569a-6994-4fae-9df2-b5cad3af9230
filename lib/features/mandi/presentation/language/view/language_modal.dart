import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/app/view/restart_widget.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/features/mandi/presentation/language/cubit/language_cubit.dart';

class LanguageModal extends StatelessWidget {
  const LanguageModal({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageCubit, LanguageState>(
      builder: (context, state) {
        return state.map(
          initial: (_) => const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
            ],
          ),
          error: (e) => ErrorScreen(
            message: e.error.message,
            onPressed: () async {
              await context.read<LanguageCubit>().getLanguages();
            },
          ),
          success: (s) {
            return SimpleDialog(
              title: Text(LanguageEnum.updateLanguageDialogTitle.localized()),
              children: [
                ...s.supportedLanguages.map(
                  (lang) => InkWell(
                    onTap: () {
                      context
                          .read<LanguageCubit>()
                          .updateSelectedLanguage(lang.languageCode);
                    },
                    child: ListTile(
                      title: Text(lang.languageName),
                      leading: Radio<String>(
                        value: lang.languageCode,
                        groupValue: s.selectedLanguage,
                        onChanged: (value) {
                          context
                              .read<LanguageCubit>()
                              .updateSelectedLanguage(value!);
                        },
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8, right: 8),
                  child: ElevatedButton(
                    style: ButtonStyle(
                      fixedSize: MaterialStateProperty.all(
                        Size(MediaQuery.of(context).size.width, 40),
                      ),
                    ),
                    onPressed: s.selectedLanguage == null
                        ? null
                        : () async {
                            final isSuccess = await context
                                .read<LanguageCubit>()
                                .updateLanguage();
                            if (isSuccess) {
                              // ignore: use_build_context_synchronously
                              context.pop();
                              // ignore: use_build_context_synchronously
                              RestartWidget.restartApp(context);
                            }
                          },
                    child: s.isLoading
                        ? const SizedBox(
                            height: 24,
                            width: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          )
                        : Text(
                            LanguageEnum.updateLanguageDialogCtaLabel
                                .localized(),
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
