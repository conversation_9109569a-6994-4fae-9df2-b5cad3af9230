import 'dart:collection';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/sku_quantity_dto.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/supply_order_input.dart';

class AddSkuDialog extends StatefulWidget {
  AddSkuDialog({
    super.key,
    this.previousSku,
    required this.terminalCount,
    required this.addedSkus,
  }) : isEditing = previousSku != null && previousSku.isNotEmpty;
  final List<SupplyOrderSku>? previousSku;
  final bool isEditing;
  final int terminalCount;
  final HashSet<String> addedSkus;

  @override
  State<AddSkuDialog> createState() => _AddSkuDialogState();
}

class _AddSkuDialogState extends State<AddSkuDialog> {
  Sku? sku;
  int stage = 0;
  ProcurementType procurementType = ProcurementType.bulk;
  String? procurementUnit;
  double? lotSize;
  String quantity = '';
  bool allowQuantityEditing = true;

  bool isDuplicate() {
    if (procurementType == ProcurementType.lots && lotSize == null) return true;
    if (quantity.isEmpty) return true;
    if (sku == null) return true;
    if (procurementUnit == null) return true;
    final String type =
        procurementType == ProcurementType.bulk ? 'BULK' : 'LOTS';
    String key;
    if (procurementType == ProcurementType.bulk) {
      key = '${sku!.id}-$type-$procurementUnit';
    } else {
      key = '${sku!.id}-$type-$procurementUnit-$lotSize';
    }

    if (widget.isEditing) {
      if (key == widget.previousSku![0].skuQuantity.compositeKey) {
        return false;
      }

      return widget.addedSkus.contains(key);
    } else {
      return widget.addedSkus.contains(key);
    }
  }

  @override
  void initState() {
    if (widget.previousSku != null) {
      sku = context.read<SkuBloc>().state.mapOrNull(
          success: (s) => s.skus.firstWhere((element) =>
              element.id == widget.previousSku![0].skuQuantity.skuId));
      procurementType = widget.previousSku![0].skuQuantity.isBulk
          ? ProcurementType.bulk
          : ProcurementType.lots;

      procurementUnit = widget.previousSku![0].skuQuantity.unit;
      lotSize = widget.previousSku![0].skuQuantity.lotSize;
      quantity = widget.previousSku![0].quantity.toString();
      allowQuantityEditing = false;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: dialogContent(context),
    );
  }

  Widget dialogContent(BuildContext context) {
    bool hasDuplicated = isDuplicate();

    bool isCtaActive = (stage == 0 && sku != null) ||
        (stage == 1 &&
            sku != null &&
            procurementUnit != null &&
            (procurementType == ProcurementType.bulk || lotSize != null) &&
            quantity.isNotEmpty);

    bool disableCta = isCtaActive ? (stage == 0 ? false : hasDuplicated) : true;

    return Stack(
      children: <Widget>[
        Container(
          padding: EdgeInsets.all(20),
          height: MediaQuery.of(context).size.height * (stage == 0 ? 0.8 : 0.5),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10.0,
                offset: const Offset(0.0, 10.0),
              ),
            ],
          ),
          child: BlocBuilder<SkuBloc, SkuState>(
            builder: (context, state) {
              return state.maybeMap(
                orElse: () => Center(
                  child: CircularProgressIndicator(),
                ),
                success: (success) {
                  return Column(
                    mainAxisSize: MainAxisSize.max,
                    children: <Widget>[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            widget.isEditing ? 'Edit Sku' : "Add Sku",
                            style: TextStyle(
                              fontSize: 24.0,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          IconButton(
                            icon: Icon(Icons.close),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      ),
                      SizedBox(height: 16.0),
                      if (stage == 0)
                        Expanded(
                          // Set as per your requirement
                          child: GridView.builder(
                            itemCount: success.skus.length,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 8,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                            ),
                            itemBuilder: (BuildContext context, int index) {
                              final currentSku = success.skus[index];
                              return GridTile(
                                child: InkWell(
                                  onTap: () {
                                    setState(() {
                                      sku = currentSku;
                                    });
                                  },
                                  child: Card(
                                    shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(12)),
                                    color: currentSku.id == sku?.id
                                        ? Colors.green.shade100
                                        : Colors.white,
                                    elevation: 2,
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: 4,
                                          ),
                                          Expanded(
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              child: Image.network(
                                                currentSku.image,
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 8,
                                          ),
                                          AutoSizeText(
                                            currentSku.name,
                                            minFontSize: 12,
                                            maxFontSize: 16,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }, // Set as per your requirement
                          ),
                        ),
                      if (stage == 1)
                        Expanded(
                            child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                Card(
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12)),
                                  color: Colors.green.shade100,
                                  elevation: 2,
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: SizedBox(
                                      width: 120,
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: 4,
                                          ),
                                          ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            child: Image.network(
                                              sku!.image,
                                            ),
                                          ),
                                          SizedBox(
                                            height: 8,
                                          ),
                                          AutoSizeText(
                                            sku!.name,
                                            minFontSize: 12,
                                            maxFontSize: 16,
                                            style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: procurementTypeField(),
                                ),
                                const SizedBox(height: 12),
                                Expanded(
                                  child: unitField(),
                                ),
                                if (procurementType == ProcurementType.lots)
                                  Expanded(child: lotSizeField()),
                                Expanded(
                                    child: quantityField(procurementType ==
                                        ProcurementType.bulk))
                              ],
                            ),
                            SizedBox(
                              height: 16,
                            ),
                            if (widget.isEditing)
                              CheckboxListTile(
                                title: LangText(
                                    'editSupplyOrder.addSkuDialogEnableQuantityCheckboxTitle',
                                    'Enable quantity update?'),
                                subtitle: LangText(
                                  'editSupplyOrder.addSkuDialogEnableQuantityCheckboxSubTitle',
                                  'Updating the quantity will update the quantity for the entire row for this sku!',
                                ),
                                value: allowQuantityEditing,
                                onChanged: (value) {
                                  setState(
                                    () {
                                      allowQuantityEditing = value ?? false;
                                    },
                                  );
                                },
                              ),
                            SizedBox(
                              height: 8,
                            ),
                            if (isCtaActive && hasDuplicated)
                              Center(
                                child: Text(
                                  'Sku already added!',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ),
                          ],
                        )),
                      SizedBox(height: 24.0),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (stage == 1)
                              ElevatedButton.icon(
                                style: ElevatedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 32,
                                    vertical: 16,
                                  ),
                                ),
                                onPressed: () {
                                  setState(() {
                                    stage = 0;
                                  });
                                },
                                icon: Icon(Icons.arrow_back),
                                label: Text(
                                  getLangText(
                                    'editSupplyOrder.addSkuDialogCtaGoBack',
                                    'Go Back',
                                  ),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            SizedBox(
                              width: 16,
                            ),
                            SizedBox(
                              width: 16,
                            ),
                            ElevatedButton.icon(
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 32,
                                  vertical: 16,
                                ),
                              ),
                              onPressed: disableCta
                                  ? null
                                  : () {
                                      if (stage == 0) {
                                        setState(() {
                                          stage = 1;
                                        });
                                      } else {
                                        if (widget.previousSku == null) {
                                          final newEntry = new List.filled(
                                            widget.terminalCount,
                                            SupplyOrderSku(
                                              skuQuantity: SkuQuantityDto(
                                                skuId: sku!.id,
                                                type: procurementType ==
                                                        ProcurementType.bulk
                                                    ? 'BULK'
                                                    : 'LOTS',
                                                unit: procurementUnit!,
                                                lotSize: lotSize ?? 0,
                                                quantity: quantity.toDouble(),
                                              ),
                                              skuName: sku!.name,
                                              quantity: quantity,
                                            ),
                                          );
                                          context.pop(newEntry);
                                        } else {
                                          final updatedItems = widget
                                              .previousSku!
                                              .map(
                                                (e) => e.copyWith(
                                                  skuQuantity: SkuQuantityDto(
                                                    skuId: sku!.id,
                                                    type: procurementType ==
                                                            ProcurementType.bulk
                                                        ? 'BULK'
                                                        : 'LOTS',
                                                    unit: procurementUnit!,
                                                    lotSize: lotSize,
                                                    quantity:
                                                        quantity.toDouble(),
                                                  ),
                                                  skuName: sku!.name,
                                                  quantity: allowQuantityEditing
                                                      ? quantity
                                                      : e.quantity,
                                                ),
                                              )
                                              .toList();
                                          context.pop(updatedItems);
                                        }
                                      }
                                    },
                              icon: Icon(stage == 0
                                  ? Icons.arrow_forward
                                  : Icons.check),
                              label: Text(
                                stage == 0
                                    ? getLangText(
                                        'editSupplyOrder.addSkuDialogCtaStage0',
                                        'Next',
                                      )
                                    : getLangText(
                                        'editSupplyOrder.addSkuDialogCtaStage1',
                                        'Add to Supply Order',
                                      ),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget quantityField(bool isBulk) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              isBulk
                  ? LanguageEnum.addProcSkuSelectQuantityLabel.localized()
                  : LanguageEnum.addProcSkuSelectNumberOfLotsLabel.localized(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              )),
          const SizedBox(height: 8),
          TextFormField(
            enabled: allowQuantityEditing,
            initialValue: quantity,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.numberInputFilters,
            onChanged: (val) {
              setState(() {
                quantity = val;
              });
            },
          )
        ],
      ),
    );
  }

  Widget unitField() {
    final lotsUnit = <String>[];
    if (procurementType == ProcurementType.lots) {
      sku!.lotSizes.forEach((key, value) {
        if (value is List && value.isNotEmpty) {
          lotsUnit.add(key);
        }
      });
    }
    final items =
        procurementType == ProcurementType.bulk ? sku!.bulkUnitTypes : lotsUnit;
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectUnitLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          DropdownButton(
            // Initial Value
            value: procurementUnit,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: items.map((String item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.capitalize().localized()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (String? newValue) {
              setState(() {
                procurementUnit = newValue;
                lotSize = null;
              });
            },
          )
        ],
      ),
    );
  }

  Widget lotSizeField() {
    final selectedLotSize = procurementUnit == null
        ? []
        : sku!.lotSizes[procurementUnit!.toUpperCase()] ?? [];
    // state.sku?.lotSizes[state.procurementUnit?.toUpperCase() ?? ''] ??
    //     <String>[];
    final items = <double>[];
    if (selectedLotSize is List) {
      for (final element in selectedLotSize) {
        items.add(double.parse(element.toString()));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectLotSizeLabel.localized(),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          DropdownButton(
            // Initial Value
            value: lotSize,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: items.map((double item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.toString()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (double? newValue) {
              setState(() {
                lotSize = newValue;
              });
            },
          )
        ],
      ),
    );
  }

  Widget procurementTypeField() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectProcTypeLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          DropdownButton<ProcurementType>(
            // Initial Value
            value: procurementType,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: ProcurementType.values.map((ProcurementType item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.value.capitalize().localized()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (ProcurementType? newValue) {
              setState(() {
                procurementType = newValue ?? ProcurementType.bulk;
                procurementUnit = null;
                lotSize = null;
              });
            },
          )
        ],
      ),
    );
  }
}
