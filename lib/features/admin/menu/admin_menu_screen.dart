import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/config.dart';

class AdminMenuScreen extends StatelessWidget {
  const AdminMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final config = di.get<AppConfig>();
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(
            'adminMenu.adminMenu'.tr('Admin Panel'),
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          padding: const EdgeInsets.only(
            left: 5,
            right: 5,
            top: 10,
          ),
          child: Scrollbar(
            child: GridView.count(
              crossAxisCount: 8,
              children: [
                menuButton(
                  context: context,
                  name: 'adminMenu.procAdmin'.tr('Proc Admin'),
                  icon: Icons.admin_panel_settings,
                  routeName: 'procAdminSearch',
                ),
                menuButton(
                  context: context,
                  name: 'adminMenu.procOrder'.tr('Proc Order'),
                  icon: Icons.add,
                  routeName: 'adminProcOrder',
                ),
                menuButton(
                  context: context,
                  name: 'adminMenu.procOrderUpdate'.tr('Proc Order Update'),
                  icon: Icons.update,
                  routeName: 'adminProcOrderUpdate',
                ),
                menuButton(
                  context: context,
                  name: 'adminMenu.manageUsers'.tr('Manage Users'),
                  icon: Icons.person_2_rounded,
                  routeName: 'adminManageUsers',
                ),
                menuButton(
                  context: context,
                  name: 'adminMenu.weighingMachine'.tr('Weighing Machine'),
                  icon: Icons.skip_next,
                  routeName: 'weighingMachineBypass',
                ),
                if (!config.isDev)
                  menuButton(
                    context: context,
                    name: 'adminMenu.bugReports'.tr('Bug Reports'),
                    icon: Icons.bug_report,
                    routeName: 'adminBugReports',
                  ),
                menuButton(
                  context: context,
                  name: 'adminMenu.otpDashboard'.tr('OTP Dashboard'),
                  icon: Icons.numbers_rounded,
                  routeName: 'adminOtpDashboard',
                ),
                menuButton(
                  context: context,
                  name: 'adminMenu.returnDownload'.tr('Return Download'),
                  icon: Icons.download_rounded,
                  routeName: 'adminReturnDownload',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget menuButton({
    required BuildContext context,
    required String name,
    required IconData icon,
    required String routeName,
  }) {
    return InkWell(
      onTap: () {
        context.push(
          context.namedLocation(
            routeName,
          ),
        );
      },
      child: Card(
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Container(
          height: 120,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, color: Config.primaryColor, size: 26),
              Text(
                name,
                textAlign: TextAlign.start,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
