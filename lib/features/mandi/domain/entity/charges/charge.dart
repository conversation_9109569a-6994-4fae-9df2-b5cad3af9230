import 'package:freezed_annotation/freezed_annotation.dart';

part 'charge.freezed.dart';
part 'charge.g.dart';

@freezed
class Charge with _$Charge {
  const factory Charge({
    required String type,
    required double amount,
    required String comment,
    required int timestamp,
    required int createdByUserId,
    required String? createdByUserName,
  }) = _Charge;

  @override
  factory Charge.fromJson(Map<String, dynamic> json) => _$ChargeFromJson(json);
}
