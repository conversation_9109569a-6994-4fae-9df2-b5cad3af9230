import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/cubit/add_parent_proc_cubit.dart';

class CreateParentProcOrderRequest extends BaseRequest<dynamic, String> {
  final int smoId;
  final int vendorLocationId;
  final List<dynamic> columns;
  final List<List<dynamic>> rows;
  final List<dynamic> comments;
  final DateTime selectedSupplyDate;

  CreateParentProcOrderRequest({
    required this.smoId,
    required this.vendorLocationId,
    required this.columns,
    required this.rows,
    required this.comments,
    required this.selectedSupplyDate,
  }) {
    if (columns.length < 3) throw 'Invalid columns';
    if (rows.length < 1) throw 'Invalid rows';
  }

  @override
  String getPath() => 'procurements/createChildProcurements';

  @override
  Map<String, dynamic> getBody() {
    final orders = <Map<String, dynamic>>[];
    for (var i = 2; i < columns.length; i++) {
      final order = <String, dynamic>{
        'facilityId': columns[i],
        'vendorLocationId': vendorLocationId,
        'images': [],
        'comments': comments[i].toString(),
      };
      final skus = <Map<String, dynamic>>[];
      for (var j = 0; j < rows.length; j++) {
        final currentRow = rows[j];
        final sku = currentRow[0];
        final qty = currentRow[i].toString().toDouble();
        if (sku is SkuQuantityWithName && qty > 0) {
          skus.add({
            'skuId': sku.skuQuantity.skuId,
            'type': sku.skuQuantity.type,
            'unit': sku.skuQuantity.unit,
            'quantity': 0,
            'lotSize': sku.skuQuantity.lotSize,
            'amount': 0,
            'orderedQuantity': qty,
            'finalSubmit': false,
          });
        }
      }
      order['skus'] = skus;
      order['supplyDate'] = selectedSupplyDate.millisecondsSinceEpoch ~/ 1000;

      orders.add(order);
    }

    final parentSkus = <Map<String, dynamic>>[];
    for (var i = 0; i < rows.length; i++) {
      final currentRow = rows[i];
      final sku = currentRow[0];
      final amountPerQty = currentRow[1].toString().toDouble();
      var qty = 0.0;
      for (var j = 2; j < columns.length; j++) {
        qty += currentRow[j].toString().toDouble();
      }

      if (sku is SkuQuantityWithName && qty > 0) {
        parentSkus.add({
          'skuId': sku.skuQuantity.skuId,
          'type': sku.skuQuantity.type,
          'unit': sku.skuQuantity.unit,
          'quantity': 0,
          'lotSize': sku.skuQuantity.lotSize,
          'amount': qty * amountPerQty,
          'orderedCostPrice': amountPerQty,
          'orderedQuantity': qty,
          'finalSubmit': false,
        });
      }
    }

    return {
      'smoId': smoId,
      'vendorLocationId': vendorLocationId,
      'skus': parentSkus,
      'childOrders': orders,
      'images': [],
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
