import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/features/mandi/presentation/close_ops/bloc/close_ops_bloc.dart';

class CloseOps extends StatefulWidget {
  const CloseOps({
    super.key,
    required this.smoId,
    required this.mandiId,
    required this.smoContext,
  });

  final int smoId;
  final int mandiId;
  final BuildContext smoContext;
  @override
  State<CloseOps> createState() => _CloseOps();
}

class _CloseOps extends State<CloseOps> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CloseOpsBloc, CloseOpsState>(
      listener: (context, state) {},
      builder: (context, state) {
        return WillPopScope(
            onWillPop: () async {
              return true;
            },
            child: Scaffold(
              appBar: WAppBar.getAppBar(
                title: Text(LanguageEnum.closeOps.localized()),
              ),
              body: BlocBuilder<CloseOpsBloc, CloseOpsState>(
                builder: (context, state) => state.when(
                  initial: loading,
                  loading: loading,
                  success: successMessage,
                  failure: errMessage,
                ),
              ),
            ));
      },
    );
  }

  Widget loading() {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [CircularProgressIndicator()],
      ),
    );
  }

  Widget errMessage(String errCode, String errorMessage) {
    bool isProcPending = errCode == 'ERROR_OPS_CANNOT_BE_CLOSED_PROC';
    bool isReturnsPending = errCode == 'ERROR_OPS_CANNOT_BE_CLOSED_RETURNS';
    bool isFourHoursLimit = errCode == 'ERROR_CLOSING_SMO_BEFORE_TIME';

    return SizedBox(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            errCode.localized(defaultValue: errorMessage),
            style: const TextStyle(
              color: Colors.red,
            ),
          ),
          if (!isFourHoursLimit)
            ButtonBar(
              alignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  style: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(Colors.red),
                  ),
                  onPressed: () {
                    context.pop();
                    if (isProcPending) {
                      context.push(
                        context.namedLocation(
                          'myProc',
                          pathParameters: {'mandiId': '${widget.mandiId}'},
                        ),
                        extra: widget.smoContext,
                      );
                    } else if (isReturnsPending) {
                      context.push(
                        context.namedLocation(
                          'grading',
                          pathParameters: {
                            'mandiId': widget.mandiId.toString(),
                          },
                        ),
                        extra: widget.smoContext,
                      );
                    } else {
                      context.push(
                        context.namedLocation(
                          'skuAllocation',
                          pathParameters: {'mandiId': '${widget.mandiId}'},
                        ),
                        extra: widget.smoContext,
                      );
                    }
                  },
                  child: Text(isProcPending
                      ? 'Close Procurements'
                      : isReturnsPending
                          ? 'Open Grading'
                          : 'Close Allocations'),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget successMessage() {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            LanguageEnum.closeOpsSuccessMessage.localized(),
            style: TextStyle(
              color: Colors.red,
            ),
          ),
          ElevatedButton(
              onPressed: () {
                context.pop();
              },
              child: Text(LanguageEnum.closeOpsCtaLabel.localized())),
        ],
      ),
    );
  }
}
