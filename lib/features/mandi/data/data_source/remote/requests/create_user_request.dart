import 'package:proc2/core/data/network/base_request.dart';

class CreateU<PERSON><PERSON><PERSON><PERSON> extends BaseRequest<dynamic, String> {
  final String userFirstName;
  final String? userLastName;
  final String userEmail;
  final String? userMobile;

  CreateUserRequest(
      {required this.userFirstName,
      required this.userLastName,
      required this.userEmail,
      required this.userMobile});

  @override
  String getPath() => 'users/create';

  @override
  Map<String, dynamic> getBody() {
    return {
      'email': userEmail,
      'firstName': userFirstName,
      'lastName': userLastName,
      'mobileNumber': userMobile
    };
  }

  @override
  String mapper(data) {
    if (data is String) {
      return data;
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
