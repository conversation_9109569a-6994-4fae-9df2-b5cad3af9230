part of 'inventory_allocation_bloc.dart';

@freezed
class InventoryAllocationEvent with _$InventoryAllocationEvent {
  const factory InventoryAllocationEvent.started(int smoId) = _Started;
  const factory InventoryAllocationEvent.destinationTypeChanged(
    String? destinationType,
  ) = _DestinationTypeChanged;
  const factory InventoryAllocationEvent.destinationChanged(
    String? destination,
  ) = _DestinationChanged;
  const factory InventoryAllocationEvent.deliverySlotChanged(
    String? deliverySlot,
  ) = _DeliverySlotChanged;
  const factory InventoryAllocationEvent.deliveryDateChanged(
    DateTime? deliveryDate,
  ) = _DeliveryDateChanged;
  const factory InventoryAllocationEvent.skuCategoryChanged(
    String? skuCategory,
  ) = _SkuCategoryChanged;
  const factory InventoryAllocationEvent.createAllotment(
    int smoId,
    String mandiName,
  ) = _CreateAllotment;
  const factory InventoryAllocationEvent.openAllotment(
    int allotmentId,
    String allotmentDestinationName,
    Map<String, dynamic> allotmentQueryParams,
  ) = _OpenAllotment;
  const factory InventoryAllocationEvent.getAllotments(int smoId) =
      _GetAllotments;
  const factory InventoryAllocationEvent.clearMessage() = _ClearMessage;
  const factory InventoryAllocationEvent.clearOpenAllotment() =
      _ClearOpenAllotment;
  const factory InventoryAllocationEvent.cancelAllocation(
    int allocationId,
    int smoId,
  ) = _CancelAllocation;
}
