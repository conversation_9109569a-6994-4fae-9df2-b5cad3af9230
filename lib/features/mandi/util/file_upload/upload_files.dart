// ignore_for_file: lines_longer_than_80_chars, omit_local_variable_types, prefer_final_locals, inference_failure_on_instance_creation

import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/ui/view_full_image.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';

class UploadFiles extends StatefulWidget {
  const UploadFiles({
    super.key,
    required this.reqBody,
    required this.pageTitle,
    required this.module,
    this.allowMultiple = false,
    this.fileType = FileType.image,
  });
  final Map<String, dynamic> reqBody;
  final bool allowMultiple;
  final String pageTitle;
  final FileType fileType;
  final UploadFileModule module;
  @override
  State<UploadFiles> createState() => _UploadFiles();
}

class _UploadFiles extends State<UploadFiles> {
  List<PlatformFile> selectedFilesList = [];

  bool isLoading = false;
  String? errMessage;

  void setLoading(bool val) {
    setState(() {
      isLoading = val;
    });
  }

  void setErrorMessage(String? msg) {
    setState(() {
      errMessage = msg;
    });
  }

  Future<void> selectFile() async {
    if (isLoading) {
      return;
    }
    FilePickerResult? files = await FilePicker.platform
        .pickFiles(allowMultiple: widget.allowMultiple, type: widget.fileType);

    if (files == null) {
      return;
    }

    List<PlatformFile> filesList = selectedFilesList.toList();
    // ignore: cascade_invocations
    filesList.addAll(files.files);

    setState(() {
      selectedFilesList = filesList;
    });
  }

  Future<void> uploadFiles() async {
    if (selectedFilesList.isEmpty) {
      return;
    }

    try {
      setErrorMessage(null);
      setLoading(true);

      final uploader = di.get<FileUpload>();

      final fileUrls = await uploader.uploadFile(
        widget.module,
        widget.reqBody,
        files: selectedFilesList,
      );

      // ignore: use_build_context_synchronously
      Navigator.pop(context, fileUrls);

      setLoading(false);
    } catch (e) {
      setErrorMessage(e.toString());
      setLoading(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.pageTitle),
        ),
        bottomNavigationBar: Center(
          child: Container(
            width: context.maxScreenWidth,
            padding: const EdgeInsets.all(10),
            margin: const EdgeInsets.only(bottom: 10),
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Visibility(
                  visible: isLoading,
                  child: const CircularProgressIndicator(),
                ),
                Visibility(
                  visible: !isLoading,
                  child: ElevatedButton(
                    style: ButtonStyle(
                      elevation: MaterialStateProperty.all(4),
                      padding: MaterialStateProperty.all(
                        const EdgeInsets.only(
                          left: 20,
                          right: 20,
                          top: 10,
                          bottom: 10,
                        ),
                      ),
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                    ),
                    onPressed: selectFile,
                    child: Text(
                      LanguageEnum.uploadFileCtaLabel.localized(),
                    ),
                  ),
                ),
                Visibility(
                  visible: selectedFilesList.isNotEmpty && !isLoading,
                  child: const SizedBox(
                    height: 15,
                  ),
                ),
                Visibility(
                  visible: selectedFilesList.isNotEmpty && !isLoading,
                  child: ElevatedButton.icon(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(Colors.red),
                      padding: MaterialStateProperty.all(
                        const EdgeInsets.only(
                          left: 20,
                          right: 20,
                          top: 10,
                          bottom: 10,
                        ),
                      ),
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                    ),
                    onPressed: uploadFiles,
                    icon: const Icon(
                      Icons.upload_file,
                      color: Colors.white,
                    ),
                    label: Text(
                      LanguageEnum.uploadFileCtaUploadLabel.localized(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        body: Center(
          child: Container(
            width: context.maxScreenWidth,
            height: MediaQuery.of(context).size.height,
            color: Colors.white,
            padding: const EdgeInsets.all(10),
            child: Builder(
              builder: (context) {
                if (selectedFilesList.isEmpty) {
                  return emptyBox();
                }

                return imageList();
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget emptyBox() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset('assets/images/empty_image_gallery.webp'),
        Text(
          LanguageEnum.uploadFileEmptyMessage.localized(),
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        )
      ],
    );
  }

  Widget imageList() {
    return Scrollbar(
      child: ListView(
        children: [
          Visibility(
              visible: errMessage != null,
              child: Text(
                errMessage ?? '',
                style: const TextStyle(color: Colors.red),
              )),
          Wrap(
            spacing: 4,
            runSpacing: 8,
            alignment: WrapAlignment.spaceEvenly,
            children: [
              for (PlatformFile file in selectedFilesList)
                InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ViewFullImage(
                          image: kIsWeb
                              ? Image.memory(file.bytes!).image
                              : FileImage(File(file.path!)),
                        ),
                      ),
                    );
                  },
                  child: Card(
                    elevation: 5,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        8,
                      ),
                    ),
                    child: Container(
                      height: 80,
                      width: 80,
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          fit: BoxFit.cover,
                          image: kIsWeb
                              ? Image.memory(file.bytes!).image
                              : FileImage(File(file.path!)),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              InkWell(
                                onTap: () {
                                  remove(file);
                                },
                                child: const CircleAvatar(
                                  radius: 10,
                                  backgroundColor: Colors.red,
                                  child: Icon(
                                    Icons.close,
                                    size: 18,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  void remove(PlatformFile file) {
    setState(() {
      selectedFilesList.remove(file);
    });
  }
}
