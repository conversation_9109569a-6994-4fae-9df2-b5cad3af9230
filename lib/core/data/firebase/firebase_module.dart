import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';

@module
abstract class FirebaseModule {
  FirebaseAuth get firebaseAuth => FirebaseAuth.instance;
  GoogleSignIn get googleSignIn => kIsWeb
      ? GoogleSignIn(
          scopes: ['email', 'profile'],
          clientId:
              '175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com',
        )
      : GoogleSignIn(
          scopes: ['email', 'profile'],
          clientId:
              '175834420930-0fc8e1qb8l41mdvklq07gadjfuiiedfr.apps.googleusercontent.com');
  Uuid get uuid => const Uuid();

  FirebaseApp get firebaseApp => Firebase.app();

  FirebaseDatabase get firebaseDatabase => FirebaseDatabase.instanceFor(
      app: firebaseApp,
      databaseURL: 'https://signintest-84632-flow.firebaseio.com/');

  FirebaseStorage get firebaseStorage =>
      FirebaseStorage.instanceFor(bucket: 'gs://wow-proc-flow');
}
