import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_change_response.dart';

class SubmitFinalAllocationRequest
    extends BaseRequest<dynamic, AllocationChangeResponse> {
  final int allocationId;

  SubmitFinalAllocationRequest({required this.allocationId});

  @override
  String getPath() => 'allotments/completeAllocation/$allocationId';

  @override
  AllocationChangeResponse mapper(data) {
    if (data is Map<String, dynamic>)
      return AllocationChangeResponse.fromJson(data);
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
