import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';

part 'proc_user.freezed.dart';
part 'proc_user.g.dart';

@freezed
class ProcUser with _$ProcUser implements DropDownItem {
  const ProcUser._();
  const factory ProcUser({
    required int id,
    required String firstName,
    required String? lastName,
    required String? email,
    required String? status,
  }) = _ProcUser;

  @override
  String get displayText => "$firstName $lastName";

  bool match(String query) {
    final hasMatched = firstName.toLowerCase().contains(query) ||
        lastName?.toLowerCase().contains(query) == true ||
        email?.toLowerCase().contains(query) == true;
    return hasMatched;
  }

  @override
  factory ProcUser.fromJson(Map<String, dynamic> json) =>
      _$ProcUserFromJson(json);
}
