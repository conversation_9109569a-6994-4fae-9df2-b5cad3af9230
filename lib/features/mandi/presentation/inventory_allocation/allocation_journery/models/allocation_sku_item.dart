import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/total_allocation_popup.dart';

part 'allocation_sku_item.freezed.dart';

@freezed
class AllocationSkuItem with _$AllocationSkuItem {
  const AllocationSkuItem._();
  const factory AllocationSkuItem({
    required int id,
    required String skuName,
    required String skuImage,
    required SkuQuantity skuQuantity,
    required double? orderedQty,
    required double mandiQty,
    required AllocationQty backendAllocationQty,
    required AllocationQty updateAllocationQty,
    required String distributionStatus,
    required bool isManuallyEdited,
    @Default(null) double? distributionQuantity,
    required String groupKey,
  }) = _AllocationSkuItem;

  double get totalQuantityBackend => backendAllocationQty.totalQty.toDouble();
  double get totalQuantityUpdate => updateAllocationQty.totalQty.toDouble();

  bool get isPending => distributionStatus.toLowerCase() == 'pending';
  bool get isReady => distributionStatus.toLowerCase() == 'ready';
  bool get isValidId => id != -1;
}
