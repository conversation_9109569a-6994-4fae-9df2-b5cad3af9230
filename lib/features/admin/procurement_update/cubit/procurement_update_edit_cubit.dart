import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/admin/procurement_update/model/proc_table.dart';
import 'package:proc2/features/admin/procurement_update/model/procurement_order.dart';
import 'package:proc2/features/admin/procurement_update/request/get_child_orders_for_parent_request.dart';
import 'package:proc2/features/admin/procurement_update/request/update_proc_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';

part 'procurement_update_edit_state.dart';

@injectable
class ProcurementUpdateEditCubit extends Cubit<ProcurementUpdateEditState> {
  ProcurementUpdateEditCubit() : super(ProcurementUpdateEditState.initial());

  void init({
    required MandiInfo mandi,
    required int smoId,
    required ProcurementOrder order,
    required Map<int, Sku> skuMap,
    required Map<int, MandiInfo> mandis,
  }) async {
    emit(state.copyWith(
      selectedMandi: mandi,
      smoId: smoId,
      skuMap: skuMap,
      mandis: mandis,
      procurementOrder: order,
    ));
    await loadOrder(parentId: order.id);
  }

  Future<void> loadOrder({
    required int parentId,
  }) async {
    final result =
        await GetChildOrdersForParentRequest(parentId: parentId).execute();
    result.fold(
      (l) => emit(state.copyWith(isLoading: false, message: l.message)),
      (r) => emit(state.copyWith(
        isLoading: false,
        procTable: ProcTable.fromResponse(
          skuMap: state.skuMap,
          childOrders: r,
          mandis: state.mandis,
        ),
        message: null,
      )),
    );
  }

  void clearMessage() {
    emit(state.copyWith(message: null));
  }

  void deleteSku({required int columnIndex}) {
    emit(state.copyWith(
      procTable: state.procTable?.deleteSku(skuIndex: columnIndex),
    ));
  }

  void updateSkuOrderedQuantity({
    required int columnIndex,
    required int rowIndex,
    required String value,
  }) {
    emit(state.copyWith(
      procTable: state.procTable?.updateSku(
        skuIndex: columnIndex,
        rowIndex: rowIndex,
        orderedQuantity: value,
      ),
    ));
  }

  void setFocusItemId(int? id) {
    emit(state.copyWith(focusItemId: id));
  }

  Future<void> submitChanges() async {
    emit(state.copyWith(isCtaLoading: true));
    final result =
        await UpdateProcOrderRequest(updates: state.procTable!.getRequestBody())
            .execute();
    result.fold(
      (l) => emit(state.copyWith(isCtaLoading: false, message: l.message)),
      (r) => emit(state.copyWith(isCtaLoading: false, message: r)),
    );
    await loadOrder(parentId: state.procurementOrder!.id);
  }
}
