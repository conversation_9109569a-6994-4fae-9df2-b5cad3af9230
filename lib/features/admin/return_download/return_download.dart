import 'dart:convert';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/admin/return_download/cubit/return_download_cubit.dart';
import 'package:proc2/features/mandi/domain/entity/return_download/return_download_entity.dart';
import 'package:proc2/features/mandi/domain/use_case/get_all_mandis_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/csv_handler.dart';

class ReturnDownload extends StatefulWidget {
  const ReturnDownload({super.key});

  @override
  State<ReturnDownload> createState() => _ReturnDownloadState();
}

class _ReturnDownloadState extends State<ReturnDownload> {
  final typeController = TextEditingController();
  final pitStopController = TextEditingController();
  final List<String> types = ['INVENTORY', 'WASTAGE', 'CANCELLED_ORDER'];

  @override
  void dispose() {
    typeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => ReturnDownloadCubit(di.get<GetAllMandisUseCase>()),
      child: SafeArea(
        child: Scaffold(
          appBar: AppBar(
            title: Text('returnDownload'.tr('Return Download')),
          ),
          body: BlocBuilder<ReturnDownloadCubit, ReturnDownloadState>(
            builder: (context, state) {
              final cubit = context.read<ReturnDownloadCubit>();
              return Column(
                children: [
                  _filters(cubit, state),
                  if (state.message != null)
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(state.message!,
                          style: const TextStyle(color: Colors.red)),
                    ),
                  if (state.isLoading)
                    Expanded(
                      child: SizedBox(
                        height: 30,
                        width: 30,
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                    )
                  else if (state.returnList.isNotEmpty)
                    Expanded(
                      child: ListView.builder(
                        itemCount: state.returnList.length,
                        itemBuilder: (_, index) {
                          final item = state.returnList[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('SKU ID: ${item.skuId}',
                                      style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold)),
                                  const SizedBox(height: 4),
                                  Text(
                                      'Received Quantity: ${item.receivedQuantity}',
                                      style: const TextStyle(fontSize: 14)),
                                  const SizedBox(height: 4),
                                  Text(
                                      'Return IDs: ${item.returnOrderIds.join(", ")}',
                                      style: const TextStyle(
                                          fontSize: 14, color: Colors.grey)),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    )
                  else
                    Expanded(
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Text(
                            'No data found',
                            style: TextStyle(
                                fontSize: 12,
                                color: Colors.red,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                    ),
                  const SizedBox(width: 10),
                  Visibility(
                    visible: state.returnList.isNotEmpty,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 40, top: 20),
                      child: ElevatedButton.icon(
                        onPressed: () => _downloadReturnExcel(cubit, state,
                            data: state.returnList),
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green),
                        icon: Icon(Icons.download_rounded),
                        label: const Text('Download Excel'),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _downloadReturnExcel(
    ReturnDownloadCubit cubit,
    ReturnDownloadState state, {
    required List<ReturnDownloadEntity> data,
  }) async {
    if (data.isEmpty) return;

    final header = [
      'Field Name',
      'Vendor Code',
      'Status',
      'Delivery Location',
      'Invoice No',
      'Invoice Date',
      'Challan No',
      'Challan Date',
      'Remarks',
      'Sku/EAN Code',
      'Received Qty',
      'Invoice Qty',
      'Bin',
      'Lottable01',
      'Expiry Date',
      'Lottable03',
      'Lottable04',
      'Lottable05',
      'Lottable06',
      'Lottable 07',
      'Ext USN No',
      'LPN',
      'IMEI No',
      'UDF1',
      'UDF2',
      'UDF3',
      'UDF4',
      'UDF5',
      'UDF6',
      'UDF7',
      'UDF8',
      'UDF9',
      'UDF10',
      'LineUdf2',
      'LineUdf3',
      'LineUdf7',
      'LineUdf8',
      'LineUdf9',
      'LineUdf10',
    ];
    const List<String> header1 = [
      'FieldDescription',
      '',
      '',
      '',
      '',
      'Date Format should be dd/MM/yyyy',
      '',
      'Date Format should be dd/MM/yyyy',
      '',
      '',
      '',
      '',
      '',
      '',
      'Date Format should be dd/MM/yyyy',
      'Date Format should be dd/MM/yyyy',
      'Date Format should be dd/MM/yyyy',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
    ];
    final rows = <List<String>>[
      header1,
      ...data.map((item) => [
            '',
            'V01',
            'Confirmed',
            'PO1',
            '',
            '',
            '',
            '',
            '${jsonEncode({'returnOrderIds': item.returnOrderIds.join(',')})}',
            item.skuId.toString(),
            '${item.receivedQuantity.toInt()}',
            '',
            '${state.selectedType == 'WASTAGE' ? 'TEDBIN' : 'SELBIN'}',
            '1',
            '${DateFormat('dd/MM/yyyy').format(DateTime.now().add(const Duration(days: 30)))}',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
          ])
    ];

    await CSVHandler.downloadExcel(
      fileName:
          'Return_data_${DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now())}.xlsx',
      values: rows,
      headers: header,
    );
  }

  Widget _filters(ReturnDownloadCubit cubit, ReturnDownloadState state) {
    return Container(
      height: 55,
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (state.allMandisList.isNotEmpty)
            SizedBox(
              width: 250,
              child: CustomDropdown.search(
                items: state.allMandisList.map((e) => e.name).toList(),
                controller: pitStopController,
                hintText: 'Select Mandi',
                onChanged: (name) {
                  final selected = state.allMandisList.firstWhere(
                    (e) => e.name == name,
                  );
                  cubit.setSelectedMandiId(selected.id);
                },
              ),
            ),
          const SizedBox(width: 10),
          ElevatedButton(
            onPressed: () async {
              final picked = await _pickDateTime(context);
              if (picked != null) cubit.setSelectedStartDateTime(picked);
            },
            child: Text(state.selectedStartDateTime != null
                ? _format(state.selectedStartDateTime!)
                : 'startDateTime'.tr('Start Date Time')),
          ),
          const SizedBox(width: 10),
          ElevatedButton(
            onPressed: () async {
              final picked = await _pickDateTime(context);
              if (picked != null) cubit.setSelectedEndDateTime(picked);
            },
            child: Text(state.selectedEndDateTime != null
                ? _format(state.selectedEndDateTime!)
                : 'endDateTime'.tr('End Date Time')),
          ),
          const SizedBox(width: 10),
          SizedBox(
            width: 250,
            child: CustomDropdown(
              items: types,
              controller: typeController,
              hintText: 'Select Type',
              onChanged: cubit.setSelectedType,
            ),
          ),
          const SizedBox(width: 20),
          ElevatedButton(
            onPressed: (state.selectedStartDateTime == null ||
                    state.selectedEndDateTime == null ||
                    state.selectedType == null)
                ? null
                : cubit.getReturns,
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blueAccent),
            child: Text('apply'.tr('Apply')),
          ),
        ],
      ),
    );
  }

  Future<DateTime?> _pickDateTime(BuildContext context) async {
    final now = DateTime.now();
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: now,
      firstDate: DateTime.now().subtract(const Duration(days: 2)),
      lastDate: now,
    );
    if (pickedDate == null) return null;

    final pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(now),
    );
    if (pickedTime == null) return null;

    return DateTime(pickedDate.year, pickedDate.month, pickedDate.day,
        pickedTime.hour, pickedTime.minute);
  }

  String _format(DateTime dt) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dt);
  }
}
