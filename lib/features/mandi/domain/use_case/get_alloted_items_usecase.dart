import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/alloted_item.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetAllotedItemsUseCase {
  GetAllotedItemsUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, List<AllotedItem>>> call(
    int allotmentId,
  ) async {
    return _mandiRepository.getAllotedItems(allotmentId);
  }
}
