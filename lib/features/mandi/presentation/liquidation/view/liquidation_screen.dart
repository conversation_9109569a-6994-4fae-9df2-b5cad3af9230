import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_tab_bar.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_liquidation_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';
import 'package:proc2/features/mandi/presentation/liquidation/cubit/liquidation_cubit.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

class LiquidationScreen extends StatefulWidget {
  const LiquidationScreen({
    super.key,
    required this.smoId,
    required this.mandiId,
    required this.isEditOnlyFromWeighingMachine,
  });
  final int smoId;
  final int mandiId;
  final bool isEditOnlyFromWeighingMachine;

  @override
  State<LiquidationScreen> createState() => _LiquidationScreenState();
}

class _LiquidationScreenState extends State<LiquidationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _controller;
  @override
  void initState() {
    _controller = TabController(length: 2, vsync: this);
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          title: LangText('liquidation.title', 'Liquidation'),
          centerTitle: false,
          bottom: WTabBar(tabs: [
            Tab(text: getLangText('liquidation.activeTabKey', 'Active')),
            Tab(text: getLangText('liquidation.pastTabKey', 'Past')),
          ], controller: _controller),
        ),
        body: BlocBuilder<SkuBloc, SkuState>(
          builder: (context, skuState) {
            return skuState.maybeMap(
                orElse: () => Center(
                      child: CircularProgressIndicator(),
                    ),
                success: (s) {
                  return BlocBuilder<LiquidationCubit, LiquidationState>(
                    builder: (context, state) {
                      return Container(
                        child: TabBarView(
                          children: [
                            _liquidationScreen(
                              showEdit: true,
                              errorMessage: state.activeOrdersError,
                              orders: state.activeOrders,
                              onRetry: () {
                                context
                                    .read<LiquidationCubit>()
                                    .getLiquidationOrders(widget.smoId,
                                        LiquidationOrderStatus.pending);
                              },
                              isLoading: state.activeOrdersLoading,
                            ),
                            _liquidationScreen(
                              showEdit: false,
                              errorMessage: state.pastOrdersError,
                              orders: state.pastOrders,
                              onRetry: () {
                                context
                                    .read<LiquidationCubit>()
                                    .getLiquidationOrders(widget.smoId,
                                        LiquidationOrderStatus.completed);
                              },
                              isLoading: state.pastOrdersLoading,
                            ),
                          ],
                          controller: _controller,
                        ),
                      );
                    },
                  );
                });
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () async {
            await context.push(
                context.namedLocation(
                  'updateLiquidation',
                  pathParameters: {
                    'mandiId': widget.mandiId.toString(),
                    'smoId': widget.smoId.toString(),
                  },
                ),
                extra: {
                  'isEditOnlyFromWeighingMachine':
                      widget.isEditOnlyFromWeighingMachine,
                });
            context.read<LiquidationCubit>().getLiquidationOrders(
                widget.smoId, LiquidationOrderStatus.pending);
          },
          child: Icon(Icons.add),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  Widget _liquidationScreen({
    required bool showEdit,
    required String? errorMessage,
    required List<LiquidationOrder>? orders,
    required bool isLoading,
    required VoidCallback onRetry,
  }) {
    if (orders == null) {
      if (isLoading) {
        return Center(
          child: CircularProgressIndicator(),
        );
      }
      if (errorMessage != null) {
        return ErrorScreen(
          onPressed: onRetry,
          message: errorMessage,
        );
      }

      return EmptyScreen(
        message: 'No Liquidation Orders Found',
      );
    } else {
      if (orders.isEmpty) {
        return EmptyScreen(
          message: 'No Liquidation Orders Found',
        );
      }
      return Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          if (isLoading || errorMessage != null)
            Container(
              width: double.infinity,
              color: isLoading ? Colors.green : Colors.red,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: Text(
                  isLoading
                      ? 'Refreshing Liquidation Orders...'
                      : errorMessage ?? '',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
          _liquidationHeading(showEdit: showEdit),
          Expanded(
            child: Scrollbar(
              child: RefreshIndicator(
                onRefresh: () {
                  onRetry();
                  return Future.value();
                },
                child: ListView.builder(
                  itemCount: orders.length,
                  itemBuilder: (context, index) {
                    final order = orders[index];
                    return InkWell(
                      onTap: () async {
                        await context.push(
                          context.namedLocation(
                            'updateLiquidation',
                            pathParameters: {
                              'mandiId': widget.mandiId.toString(),
                              'smoId': widget.smoId.toString(),
                            },
                          ),
                          extra: {
                            'order': order,
                            'isEditOnlyFromWeighingMachine':
                                widget.isEditOnlyFromWeighingMachine,
                          },
                        );
                        context
                            .read<LiquidationCubit>()
                            .getAllLiquidationOrders(widget.smoId);
                      },
                      child: Container(
                        color: index % 2 == 0
                            ? Colors.grey[100]
                            : Colors.grey[200],
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 16.0,
                            horizontal: 16,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 100,
                                child: Text(
                                  order.orderedDate,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              SizedBox(
                                width: 90,
                                child: Text(
                                  order.age,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 1,
                                child: Text(
                                  order.createdBy ?? '-',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 1,
                                child: Text(
                                  order.vendor.name,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                              if (showEdit)
                                SizedBox(
                                  width: 44,
                                  child: Align(
                                    alignment: Alignment.centerRight,
                                    child: Icon(
                                      Icons.edit,
                                      size: 24,
                                      color: Colors.green,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget _liquidationHeading({required bool showEdit}) {
    return Container(
      color: Colors.grey[300],
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 8.0,
          horizontal: 16,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: 100,
              child: LangText(
                'liquidation.orderedDate',
                'Ordered Date',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              width: 4,
            ),
            SizedBox(
              width: 90,
              child: LangText(
                'liquidation.skuAge',
                'Age(Days)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              width: 4,
            ),
            Expanded(
              flex: 1,
              child: LangText(
                'liquidation.orderBy',
                'By',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              width: 4,
            ),
            Expanded(
              flex: 1,
              child: LangText(
                'liquidation.vendor',
                'Vendor',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.end,
              ),
            ),
            if (showEdit)
              SizedBox(
                width: 44,
              ),
          ],
        ),
      ),
    );
  }
}
