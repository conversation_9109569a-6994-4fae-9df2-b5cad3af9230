part of 'add_procurement_bloc.dart';

@freezed
class AddProcurementState with _$AddProcurementState {
  const AddProcurementState._();
  const factory AddProcurementState.input({
    required ProcurementData data,
    required String mandiName,
    required Map<int, List<SkuInputData>> inputSku,
    @Default(0) int skuCount,
    required bool isSubmitLoading,
    required bool isSubmitCtaActive,
    required String message,
    @Default(false) bool shouldPop,
    @Default([]) List<PickedFile> files,
    @Default(null) VendorLocation? vendorLocation,
    @Default(false) bool isOpenToReceive,
    @Default(null) ProcDetail? procDetail,
    @Default([]) List<ProcFieldCharge> procFieldCharges,
    @Default('') String comment,
    @Default(null) DateTime? selectedSupplyDate,
  }) = _Initial;

  bool get isParentOrder => procDetail?.isParentOrder == true;
  bool get isBulkPlacement => procDetail?.isBulkPlacement == true;

  static const empty = AddProcurementState.input(
    data: ProcurementData.empty,
    inputSku: {},
    isSubmitLoading: false,
    isSubmitCtaActive: false,
    mandiName: '',
    message: '',
  );
}
