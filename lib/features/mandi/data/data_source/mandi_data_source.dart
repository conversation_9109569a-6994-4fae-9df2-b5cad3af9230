import 'package:either_dart/either.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/alloted_item_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/allotment_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/allotment_init_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/trip_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/carry_forward/carry_forward_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/charges/get_charge_types_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/create/create_smo_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/inventory/get_inventory_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/losses/get_loss_init_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/mandi_inventory/mandi_inventory_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/mandis/mandi_info_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/consignment_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/sku/sku_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/sku_types/get_sku_types_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo/smo_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/smo_detail_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/smo_history/smo_history_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/user_mandi/user_mandi_dto.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/input_model/mandi_inventory_input_model.dart';

import '../../domain/entity/lotting/lotting_deviation.dart';

abstract class MandiDataSource {
  // SMO
  Future<Either<ErrorResult<dynamic>, CreateSmoDto>> createSmo(String mandiId);
  Future<Either<ErrorResult<dynamic>, List<MandiInfoDto>>> getAllMandis();
  Future<Either<ErrorResult<dynamic>, UserMandiDto>> getUserMandis();
  Future<Either<ErrorResult<dynamic>, SmoDto>> getSmo(String mandiId);

  // Sku
  Future<Either<ErrorResult<dynamic>, List<SkuDto>>> getSku(String language);

  // Procurement
  Future<Either<ErrorResult<dynamic>, String>> addProcurement(
    Map<String, dynamic> body,
  );

  //Get incoming stock list
  Future<Either<ErrorResult<dynamic>, List<ConsignmentDto>>>
      getIncomingStockList(int mandiId);

  // Get carry forward
  Future<Either<ErrorResult<dynamic>, CarryForwardDto>> getCarryForward({
    required int smoId,
  });

  // Get charges type
  Future<Either<ErrorResult<dynamic>, GetChargeTypesDto>> getChargesType();

  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getFieldCharges(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, GetLossInitDto>> getLossInit();

  Future<Either<ErrorResult<dynamic>, GetSkuTypesDto>> getSkuTypes();

  Future<Either<ErrorResult<dynamic>, void>> acceptIncomingStocks(
    int smoId,
    InventoryRecievingInputModel inventoryRecievingInputModel,
  );
  Future<Either<ErrorResult<dynamic>, String>> addCharges(
    Map<String, dynamic> body,
  );

  Future<Either<ErrorResult<dynamic>, MandiInventoryDto>> getMandiInventory(
    int mandiId,
  );

  Future<Either<ErrorResult<dynamic>, void>> updateMandiInventory(
    MandiInventoryInputModel inputModel,
  );

  Future<Either<ErrorResult<dynamic>, String>> acceptCarryForward(
    Map<String, dynamic> body,
  );

  Future<Either<ErrorResult<dynamic>, GetInventoryDto>> getInventory(
    int mandiId,
    String type,
  );

  Future<Either<ErrorResult<dynamic>, void>> closeInventory(
    int smoId,
    int mandiId,
    List<SkuInputWithLoss> data,
  );

  Future<Either<ErrorResult<dynamic>, AllotmentInitDto>> getAllotmentInit(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, int>> createAllotment({
    required int smoId,
    required int destinationId,
    required String destinationType,
    required int deliveryDate,
    required String deliverySlot,
    required String? customerGroup,
  });

  Future<Either<ErrorResult<dynamic>, List<AllotmentDto>>> getAllotments(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, List<AllotedItemDto>>> getAllotedItems(
    int allotmentId,
  );

  Future<Either<ErrorResult<dynamic>, String>> submitAllotment(
    Map<String, dynamic> body,
  );

  Future<Either<ErrorResult<dynamic>, List<SmoHistoryDto>>> getSmoHistory();

  Future<Either<ErrorResult<dynamic>, List<SmoDetailDto>>> getSmoDetails(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, String>> updateOrSubmitEditProcurement(
    Map<String, dynamic> body, {
    bool isSubmit = false,
  });

  Future<Either<ErrorResult<dynamic>, String>> submitConversion(
    Map<String, dynamic> body,
  );
  Future<Either<ErrorResult<dynamic>, void>> closeSmoOps(
    int smoId,
  );

  Future<Either<ErrorResult<dynamic>, List<TripDto>>> getTripsForAllocation(
    int allocationId,
  );

  Future<Either<ErrorResult<dynamic>, String>> dispatchTrip({
    required int tripId,
    required String driverName,
    required String driverPhone,
    required String vehicleNumber,
    required int smoId,
  });

  Future<Either<ErrorResult<dynamic>, void>> cancelAllocation(
    int allocationId,
  );

  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getLanguages();

  Future<Either<ErrorResult<dynamic>, Map<String, dynamic>>> getLanguageFile(
    String code,
  );

  Future<Either<ErrorResult<dynamic>, LottingDeviation>> getLottingDeviation();
  Future<Either<ErrorResult<dynamic>, bool>> regenerateDeliveryMemo(
      int allotmentId, String langCode);
}
