import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order_details.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/liquidation/summary/cubit/liquidation_summary_cubit.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

class LiquidationSummaryScreen extends StatefulWidget {
  final int mandiId;
  final int smoId;
  final bool isEditOnlyFromWeighingMachine;

  const LiquidationSummaryScreen({
    super.key,
    required this.mandiId,
    required this.smoId,
    required this.isEditOnlyFromWeighingMachine,
  });
  @override
  State<LiquidationSummaryScreen> createState() =>
      _LiquidationSummaryScreenState();
}

class _LiquidationSummaryScreenState extends State<LiquidationSummaryScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _loadData();
    });
    super.initState();
  }

  List<Sku> get skus => context
      .read<SkuBloc>()
      .state
      .maybeMap(orElse: () => <Sku>[], success: (s) => s.skus);

  void _loadData() {
    final cubit = context.read<LiquidationSummaryCubit>();
    cubit.loadLiquidationSummary(
      widget.smoId,
      skus,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SkuBloc, SkuState>(
      listener: (context, state) {
        state.maybeMap(
          orElse: () {},
          success: (s) {
            _loadData();
          },
        );
      },
      child: SafeArea(
        child: BlocBuilder<LiquidationSummaryCubit, LiquidationSummaryState>(
          builder: (context, state) {
            return Scaffold(
              backgroundColor: Colors.grey.shade300,
              appBar: WAppBar.getAppBar(
                title: Text('Liquidation Summary'),
                centerTitle: false,
                actions: [
                  IconButton(
                    icon: Icon(Icons.list),
                    onPressed: () async {
                      await context.push(
                          context.namedLocation(
                            'liquidationList',
                            pathParameters: {
                              'mandiId': widget.mandiId.toString(),
                              'smoId': widget.smoId.toString(),
                            },
                          ),
                          extra: {
                            'isEditOnlyFromWeighingMachine':
                                widget.isEditOnlyFromWeighingMachine,
                          });
                      _loadData();
                      // Handle list icon press
                    },
                  ),
                ],
              ),
              floatingActionButton: FloatingActionButton(
                onPressed: () async {
                  await context.push(
                      context.namedLocation(
                        'updateLiquidation',
                        pathParameters: {
                          'mandiId': widget.mandiId.toString(),
                          'smoId': widget.smoId.toString(),
                        },
                      ),
                      extra: {
                        'isEditOnlyFromWeighingMachine':
                            widget.isEditOnlyFromWeighingMachine,
                      });
                  _loadData();
                },
                child: Icon(Icons.add),
              ),
              body: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    color: Colors.grey[100],
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 4.0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'unit',
                              'Unit',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'liqQty',
                              'Liq Qty',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'pending',
                              'Pending',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'amount',
                              'Amount',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'vendor',
                              'Vendor',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: state.isLoading
                        ? Center(
                            child: CircularProgressIndicator(),
                          )
                        : state.liquidationSummary.isEmpty
                            ? EmptyScreen(
                                message: 'No Liquidations Summary for SMO!',
                              )
                            : Scrollbar(
                                child: ListView(
                                  children: [
                                    for (final item in state.liquidationSummary)
                                      skuCard(context, item),
                                  ],
                                ),
                              ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget skuCard(BuildContext context, List<LiquidationOrderSummary> summary) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 4,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              summary.first.skuName,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            for (final input in summary)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: InkWell(
                  onTap: () async {
                    await context.push(
                      context.namedLocation(
                        'updateLiquidation',
                        pathParameters: {
                          'mandiId': widget.mandiId.toString(),
                          'smoId': widget.smoId.toString(),
                        },
                      ),
                      extra: {
                        'itemId': input.item.id,
                        'isEditOnlyFromWeighingMachine':
                            widget.isEditOnlyFromWeighingMachine,
                      },
                    );
                    _loadData();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                        color: input.item.isFinalized
                            ? Colors.green.shade100
                            : Colors.grey.shade100,
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade300,
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ]),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 4.0),
                          child: Text(
                            input.item.isFinalized
                                ? '• completed'
                                : '• pending',
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 2,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 2,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  left: 4,
                                ),
                                child: Text(
                                  input.item.skuQuantity.getUnitString(),
                                  style: TextStyle(
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                input.item.skuQuantity.quantity?.asString() ??
                                    '-',
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                input.item.availableForLiquidation.asString(),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              flex: 2,
                              child: Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 8.0),
                                  child: Text(
                                    input.amount.asString(),
                                    style: TextStyle(
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.end,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 4),
                            Expanded(
                              flex: 2,
                              child: Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 0.0),
                                  child: Text(
                                    input.vendor.name,
                                    style: TextStyle(
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.end,
                                  ),
                                ),
                              ),
                            ),
                            // SizedBox(width: 4),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 0.0),
              child: Container(
                padding: EdgeInsets.symmetric(
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: 4,
                        ),
                        child: Text(
                          'Total',
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(flex: 2, child: SizedBox()),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(flex: 2, child: SizedBox()),
                    SizedBox(width: 4),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Text(
                            summary
                                .fold(0.0, (p, e) => p + (e.amount))
                                .toString(),
                            style: TextStyle(
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 4),
                    Expanded(
                      flex: 2,
                      child: SizedBox(),
                    ),
                    // SizedBox(width: 4),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
