import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/utils/file_upload/model/aws_file_url.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/features/mandi/data/data_source/remote/service/mandi_service.dart';
import 'package:uuid/uuid.dart';

abstract class S3BaseUploader {
  String _appName = "";
  Map<String, dynamic> _entityMap = {};
  String host = "";
  Map<String, dynamic> _metadataMap = {};

  String bytesToString(int bytes) {
    if (bytes < 1024) {
      return "$bytes B";
    } else if (bytes < 1024 * 1024) {
      return "${(bytes / 1024).toStringAsFixed(2)} KB";
    } else {
      return "${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB";
    }
  }

  String pathGenerator({
    required String entity,
    required String fileName,
    String? metaData,
  }) {
    if (!_entityMap.containsKey(entity)) {
      return entity;
    }
    String template = _entityMap[entity]['path'];
    String uuid = const Uuid().v4();
    final dateFormatRegex = RegExp(r'##DATE\[(.*?)\]##');
    final dateFormatMatch = dateFormatRegex.firstMatch(template);
    String dateFormat = dateFormatMatch?.group(1) ?? 'yyyyMMdd';
    String fileExtension = getFileExtension(fileName);

    String result = template
        .replaceAll('##APPNAME##', _appName)
        .replaceAll('##ENTITY##', entity)
        .replaceAll(
            dateFormatRegex, DateFormat(dateFormat).format(DateTime.now()))
        .replaceAll('##UUID##', uuid)
        .replaceAll('##FILENAME##', fileName.replaceAll('.$fileExtension', ''))
        .replaceAll('##EXT##', fileExtension);
    final metadataRegex = RegExp(r'##METADATA(?:\[(.*?)\])?##');
    result = result.replaceAllMapped(metadataRegex, (match) {
      String? key = match.group(1);
      if (key == null) {
        return metaData ?? '';
      } else if (_metadataMap.containsKey(key)) {
        return _metadataMap[key]!;
      }
      return '';
    });

    return result;
  }

  String getFileExtension(String fileName) {
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last : 'jpg';
  }

  void updateData(
      {required Map<String, dynamic> entityMap,
      required String appName,
      required String hostData}) {
    _appName = appName;
    _entityMap = entityMap;
    host = hostData;
  }

  Future<List<AWSFileUrl>> getUploadUrl(
    UploadFileModule module,
    Map<String, dynamic> body,
    List<PlatformFile> files,
    MandiService mandiService,
  );
  Future<bool> uploadFileToAws(String preSignedUrl, PlatformFile file);
}
