import 'package:freezed_annotation/freezed_annotation.dart';

part 'allocate_item_input_model.freezed.dart';

@freezed
class AllocateItemInputModel with _$AllocateItemInputModel {
  const AllocateItemInputModel._();
  const factory AllocateItemInputModel({
    required int? id,
    required int skuId,
    required String type,
    required String unit,
    required double lotSize,
    required double quantity,
    @Default(null) double? supplyQuantity,
    @Default('') String allotmentQuantity,
  }) = _AllocateItemInputModel;

  bool isBulk() {
    return type.toLowerCase() == 'bulk';
  }

  String getCompositeKey() {
    if (isBulk()) {
      return '$skuId-$type-$unit';
    } else {
      return '$skuId-$type-$unit-$lotSize';
    }
  }

  String getUnitString() {
    if (isBulk()) {
      return 'Bulk - $unit';
    }
    return 'Lots - $lotSize $unit';
  }
}
