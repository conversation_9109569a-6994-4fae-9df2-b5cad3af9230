import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';

class GetSmoHistoryRequest extends BaseRequest<dynamic, List<List<SmoOps>>> {
  @override
  String getPath() => 'smo/smoHistory/getSmoHistory';

  @override
  List<List<SmoOps>> mapper(data) {
    if (data is List<dynamic>) {
      return _preprocessList(data
          .map((e) => e as Map<String, dynamic>)
          .map((e) => SmoOps(
                smoId: e['id'] as int,
                mandiId: e['mandiId'] as int,
                currentStatus: e['status'] as String,
                smoStartTime: e['date'] as int,
                smoEndTime: e['closeDate'] as int?,
              ))
          .toList());
    }
    throw Exception('Invalid response');
  }

  @override
  RequestMethod get method => RequestMethod.GET;

  List<List<SmoOps>> _preprocessList(List<SmoOps> list) {
    final map = <String, List<SmoOps>>{};

    for (final smoOps in list) {
      if (smoOps.smoEndTime != null) {
        final date = smoOps.smoEndTime!.toDate('dd-MM-YYYY');
        if (!map.containsKey(date)) {
          map[date] = [];
        }
        map[date]!.add(smoOps);
      }
    }
    for (final key in map.keys) {
      map[key]!.sort((a, b) => b.smoEndTime!.compareTo(a.smoEndTime!));
    }

    return map.values.toList();
  }
}
