import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/add_field_charges/input_models/charge_input_model.dart';

@injectable
class AddChargesUseCase {
  AddChargesUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, String>> call(
    int smoId,
    List<String> files,
    List<ChargeInputModel> charges,
  ) async {
    final chargesJson = charges
        .map(
          (e) => {
            'type': e.chargeType,
            'amount': e.amount,
            'comment': e.comment,
          },
        )
        .toList();
    return _mandiRepository.addCharges(
      smoId: smoId,
      files: files,
      charges: chargesJson,
    );
  }
}
