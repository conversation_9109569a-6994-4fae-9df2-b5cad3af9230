import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';

part 'proc_item_state.freezed.dart';

@freezed
abstract class ProcItemState with _$ProcItemState {
  const factory ProcItemState.initial() = ProcItemInitial;

  const factory ProcItemState.loading() = ProcItemLoading;

  const factory ProcItemState.loaded(
    List<List<ProcDetailItem>> myProcDetailList,
    List<ProcFieldCharge> fieldCharges,
    VendorLocation? vendorLocation,
    Map<String, dynamic> urls,
    String comments,
    ParentOrder? parentOrder,
    String? refId,
    String? refSource,
  ) = ProcItemLoaded;

  const factory ProcItemState.error(String errorMessage) = ProcItemError;
}
