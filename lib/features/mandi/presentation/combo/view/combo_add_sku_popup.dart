import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_quantity.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/grading/cubit/grading_conversion_cubit.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class ComboAddSkuPopup extends StatefulWidget {
  const ComboAddSkuPopup({
    super.key,
    required this.smoId,
    required this.skus,
    this.procurementType,
    this.unit,
    this.lotSize,
    this.qty,
    this.sku,
    this.isEditing = false,
    required this.skuImages,
    this.onDelete,
    required this.compositeKey,
    required this.config,
  });
  final int smoId;
  final List<Sku> skus;
  final ProcurementType? procurementType;
  final String? unit;
  final double? lotSize;
  final WeighingQuantity? qty;
  final Sku? sku;
  final bool isEditing;
  final List<PickedFile> skuImages;
  final GestureTapCallback? onDelete;
  final List<String> compositeKey;
  final ConversionInventoryConfig config;

  @override
  State<ComboAddSkuPopup> createState() => _ComboAddSkuPopupState();
}

class _ComboAddSkuPopupState extends State<ComboAddSkuPopup> {
  final TextEditingController _skuSearchController = TextEditingController();
  final TextEditingController _qtyController = TextEditingController();
  ProcurementType _procurementType = ProcurementType.bulk;
  String? procUnit;
  double? lotSize;
  List<PickedFile> skuImages = [];
  Sku? selectedSku;
  WeighingQuantity? _weighingQuantity = WeighingQuantity.noSouce(null);

  @override
  void initState() {
    if (widget.sku != null) {
      selectedSku = widget.sku;
      final foundSku =
          widget.skus.where((e) => e.id == widget.sku!.id).firstOrNull;
      if (foundSku != null) {
        _skuSearchController.text = foundSku.name;
      }
    }
    if (widget.qty != null) {
      _qtyController.text = widget.qty!.value ?? '';
      _weighingQuantity = widget.qty;
    }

    if (widget.procurementType != null) {
      _procurementType = widget.procurementType!;
    }
    if (widget.unit != null) {
      procUnit = widget.unit!;
    }
    if (widget.lotSize != null) {
      lotSize = widget.lotSize!;
    }

    if (widget.skuImages.isNotEmpty) {
      skuImages.addAll(widget.skuImages);
    }

    super.initState();
  }

  @override
  void dispose() {
    _skuSearchController.dispose();
    _qtyController.dispose();
    super.dispose();
  }

  String? getCurrentCompositeKey() {
    if (selectedSku == null) return null;
    if (_procurementType == ProcurementType.bulk)
      return '${selectedSku!.id}-${_procurementType.value.toUpperCase()}-${procUnit ?? ''}';
    return '${selectedSku!.id}-${_procurementType.value.toUpperCase()}-${procUnit ?? ''}-${lotSize ?? ''}';
  }

  @override
  Widget build(BuildContext context) {
    bool hasSameCompositeKey =
        widget.compositeKey.contains(getCurrentCompositeKey() ?? '');
    return Popup(
        title: widget.isEditing ? 'Edit Sku' : 'Add Sku',
        height: 0.5,
        actions: widget.isEditing
            ? [
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: InkWell(
                    onTap: widget.onDelete,
                    child: const Icon(Icons.delete, color: Colors.white),
                  ),
                ),
              ]
            : null,
        children: [
          Expanded(
              child: ListView(
            children: [
              CustomDropdown.search(
                items: widget.skus.map((e) => e.name).toList(),
                controller: _skuSearchController,
                hintText: getLangText(
                  'gradingConversion.selectSku',
                  'Select Sku',
                ),
                onChanged: (value) {
                  final selectedSku = widget.skus
                      .where((element) => element.name == value)
                      .firstOrNull;
                  if (selectedSku != null) {
                    setState(() {
                      if (this.selectedSku != selectedSku) {
                        procUnit = null;
                        lotSize = null;
                      }
                      this.selectedSku = selectedSku;
                    });
                  }
                },
              ),
              SizedBox(
                height: 8,
              ),
              if (this.selectedSku != null) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: 16.0,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: procurementTypeField(
                          _procurementType,
                          true,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: unitField(
                          unit: procUnit,
                          isBulk: _procurementType == ProcurementType.bulk,
                          bulkUnits: selectedSku!.bulkUnitTypes,
                          lotSizesUnits: selectedSku!.lotSizes.keys.toList(),
                          allowEditing: true,
                        ),
                      ),
                      if (_procurementType == ProcurementType.lots &&
                          procUnit != null) ...[
                        Expanded(
                          child: lotSizeField(
                            lotSize: lotSize,
                            lotSizes: selectedSku!.lotSizes[procUnit] ?? [],
                            allowEditing: true,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if (_procurementType == ProcurementType.bulk &&
                          procUnit?.toLowerCase() == 'kg')
                        Padding(
                          padding: const EdgeInsets.only(
                            right: 16,
                          ),
                          child: LangText(
                            'gradingConversion.qtyLabel',
                            'Qty',
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                        ),
                      SizedBox(
                        width: 120,
                        child: _procurementType == ProcurementType.bulk &&
                                procUnit?.toLowerCase() == 'kg'
                            ? WeighingQuantityButton(
                                isReadOnly: false,
                                label: _qtyController.text,
                                popupBuilder: () {
                                  return WeightCapturePopupProcurementReceive(
                                    isBulkKg: true,
                                    isManualEditAllowed: !(widget
                                        .config.isEditOnlyFromWeighingMachine),
                                    initialWeight: _qtyController.text
                                        .toDouble()
                                        .asString()
                                        .toDouble(),
                                    skuName: selectedSku?.name ?? '',
                                    unitInfo: 'BULK - KG',
                                  );
                                },
                                onChange: (quantity) {
                                  if (quantity != null) {
                                    setState(() {
                                      _qtyController.text =
                                          quantity.value ?? '';
                                      _weighingQuantity = quantity;
                                    });
                                  }
                                },
                              )
                            : TextFormField(
                                controller: _qtyController,
                                decoration: InputDecoration(
                                  label: LangText(
                                    'gradingConversion.qtyLabel',
                                    'Qty',
                                  ),
                                  isDense: true,
                                  contentPadding: EdgeInsets.symmetric(
                                    vertical: 8,
                                    horizontal: 16,
                                  ),
                                  border: OutlineInputBorder(),
                                  counter: SizedBox(),
                                ),
                                keyboardType: TextInputType.number,
                                maxLength: 10,
                                textInputAction: TextInputAction.next,
                                inputFormatters: Config.getNumberFilter(
                                    isBulk: _procurementType ==
                                        ProcurementType.bulk,
                                    unit: procUnit?.toLowerCase() ?? ''),
                                onChanged: (val) {
                                  setState(() {
                                    _weighingQuantity =
                                        WeighingQuantity.manual(val);
                                  });
                                },
                              ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  child: InlineImagePicker(
                    files: skuImages,
                    minFileAllowed: 1,
                    maxFileAllowed: 4,
                    allowMultiple: true,
                    uploadAlso: true,
                    module: UploadFileModule.grading,
                    smoId: widget.smoId,
                    updateFile: (files) {
                      setState(() {
                        skuImages = files;
                      });
                    },
                  ),
                ),
              ],
            ],
          )),
          if (hasSameCompositeKey)
            Align(
              alignment: Alignment.center,
              child: Text(
                'Sku is already added!',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 15,
                ),
              ),
            ),
          WStickyBottomCta(
            isEnabled: _qtyController.text.isNotEmpty &&
                procUnit != null &&
                !hasSameCompositeKey &&
                (_procurementType == ProcurementType.lots
                    ? lotSize != null
                    : true),
            icon: Icons.check,
            label: LangText('gradingPopup.submitCta', 'Submit'),
            onPressed: () {
              context.pop(
                GradingInput(
                  sku: selectedSku!,
                  files: skuImages,
                  skuQuantity: SkuQuantity(
                      skuId: selectedSku!.id,
                      type: _procurementType.value.toUpperCase(),
                      unit: procUnit!,
                      lotSize: lotSize,
                      quantity: _qtyController.text.toDouble()),
                  quantity: _qtyController.text,
                  weighingQuantity: _weighingQuantity,
                ),
              );
            },
          ),
        ]);
  }

  Widget unitField({
    required String? unit,
    required bool isBulk,
    required List<String> bulkUnits,
    required List<String> lotSizesUnits,
    required bool allowEditing,
  }) {
    ;
    final items = isBulk ? bulkUnits : lotSizesUnits;
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectUnitLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: unit,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((String item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (String? newValue) {
                          setState(() {
                            procUnit = newValue;
                            this.lotSize = null;
                            _qtyController.clear();
                            _weighingQuantity = WeighingQuantity.noSouce(null);
                          });
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    unit?.capitalize().localized() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget lotSizeField({
    required double? lotSize,
    required dynamic lotSizes,
    required bool allowEditing,
  }) {
    final items = <double>[];
    if (lotSizes is List) {
      for (final element in lotSizes) {
        items.add(double.parse(element.toString()));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectLotSizeLabel.localized(),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: lotSize,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((double item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.toString()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (double? newValue) {
                          setState(() {
                            this.lotSize = newValue;
                          });
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    lotSize?.toString() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget procurementTypeField(
    ProcurementType procurementType,
    bool allowEditing,
  ) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectProcTypeLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton<ProcurementType>(
                  // Initial Value
                  value: procurementType,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: ProcurementType.values.map((ProcurementType item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.value.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (ProcurementType? newValue) {
                          setState(() {
                            _procurementType = newValue!;
                            procUnit = null;
                            this.lotSize = null;
                            _qtyController.clear();
                          });
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    procurementType.value.capitalize().localized(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }
}
