import 'package:freezed_annotation/freezed_annotation.dart';

part 'allocation_change_response.freezed.dart';
part 'allocation_change_response.g.dart';

@freezed
class AllocationChangeResponse with _$AllocationChangeResponse {
  const factory AllocationChangeResponse({
    required String message,
    required bool refreshSupplyOrder,
  }) = _AllocationChangeResponse;

  @override
  factory AllocationChangeResponse.fromJson(Map<String, dynamic> json) =>
      _$AllocationChangeResponseFromJson(json);
}
