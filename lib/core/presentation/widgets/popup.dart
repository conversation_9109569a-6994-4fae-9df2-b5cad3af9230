import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:proc2/core/utils/config.dart';

class Popup extends StatelessWidget {
  Popup({
    super.key,
    required this.title,
    required this.children,
    this.height = 0.6,
    List<Widget>? actions,
    this.width = kIsWeb ? 0.5 : 0.9,
    this.maxWidth = 400,
  }) : this.actions = actions ?? [];
  final String title;
  final List<Widget> actions;
  final List<Widget> children;
  final double height;
  final double width;
  final double maxWidth;

  @override
  Widget build(BuildContext context) {
    double containerWidth = MediaQuery.of(context).size.width * width;
    if (containerWidth > maxWidth) containerWidth = maxWidth;
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            width: containerWidth,
            height: MediaQuery.of(context).size.height * height,
            padding: const EdgeInsets.only(
              top: 0,
              left: 20,
              right: 20,
            ),
            child: Container(
              color: Colors.white,
              width: MediaQuery.of(context).size.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Config.primaryColor,
                      boxShadow: const <BoxShadow>[
                        BoxShadow(
                          color: Colors.black54,
                          blurRadius: 10,
                          offset: Offset(0, 0.75),
                        )
                      ],
                    ),
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Text(
                                title,
                                style: const TextStyle(
                                  fontSize: 18,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            ],
                          ),
                        ),
                        ...actions,
                        InkWell(
                          onTap: () => Navigator.of(context).pop(),
                          child: const Icon(
                            Icons.cancel,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...children,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
