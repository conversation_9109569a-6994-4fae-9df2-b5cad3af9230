import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_configuration.dart';

class GetAllocationConfigurationRequest
    extends BaseRequest<dynamic, AllocationConfiguration> {
  final int allocationId;

  GetAllocationConfigurationRequest({
    required this.allocationId,
  });

  @override
  String getPath() => 'allotments/config/allotmentId/$allocationId';

  @override
  AllocationConfiguration mapper(data) {
    if (data is Map<String, dynamic>) {
      return AllocationConfiguration.fromJson(data);
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
