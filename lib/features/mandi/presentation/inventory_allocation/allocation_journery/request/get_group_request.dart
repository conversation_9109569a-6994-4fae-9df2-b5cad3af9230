import 'package:either_dart/src/either.dart';
import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/group_info.dart';

class GetGroupRequest extends BaseRequest<dynamic, List<GroupInfo>> {
  final int allocationId;

  GetGroupRequest({required this.allocationId});

  @override
  String getPath() => 'allotments/$allocationId/groups';

  @override
  Future<Either<ErrorResult, List<GroupInfo>>?> mockData() {
    return Future.value(Right([]));
  }

  @override
  List<GroupInfo> mapper(data) {
    if (data is List<dynamic>) {
      return data.map((e) => GroupInfo.fromJson(e)).toList();
    }
    throw Exception('Invalid Response!');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
