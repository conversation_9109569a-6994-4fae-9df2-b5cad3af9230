import 'package:freezed_annotation/freezed_annotation.dart';

part 'loss_type_v2.freezed.dart';
part 'loss_type_v2.g.dart';

@freezed
class LossTypeV2 with _$LossTypeV2 {
  const factory LossTypeV2({
    required String lossType,
    required int maxImageUpload,
    @Default(0) int minImageUpload,
  }) = _LossTypeV2;

  @override
  factory LossTypeV2.fromJson(Map<String, dynamic> json) =>
      _$LossTypeV2FromJson(json);
}
