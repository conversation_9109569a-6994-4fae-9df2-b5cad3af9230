import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/vendor/view/vendor_drop_down.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/bloc/add_procurement_bloc.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/view/proc_field_charges_widget.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/place_order_popup/cubit/place_order_cubit.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/place_order_popup/view/place_order_popup.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/view/add_procurement_header.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

class AddProcurement extends StatelessWidget {
  const AddProcurement({
    super.key,
    required this.mandiId,
    required this.mandiName,
    required this.smoId,
    ProcDetail? this.procDetail,
  });
  final int mandiId;
  final String mandiName;
  final int smoId;
  final ProcDetail? procDetail;

  @override
  Widget build(BuildContext context) {
    final userName = context.read<AuthBloc>().state.maybeWhen(
          authenticated: (user) => user.name,
          orElse: () => '',
        );
    return context.watch<SkuBloc>().state.maybeMap(
        orElse: () => Center(
              child: CircularProgressIndicator(),
            ),
        success: (s) {
          return BlocProvider(
            create: (context) => di.get<AddProcurementBloc>()
              ..add(
                AddProcurementEvent.init(
                  userName: userName,
                  smoId: smoId,
                  mandiName: mandiName,
                  procDetail: procDetail,
                  skus: s.skus,
                ),
              ),
            child: _AddProcurement(
              smoId: smoId,
              mandiId: mandiId,
            ),
          );
        });
  }
}

class _AddProcurement extends StatefulWidget {
  const _AddProcurement({
    required this.smoId,
    required this.mandiId,
  });
  final int smoId;
  final int mandiId;
  @override
  State<_AddProcurement> createState() => __AddProcrement();
}

class __AddProcrement extends State<_AddProcurement> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _procEndKey = GlobalKey();
  final GlobalKey _fieldChargeStartKey = GlobalKey();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final procConfig = context
        .read<SmoBloc>()
        .state
        .mapOrNull(
          success: (s) => s.config,
        )
        ?.procurement;
    return BlocBuilder<AddProcurementBloc, AddProcurementState>(
      builder: (context, state) {
        final allInputs = context.watch<AddProcurementBloc>().state.inputSku;
        return SafeArea(
          child: BlocListener<AddProcurementBloc, AddProcurementState>(
            listener: (context, state) {
              if (state.shouldPop) {
                context.pop();
              }
              if (state.message.isNotEmpty) {
                showSnackBar(state.message);
                context
                    .read<AddProcurementBloc>()
                    .add(const AddProcurementEvent.clearMessage());
              }
            },
            child: WillPopScope(
              onWillPop: () async {
                // return true;
                if (allInputs.isEmpty) return true;
                return await _showExitPopup() ?? false;
              },
              child: Scaffold(
                body: Container(
                  color: Colors.grey[300],
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AddProcHeader(
                        procConfig: procConfig,
                        isParentOrder: state.isParentOrder,
                        smoId: widget.smoId,
                        mandiId: widget.mandiId,
                        isUploadSlipsEnabled: state.isSubmitCtaActive,
                        procCount: state.skuCount,
                        files: state.files,
                        comment: state.comment,
                        onCommentUpdated: (comment) {
                          context
                              .read<AddProcurementBloc>()
                              .add(AddProcurementEvent.updateComment(comment));
                        },
                        onAddedSku: () async {
                          await Future<void>.delayed(
                            const Duration(
                              milliseconds: 200,
                            ),
                          );
                          if (_procEndKey.currentContext != null) {
                            Scrollable.ensureVisible(
                                _procEndKey.currentContext!);
                            await Future<void>.delayed(
                              const Duration(
                                milliseconds: 100,
                              ),
                            );
                            _scrollController.animateTo(
                              _scrollController.offset - 100,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                          }
                        },
                        onBack: () async {
                          // context.pop();
                          if (allInputs.isEmpty) {
                            return context.pop();
                          }
                          final shouldPop = await _showExitPopup() ?? false;
                          if (shouldPop) {
                            context.pop();
                          }
                        },
                      ),
                      Container(
                        color: Colors.white,
                        width: double.infinity,
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: !state.isOpenToReceive ||
                                      state.vendorLocation == null
                                  ? 4
                                  : 16,
                              horizontal: 16,
                            ),
                            child: VendorDropDown(
                              isEnabled: !state.isOpenToReceive ||
                                  state.vendorLocation == null,
                              onChanged: (location) {
                                context.read<AddProcurementBloc>().add(
                                    AddProcurementEvent.updateVendor(location));
                              },
                              selectedVendorLocation: state.vendorLocation,
                            ),
                          ),
                        ),
                      ),
                      Container(
                        color: Colors.grey[100],
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8.0,
                            vertical: 4.0,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Expanded(
                                flex: 3,
                                child: LangText(
                                  'unit',
                                  'Unit',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'orderedQty',
                                  'Ord Qty',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'received',
                                  'Received',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              if (!state.isBulkPlacement ||
                                  state.isParentOrder) ...[
                                SizedBox(
                                  width: 4,
                                ),
                                Expanded(
                                  flex: 2,
                                  child: LangText(
                                    'amount',
                                    'Amount',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.end,
                                  ),
                                ),
                              ],
                              if (!state.isBulkPlacement ||
                                  state.isParentOrder ||
                                  procConfig?.receiveProcurement.isAllowed ==
                                      true)
                                SizedBox(
                                  width: 44,
                                ),
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 12,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 0,
                            horizontal: 0,
                          ),
                          child: allInputs.isEmpty
                              ? EmptyScreen(
                                  message: LanguageEnum.addProcNoSkuDescription
                                      .localized(),
                                )
                              : Scrollbar(
                                  child: SingleChildScrollView(
                                    controller: _scrollController,
                                    child: Column(
                                      children: [
                                        for (final key in allInputs.keys)
                                          skuCard(
                                            allInputs[key]!,
                                            showAmount:
                                                !state.isBulkPlacement ||
                                                    state.isParentOrder,
                                            isParentOrder: state.isParentOrder,
                                            receiveProcurementConfig:
                                                procConfig?.receiveProcurement,
                                          ),
                                        SizedBox(
                                          key: _procEndKey,
                                        ),
                                        SizedBox(
                                          height: 16,
                                        ),
                                        Visibility(
                                          visible: !state.isBulkPlacement ||
                                              state.isParentOrder,
                                          child: ProcFieldChargesWidget(
                                            key: _fieldChargeStartKey,
                                            smoId: widget.smoId,
                                            padding: EdgeInsets.all(8),
                                            fieldCharges:
                                                state.procFieldCharges,
                                            onChanged: (value, index) {
                                              context.read<AddProcurementBloc>()
                                                ..add(
                                                  AddProcurementEvent
                                                      .updateProcCharge(
                                                    value,
                                                    index,
                                                  ),
                                                );
                                            },
                                            onDelete: (index) {
                                              context.read<AddProcurementBloc>()
                                                ..add(
                                                  AddProcurementEvent
                                                      .deleteProcCharge(
                                                    index,
                                                  ),
                                                );
                                            },
                                            onAdd: (charge) async {
                                              context.read<AddProcurementBloc>()
                                                ..add(
                                                  AddProcurementEvent
                                                      .addProcCharge(charge),
                                                );
                                              await Future<void>.delayed(
                                                const Duration(
                                                  milliseconds: 200,
                                                ),
                                              );
                                              await _scrollController.animateTo(
                                                _scrollController
                                                    .position.maxScrollExtent,
                                                duration: const Duration(
                                                    milliseconds: 300),
                                                curve: Curves.easeOut,
                                              );
                                            },
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                        ),
                      ),
                      Card(
                        color: Colors.white,
                        elevation: 4,
                        child: Column(
                          children: [
                            Visibility(
                              visible:
                                  !state.isBulkPlacement || state.isParentOrder,
                              child:
                                  AmountSummary(state, onFieldChargeClick: () {
                                Scrollable.ensureVisible(
                                  _fieldChargeStartKey.currentContext!,
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeOut,
                                );
                              }),
                            ),
                            Builder(builder: (_) {
                              if (state.isBulkPlacement) {
                                final placedBy =
                                    state.procDetail?.parentOrder?.placedBy;
                                return Padding(
                                  padding: const EdgeInsets.only(
                                    left: 16,
                                    right: 16,
                                    bottom: 8,
                                    top: 8,
                                  ),
                                  child: Container(
                                    width: double.infinity,
                                    child: Text(
                                      state.isParentOrder
                                          ? 'This is a parent order. You can only update amount and charges.'
                                          : placedBy == null
                                              ? 'This order has been placed by other facility'
                                              : 'This order has been placed by $placedBy',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.blue,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                );
                              }
                              return SizedBox();
                            }),
                            Padding(
                              padding: const EdgeInsets.only(
                                left: 16,
                                right: 16,
                                bottom: 8,
                                top: 8,
                              ),
                              child: submitButton(
                                isActive: state.isSubmitCtaActive &&
                                    ((state.vendorLocation?.locationId ?? -1) !=
                                            -1 ||
                                        state.procDetail?.vendorName != null),
                                isLoading: state.isSubmitLoading,
                                context: context,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget AmountSummary(AddProcurementState state,
      {required VoidCallback onFieldChargeClick}) {
    if (state.skuCount == 0) return SizedBox();
    final productCost = state.inputSku.values.fold(0.0,
        (p, i) => p + i.fold(0.0, (p, e) => p + (e.amount?.toDouble() ?? 0.0)));
    final fieldChargesCost =
        state.procFieldCharges.fold(0.0, (p, e) => p + (e.amount.toDouble()));

    final totalCost = productCost + fieldChargesCost;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'addProc.productCost'.tr('Product Cost'),
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  productCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: onFieldChargeClick,
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 4,
                    bottom: 4,
                  ),
                  child: Text(
                    'addProc.fieldCharges'.tr('Field Charges'),
                    style: TextStyle(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  fieldChargesCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Divider(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  totalCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget submitButton({
    required bool isActive,
    required bool isLoading,
    required BuildContext context,
  }) {
    return InkWell(
      onTap: !isActive
          ? null
          : () async {
              final shouldSubmit = await context.showAlertDialog(
                  title:
                      getLangText('addProc.submitPopupTitle', 'Are you sure?'),
                  message: getLangText(
                    'addProc.submitPopupMessage',
                    'The procurements will submit.',
                  ));
              if (shouldSubmit == true) {
                context
                    .read<AddProcurementBloc>()
                    .add(const AddProcurementEvent.submit());
              }
            },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
        decoration: BoxDecoration(
          color: isActive ? Config.primaryColor : Colors.grey,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: Colors.white),
          boxShadow: const [
            BoxShadow(
              color: Colors.black45,
              blurRadius: 1,
            ),
          ],
        ),
        child: Center(
          child: isLoading
              ? const SizedBox(
                  height: 24,
                  width: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                )
              : Text(
                  LanguageEnum.addProcSubmitBtn.localized(),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
        ),
      ),
    );
  }

  Widget skuCard(
    List<SkuInputData> skuInputData, {
    bool showAmount = true,
    bool isParentOrder = true,
    required ReceiveProcurementConfig? receiveProcurementConfig,
  }) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 4,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Image.network(
                  skuInputData.first.sku.image,
                  width: 48,
                  height: 48,
                ),
                SizedBox(
                  width: 16,
                ),
                Text(
                  skuInputData.first.sku.name,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            for (int i = 0; i < skuInputData.length; i++)
              Builder(
                builder: (context) {
                  final input = skuInputData[i];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            input.getUnitString(),
                            style: TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 4,
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            input.orderedQuantity.toString(),
                            style: TextStyle(
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        SizedBox(
                          width: 4,
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            input.totalReceived(),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        if (showAmount) ...[
                          SizedBox(width: 4),
                          Expanded(
                            flex: 2,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: Text(
                                  input.amount ?? '-',
                                  style: TextStyle(
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ),
                          ),
                        ],
                        // SizedBox(width: 4),

                        !input.isReceived &&
                                (receiveProcurementConfig?.isAllowed ?? false)
                            ? InkWell(
                                onTap: () async {
                                  final editedData =
                                      await showDialog<SkuInputData?>(
                                    context: context,
                                    builder: (_) => BlocProvider(
                                      create: (context) => di
                                          .get<PlaceOrderCubit>()
                                        ..disableAmountEdit(!showAmount)
                                        ..disableReceiveQty(isParentOrder)
                                        ..editOrder(
                                            input,
                                            skuInputData
                                                .where((e) => e != input)
                                                .map((e) => e.getCompositeKey())
                                                .toList()),
                                      // ..add(SkuEditEvent.notAllowedSkus(
                                      //     skuInputData
                                      //         .where((e) => e != input)
                                      //         .map((e) => e.getCompositeKey())
                                      //         .toList()))
                                      // ..add(
                                      //   SkuEditEvent.init(
                                      //     input,
                                      //     isOpenForEdit: true,
                                      //   ),

                                      child: PlaceOrderPopup(
                                        receiveProcurementConfig:
                                            receiveProcurementConfig,
                                        onDelete: () async {
                                          final shouldDelete =
                                              await context.showAlertDialog(
                                                  title: getLangText(
                                                      'addProc.deletePopupTitle',
                                                      'Are you sure?'),
                                                  message: getLangText(
                                                    'addProc.deletePopupMessage',
                                                    'The procurement will be deleted.',
                                                  ));

                                          if (shouldDelete == true) {
                                            context
                                                .read<AddProcurementBloc>()
                                                .add(AddProcurementEvent.delete(
                                                    input));
                                          }
                                        },
                                      ),
                                    ),
                                  );

                                  if (editedData != null) {
                                    // ignore: use_build_context_synchronously
                                    context.read<AddProcurementBloc>().add(
                                          AddProcurementEvent.update(
                                            editedData,
                                          ),
                                        );
                                  }
                                },
                                child: Container(
                                  width: 44,
                                  child: const Icon(
                                    Icons.edit,
                                    color: Colors.green,
                                  ),
                                ),
                              )
                            : SizedBox(
                                width: 44,
                              ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget keyValue({required String key, required String value}) {
    return SizedBox(
      width: (MediaQuery.of(context).size.width / 2) - 20,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '$key : ',
            style: TextStyle(
                fontWeight: FontWeight.w500, color: Colors.grey.shade600),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          )
        ],
      ),
    );
  }

  Future<bool?> _showExitPopup() {
    return showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Are you sure?'),
        content: const Text(
          'You will lose all the data if you go back!',
        ),
        actions: [
          TextButton(
            onPressed: () {
              context.pop(false);
            },
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              context.pop(true);
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }
}
