import 'package:freezed_annotation/freezed_annotation.dart';

part 'my_proc_summary.freezed.dart';
part 'my_proc_summary.g.dart';

@freezed
class MyProcSummary with _$MyProcSummary {
  const factory MyProcSummary({
    required int skuId,
    required List<MyProcDetail> detail,
  }) = _MyProcSummary;

  factory MyProcSummary.fromJson(Map<String, dynamic> json) =>
      _$MyProcSummaryFromJson(json);
}

@freezed
class MyProcDetail with _$MyProcDetail {
  const MyProcDetail._();
  const factory MyProcDetail({
    required String unit,
    required double quantity,
    @Default(null) double? lotSize,
    @Default(null) String? type,
    @Default(0) double amount,
    @Default(0) double orderedQuantity,
  }) = _MyProcDetail;

  bool get isBulk => type?.toLowerCase() == 'bulk';
  String get diplayLots => lotSize != null ? quantity.toString() : '-';
  String get displayQuantity =>
      lotSize != null ? (lotSize! * quantity).toString() : quantity.toString();

  String getUnitString() {
    if (isBulk) {
      return 'Bulk - $unit';
    }
    return 'Lots - $lotSize $unit';
  }

  factory MyProcDetail.fromJson(Map<String, dynamic> json) =>
      _$MyProcDetailFromJson(json);
}
