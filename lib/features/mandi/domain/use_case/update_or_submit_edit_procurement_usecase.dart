import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/sku_history.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';

@injectable
class UpdateOrSubmitEditProcurementUseCase {
  UpdateOrSubmitEditProcurementUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, String>> call({
    required List<SkuHistory> items,
    required int smoId,
    required int procId,
    required List<String> images,
    bool isSubmit = false,
    required List<ProcFieldCharge> charges,
  }) async {
    final body = {
      'smoId': smoId,
      'procId': procId,
      'images': images,
      'items': items
          .map(
            (e) => {
              'procItemId': e.id,
              'amount': e.amount?.toDouble(),
              'billedCostPrice': e.quantity == 0
                  ? e.orderedCostPrice
                  : ((e.amount?.toDouble() ?? 0) /
                      (e.quantity?.toDouble() ?? 1)),
              'images': e.files.map((e) => e.uploadKey).toList(),
              'isAmountUpdated': e.isAmountUpdated,
            },
          )
          .toList(),
      'charges': charges
          .map(
            (e) => {
              'type': e.type,
              'amount': e.amount.toDouble(),
              'comment': e.comment,
              'images': e.images.map((e) => e.uploadKey).toList(),
            },
          )
          .toList(),
    };

    return _mandiRepository.updateOrSubmitEditProcurement(
      body,
      isSubmit: isSubmit,
    );
  }
}
