import 'package:proc2/features/mandi/data/data_source/remote/dto/inventory/get_inventory_dto.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';

Inventory toInventory(GetInventoryDto inventoryDto) {
  // ignore: omit_local_variable_types, prefer_final_locals
  List<InventoryItem> inventoryItems = [];

  for (final dtoItem in inventoryDto.skus) {
    inventoryItems.add(
      InventoryItem(
        skuId: dtoItem.skuId,
        type: dtoItem.type,
        unit: dtoItem.unit,
        lotSize: dtoItem.lotSize,
        quantity: dtoItem.quantity,
      ),
    );
  }

  return Inventory(skus: inventoryItems);
}
