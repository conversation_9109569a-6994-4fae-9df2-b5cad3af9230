import 'dart:convert';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/utils/local_storage_interface.dart';

@singleton
class LoginBypassConfigInterface extends LocalStorageInterface {
  LoginConfig? loginConfig;

  LoginBypassConfigInterface(@Named('HiveDirPath') String hiveDirPath)
      : super('login_bypass_config', hiveDirPath);

  Future<LoginConfig> _fetchLoginConfig() async {
    try {
      DocumentReference<Map<String, dynamic>> reference = FirebaseFirestore
          .instance
          .collection('allowed_login_type')
          .doc('allowed_login_type');

      DocumentSnapshot<Map<String, dynamic>> result = await reference.get();

      return result.exists
          ? LoginConfig.fromJSON(
              result.data() ?? {'google': false, 'mobile': false})
          : LoginConfig.defaultInit();
    } catch (e) {
      print("Login Config error $e");
      return LoginConfig.defaultInit();
    }
  }

  Future<LoginConfig> getConfig({bool overrideLocal = false}) async {
    try {
      if ((!overrideLocal) && (loginConfig != null)) {
        return loginConfig!;
      }

      LoginConfig config;

      LoginConfig? localConfig = await _read();
      print("Local config = $localConfig Override Local = $overrideLocal");
      if (overrideLocal || localConfig == null) {
        print("Fetching from remote");
        config = await _fetchLoginConfig();
      } else {
        print("USing local");
        config = localConfig;
      }
      await _writeConfig(config);
      loginConfig = config;
      return config;
    } catch (e) {
      print(e);
      return LoginConfig.defaultInit();
    }
  }

  Future<LoginConfig?> _read() async {
    try {
      String? data = await read(storageKey);

      if (data != null) {
        return LoginConfig.fromJSON(jsonDecode(data));
      }
      return null;
    } catch (e) {
      print(e);
      return null;
    }
  }

  Future<bool> _writeConfig(LoginConfig config) async {
    try {
      await write(storageKey, jsonEncode(config.toJSON()));
      return true;
    } catch (e) {
      print(e);
      return false;
    }
  }

  void print(dynamic p) {
    log("Login ByPass Config : ${p.toString()}");
  }
}

class LoginConfig {
  final bool allowGoogleLogin;
  final bool allowPhoneLogin;
  final List<String> skipOTPUsers;

  const LoginConfig(
      {required this.allowGoogleLogin,
      required this.allowPhoneLogin,
      required this.skipOTPUsers});

  Map<String, dynamic> toJSON() {
    return {'google': allowGoogleLogin, 'mobile': allowPhoneLogin};
  }

  factory LoginConfig.defaultInit() {
    return const LoginConfig(
        allowGoogleLogin: false, allowPhoneLogin: false, skipOTPUsers: []);
  }

  factory LoginConfig.fromJSON(Map<String, dynamic> json) {
    log("Login ByPass Config : Login Config json = $json");
    return LoginConfig(
        allowGoogleLogin: json['google'] ?? true,
        allowPhoneLogin: json['mobile'] ?? true,
        skipOTPUsers: List<String>.from(json['flowSkipOTPUsers'] ?? []));
  }

  bool checkOTPSkip(String phone) {
    if (skipOTPUsers.contains('*')) {
      return true;
    }
    print(
        "Checking phone users = $phone. Skip Users = $skipOTPUsers contains in = ${skipOTPUsers.contains(phone)} ");
    return skipOTPUsers.contains(phone);
  }

  bool isAllAllowed() {
    return allowGoogleLogin && allowPhoneLogin;
  }

  bool isAnyoneAllowed() {
    return allowGoogleLogin || allowPhoneLogin;
  }

  bool isAllDisabled() {
    return (!allowGoogleLogin && !allowPhoneLogin);
  }

  void print(dynamic p) {
    log("Login ByPass Config : ${p.toString()}");
  }
}
