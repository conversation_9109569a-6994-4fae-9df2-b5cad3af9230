import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/data/data_source/mapper/to_inventory_mapper.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/inventory/get_inventory_dto.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';

class GetMandiInventoryRequest extends BaseRequest<dynamic, Inventory> {
  final int mandiId;
  final String type;

  GetMandiInventoryRequest({required this.mandiId, required this.type});

  @override
  Map<String, dynamic>? getQuery() {
    return {'type': type};
  }

  @override
  String getPath() => 'mandis/inventory/$mandiId';
  @override
  Inventory mapper(data) {
    if (data is List<dynamic>) {
      return toInventory(GetInventoryDto.fromJson({'skus': data}));
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
