import 'package:chopper/chopper.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/data/network/interceptors/auth_interceptor.dart';
import 'package:proc2/core/data/network/interceptors/logger/talker_chopper_logger_interceptor.dart';
import 'package:proc2/core/data/network/interceptors/logger/talker_chopper_logger_settings.dart';
import 'package:proc2/core/data/network/interceptors/metadata_json_converter.dart';

@module
abstract class ChopperRegisterModule {
  @injectable
  ChopperClient chopper(
    @Named('chopperBaseUrl') String baseUrl,
    Authenticator authenticator,
    MetadataJsonConverter metadataJsonConverter,
    AppConfig config,
  ) =>
      ChopperClient(
        baseUrl: Uri.parse(baseUrl),
        converter: metadataJsonConverter,
        errorConverter: const JsonConverter(),
        interceptors: [
          authInterceptor,
          TalkerChopperLogger(
            talker: config.talker,
            settings: TalkerChopperLoggerSettings(
              printRequestData: true,
              printRequestHeaders: config.isDev,
              printResponseData: true,
              printResponseHeaders: false,
              printResponseMessage: false,
              responseFilter: (response) {
                final path = response.base.request?.url.path ?? '';
                if (path.contains('login') ||
                    path.contains('/flow/skus/') ||
                    path.contains('mandis/getMandis') ||
                    path.contains('lang/en') ||
                    path.contains('lang/ta') ||
                    path.contains('vendor/all')) {
                  return false;
                }
                return true;
              },
            ),
          ),
        ],
        authenticator: authenticator,
      );

  @Named('chopperBaseUrl')
  @dev
  String get baseUrlDev => 'https://test-apis-flow.wheelocity.com/flow/';
  // test api
  // https://test.apis.flow.wheelocity.com/flow/

  @Named('chopperBaseUrl')
  @prod
  String get baseUrlProd => 'https://apis-flow.wheelocity.com/flow/';
}
