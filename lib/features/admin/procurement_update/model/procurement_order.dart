import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';

class ProcurementOrder {
  final int id;
  final int createdAt;
  final String? createdBy;
  final Vendor? vendor;
  final List<int> pitstops;
  final List<int> skus;
  final int smoId;
  final String status;
  final List<ProcDetailItem> items;

  ProcurementOrder({
    required this.id,
    required this.createdAt,
    required this.createdBy,
    required this.vendor,
    required this.pitstops,
    required this.skus,
    required this.smoId,
    required this.status,
    required this.items,
  });

  ProcurementOrder copyWith({
    int? id,
    int? createdAt,
    String? createdBy,
    Vendor? vendor,
    List<int>? pitstops,
    List<int>? skus,
    int? smoId,
    String? status,
    List<ProcDetailItem>? items,
  }) {
    return ProcurementOrder(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      vendor: vendor ?? this.vendor,
      pitstops: pitstops ?? this.pitstops,
      skus: skus ?? this.skus,
      smoId: smoId ?? this.smoId,
      status: status ?? this.status,
      items: items ?? this.items,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ProcurementOrder && other.id == id && other.smoId == smoId && other.createdAt == createdAt && other.createdBy == createdBy && other.vendor == vendor && other.status == status && listEquals(other.pitstops, pitstops) && listEquals(other.skus, skus) && listEquals(other.items, items);
  }

  @override
  int get hashCode {
    return id.hashCode ^ createdAt.hashCode ^ createdBy.hashCode ^ vendor.hashCode ^ pitstops.hashCode ^ status.hashCode ^ skus.hashCode ^ items.hashCode;
  }

  static bool listEquals(List a, List b) {
    if (a.length != b.length) return false;
    for (var i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}
