import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/input_model/inventory_item_input_model.dart';

@injectable
class SubmitLottingUseCase {
  SubmitLottingUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, String>> call({
    required int smoId,
    required List<InventoryItemInputModel> from,
    required List<InventoryItemInputModel> to,
    required List<LossInputModel> losses,
    required bool isLotting,
  }) async {
    return _mandiRepository.submitConversion(
        smoId: smoId, from: from, to: to, losses: losses, isLotting: isLotting);
  }
}
