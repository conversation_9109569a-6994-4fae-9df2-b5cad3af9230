import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/app/route/router.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/w_mandi_status_card.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/presentation/widgets/w_tab_bar.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/language/cubit/language_cubit.dart';
import 'package:proc2/features/mandi/presentation/language/view/language_modal.dart';
import 'package:proc2/features/mandi/util/get_mandi_name.dart';
import 'package:screenshot_callback/screenshot_callback.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  static Page<void> page() => const MaterialPage<void>(child: Home());
  @override
  State<Home> createState() => _Home();
}

class _Home extends State<Home> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ScreenshotCallback? screenshotCallback;

  void _tabControllerListener() {
    if (_tabController.index == 0) {
      context.read<MandiBloc>().add(MandiEvent.getOps('current'));
    } else {
      context.read<MandiBloc>().add(MandiEvent.getOps('pending'));
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      final config = di.get<AppConfig>();
      final authState = context.read<AuthBloc>().state;

      if (authState is Authenticated && !kIsWeb && !config.isDev) {
        screenshotCallback = ScreenshotCallback();
        screenshotCallback?.addListener(() {
          if (!router.routeInformationProvider.value.uri.path
              .contains('report-bug')) {
            showReportBugSnackBar(() {
              router.push(router.namedLocation('reportBug'));
            });
          }
        });
      }
    });

    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_tabControllerListener);
    // _tabControllerListener();
  }

  @override
  void dispose() {
    screenshotCallback?.dispose();
    _tabController.removeListener(_tabControllerListener);
    _tabController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        state.maybeWhen(
          orElse: () {},
          unauthenticated: (error) {
            context.read<MandiBloc>().add(const MandiEvent.clearData());
            context.go('/login');
          },
          authenticated: (s) {
            context.fetchGlobal();
          },
        );
      },
      builder: (context, state) {
        final user = state.maybeWhen(
          authenticated: (user) => user,
          orElse: () => null,
        );
        final isAdmin =
            user?.roles.contains('ROLE_CENTRAL_PROCUREMENT') ?? false;
        return SafeArea(
          child: Scaffold(
            backgroundColor: Colors.grey.shade50,
            body: Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              color: Colors.grey.shade50,
              child: BlocConsumer<MandiBloc, MandiState>(
                listener: (context, state) {
                  if (state is MandiSuccess) {
                    if (state.error != null) {
                      showSnackBar(state.error!.message);
                      context.read<MandiBloc>().add(MandiEvent.clearError());
                    } else if (state.liveOps == null) {
                      _tabControllerListener();
                    }
                  }
                },
                builder: (context, state) {
                  return state.map(loading: (l) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }, success: (s) {
                    if (s.allMandis.isEmpty) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }
                    return Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(
                            left: 15,
                            right: 15,
                            top: 25,
                            bottom: 15,
                          ),
                          decoration: BoxDecoration(
                            boxShadow: const <BoxShadow>[
                              BoxShadow(
                                color: Colors.black54,
                                blurRadius: 15,
                                offset: Offset(0, 0.75),
                              )
                            ],
                            color: Config.primaryColor,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    LanguageEnum.welcomeBack.localized(),
                                    style: const TextStyle(
                                        fontSize: 14, color: Colors.white),
                                  ),
                                  Text(
                                    user?.name ?? '-',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  )
                                ],
                              ),
                              Row(
                                children: [
                                  if ((kIsWeb || kDebugMode) && isAdmin) ...[
                                    OutlinedButton.icon(
                                      icon: Icon(Icons.admin_panel_settings),
                                      onPressed: () {
                                        context.pushNamed('adminPanel');
                                      },
                                      label: Text(
                                        'adminPanel'.tr('Admin Panel'),
                                      ),
                                      style: OutlinedButton.styleFrom(
                                        foregroundColor: Colors.white,
                                        side: BorderSide(
                                          color: Colors.white,
                                        ),
                                        padding: EdgeInsets.symmetric(
                                          vertical: 16,
                                          horizontal: 16,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      width: 32,
                                    ),
                                  ],
                                  PopupMenuButton(
                                    itemBuilder: (context) => [
                                      PopupMenuItem(
                                        value: 'logout',
                                        child: Text(
                                          LanguageEnum.signout.localized(),
                                        ),
                                      ),
                                      PopupMenuItem(
                                        value: 'language',
                                        child: Text(
                                          LanguageEnum.updateLanguageMenu
                                              .localized(),
                                        ),
                                      ),
                                      PopupMenuItem(
                                        value: 'weighingMachine',
                                        child: Text('weighingMachine'
                                            .tr('Weighing Machine')),
                                      ),
                                      PopupMenuItem(
                                        value: 'bugReport',
                                        child:
                                            Text('bugReport'.tr('Bug Report')),
                                      ),
                                    ],
                                    onSelected: (value) async {
                                      if (value == 'logout') {
                                        context
                                            .read<AuthBloc>()
                                            .add(const AuthEvent.logout());
                                      } else if (value == 'language') {
                                        await showDialog(
                                          context: context,
                                          builder: (context) {
                                            return BlocProvider.value(
                                              value:
                                                  context.read<LanguageCubit>(),
                                              child: const LanguageModal(),
                                            );
                                          },
                                        );
                                      } else if (value == 'weighingMachine') {
                                        context.push(context
                                            .namedLocation('weighingMachine'));
                                      } else if (value == 'bugReport') {
                                        router.push(
                                            router.namedLocation('reportBug'));
                                      }
                                    },
                                    child: user?.photo == null
                                        ? CircleAvatar(
                                            backgroundImage: AssetImage(
                                                Config.logoImagePath),
                                          )
                                        : CircleAvatar(
                                            backgroundImage:
                                                NetworkImage(user!.photo!),
                                          ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        _content(s),
                        WStickyBottomCta(
                          icon: Icons.history,
                          label: LangText(
                            'smoHistory',
                            'SMO History',
                          ),
                          onPressed: () {
                            context.push(context.namedLocation('smoHistory'));
                          },
                        ),
                      ],
                    );
                  });
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _content(MandiSuccess state) {
    return Expanded(
      child: Column(
        children: [
          WTabBar(
            controller: _tabController,
            tabs: [
              Tab(
                text: 'Live Ops',
              ),
              Tab(
                text: 'Pending Ops',
              ),
            ],
          ),
          Flexible(
            fit: FlexFit.tight,
            child: TabBarView(
              controller: _tabController,
              children: [
                _liveOps(state.liveOps),
                _pendingOps(state.pendingOps),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _opsList({required bool isLiveOps, required List<SmoOps>? ops}) {
    if (ops == null)
      return Center(
        child: CircularProgressIndicator(),
      );
    if (ops.isEmpty) {
      return EmptyScreen(
        message: getLangText('home.noOpsFound', 'No operations found!'),
      );
    }
    return Scrollbar(
      child: RefreshIndicator.adaptive(
        strokeWidth: 4,
        onRefresh: () {
          context.read<MandiBloc>().add(MandiEvent.getOps(
                isLiveOps ? 'current' : 'pending',
                forceRefresh: true,
              ));
          return Future.delayed(Duration(seconds: 2));
        },
        child: ListView(
          children: [
            SizedBox(
              height: 8,
            ),
            for (final op in ops)
              Padding(
                padding: const EdgeInsets.only(
                  left: 8,
                  right: 8,
                  bottom: 8,
                ),
                child: WMandiStatusCard(
                  status: op.currentStatus.localized(),
                  mandiName: getMandiName(context, mandiId: op.mandiId),
                  startTime: op.smoStartTime,
                  closedAt: op.smoEndTime,
                  onTap: () async {
                    if (isLiveOps) {
                      await context.pushNamed('mandiDetail', pathParameters: {
                        'mandiId': op.mandiId.toString(),
                      }, queryParameters: {
                        'smoId': op.smoId.toString(),
                      });
                      context.read<MandiBloc>()
                        ..add(MandiEvent.getOps('current', forceRefresh: true));
                      await Future.delayed(Duration(seconds: 2));
                      context.read<MandiBloc>()
                        ..add(MandiEvent.getOps('pending', forceRefresh: true));
                    } else {
                      await context.pushNamed(
                        'opsSummary',
                        pathParameters: {
                          'smoId': op.smoId.toString(),
                          'mandiId': op.mandiId.toString(),
                          'status': op.currentStatus,
                          'startTime': op.smoStartTime.toString(),
                          'endTime': op.smoEndTime.toString(),
                        },
                      );
                      context.read<MandiBloc>()
                        ..add(MandiEvent.getOps('pending', forceRefresh: true));
                    }
                  },
                  statusBackgroundColor: isLiveOps ? null : Color(0x89f2efd3),
                  cardBackgroundColor: isLiveOps ? null : Color(0xFFfefdfa),
                ),
              )
          ],
        ),
      ),
    );
  }

  Widget _liveOps(List<SmoOps>? ops) {
    return _opsList(ops: ops, isLiveOps: true);
  }

  Widget _pendingOps(List<SmoOps>? ops) {
    return _opsList(ops: ops, isLiveOps: false);
  }
}
