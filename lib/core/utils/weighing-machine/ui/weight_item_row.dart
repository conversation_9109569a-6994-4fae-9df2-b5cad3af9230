import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:proc2/core/presentation/widgets/w_text_form_field.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_item.dart';

class WeightItemRow extends StatelessWidget {
  const WeightItemRow({
    super.key,
    required this.canDelete,
    required this.isActive,
    required this.canEdit,
    required this.type,
    required this.weight,
    required this.onDelete,
    required this.onWeightChanged,
    required this.inputFormatters,
    required this.showCaptureButton,
    required this.hasError,
    required this.onCapture,
  });
  final bool canDelete;
  final bool isActive;
  final bool canEdit;
  final WeightItemType type;
  final String weight;
  final VoidCallback onDelete;
  final ValueChanged<String> onWeightChanged;
  final List<FilteringTextInputFormatter> inputFormatters;
  final bool showCaptureButton;
  final VoidCallback onCapture;
  final bool hasError;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (canDelete)
          IconButton(
            icon: const Icon(
              Icons.delete,
              color: Colors.red,
            ),
            onPressed: onDelete,
          ),
        if (type != WeightItemType.none)
          Container(
            padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color: type == WeightItemType.none
                  ? Colors.grey.shade200
                  : type == WeightItemType.added
                      ? Colors.green.shade200
                      : Colors.red.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                type.name.capitalize(),
                style: TextStyle(
                  fontSize: 12,
                ),
              ),
            ),
          ),
        SizedBox(
          width: 8,
        ),
        Expanded(
          child: WTextFormField(
            initialValue: weight,
            enabled: isActive,
            onChanged: onWeightChanged,
            inputFormatters: inputFormatters,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.end,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 8,
              ),
              border: const OutlineInputBorder(),
              isDense: true,
              error: hasError ? SizedBox() : null,
            ),
            readOnly: !canEdit,
          ),
        ),
        if (showCaptureButton)
          Padding(
            padding: const EdgeInsets.only(
              left: 8,
            ),
            child: isActive
                ? SizedBox(
                    width: 40,
                    child: InkWell(
                      child: CircleAvatar(
                        backgroundColor: Colors.grey.shade200,
                        child: Icon(Icons.monitor_weight_rounded),
                      ),
                      onTap: onCapture,
                    ),
                  )
                : const SizedBox(
                    width: 40,
                  ),
          ),
      ],
    );
  }
}
