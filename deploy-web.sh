#!/bin/sh

# Check if the environment parameter is provided
if [ $# -eq 0 ]; then
  echo "Please provide the environment parameter: prod or dev"
  exit 1
fi

NO_CLEAN=0
for arg in "$@"
do
    if [ "$arg" == "--no-clean" ]; then
        NO_CLEAN=1
    fi
done

# Check if the environment parameter is valid
if [ "$1" != "prod" ] && [ "$1" != "dev" ]; then
  echo "Invalid environment parameter. Allowed values: prod or dev"
  exit 1
fi

# Set the correct entry point file based on the environment
if [ "$1" == "prod" ]; then
  entry_point="lib/main_production.dart"
  file_to_copy="firebase-prod.json"
  project_id="signintest-84632"
  site="flow-woc"
else
  entry_point="lib/main_development.dart"
  file_to_copy="firebase-dev.json"
  project_id="wowfirebaseapps-test"
  site="flow-woc-test"
fi

if [ "$NO_CLEAN" -eq 0 ]; then
  fvm flutter clean
  fvm flutter pub get
  fvm dart run build_runner build --delete-conflicting-outputs
fi

# Invoke the flutter build apk command
fvm flutter build web -t $entry_point --no-tree-shake-icons

#copy file_to_copy as firebase.json
rm -f firebase.json
cp $file_to_copy firebase.json
firebase use $project_id
firebase deploy --only hosting:$site
rm -f firebase.json
