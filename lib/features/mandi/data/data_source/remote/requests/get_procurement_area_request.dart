import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/proc_area.dart';

class GetProcurementAreaRequest extends BaseRequest<dynamic, List<ProcArea>> {
  @override
  String getPath() => 'vendor/getProcurementAreas';

  @override
  List<ProcArea> mapper(data) {
    if (data is List) {
      return data
          .map((e) => ProcArea.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
