import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

class GetReturnsHistoryRequest
    extends BaseRequest<dynamic, List<ReturnsHistory>> {
  final int smoId;
  final bool isWastageReturns;

  GetReturnsHistoryRequest({
    required this.smoId,
    required this.isWastageReturns,
  });

  @override
  String getPath() => 'returns/returnHistory';

  @override
  Map<String, dynamic>? getQuery() {
    return {'smoId': smoId, 'days': 3};
  }

  @override
  List<ReturnsHistory> mapper(data) {
    if (data is List) {
      return data
          .map((e) => e as Map<String, dynamic>)
          .where((e) =>
              e['inventoryType'] ==
              (isWastageReturns ? 'WASTAGE' : 'INVENTORY'))
          .map((e) => ReturnsHistory.fromJson(e))
          .toList();
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
