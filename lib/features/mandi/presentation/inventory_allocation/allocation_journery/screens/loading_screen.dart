import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key, required this.state});
  final AllocationJourneyState state;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          centerTitle: false,
          title: Text('allocationLoading'.tr('Allocation Loading...')),
        ),
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (state.loadingMessage != null) ...[
                Text(
                  state.loadingMessage!,
                  style: TextStyle(
                    color: Colors.blue,
                  ),
                ),
                SizedBox(
                  height: 32,
                ),
              ],
              CircularProgressIndicator(),
            ],
          ),
        ),
      ),
    );
  }
}
