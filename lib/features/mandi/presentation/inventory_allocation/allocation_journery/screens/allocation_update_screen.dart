import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/allocation_list_ui.dart';

class AllocationUpdateScreen extends StatelessWidget {
  const AllocationUpdateScreen({
    super.key,
    required this.state,
  });
  final AllocationJourneyState state;

  @override
  Widget build(BuildContext context) {
    final bool isCtaActive = state.items.any(
            (element) => element.updateAllocationQty.totalQty.isNotEmpty) ||
        state.itemsPartial.any(
            (element) => element.updateAllocationQty.totalQty.isNotEmpty) ||
        state.itemsInMandi.any(
            (element) => element.updateAllocationQty.totalQty.isNotEmpty) ||
        state.itemsInSupplyOrder
            .any((element) => element.updateAllocationQty.totalQty.isNotEmpty);
    final bool hasAnyError =
        state.items.any((e) => e.updateAllocationQty.hasError) ||
            state.itemsPartial.any((e) => e.updateAllocationQty.hasError) ||
            state.itemsInMandi.any((e) => e.updateAllocationQty.hasError) ||
            state.itemsInSupplyOrder.any((e) => e.updateAllocationQty.hasError);
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: WAppBar.getAppBar(
          title: Text(
            'updateAllocation'.tr('Update Allocation'),
          ),
          actions: [
            if (!state.isCustomerGroup)
              ElevatedButton.icon(
                onPressed: () async {
                  final shouldCopy = await context.showAlertDialog(
                    title: 'copyAllocation'.tr('Copy Allocation'),
                    message: 'copyAllocationMessage'
                        .tr('Are you sure you want to copy the allocation?'),
                  );
                  if ((shouldCopy ?? false) && context.mounted) {
                    context
                        .read<AllocationJourneyCubit>()
                        .copyMandiAllocation();
                  }
                },
                icon: Icon(Icons.copy),
                label: Text(
                  'copyAllocation'.tr('Copy Allocation'),
                ),
              ),
            //   IconButton(
            //       onPressed: () async {
            //         final shouldRefresh = await context.push<bool>(
            //               context.namedLocation('gradingConversion',
            //                   pathParameters: {
            //                     'mandiId': state.mandiId.toString(),
            //                     'smoId': state.smoId.toString(),
            //                   },
            //                   queryParameters: {
            //                     'title': 'Lotting/Delotting',
            //                     'allowGrading': 'false',
            //                   }),
            //               extra: InventoryType.primary,
            //             ) ??
            //             false;
            //         if (shouldRefresh) {
            //           context
            //               .read<AllocationJourneyCubit>()
            //               .refreshAllocationAndInventory(
            //                 stage: AllocationJourneyStage.allocation,
            //               );
            //         }
            //       },
            //       icon: Icon(Icons.recycling)),
            //   SizedBox(
            //     width: 16,
            //   ),
          ],
          centerTitle: false,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AllocationListUi(
              state: state,
              isUpdateUi: true,
              selectedItems: [],
              onSelectedItemChange: (value) {},
              isSelectionActive: false,
              isEditingAllowed: state.isEditingAllowed,
            ),
            WStickyBottomCta(
              isEnabled: isCtaActive && state.isEditingAllowed && !hasAnyError,
              icon: Icons.update,
              label: Text('updateAllocation'.tr('Update Allocation')),
              onPressed: () {
                context.read<AllocationJourneyCubit>().updateAllocation();
              },
            ),
          ],
        ),
      ),
    );
  }
}
