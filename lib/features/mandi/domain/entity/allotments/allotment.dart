import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/delivery_memo.dart';

part 'allotment.freezed.dart';

@freezed
class Allotment with _$Allotment {
  const factory Allotment({
    required int id,
    required int smoId,
    required String? destinationType,
    required int destinationId,
    required String status,
    required String consignmentId,
    required bool canRetryDeliveryMemo,
    DeliveryMemo? deliveryMemo,
  }) = _Allotment;
}
