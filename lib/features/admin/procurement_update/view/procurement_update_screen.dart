import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/admin/procurement_update/cubit/procurement_update_cubit.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_ops.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/mandi_dropdown.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

class ProcurementUpdateScreen extends StatefulWidget {
  const ProcurementUpdateScreen({super.key});

  @override
  State<ProcurementUpdateScreen> createState() => _ProcurementUpdateScreenState();
}

class _ProcurementUpdateScreenState extends State<ProcurementUpdateScreen> {
  Map<int, MandiInfo> allMandis = {};
  Map<int, Sku> allSkus = {};
  TextEditingController statusController = TextEditingController(text: "PLACED");
  int limit = 10;
  int offset = 0;
  int currentPage = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        allMandis = Map.fromEntries((context.read<MandiBloc>().state.mapOrNull(success: (s) => s.allMandis) ?? <MandiInfo>[]).map((e) => MapEntry(e.id, e)));
        allSkus = Map.fromEntries((context.read<SkuBloc>().state.mapOrNull(success: (s) => s.skus) ?? <Sku>[]).map((e) => MapEntry(e.id, e)));
      });
    });
  }

  @override
  void dispose() {
    statusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          centerTitle: false,
          title: Text(
            'procurementUpdate.title'.tr('Procurement Update'),
          ),
        ),
        body: BlocConsumer<ProcurementUpdateCubit, ProcurementUpdateState>(
          listener: (context, state) {
            if (state.message != null) {
              showSnackBar(state.message!);
              context.read<ProcurementUpdateCubit>().clearMessage();
            }
          },
          builder: (context, state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  color: Colors.grey[200],
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: IntrinsicHeight(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 380,
                            child: MandiDropdown(
                              selectedMandi: state.selectedMandi,
                              onChange: (val) {
                                if (val == null) return;
                                final ops = context.read<MandiBloc>().state.mapOrNull(success: (s) {
                                  return s.liveOps ?? <SmoOps>[];
                                });
                                final smoId = ops?.where((e) => e.mandiId == val.id).firstOrNull?.smoId;
                                if (smoId == null) return;
                                context.read<ProcurementUpdateCubit>().updateSelectedMandi(mandi: val, smoId: smoId);
                              },
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8),
                            child: SizedBox(
                              height: 40,
                              width: 400,
                              child: CustomDropdown(
                                controller: statusController,
                                items: [
                                  "PLACED",
                                  "QUANTITY_SUBMITTED",
                                  "COMPLETED"
                                ],
                                hintText: "Select Status",
                                fillColor: Colors.grey.shade100,
                                onChanged: (status) {
                                  context.read<ProcurementUpdateCubit>().updateStatus(status: status);
                                },
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 32,
                          ),
                          Row(
                            children: [
                              ElevatedButton.icon(
                                onPressed: state.selectedMandi == null || state.isSearchLoading
                                    ? null
                                    : () async {
                                        currentPage = 0;
                                        await context.read<ProcurementUpdateCubit>().search(offset: 0, limit: 10);
                                      },
                                label: Text('procurementUpdate.search'.tr('Search Orders')),
                                icon: state.isSearchLoading
                                    ? SizedBox(
                                        height: 18,
                                        width: 18,
                                        child: CircularProgressIndicator(
                                          color: Colors.grey,
                                        ),
                                      )
                                    : Icon(
                                        Icons.search,
                                      ),
                              ),
                              const SizedBox(width: 8),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Card(
                  color: Colors.grey[200],
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 60,
                          child: Text(
                            'serialNumber'.tr('S.No.'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'createdAt'.tr('Created At'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'createdBy'.tr('Created By'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'vendorName'.tr('Vendor Name'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'pitstops'.tr('Pitstops'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'quantity'.tr('Quantity'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'price'.tr('Price'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'skus'.tr('Skus'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                        Expanded(
                          child: Text(
                            'status'.tr('Status'),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16,
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: state.procurementOrders == null
                      ? EmptyScreen(
                          message: 'procurementUpdate.searchOrderMessage'.tr('Select Date and Search Orders'),
                          description: 'procurementUpdate.searchOrdersDescription'.tr('To get the list of procurement orders'),
                        )
                      : state.procurementOrders!.isEmpty
                          ? EmptyScreen(
                              message: 'procurementUpdate.noOrderMessage'.tr('No procurement order found for this date!'),
                            )
                          : ListView.builder(
                              itemCount: state.procurementOrders!.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                final order = state.procurementOrders![index];
                                return InkWell(
                                  onTap: () {
                                    context.push(
                                      context.namedLocation('adminProcOrderUpdateEdit'),
                                      extra: {
                                        'mandi': state.selectedMandi!,
                                        'smoId': state.selectedSmoId!,
                                        'order': order,
                                        'skuMap': allSkus,
                                        'mandis': allMandis,
                                      },
                                    );
                                  },
                                  child: Card(
                                    elevation: 0,
                                    color: Colors.grey[200],
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 16,
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            width: 60,
                                            child: Text(
                                              (index + 1).toString(),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(
                                              order.createdAt.toDate('dd MMM yyyy | hh:mm a'),
                                            ),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(order.createdBy ?? '-'),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(order.vendor?.displayText ?? '-'),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(
                                              order.pitstops.map((e) => allMandis[e]?.displayText ?? '-').join(', '),
                                              maxLines: 10,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(
                                              order.items.map((order) => order.quantity.toString().toDouble()).reduce((a, b) => a + b).toStringAsFixed(2),
                                              maxLines: 10,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(
                                              "₹ ${order.items.map((order) => order.amount.toString().toDouble()).reduce((a, b) => a + b).toStringAsFixed(2)}",
                                              maxLines: 10,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(
                                              order.skus.map((e) => allSkus[e]?.name ?? '-').join(', '),
                                              maxLines: 10,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                          Expanded(
                                            child: Text(
                                              order.status,
                                              maxLines: 1,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 16,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                ),
                if (state.procurementOrders != null)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                          width: 200,
                          child: Padding(
                            padding: const EdgeInsets.only(left: 20, top: 6, bottom: 6),
                            child: ElevatedButton(
                              onPressed: state.isSearchLoading || currentPage <= 0
                                  ? null
                                  : () {
                                      setState(() {
                                        currentPage--;
                                        offset = currentPage * limit; // Update offset
                                      });
                                      context.read<ProcurementUpdateCubit>().search(offset: offset, limit: limit);
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                              ),
                              child: Text('Previous'),
                            ),
                          )),
                      Container(
                        width: 50,
                        height: 50,
                        child: Card(
                          color: Colors.green,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.all(4),
                              child: Text(
                                "${currentPage + 1}",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                          width: 200,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 20, top: 6, bottom: 6),
                            child: ElevatedButton(
                              onPressed: (state.procurementOrders == null || (state.procurementOrders ?? []).isEmpty) || state.isSearchLoading
                                  ? null
                                  : () {
                                      setState(() {
                                        currentPage++;
                                        offset = currentPage * limit; // Update offset
                                      });
                                      context.read<ProcurementUpdateCubit>().search(offset: offset, limit: limit);
                                    },
                              child: Text('Next'),
                            ),
                          )),
                    ],
                  )
              ],
            );
          },
        ),
      ),
    );
  }
}
