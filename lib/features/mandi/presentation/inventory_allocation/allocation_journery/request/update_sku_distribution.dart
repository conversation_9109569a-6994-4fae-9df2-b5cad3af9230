import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_change_response.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/sku_distribution_item.dart';

class UpdateSkuDistribution
    extends BaseRequest<dynamic, AllocationChangeResponse> {
  final int allocationId;
  final List<SkuDistributionItem> items;
  final double lossQuantity;
  final bool isDraft;
  final SkuQuantity skuQuantity;

  UpdateSkuDistribution({
    required this.allocationId,
    required this.items,
    required this.lossQuantity,
    required this.isDraft,
    required this.skuQuantity,
  });

  @override
  String getPath() => 'deliveryMemos/update-delivery-memo-items/$allocationId';

  @override
  Map<String, dynamic> getBody() {
    final json = {
      'sku': skuQuantity.toJson(),
      'isSubmit': !isDraft,
      'updateItems': items
          .where((e) => e.id != -1)
          .map((e) => e
              .copyWith(
                fineTunedQuantity:
                    (e.weighingQuantity?.value ?? '').trim().isEmpty
                        ? null
                        : e.weighingQuantity!.value!.toDouble(),
              )
              .toJson()
            ..addAll({
              'weighingSource': e.weighingQuantity?.source,
            })
            ..remove('quantityString'))
          .toList(),
      'losses': lossQuantity <= 0
          ? []
          : [
              {
                'skuId': skuQuantity.skuId,
                'type': skuQuantity.type,
                'unit': skuQuantity.unit,
                'lotSize': skuQuantity.lotSize,
                'quantity': 0,
                'losses': [
                  {
                    'comment': '',
                    'lossType': 'DAMAGED_AND_ROTTEN',
                    'quantity': lossQuantity
                  }
                ]
              }
            ]
    };
    return json;
  }

  @override
  AllocationChangeResponse mapper(data) {
    if (data is Map<String, dynamic>)
      return AllocationChangeResponse.fromJson(data);
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.PUT;
}
