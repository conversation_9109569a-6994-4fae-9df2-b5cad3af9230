part of 'sku_edit_bloc.dart';

@freezed
class SkuEditState with _$SkuEditState {
  const factory SkuEditState.data({
    required Sku? sku,
    required ProcurementType procurementType,
    required String? procurementUnit,
    required double? lotSize,
    required String? quantity,
    required String? amount,
    required String? weight,
    required bool isCtaActive,
    required bool showLotSizeField,
    required String uuid,
    required bool isOpenForEdit,
    @Default(null) String? weighingSource,
  }) = _Initial;

  static final empty = SkuEditState.data(
    sku: null,
    procurementType: ProcurementType.bulk,
    procurementUnit: null,
    lotSize: null,
    quantity: null,
    amount: null,
    isCtaActive: false,
    showLotSizeField: false,
    uuid: di.get<Uuid>().v1(),
    weight: null,
    isOpenForEdit: false,
  );
}
