part of 'close_inventory_bloc.dart';

@freezed
class CloseInventoryEvent with _$CloseInventoryEvent {
  // const factory CloseInventoryEvent.started({required int mandiId}) = _Started;

  // const factory CloseInventoryEvent.onEdit({
  //   required CloseInventorySkuInput input,
  //   required int index,
  //   required bool isBulk,
  // }) = _OnEdit;
  const factory CloseInventoryEvent.submitCloseInventory({
    required int smoId,
    required int mandiId,
  }) = _Submit;
  // const factory CloseInventoryEvent.reset() = _Reset;
  const factory CloseInventoryEvent.add(SkuInputWithLoss skuInputData) = _AddSkuData;
  const factory CloseInventoryEvent.update(SkuInputWithLoss skuInputData) = _UpdateSkuData;
  const factory CloseInventoryEvent.delete(SkuInputWithLoss skuInputData) = _DeleteSkuData;
  const factory CloseInventoryEvent.clearMessage() = _ClearMessage;

  const factory CloseInventoryEvent.getInventory({
    required int mandiId,
    required int smoId,
  }) = _GetInventory;
}
