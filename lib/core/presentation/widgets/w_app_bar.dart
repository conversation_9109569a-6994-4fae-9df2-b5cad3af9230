import 'package:flutter/material.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';

class WAppBar {
  MandiInfo? _selectedMandi;
  int? _smoId;

  WAppBar._()
      : _selectedMandi = null,
        _smoId = null;

  static final instance = WAppBar._();

  void setMandiAndSmo({
    MandiInfo? mandi,
    int? smoId,
  }) {
    _selectedMandi = mandi;
    _smoId = smoId;
  }

  static AppBar getAppBar({
    required Widget title,
    bool centerTitle = false,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    double? elevation,
    Widget? leading,
  }) {
    return AppBar(
      title: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (instance._selectedMandi != null)
            Text(
              instance._selectedMandi!.key,
              style: const TextStyle(fontSize: 12),
            ),
          title,
        ],
      ),
      bottom: bottom,
      leading: leading,
      centerTitle: centerTitle,
      actions: actions,
      elevation: elevation,
    );
  }
}
