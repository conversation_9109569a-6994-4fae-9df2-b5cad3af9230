part of 'procurement_update_edit_cubit.dart';

class ProcurementUpdateEditState {
  final MandiInfo? selectedMandi;
  final int? smoId;
  final ProcurementOrder? procurementOrder;
  final bool isLoading;
  final bool isCtaLoading;
  final ProcTable? procTable;
  final String? message;
  final Map<int, Sku> skuMap;
  final Map<int, MandiInfo> mandis;
  final int? focusItemId;

  const ProcurementUpdateEditState._({
    required this.selectedMandi,
    required this.smoId,
    required this.procurementOrder,
    required this.isLoading,
    required this.isCtaLoading,
    required this.procTable,
    required this.message,
    required this.skuMap,
    required this.mandis,
    required this.focusItemId,
  });

  factory ProcurementUpdateEditState.initial() => ProcurementUpdateEditState._(
        selectedMandi: null,
        smoId: null,
        procurementOrder: null,
        isLoading: true,
        isCtaLoading: false,
        procTable: null,
        message: null,
        skuMap: {},
        mandis: {},
        focusItemId: null,
      );

  ProcurementUpdateEditState copyWith({
    MandiInfo? selectedMandi,
    int? smoId,
    ProcurementOrder? procurementOrder,
    bool? isLoading,
    bool? isCtaLoading,
    ProcTable? procTable,
    String? message,
    Map<int, Sku>? skuMap,
    Map<int, MandiInfo>? mandis,
    int? focusItemId,
  }) {
    return ProcurementUpdateEditState._(
      selectedMandi: selectedMandi ?? this.selectedMandi,
      smoId: smoId ?? this.smoId,
      procurementOrder: procurementOrder ?? this.procurementOrder,
      isLoading: isLoading ?? this.isLoading,
      isCtaLoading: isCtaLoading ?? this.isCtaLoading,
      procTable: procTable ?? this.procTable,
      message: message,
      skuMap: skuMap ?? this.skuMap,
      mandis: mandis ?? this.mandis,
      focusItemId: focusItemId ?? this.focusItemId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ProcurementUpdateEditState &&
        other.selectedMandi == selectedMandi &&
        other.smoId == smoId &&
        other.procurementOrder == procurementOrder &&
        other.isLoading == isLoading &&
        other.isCtaLoading == isCtaLoading &&
        other.message == message &&
        other.skuMap == skuMap &&
        other.mandis == mandis &&
        other.focusItemId == focusItemId &&
        other.procTable == procTable;
  }

  @override
  int get hashCode {
    return selectedMandi.hashCode ^
        smoId.hashCode ^
        procurementOrder.hashCode ^
        isLoading.hashCode ^
        isCtaLoading.hashCode ^
        message.hashCode ^
        skuMap.hashCode ^
        mandis.hashCode ^
        focusItemId.hashCode ^
        procTable.hashCode;
  }
}
