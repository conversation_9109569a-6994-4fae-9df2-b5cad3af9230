import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';

@singleton
class VendorCache {
  VendorCache(@Named('HiveDirPath') this._hiveDirPath) {
    init();
  }
  final String _hiveDirPath;
  final String boxName = 'vendor';
  final int _daysToRetain = 7 * 24 * 60 * 60 * 1000; // 7 days

  Future<void> init() async {
    await Hive.initFlutter();
    final box = await open(boxName);
  }

  Future<Box<Map<dynamic, dynamic>>> open(
    String boxName,
  ) async {
    if (Hive.isBoxOpen(boxName)) {
      return Hive.box<Map<dynamic, dynamic>>(boxName);
    }
    return Hive.openBox<Map<dynamic, dynamic>>(boxName, path: _hiveDirPath);
  }

  Future<Map<String, String>> getVendors() async {
    final result = <String, String>{};
    final box = await open(boxName);
    final mp = box.get(boxName) ?? {};
    final thresholdMs = DateTime.now().millisecondsSinceEpoch - _daysToRetain;
    mp.entries.forEach((element) {
      final key = element.key as String;
      if (element.value is Map<dynamic, dynamic>) {
        final value = element.value as Map<dynamic, dynamic>;
        final lastUpdatedMs = value['lastUpdatedMs'] as int?;
        final vendorMobile = value['vendorMobile'] as String?;

        if (lastUpdatedMs != null && lastUpdatedMs >= thresholdMs) {
          result[key] = vendorMobile ?? '';
        }
      }
    });
    return result;
  }

  Future<void> clear() async {
    final box = await open(boxName);
    await box.clear();
  }

  Future<void> updateVendorNameAndMobile(String vendorName,
      {String? vendorMobile}) async {
    final box = await open(boxName);
    final mp = box.get(boxName) ?? {};
    final currentMs = DateTime.now().millisecondsSinceEpoch;
    final threshold = currentMs - _daysToRetain;
    mp[vendorName] = {
      'lastUpdatedMs': currentMs,
      'vendorMobile': vendorMobile,
    };
    mp.removeWhere((key, value) =>
        ((value as Map<dynamic, dynamic>)['lastUpdatedMs'] as int? ?? 0) <
        threshold);
    await box.put(boxName, mp);
  }
}
