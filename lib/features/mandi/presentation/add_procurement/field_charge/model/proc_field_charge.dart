import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/utils/extensions.dart';

part 'proc_field_charge.freezed.dart';

@freezed
class ProcFieldCharge with _$ProcFieldCharge {
  const ProcFieldCharge._();
  const factory ProcFieldCharge({
    required String type,
    @Default('') String amount,
    @Default([]) List<PickedFile> images,
    @Default('') String comment,
  }) = _ProcFieldCharge;

  factory ProcFieldCharge.empty() => const ProcFieldCharge(
        type: '',
        amount: '',
        images: [],
        comment: '',
      );

  factory ProcFieldCharge.fromJson(Map<String, dynamic> json) {
    return ProcFieldCharge(
      type: json['type'],
      amount: (json['amount'] as double).asString(
        maxDecimalDigits: 2,
      ),
      comment: json['comment'] ?? '',
      images: (json['images'] as List<dynamic>)
          .map((e) => PickedFile.fromUploadPath(e as String))
          .toList(),
    );
  }
}
