import 'package:either_dart/either.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/auth/data/data_source/remote/dto/login_dto.dart';

abstract class AuthDataSource {
  Future<Either<ErrorResult<dynamic>, dynamic>> sendOtp({
    required String mobile,
    bool isResend = false,
  });

  Future<Either<ErrorResult<dynamic>, LoginDto>> verifyOtp({
    required int otp,
    required String mobile,
  });

  Future<Either<ErrorResult<dynamic>, String>> getFirebaseCustomToken({
    required String jwt,
  });

  Future<Either<ErrorResult<dynamic>, LoginDto>> login({
    required String idToken,
  });
}
