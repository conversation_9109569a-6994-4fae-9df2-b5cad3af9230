import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/presentation/vendor/view/vendor_drop_down.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/view/proc_field_charges_widget.dart';
import 'package:proc2/features/mandi/presentation/procurement_item/cubit/proc_item_state.dart';
import 'package:proc2/features/mandi/presentation/procurement_item/cubit/procurement_item_cubit.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class ProcItem extends StatelessWidget {
  final String? refId;
  final String? refSource;
  const ProcItem({Key? key, this.refId, this.refSource}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LangText(
              'procItem.title',
              'Procurement Order',
              style: TextStyle(fontSize: 18),
            ),
            if (refId != null)
              Text(
                refId! + ' (${refSource})',
                style: TextStyle(fontSize: 14),
              ),
          ],
        ),
        centerTitle: false,
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[200],
        child: BlocBuilder<ProcItemCubit, ProcItemState>(
          builder: (context, state) {
            return state.maybeWhen(
              orElse: () => Center(
                child: CircularProgressIndicator(),
              ),
              error: (e) => ErrorScreen(
                onPressed: () {},
                message: e,
              ),
              loaded: (data, charges, vendorLocation, urls, comments,
                  parentOrder, refId, refSource) {
                if (data.isEmpty)
                  return EmptyScreen(
                    message: getLangText(
                        'myProcDetail.noData', 'No Procurements Found!'),
                  );
                return Column(
                  children: [
                    Container(
                      color: Colors.white,
                      width: double.infinity,
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: 16,
                            horizontal: 16,
                          ),
                          child: VendorDropDown(
                            isEnabled: false,
                            onChanged: (location) {},
                            selectedVendorLocation: vendorLocation,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      color: Colors.grey[100],
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8.0,
                          horizontal: 16,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              flex: 3,
                              child: LangText(
                                'unit',
                                'Unit',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Expanded(
                              flex: 2,
                              child: LangText(
                                'orderedQty',
                                'Ord Qty',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Expanded(
                              flex: 2,
                              child: LangText(
                                'receivedQty',
                                'Rec Qty',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            if (parentOrder == null ||
                                parentOrder.isParent) ...[
                              SizedBox(
                                width: 4,
                              ),
                              Expanded(
                                flex: 2,
                                child: LangText(
                                  'amount',
                                  'Amount',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Scrollbar(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: data.length + 1,
                          itemBuilder: (context, index) {
                            if (index == data.length) {
                              return ProcFieldChargesWidget(
                                enableEditing: false,
                                smoId: -1,
                                padding: EdgeInsets.all(8),
                                fieldCharges: charges,
                              );
                            }

                            final procItems = data[index];

                            return Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: 8.0, horizontal: 0.0),
                              padding: EdgeInsets.symmetric(
                                  vertical: 8.0, horizontal: 16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    getSKU(context,
                                            skuID: procItems.first.skuId)
                                        .name,
                                    style: TextStyle(
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  SizedBox(height: 8.0),
                                  Divider(
                                    height: 1,
                                    color: Colors.grey[300],
                                  ),
                                  SizedBox(height: 8.0),
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemCount: procItems.length,
                                    itemBuilder: (context, index) {
                                      final procItem = procItems[index];

                                      return Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(height: 8.0),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                flex: 3,
                                                child: Text(
                                                  procItem.getUnitString(),
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 4,
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  procItem.orderedQuantity
                                                          ?.asString() ??
                                                      '-',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),
                                              SizedBox(
                                                width: 4,
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Text(
                                                  procItem.quantity
                                                          ?.asString() ??
                                                      '-',
                                                  style: TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),
                                              if (parentOrder == null ||
                                                  parentOrder.isParent) ...[
                                                SizedBox(width: 4),
                                                Expanded(
                                                  flex: 2,
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 16.0),
                                                    child: Text(
                                                      procItem
                                                          .receivedAmountString,
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                      textAlign: TextAlign.end,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ],
                                          ),
                                          SizedBox(height: 8.0),
                                          if (procItem.images.isNotEmpty)
                                            InlineImagePicker(
                                              files: procItem.images
                                                  .map((e) =>
                                                      PickedFile.fromUploadPath(
                                                        e,
                                                        uploadUrl:
                                                            urls[e]?.toString(),
                                                      ))
                                                  .toList(),
                                              allowMultiple: false,
                                              updateFile: (files) {},
                                              uploadAlso: true,
                                              isEnabled: false,
                                            ),
                                          if (procItem.images.isNotEmpty)
                                            SizedBox(
                                              height: 8,
                                            ),
                                        ],
                                      );
                                    },
                                  ),
                                  Divider(),
                                  if (parentOrder == null ||
                                      parentOrder.isParent)
                                    Padding(
                                      padding: const EdgeInsets.only(right: 12),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Total',
                                            style: TextStyle(
                                              fontSize: 16.0,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          Text(
                                            '₹${(procItems.fold(.0, (p, e) => p + e.receivedAmount)).asString(maxDecimalDigits: 2)}',
                                            style: TextStyle(
                                              fontSize: 14.0,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    if (comments.isNotEmpty)
                      Container(
                        width: double.infinity,
                        margin: EdgeInsets.only(
                          bottom: 4,
                          left: 4,
                          right: 4,
                        ),
                        padding:
                            EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'comment'.tr('Comment'),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                            SizedBox(
                              height: 4,
                            ),
                            Text(
                              comments,
                              maxLines: 3,
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    Card(
                      margin: EdgeInsets.only(
                        top: 8,
                      ),
                      elevation: 24,
                      shadowColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: parentOrder == null || parentOrder.isParent
                          ? AmountSummary(state as ProcItemLoaded)
                          : Container(
                              width: double.infinity,
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  left: 16,
                                  right: 16,
                                  bottom: 8,
                                  top: 8,
                                ),
                                child: Center(
                                  child: Text(
                                    parentOrder.placedBy == null
                                        ? 'This order has been placed by other facility'
                                        : 'This order has been placed by ${parentOrder.placedBy}',
                                    style: TextStyle(
                                      color: Colors.blue,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ],
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget AmountSummary(ProcItemLoaded state) {
    final productCost = state.myProcDetailList
        .expand((e) => e)
        .fold(0.0, (p, i) => p + i.receivedAmount);
    final fieldChargesCost =
        state.fieldCharges.fold(0.0, (p, e) => p + (e.amount.toDouble()));

    final totalCost = productCost + fieldChargesCost;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'addProc.productCost'.tr('Product Cost'),
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  productCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  top: 4,
                  bottom: 4,
                ),
                child: Text(
                  'addProc.fieldCharges'.tr('Field Charges'),
                  style: TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  fieldChargesCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Divider(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                    .format(
                  totalCost,
                ),
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
