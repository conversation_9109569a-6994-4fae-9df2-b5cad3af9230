import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/auth/domain/entity/user.dart';

@singleton
class Db {
  Db(@Named('HiveDirPath') this._hiveDirPath) {
    init();
  }

  final String _hiveDirPath;

  final String userBox = 'userBox';

  ValueListenable<Box<Map<dynamic, dynamic>>>? listenable;

  final streamController = StreamController<User?>.broadcast();

  Future<void> init() async {
    await Hive.initFlutter();
    final box = await open(userBox);
    listenable = box.listenable();
    listenable?.addListener(listener);
  }

  Future<void> listener() async {
    final box = await open(userBox);
    final map = box.get('user');
    if (map != null) {
      final user = User.fromMap(map);
      streamController.add(user);
    } else {
      streamController.add(null);
    }
  }

  @disposeMethod
  void dispose() {
    listenable?.removeListener(listener);
    streamController.close();
  }

  void getLastState() async {
    await Future.delayed(const Duration(milliseconds: 200));
    await listener(); // Call for the first time manually
  }

  Stream<User?> getCurrentUser() {
    getLastState(); // Since the stream is broadcast stream, add last state.
    return streamController.stream;
  }

  Future<void> updateUser(User user) async {
    final box = await open(userBox);
    await box.put('user', user.toMap());
  }

  Future<void> deleteUser() async {
    final box = await open(userBox);
    await box.delete('user');
  }

  Future<Box<Map<dynamic, dynamic>>> open(
    String boxName,
  ) async {
    if (Hive.isBoxOpen(boxName)) {
      return Hive.box<Map<dynamic, dynamic>>(boxName);
    }
    return Hive.openBox<Map<dynamic, dynamic>>(boxName, path: _hiveDirPath);
  }
}
