import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get_utils/src/extensions/string_extensions.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/sku_edit/bloc/sku_edit_bloc.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/view/widgets/app_bar.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/view/widgets/close_inv_edit_dialog.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/view/widgets/show_submit_alert.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

class CloseInventory extends StatefulWidget {
  const CloseInventory({
    super.key,
    required this.mandiId,
    required this.smoId,
    required this.isEditOnlyFromWeighingMachine,
  });

  final int mandiId;
  final int smoId;
  final bool isEditOnlyFromWeighingMachine;
  @override
  State<CloseInventory> createState() => _CloseInventory();
}

class _CloseInventory extends State<CloseInventory> {
  bool isBulkExpanded = true;
  bool isLotExpanded = true;
  String _selectedFilter = 'all';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    context.read<CloseInventoryBloc>().add(
          CloseInventoryEvent.getInventory(
            mandiId: widget.mandiId,
            smoId: widget.smoId,
          ),
        );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        floatingActionButton: FloatingActionButton(
          onPressed: () async {
            final skuInputData = await showDialog<SkuInputWithLoss?>(
              context: context,
              builder: (_) => BlocProvider(
                create: (context) => di.get<SkuEditBloc>(),
                child: CloseInvEditDialog(
                  losses: [],
                  pickedFiles: [],
                  isEditOnlyFromWeighingMachine:
                      widget.isEditOnlyFromWeighingMachine,
                ),
              ),
            );

            if (skuInputData != null) {
              context
                  .read<CloseInventoryBloc>()
                  .add(CloseInventoryEvent.add(skuInputData));
              // ignore: use_build_context_synchronously
            }
          },
          child: Icon(Icons.add),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        bottomNavigationBar: buttonSubmit(),
        body: SizedBox(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: BlocBuilder<SkuBloc, SkuState>(
            builder: (context, skuState) {
              return BlocConsumer<CloseInventoryBloc, CloseInventoryState>(
                listener: blocListener,
                builder: (context, state) {
                  if (state.isGetInventoryLoading) {
                    return const Center(
                      child: SizedBox(
                          height: 30,
                          width: 30,
                          child: CircularProgressIndicator()),
                    );
                  }
                  return state.map(
                    input: (success) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          appBar(context, mandiId: widget.mandiId),
                          // Add search widget
                          searchWidget(),
                          filters(
                            state: state,
                            selectedFilter: _selectedFilter,
                            filterCount: state.filterCounts,
                            onFilterSelected: (filter) {
                              setState(() {
                                _selectedFilter = filter;
                              });
                            },
                          ),
                          Container(
                            color: Colors.grey[100],
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                                vertical: 4.0,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: LangText(
                                      'unit',
                                      'Unit',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 4,
                                  ),
                                  // Expanded(
                                  //   child: LangText(
                                  //     'lotSize',
                                  //     'Lot Size',
                                  //     style: TextStyle(
                                  //       fontSize: 14,
                                  //       fontWeight: FontWeight.bold,
                                  //     ),
                                  //   ),
                                  // ),
                                  // SizedBox(
                                  //   width: 4,
                                  // ),
                                  // Expanded(
                                  //   child: LangText(
                                  //     'noOfLots',
                                  //     '# of Lots',
                                  //     style: TextStyle(
                                  //       fontSize: 14,
                                  //       fontWeight: FontWeight.bold,
                                  //     ),
                                  //     textAlign: TextAlign.center,
                                  //   ),
                                  // ),
                                  // SizedBox(
                                  //   width: 4,
                                  // ),
                                  Expanded(
                                    child: LangText(
                                      'qty',
                                      'Qty',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 4,
                                  ),
                                  Expanded(
                                    child: LangText(
                                      'losses',
                                      'Losses',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.end,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 48,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // const SizedBox(height: 10),
                          // Visibility(
                          //   visible:
                          //       success.submitErrorMessage?.isNotEmpty ?? false,
                          //   child: Padding(
                          //     padding: const EdgeInsets.all(8),
                          //     child: Text(
                          //       success.submitErrorMessage ?? '',
                          //       style: const TextStyle(color: Colors.red),
                          //     ),
                          //   ),
                          // ),
                          Expanded(
                            flex: 12,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                vertical: 0,
                                horizontal: 0,
                              ),
                              child: success.inputSku.isEmpty
                                  ? EmptyScreen(
                                      message: getLangText(
                                          'closeInventory.noSkuMessage',
                                          'Please add SKUs for closing inventory.'),
                                    )
                                  : Scrollbar(
                                      child: ListView(
                                        children: [
                                          for (final key
                                              in success.inputSku.keys)
                                            skuCard(success.inputSku[key]!),
                                          SizedBox(height: 100),
                                        ],
                                      ),
                                    ),
                            ),
                          ),
                          const SizedBox(height: 8),
                        ],
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  bool _matchesFilter(String? topLevelCategory) {
    return _selectedFilter == 'all' ||
        _selectedFilter == topLevelCategory?.toLowerCase();
  }

  bool _matchesSearch(String skuName) {
    if (_searchQuery.isEmpty) return true;
    return skuName.toLowerCase().contains(_searchQuery.toLowerCase());
  }

  Widget searchWidget() {
    return Container(
      margin: const EdgeInsets.all(8.0),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'Search SKU name...',
          hintStyle: TextStyle(
            color: Colors.grey.shade500,
            fontSize: 13,
          ),
          prefixIcon: Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: Colors.grey.shade300,
              width: 1,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget skuCard(List<SkuInputWithLoss> skuInputData) {
    final skuName = skuInputData.first.skuInputData.sku.name;
    final topLevelCategory =
        skuInputData.first.skuInputData.sku.topLevelCategory;

    if (!_matchesFilter(topLevelCategory) || !_matchesSearch(skuName)) {
      return const SizedBox.shrink();
    }
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 4,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              skuName,
              style: TextStyle(
                color: Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(height: 4),
            for (final input in skuInputData)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        input.skuInputData.procurementUnit!,
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    // Expanded(
                    //   child: Text(
                    //     input.skuInputData.lotSize != null
                    //         ? input.skuInputData.lotSize.toString()
                    //         : '-',
                    //     style: TextStyle(
                    //       fontSize: 14,
                    //     ),
                    //     textAlign: TextAlign.center,
                    //   ),
                    // ),
                    // SizedBox(
                    //   width: 4,
                    // ),
                    // Expanded(
                    //   child: Text(
                    //     input.skuInputData.lotSize == null
                    //         ? '-'
                    //         : input.skuInputData.quantity ?? '-',
                    //     style: TextStyle(
                    //       fontSize: 14,
                    //     ),
                    //     textAlign: TextAlign.center,
                    //   ),
                    // ),
                    // SizedBox(
                    //   width: 4,
                    // ),
                    Expanded(
                      child: Text(
                        input.skuInputData.lotSize != null
                            ? '-'
                            : input.skuInputData.quantity == null ||
                                    input.skuInputData.quantity!.isEmpty
                                ? '-'
                                : input.skuInputData.quantity.toString(),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(right: 16.0),
                        child: Text(
                          input.totalLoss().toString(),
                          style: TextStyle(
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ),
                    ),
                    SizedBox(width: 4),
                    InkWell(
                      onTap: () async {
                        final skuInputData =
                            await showDialog<SkuInputWithLoss?>(
                          context: context,
                          builder: (_) => BlocProvider(
                            create: (context) => di.get<SkuEditBloc>()
                              ..add(SkuEditEvent.init(
                                input.skuInputData,
                                isOpenForEdit: true,
                              )),
                            child: CloseInvEditDialog(
                              isEditOnlyFromWeighingMachine:
                                  widget.isEditOnlyFromWeighingMachine,
                              losses: input.losses,
                              pickedFiles: input.files,
                              onDelete: () {
                                context.read<CloseInventoryBloc>().add(
                                      CloseInventoryEvent.delete(input),
                                    );
                              },
                            ),
                          ),
                        );

                        if (skuInputData != null) {
                          context
                              .read<CloseInventoryBloc>()
                              .add(CloseInventoryEvent.update(skuInputData));
                          // ignore: use_build_context_synchronously
                        }
                      },
                      child: Container(
                        width: 44,
                        child: const Icon(
                          Icons.edit,
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget filters({
    required CloseInventoryState state,
    required String selectedFilter,
    required Map<String, int> filterCount,
    required Function(String) onFilterSelected,
  }) {
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SizedBox(
        height: 50,
        child: ListView.separated(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(
            vertical: 4,
            horizontal: 8,
          ),
          itemBuilder: (context, index) {
            final filterEntry = state.filterCounts.entries.toList()[index];
            return FilterChip(
              padding: const EdgeInsets.symmetric(
                vertical: 4,
                horizontal: 8,
              ),
              label: Text(
                '${filterEntry.key.capitalize} (${filterEntry.value})',
                style: TextStyle(
                  color: selectedFilter == filterEntry.key
                      ? Colors.white
                      : Colors.black,
                  fontSize: 12,
                ),
              ),
              backgroundColor: selectedFilter == filterEntry.key
                  ? bgColorForFilter(filterEntry.key)
                  : Colors.grey.shade400,
              onSelected: (selected) {
                onFilterSelected(filterEntry.key);
              },
            );
          },
          separatorBuilder: (context, index) {
            return const SizedBox(width: 8);
          },
          itemCount: state.filterCounts.length,
        ),
      ),
    );
  }

  Color bgColorForFilter(String filter) {
    switch (filter) {
      case 'all':
        return Colors.blue;
      case 'fnv':
        return Colors.green;
      case 'fmcg':
        return Colors.red;
      case 'bazzar':
        return const Color.fromARGB(255, 11, 11, 11);
      default:
        return Colors.orange;
    }
  }

  void blocListener(BuildContext context, CloseInventoryState state) {
    if (state.isSubmittedSuccess) {
      showSnackBar(getLangText(
          'closeInventory.successMessage', 'Inventory closed successfully'));
      context.pop();
    }

    if (state.message.isNotEmpty) {
      showSnackBar(state.message);
      context
          .read<CloseInventoryBloc>()
          .add(CloseInventoryEvent.clearMessage());
    }
  }

  Widget successPage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        appBar(context, mandiId: widget.mandiId),
        const SizedBox(height: 10),
        Expanded(
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircleAvatar(
                  radius: 40,
                  backgroundColor: Colors.green,
                  child: Icon(
                    Icons.done,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 30),
                Text(
                  LanguageEnum.closeInventorySuccessMessage.localized(),
                  style: const TextStyle(
                      fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                ElevatedButton.icon(
                  onPressed: () {
                    context.pop();
                  },
                  icon: const Icon(Icons.arrow_forward_ios),
                  label: Text(LanguageEnum.closeInventoryDoneBtn.localized()),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget buttonSubmit() {
    final state = context.watch<CloseInventoryBloc>().state;

    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        top: 8,
        bottom: 8,
      ),
      child: FilledButton(
        onPressed: state.isSubmitLoading ||
                state.isGetInventoryLoading ||
                (!state.isSubmitCtaActive && state.inputSku.isNotEmpty)
            ? null
            : () => showSubmitAlert(context,
                mandiId: widget.mandiId, smoId: widget.smoId),
        style: FilledButton.styleFrom(
          minimumSize: const Size(double.infinity, 50),
        ),
        child: state.isSubmitLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(color: Colors.white),
              )
            : Text(LanguageEnum.closeInventorySubmitBtn.localized()),
      ),
    );
  }

  // ExpansionPanel bulkPanel(
  //   List<CloseInventorySkuInput> data, {
  //   required bool isExpanded,
  // }) {
  //   return ExpansionPanel(
  //     headerBuilder: (BuildContext context, bool isExpanded) {
  //       return ListTile(
  //         title: Text(
  //           LanguageEnum.bulk.localized(),
  //           style: const TextStyle(
  //             color: Colors.black,
  //             fontSize: 18,
  //             fontWeight: FontWeight.w500,
  //           ),
  //         ),
  //       );
  //     },
  //     body: Padding(
  //       padding: const EdgeInsets.only(left: 16, right: 16),
  //       child: Column(
  //         // Add 16 px padding to left and
  //         // right of column
  //         children: [
  //           SkuHeader(
  //             heading1: LanguageEnum.sku.localized(),
  //             heading2: LanguageEnum.unit.localized(),
  //             heading3: LanguageEnum.quantity.localized(),
  //             heading4: LanguageEnum.loss.localized(),
  //           ),
  //           const SizedBox(
  //             height: 16,
  //           ),
  //           for (int i = 0; i < data.length; i++)
  //             SkuWidget(
  //               index: i,
  //               isBulk: true,
  //               skuInput: data[i],
  //             ),
  //         ],
  //       ),
  //     ),
  //     isExpanded: isExpanded,
  //     canTapOnHeader: true,
  //   );
  // }

  // ExpansionPanel lotPanel(
  //   List<CloseInventorySkuInput> data, {
  //   required bool isExpanded,
  // }) {
  //   return ExpansionPanel(
  //     headerBuilder: (BuildContext context, bool isExpanded) {
  //       return ListTile(
  //         title: Text(
  //           LanguageEnum.lots.localized(),
  //           style: const TextStyle(
  //             color: Colors.black,
  //             fontSize: 18,
  //             fontWeight: FontWeight.w500,
  //           ),
  //         ),
  //       );
  //     },
  //     body: Padding(
  //       padding: const EdgeInsets.only(
  //         left: 16,
  //         right: 16,
  //       ),
  //       child: Column(
  //         // Add 16 px padding to left and right of column
  //         children: [
  //           SkuHeader(
  //             heading1: LanguageEnum.sku.localized(),
  //             heading2: LanguageEnum.lotSize.localized(),
  //             heading3: LanguageEnum.quantity.localized(),
  //             heading4: LanguageEnum.loss.localized(),
  //           ),
  //           const SizedBox(
  //             height: 16,
  //           ),
  //           for (int i = 0; i < data.length; i++)
  //             SkuWidget(
  //               index: i,
  //               isBulk: false,
  //               skuInput: data[i],
  //             ),
  //         ],
  //       ),
  //     ),
  //     isExpanded: isExpanded,
  //     canTapOnHeader: true,
  //   );
  // }
}
