part of 'add_procurement_bloc.dart';

@freezed
class AddProcurementEvent with _$AddProcurementEvent {
  const factory AddProcurementEvent.add(SkuInputData skuInputData) =
      _AddSkuData;
  const factory AddProcurementEvent.update(SkuInputData skuInputData) =
      _UpdateSkuData;
  const factory AddProcurementEvent.delete(SkuInputData skuInputData) =
      _DeleteSkuData;
  const factory AddProcurementEvent.submit() = _SubmitProcurement;
  const factory AddProcurementEvent.clearMessage() = _ClearMessage;

  const factory AddProcurementEvent.init({
    required String userName,
    required int smoId,
    required String mandiName,
    ProcDetail? procDetail,
    @Default([]) List<Sku> skus,
  }) = _InitSmoo;

  // Add slips
  const factory AddProcurementEvent.addSlips(List<PickedFile> slips) =
      _AddSlips;
  const factory AddProcurementEvent.clearSlips() = _ClearSlips;
  const factory AddProcurementEvent.updateVendor(VendorLocation vendor) =
      _UpdateVendor;
  const factory AddProcurementEvent.updateProcCharge(
    ProcFieldCharge charge,
    int index,
  ) = _UpdateProcCharge;
  const factory AddProcurementEvent.deleteProcCharge(
    int index,
  ) = _DeleteProcCharge;
  const factory AddProcurementEvent.addProcCharge(ProcFieldCharge charge) =
      _AddProcCharge;

  const factory AddProcurementEvent.updateComment(String comment) =
      _UpdateComment;

  const factory AddProcurementEvent.updateSupplyDate(DateTime? date) =
      _UpdateSupplyDate;
}
