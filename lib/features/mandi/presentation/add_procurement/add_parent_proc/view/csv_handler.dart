import 'dart:convert';
import 'dart:typed_data';

import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/download_file.dart';

class CSVHandler {
  static Future<bool> downloadCSV(
      {required String fileName,
      required List<List<dynamic>> values,
      List<String>? headers}) async {
    try {
      List<List<dynamic>> csvData = [];

      if (headers != null) {
        csvData.add(headers);
      }

      csvData.addAll(values);

      final String res = const ListToCsvConverter().convert(csvData);

      await downloadText(fileName, res);
      return true;
    } catch (e, s) {
      talker.handle(e, s);
      return false;
    }
  }

  static List<List<dynamic>>? stringToCSV(String value) {
    try {
      List<List<dynamic>> rowsAsListOfValues =
          const CsvToListConverter().convert(value);
      return rowsAsListOfValues;
    } catch (e, s) {
      talker.handle(e, s);
      return null;
    }
  }

  static Future<bool> downloadExcel({
    required String fileName,
    required List<List<dynamic>> values,
    List<String>? headers,
  }) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      if (headers != null) {
        sheet.appendRow(headers.map((e) => TextCellValue(e)).toList());
      }

      for (var row in values) {
        sheet.appendRow(row.map((e) => TextCellValue(e.toString())).toList());
      }

      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to encode Excel file');
      }

      await downloadBlob('$fileName.xlsx', excelBytes);
      return true;
    } catch (e, s) {
      talker.handle(e, s);
      return false;
    }
  }

  static Future<List<Map<String, dynamic>>> uploadAndReadAsMap() async {
    try {
      List<Map<String, dynamic>> result = [];
      List<List<dynamic>>? data = await uploadFromFile();
      if (data == null) {
        return [];
      }

      if (data.isEmpty) {
        return [];
      }
      List<String> firstRow = data[0].map((e) => e.toString()).toList();

      for (List<dynamic> row in data.sublist(1)) {
        Map<String, dynamic> rowVal = {};
        for (int i = 0; i < firstRow.length; i++) {
          rowVal[firstRow[i]] = row[i];
        }
        result.add(rowVal);
      }

      return result;
    } catch (e, s) {
      talker.handle(e, s);
      return [];
    }
  }

  static Future<List<List<dynamic>>?> uploadFromFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform
          .pickFiles(type: FileType.custom, allowedExtensions: ['csv']);

      if (result != null) {
        Uint8List? fileBytes = result.files.first.bytes;
        if (fileBytes == null) {
          throw Exception('Failed to read file bytes');
        }
        String content = const Utf8Decoder().convert(fileBytes);

        List<List<dynamic>>? rowsAsListOfValues = stringToCSV(content);
        return rowsAsListOfValues;
      } else {
        return null;
        // User canceled the picker
      }
    } catch (e, s) {
      talker.handle(e, s);
      return null;
    }
  }
}
