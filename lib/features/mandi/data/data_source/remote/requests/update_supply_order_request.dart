import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/supply_order_input.dart';

class UpdateSupplyOrderRequest extends BaseRequest<dynamic, String> {
  final List<Terminal> terminals;
  final List<SupplyOrder> orders;

  UpdateSupplyOrderRequest(this.orders, {required this.terminals});
  @override
  String getPath() => 'supplyOrder/updateItems';

  @override
  Map<String, dynamic> getBody() {
    return {
      'orders': orders.indexed.map((t) {
        final index = t.$1;
        final o = t.$2;
        final allItems = terminals[index].skus.where(
            (element) => element.quantity.isNotEmpty || element.wasOrdered);
        return {
          'id': o.id,
          'timestamp': o.timestamp,
          'customer': o.customer.toJson(),
          'items': allItems
              .map(
                (e) => {
                  'orderedPrice': 0,
                  'skuQuantity': e.skuQuantity.toJson(),
                },
              )
              .toList(),
        };
      }).toList(),
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid response';
  }

  @override
  RequestMethod get method => RequestMethod.PUT;
}
