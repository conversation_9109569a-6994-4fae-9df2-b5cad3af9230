import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/ir_sku_edit.dart';
import 'package:proc2/features/mandi/presentation/loss_types/bloc/loss_types_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class ReceiveEditDialog extends StatefulWidget {
  final int index;
  final IRSKUEditInputModel inputModel;
  final bool isBulk;
  final bool isManualWeightAllowed;

  const ReceiveEditDialog({
    super.key,
    required this.index,
    required this.inputModel,
    required this.isBulk,
    required this.isManualWeightAllowed,
  });

  @override
  _ReceiveEditDialogState createState() => _ReceiveEditDialogState();
}

class _ReceiveEditDialogState extends State<ReceiveEditDialog> {
  late IRSKUEditInputModel inputModel;
  List<LossTypeV2> losses = [];

  @override
  void initState() {
    super.initState();
    inputModel = widget.inputModel;
    final lossState = context.read<LossTypesBloc>().state;
    lossState.maybeMap(
      orElse: () {},
      success: (s) {
        final list = s.losses['INCOMING_STOCK'];
        if (list != null) {
          losses = list;
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final sku = getSKU(context, skuID: inputModel.sku.skuId);
    final totalLosses = inputModel.getTotalLosses();
    final received = inputModel.recieved.toDouble();
    final missing = inputModel.sku.quantity - received;
    final isLossInvalid = inputModel.losses
        .where((e) =>
            e.lossValue.isNotEmpty && e.files.length < e.minFileUploadCount)
        .isNotEmpty;
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            height: MediaQuery.of(context).size.height * 0.6,
            child: BlocConsumer<LossTypesBloc, LossTypesState>(
              listener: (context, state) {
                state.maybeWhen(
                  orElse: () {},
                  success: (mp) {
                    final list = mp['INCOMING_STOCK'];
                    if (list != null) {
                      setState(() {
                        losses = list;
                      });
                    }
                  },
                );
              },
              builder: (context, state) {
                return state.maybeMap(
                  orElse: () => CircularProgressIndicator(),
                  success: (success) {
                    return Dialog(
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(0.0)),
                      child: Padding(
                        padding: const EdgeInsets.all(0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration:
                                  BoxDecoration(color: Colors.grey[300]),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 4,
                                  horizontal: 16,
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      widget.isBulk
                                          ? '${sku.name} - ${inputModel.sku.unit.localized()}'
                                          : '${sku.name} - ${inputModel.sku.lotSize} ${inputModel.sku.unit}',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Colors.black,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    IconButton(
                                      visualDensity: VisualDensity.compact,
                                      icon: Icon(Icons.close),
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Expanded(
                              child: Scrollbar(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                    horizontal: 16,
                                  ),
                                  child: ListView(
                                    shrinkWrap: true,
                                    children: [
                                      _headingAndValue(
                                        heading: widget.isBulk
                                            ? getLangText(
                                                'receiveInventory.expectedQty',
                                                'Expected',
                                              )
                                            : getLangText(
                                                'receiveInventory.expectedLots',
                                                'Expected Lots',
                                              ),
                                        value:
                                            inputModel.sku.quantity.toString(),
                                      ),
                                      SizedBox(
                                        height: 12,
                                      ),
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            widget.isBulk
                                                ? getLangText(
                                                    'receiveInventory.received',
                                                    'Received',
                                                  )
                                                : getLangText(
                                                    'receiveInventory.receivedLots',
                                                    'Received Lots',
                                                  ),
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 100,
                                            child: WeighingQuantityButton(
                                              isReadOnly: false,
                                              label: received.asString(),
                                              popupBuilder: () {
                                                return WeightCapturePopupProcurementReceive(
                                                  isBulkKg: widget
                                                      .inputModel.sku.isBulkKg,
                                                  isManualEditAllowed: widget
                                                          .isManualWeightAllowed ||
                                                      kIsWeb,
                                                  initialWeight: received,
                                                  skuName: sku.name,
                                                  unitInfo: widget
                                                      .inputModel.sku.unitInfo,
                                                );
                                              },
                                              onChange: (quantity) {
                                                final quantityValue =
                                                    quantity?.value;
                                                if (quantityValue != null) {
                                                  setState(() {
                                                    inputModel =
                                                        inputModel.copyWith(
                                                      recieved: quantityValue,
                                                    );
                                                  });
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 12,
                                      ),
                                      _headingAndValue(
                                          heading: missing >= 0
                                              ? (widget.isBulk
                                                  ? 'Missing'
                                                  : 'Missing Lots')
                                              : (widget.isBulk
                                                  ? 'Excess'
                                                  : 'Excess Lots'),
                                          value: missing.abs().asString()),
                                      Divider(),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            widget.isBulk
                                                ? getLangText(
                                                    'receiveInventory.damaged',
                                                    'Damaged',
                                                  )
                                                : getLangText(
                                                    'receiveInventory.damagedLots',
                                                    'Damaged Lots',
                                                  ),
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          if (losses.isNotEmpty)
                                            OutlinedButton.icon(
                                              icon: Icon(Icons.add),
                                              label: LangText(
                                                  'receiveInventory.addDamages',
                                                  'Add'),
                                              onPressed: () {
                                                setState(
                                                  () {
                                                    inputModel = inputModel
                                                        .copyWith(losses: [
                                                      ...inputModel.losses,
                                                      LossInputModel(
                                                        lossType:
                                                            losses[0].lossType,
                                                        lossValue: '',
                                                        unit:
                                                            inputModel.sku.unit,
                                                        comment: '',
                                                        minFileUploadCount:
                                                            losses[0]
                                                                .minImageUpload,
                                                        maxFileUploadCount:
                                                            losses[0]
                                                                .maxImageUpload,
                                                      )
                                                    ]);
                                                  },
                                                );
                                              },
                                            ),
                                        ],
                                      ),
                                      for (int i = 0;
                                          i < inputModel.losses.length;
                                          i++)
                                        _displayLoss(i, sku),
                                      SizedBox(
                                        height: 16,
                                      ),
                                      _headingAndValue(
                                        heading: getLangText(
                                          'receiveInventory.totalDamages',
                                          'Total Damage',
                                        ),
                                        value: totalLosses.asString(),
                                      ),
                                      Divider(),
                                      _headingAndValue(
                                        heading: widget.isBulk
                                            ? getLangText(
                                                'receiveInventory.goodQty',
                                                'Good Qty')
                                            : getLangText(
                                                'receiveInventory.goodLots',
                                                'Good Lots',
                                              ),
                                        value:
                                            (received - totalLosses).toString(),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            WStickyBottomCta(
                              icon: Icons.check,
                              isEnabled: !isLossInvalid,
                              label: LangText(
                                  'receiveInventory.editUpdateCta', 'Update'),
                              onPressed: () {
                                context.pop(inputModel);
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  failed: (failed) {
                    return ErrorScreen(
                      message: 'Error while loading losses!',
                      onPressed: () {},
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _displayLoss(int i, Sku sku) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Container(
              child: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: DropdownButton<String>(
                  value: inputModel.losses[i].lossType,
                  underline: Container(),
                  items: losses.map(
                    (LossTypeV2 value) {
                      return DropdownMenuItem<String>(
                        value: value.lossType,
                        child: Text(
                          value.lossType.localized(),
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ).toList(),
                  onChanged: (newValue) {
                    final selectedNewLoss = losses
                        .firstWhere((element) => element.lossType == newValue);
                    setState(() {
                      inputModel = inputModel.copyWith(
                        losses: [
                          ...inputModel.losses.sublist(0, i),
                          inputModel.losses[i].copyWith(
                            lossType: newValue!,
                            minFileUploadCount: selectedNewLoss.minImageUpload,
                            maxFileUploadCount: selectedNewLoss.maxImageUpload,
                          ),
                          ...inputModel.losses.sublist(i + 1),
                        ],
                      );
                    });
                  },
                ),
              ),
            ),
            Row(
              children: [
                // Add the weighing machine button here
                SizedBox(
                  width: 100,
                  child: WeighingQuantityButton(
                    isReadOnly: false,
                    label: inputModel.losses[i].lossValue,
                    popupBuilder: () {
                      return WeightCapturePopupProcurementReceive(
                        isBulkKg: widget.inputModel.sku.isBulkKg,
                        isManualEditAllowed:
                            widget.isManualWeightAllowed || kIsWeb,
                        initialWeight:
                            inputModel.losses[i].lossValue.toDouble(),
                        skuName: sku.name,
                        unitInfo: widget.inputModel.sku.unitInfo,
                      );
                    },
                    onChange: (quantity) {
                      final quantityValue = quantity?.value;
                      if (quantityValue != null) {
                        setState(() {
                          inputModel = inputModel.copyWith(
                            losses: [
                              ...inputModel.losses.sublist(0, i),
                              inputModel.losses[i].copyWith(
                                lossValue: quantityValue,
                              ),
                              ...inputModel.losses.sublist(i + 1)
                            ],
                          );
                        });
                      }
                    },
                  ),
                ),

                SizedBox(
                  width: 16,
                ),
                InkWell(
                    onTap: () {
                      setState(() {
                        inputModel = inputModel.copyWith(
                          losses: [
                            ...inputModel.losses.sublist(0, i),
                            ...inputModel.losses.sublist(i + 1),
                          ],
                        );
                      });
                    },
                    child: Icon(
                      Icons.delete,
                      size: 24,
                      color: Colors.red,
                    )),
              ],
            ),
          ],
        ),
        if (inputModel.losses[i].lossValue.isNotEmpty &&
            inputModel.losses[i].files.length <
                inputModel.losses[i].minFileUploadCount)
          Text(
            'Please upload at least ${inputModel.losses[i].minFileUploadCount} file.',
            style: TextStyle(color: Colors.red),
          ),
        InlineImagePicker(
            files: inputModel.losses[i].files,
            allowMultiple: true,
            minFileAllowed: inputModel.losses[i].minFileUploadCount,
            maxFileAllowed: inputModel.losses[i].maxFileUploadCount,
            updateFile: (files) {
              setState(() {
                inputModel = inputModel.copyWith(losses: [
                  ...inputModel.losses.sublist(0, i),
                  inputModel.losses[i].copyWith(files: files),
                  ...inputModel.losses.sublist(i + 1),
                ]);
              });
            }),
        Divider(),
      ],
    );
  }

  Widget _headingAndValue({required String heading, required String value}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          heading,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _headingAndInput({
    required String heading,
    required String value,
    required ValueChanged<String>? onChanged,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          heading,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(
          width: 60,
          child: TextFormField(
            initialValue: value == '0.0' ? '' : value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.end,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 8,
              ),
              border: OutlineInputBorder(),
              hintText: '0',
              counterStyle: TextStyle(
                height: double.minPositive,
              ),
              counterText: '',
              isDense: true,
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            inputFormatters: Config.numberInputFilters,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }
}
