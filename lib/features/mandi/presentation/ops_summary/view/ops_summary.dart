import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/w_mandi_status_card.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/presentation/ops_summary/cubit/ops_summary_cubit.dart';
import 'package:proc2/features/mandi/util/get_mandi_name.dart';

final String _dateFormat = 'dd MMM, yyyy | hh:mm a';

class OpsSummary extends StatefulWidget {
  @override
  State<OpsSummary> createState() => _OpsSummary();
}

class _OpsSummary extends State<OpsSummary> {
  @override
  void initState() {
    super.initState();
    //add post frame callback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OpsSummaryCubit>().load();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OpsSummaryCubit, OpsSummaryState>(
      listener: (context, state) {
        state.map(
          data: (d) {
            if (d.error != null) {
              showSnackBar(d.error!.message);
              context.read<OpsSummaryCubit>().clearError();
            }
          },
          initial: (i) {},
        );
      },
      builder: (context, state) {
        final mandiId =
            state.maybeMap(orElse: () => null, data: (d) => d.mandiId);
        return SafeArea(
            child: Scaffold(
          appBar: AppBar(
            centerTitle: false,
            title: Text(
              mandiId != null
                  ? getMandiName(context, mandiId: mandiId)
                  : 'Ops Summary',
              style: TextStyle(fontSize: 16),
            ),
          ),
          body: Builder(
            builder: (context) {
              return state.map(
                initial: (i) => Center(
                  child: CircularProgressIndicator(),
                ),
                data: (data) {
                  final smoDetailV2 = data.smoDetailV2;

                  if (smoDetailV2 == null)
                    return Center(child: CircularProgressIndicator());

                  final isEmpty = smoDetailV2.myProc.isEmpty &&
                      smoDetailV2.othersProc.isEmpty;
                  return Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    color: Colors.grey[200],
                    padding: const EdgeInsets.only(
                        left: 0, right: 0, top: 8, bottom: 0),
                    child: Column(
                      children: [
                        Expanded(
                          flex: 1,
                          child: ListView(
                            children: [
                              Container(
                                margin: EdgeInsets.symmetric(
                                  vertical: 16,
                                  horizontal: 16,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(12)),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 16.0,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (smoDetailV2.time.start != null)
                                        infoRowText(
                                          Icons.start,
                                          getLangText(
                                              'startedAt', 'Started at'),
                                          smoDetailV2.time.start!
                                              .toDate(_dateFormat),
                                        ),
                                      if (smoDetailV2.time.closeOps != null)
                                        infoRowText(
                                          Icons.stop,
                                          getLangText(
                                              'closedOpsAt', 'Ops Closed at'),
                                          smoDetailV2.time.closeOps!
                                              .toDate(_dateFormat),
                                        ),
                                      if (smoDetailV2.time.closingInventory !=
                                          null)
                                        infoRowText(
                                          Icons.playlist_add_check,
                                          getLangText(
                                              'closingInventoryClosedAt',
                                              'Inventory Closed at'),
                                          smoDetailV2.time.closingInventory!
                                              .toDate(_dateFormat),
                                        ),
                                      if (smoDetailV2.time.end != null)
                                        infoRowText(
                                          Icons.close,
                                          getLangText('endAt', 'Ended at'),
                                          smoDetailV2.time.end!
                                              .toDate(_dateFormat),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                              if (smoDetailV2.myProc.isNotEmpty) ...[
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 16, bottom: 16, left: 16),
                                  child: LangText(
                                    'opsSummary.myOpenProc',
                                    'My Open Procurements',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                    left: 16,
                                    right: 16,
                                  ),
                                  child: Container(
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(16),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 2,
                                                child: LangText(
                                                  'dateAndTime',
                                                  'Date & Time',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 8,
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: LangText(
                                                  'numberOfItems',
                                                  '# of Items',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 8,
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Text(
                                                  'Action',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87,
                                                  ),
                                                  textAlign: TextAlign.end,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Divider(
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        SizedBox(
                                          height: 8,
                                        ),
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              NeverScrollableScrollPhysics(),
                                          itemCount: smoDetailV2.myProc.length,
                                          itemBuilder: (context, index) {
                                            final item =
                                                smoDetailV2.myProc[index];

                                            return Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 4,
                                                      horizontal: 16),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    flex: 2,
                                                    child: Text(
                                                      item.createdAt.toDate(
                                                        'dd MMM - hh:mm a',
                                                      ),
                                                      style: TextStyle(
                                                        color: Colors.black87,
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 8,
                                                  ),
                                                  Expanded(
                                                    flex: 1,
                                                    child: Text(
                                                      item.items.length
                                                          .toString(),
                                                      style: TextStyle(
                                                        color: Colors.black87,
                                                      ),
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    width: 8,
                                                  ),
                                                  if (!item.canUpdatePrice)
                                                    Expanded(
                                                      flex: 1,
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .end,
                                                        children: [
                                                          Text(
                                                            'Updated',
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.green,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    )
                                                  else
                                                    Expanded(
                                                      flex: 1,
                                                      child: Align(
                                                        alignment: Alignment
                                                            .centerRight,
                                                        child: IconButton(
                                                          onPressed: () async {
                                                            await context.push(
                                                              context
                                                                  .namedLocation(
                                                                'editProcurement',
                                                                pathParameters: {
                                                                  'smoId': item
                                                                      .smoId
                                                                      .toString(),
                                                                  'mandiId': data
                                                                      .mandiId
                                                                      .toString(),
                                                                },
                                                              ),
                                                              extra: SmoDetail
                                                                  .fromMyProc(
                                                                      item),
                                                            );
                                                            context
                                                                .read<
                                                                    OpsSummaryCubit>()
                                                                .load();
                                                          },
                                                          icon: Icon(
                                                            Icons.upload,
                                                            color: Colors.green,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                        SizedBox(
                                          height: 4,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                              if (smoDetailV2.othersProc.isNotEmpty) ...[
                                Padding(
                                  padding: const EdgeInsets.only(
                                    top: 16,
                                    bottom: 16,
                                    left: 16,
                                  ),
                                  child: LangText(
                                    'opsSummary.otherOpenProc',
                                    'Other Open Procurements',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 16, right: 16),
                                  child: Container(
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(16),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 2,
                                                child: LangText(
                                                  'procurementSummary.managerTitle',
                                                  'Manager',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87,
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: LangText(
                                                  'procurementSummary.pendingTitle',
                                                  'Pending Count',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.black87,
                                                  ),
                                                  textAlign: TextAlign.end,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Divider(
                                          height: 1,
                                          color: Colors.grey[300],
                                        ),
                                        ListView.builder(
                                          shrinkWrap: true,
                                          physics:
                                              NeverScrollableScrollPhysics(),
                                          itemCount:
                                              smoDetailV2.othersProc.length,
                                          itemBuilder: (context, index) {
                                            final item =
                                                smoDetailV2.othersProc[index];
                                            return Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8,
                                                      horizontal: 16),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      item.procuredBy,
                                                      style: TextStyle(
                                                        color: Colors.black87,
                                                      ),
                                                    ),
                                                  ),
                                                  Expanded(
                                                    child: Text(
                                                      item.nosOpenProc
                                                          .toString(),
                                                      style: TextStyle(
                                                        color: Colors.black87,
                                                      ),
                                                      textAlign: TextAlign.end,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ],
                          ),
                        ),
                        if (isEmpty)
                          Expanded(
                            flex: 4,
                            child: EmptyScreen(
                              message: getLangText(
                                'opsSummary.operationsClosed',
                                'Operations Closed!',
                              ),
                            ),
                          ),
                        WStickyBottomCta(
                          icon: Icons.info_outline,
                          label: LangText(
                            'procurementSummary.detailCta',
                            'Details',
                            style: TextStyle(fontSize: 16),
                          ),
                          onPressed: () {
                            context.push(context.namedLocation(
                              'procurementHistory',
                              pathParameters: {
                                'smoId': data.smoId.toString(),
                                'mandiId': data.mandiId.toString(),
                              },
                            ));
                          },
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ));
      },
    );
  }
}
