import 'package:proc2/core/data/network/base_request.dart';

class UpdateProcOrderRequest extends BaseRequest<dynamic, String> {
  final List<Map<String, dynamic>> updates;

  UpdateProcOrderRequest({required this.updates});

  @override
  String getPath() => 'procurements/items/edit/bulk';

  @override
  Map<String, dynamic> getBody() {
    return {
      'orders': updates,
    };
  }

  @override
  String mapper(data) {
    if (data is String) {
      return data;
    }
    throw Exception('Invalid data');
  }

  @override
  RequestMethod get method => RequestMethod.PUT;
}
