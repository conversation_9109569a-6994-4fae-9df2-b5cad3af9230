import 'package:freezed_annotation/freezed_annotation.dart';

part 'sku_quantity_dto.freezed.dart';
part 'sku_quantity_dto.g.dart';

@freezed
class SkuQuantityDto with _$SkuQuantityDto {
  const factory SkuQuantityDto({
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double? quantity,
  }) = _SkuQuantityDto;

  @override
  factory SkuQuantityDto.fromJson(Map<String, dynamic> json) =>
      _$SkuQuantityDtoFromJson(json);
}
