import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/report_bug/model/bug_report.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:url_launcher/url_launcher_string.dart';

class BugReportCard extends StatefulWidget {
  final BugReport bugReport;
  final VoidCallback onResolve;

  const BugReportCard({
    super.key,
    required this.bugReport,
    required this.onResolve,
  });

  @override
  _BugReportCardState createState() => _BugReportCardState();
}

class _BugReportCardState extends State<BugReportCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            onTap: _toggleExpand,
            title: SelectableText(widget.bugReport.title),
            subtitle: SelectableText(widget.bugReport.subtitle),
            trailing: Icon(
              _isExpanded ? Icons.expand_less : Icons.expand_more,
            ),
          ),
          SizeTransition(
            sizeFactor: _animation,
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.bugReport.comment.isNotEmpty) ...[
                    Text(
                      'comment'.tr('Comment'),
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(widget.bugReport.comment),
                  ],
                  SizedBox(height: 8.0),
                  if (widget.bugReport.attachments.isNotEmpty) ...[
                    Text('attachments'.tr('Attachments'),
                        style: TextStyle(fontWeight: FontWeight.w600)),
                    Wrap(
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children: widget.bugReport.attachments.indexed
                          .map(
                            (e) => InkWell(
                              onTap: () async {
                                await launchUrlString(e.$2);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border.all(),
                                  borderRadius: BorderRadius.circular(8.0),
                                  color: Colors.grey[200],
                                ),
                                padding: EdgeInsets.all(8.0),
                                child: Text(
                                  'File ${e.$1 + 1}',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ],
                  SizedBox(
                    height: 8,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ElevatedButton.icon(
                        onPressed: widget.bugReport.logFile.isEmpty
                            ? null
                            : () async {
                                await launchUrlString(widget.bugReport.logFile);
                              },
                        icon: Icon(Icons.download),
                        label: Text(
                          'downloadLogFile'.tr('Download Log File'),
                        ),
                      ),
                      TextButton.icon(
                        onPressed: () async {
                          final shouldResolve = await context.showAlertDialog(
                                title: 'resolveABug'.tr('Resolve Bug'),
                                message: 'resolveABugMessage'.tr(
                                    'Are you sure you want to resolve this bug?'),
                              ) ??
                              false;
                          if (!shouldResolve) return;
                          // resolve bug
                          widget.onResolve();
                        },
                        icon: Icon(
                          Icons.check_circle,
                          color: Colors.green,
                        ),
                        label: Text('markResolve'.tr('Mark Resolve')),
                      )
                    ],
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
