part of 'allocating_inventory_bloc.dart';

@freezed
class AllocatingInventoryEvent with _$AllocatingInventoryEvent {
  const factory AllocatingInventoryEvent.started({
    required int mandiId,
    required int allotmentId,
    required String? customerGroup,
    required String? deliverySlot,
    required String? deliveryDate,
    required Map<int, Sku> skuMap,
  }) = _Started;

  const factory AllocatingInventoryEvent.updateAllotmentQuantity({
    required AllocateListType listType,
    required int listIndex,
    required int itemIndex,
    required String allotmentQuantity,
  }) = _UpdateAllotmentQuantity;

  // save as draft
  const factory AllocatingInventoryEvent.saveAsDraft(int allotmentId) =
      _SaveAsDraft;
  const factory AllocatingInventoryEvent.copyFromInventory(
      {@Default(true) bool isMandi}) = _CopyFromInventory;
  // dispatch
  const factory AllocatingInventoryEvent.dispatch(int allotmentId) = _Dispatch;

  // clear message
  const factory AllocatingInventoryEvent.clearMessage() = _ClearMessage;
}

enum AllocateListType {
  allList,
  mandiList,
  supplyList,
}
