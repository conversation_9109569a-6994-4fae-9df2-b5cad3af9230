import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';

class ProcChildOrder {
  final int id;
  final List<ProcDetailItem> items;
  final int mandiId;

  ProcChildOrder({
    required this.id,
    required this.items,
    required this.mandiId,
  });

  factory ProcChildOrder.fromJson(Map<String, dynamic> data) {
    final id = data['id'] as int;
    final mandiId = data['mandiId'] as int;
    final items = (data['items'] as List<dynamic>)
        .map((e) => ProcDetailItem.fromJson(e))
        .toList();

    return ProcChildOrder(
      id: id,
      items: items,
      mandiId: mandiId,
    );
  }
}
