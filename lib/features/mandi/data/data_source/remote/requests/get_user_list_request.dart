import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/manage_users.dart/proc_user.dart';

class GetUserListRequest extends BaseRequest<dynamic, List<ProcUser>> {
  @override
  String getPath() => 'users/getAllUsers';

  @override
  mapper(data) {
    if (data is List) {
      return data
          .map((e) => ProcUser.fromJson(e as Map<String, dynamic>))
          .toList();
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
