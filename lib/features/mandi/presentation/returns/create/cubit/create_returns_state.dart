part of 'create_returns_cubit.dart';

@freezed
class CreateReturnsState with _$CreateReturnsState {
  const CreateReturnsState._();

  const factory CreateReturnsState.initial({
    @Default('') String terminalId,
    @Default({}) Map<int, List<ReturnItem>> inputItems,
    @Default([]) List<String> notAllowedSkus,
    @Default(false) bool isCtaLoading,
    @Default(null) String? message,
    @Default(false) bool shouldPop,
  }) = _Initial;
  String get skuCountText => inputItems.isEmpty ? '' : '(${inputItems.length})';
}
