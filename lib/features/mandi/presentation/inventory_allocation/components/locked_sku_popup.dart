import 'package:flutter/material.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/locked_sku.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_locked_sku_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/unlock_locked_sku_request.dart';

class LockedSkuPopup extends StatefulWidget {
  LockedSkuPopup({
    super.key,
    required this.skuMap,
    required this.allocationId,
  });
  final Map<int, Sku> skuMap;
  final int allocationId;

  @override
  State<LockedSkuPopup> createState() => _LockedSkuPopupState();
}

class _LockedSkuPopupState extends State<LockedSkuPopup> {
  bool isSkuLoading = false;
  List<LockedSku> lockedSkus = [];
  String? errorMessage;
  bool isUnlockingSku = false;
  String? message;

  @override
  void initState() {
    super.initState();
    getLockedSku();
  }

  void getLockedSku() async {
    if (isSkuLoading) return;
    setState(() {
      isSkuLoading = true;
      errorMessage = null;
      lockedSkus = [];
      message = null;
    });
    final result =
        await GetLockedSkuRequest(allocationId: widget.allocationId).execute();
    result.fold(
      (left) {
        setState(() {
          errorMessage = left.message.isEmpty
              ? 'Error while loading locked skus'
              : left.message;
          isSkuLoading = false;
          lockedSkus = [];
        });
      },
      (right) {
        setState(() {
          lockedSkus = right;
          errorMessage = null;
          isSkuLoading = false;
        });
      },
    );
  }

  void unlockSku(SkuQuantity sku) async {
    if (isUnlockingSku) return;
    setState(() {
      isUnlockingSku = true;
      message = null;
    });
    final result = await UnlockLockedSkuRequest(
      allocationId: widget.allocationId,
      sku: sku,
    ).execute();
    result.fold(
      (left) {
        setState(() {
          message =
              left.message.isEmpty ? 'Error while unlocking sku' : left.message;
          isUnlockingSku = false;
        });
      },
      (right) {
        try {
          showSnackBar(right);
        } catch (e, s) {
          talker.handle(e, s);
        }
        setState(() {
          getLockedSku();
          isUnlockingSku = false;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isLoading = isSkuLoading || isUnlockingSku;
    return Popup(
      title: 'lockedSkuPopup.title'.tr('Locked Skus'),
      children: [
        if (message != null)
          Text(
            message!,
            style: TextStyle(color: Colors.red, fontSize: 14),
            textAlign: TextAlign.center,
          ),
        if (isLoading)
          Expanded(
            child: Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        if (!isLoading && errorMessage != null)
          ErrorScreen(
            message: errorMessage!,
            onPressed: getLockedSku,
          ),
        if (!isLoading && errorMessage == null && lockedSkus.isEmpty)
          ErrorScreen(
            heading: 'lockedSkuPopup.noLockedSkus'.tr('No locked skus found'),
            message: 'lockedSkuPopup.noLockedSkusMessage'
                .tr('No skus are locked for this allocation'),
            onPressed: getLockedSku,
          ),
        if (!isLoading && errorMessage == null && lockedSkus.isNotEmpty) ...[
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            padding: EdgeInsets.symmetric(
              horizontal: 8,
            ),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    'sku'.tr('Sku'),
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Text(
                    'lockedBy'.tr('Locked By'),
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(
                  width: 8,
                ),
                SizedBox(
                  width: 48,
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.symmetric(
                horizontal: 8,
              ),
              shrinkWrap: true,
              children: [
                for (final sku in lockedSkus)
                  Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.skuMap[sku.skuQuantity.skuId]?.name ??
                                    '-',
                                style: TextStyle(
                                  fontSize: 15,
                                ),
                              ),
                              Text(
                                sku.skuQuantity.unitInfo,
                                style: TextStyle(
                                  fontSize: 14,
                                ),
                              )
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        Expanded(
                          child: Text(
                            sku.lockedBy ?? '-',
                            style: TextStyle(
                              fontSize: 15,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 8,
                        ),
                        IconButton(
                          onPressed: () {
                            unlockSku(sku.skuQuantity);
                          },
                          icon: Icon(
                            Icons.lock_open,
                            color: Colors.green,
                          ),
                        )
                      ],
                    ),
                  ),
              ],
            ),
          )
        ]
      ],
    );
  }
}
