import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/create_and_submit_returns_request.dart';
import 'package:proc2/features/mandi/domain/entity/returns/returns.dart';

part 'create_returns_state.dart';
part 'create_returns_cubit.freezed.dart';

@injectable
class CreateReturnsCubit extends Cubit<CreateReturnsState> {
  CreateReturnsCubit() : super(CreateReturnsState.initial());

  void clearMessage() {
    emit(state.copyWith(message: null, shouldPop: false));
  }

  void addItem(ReturnItem input) {
    final list =
        List<ReturnItem>.from(state.inputItems[input.skuQuantity.skuId] ?? []);
    list.add(input);
    final orders = Map.fromEntries(state.inputItems.entries);
    orders[input.skuQuantity.skuId] = list;
    final notAllowedSkus = List<String>.from(state.notAllowedSkus);
    notAllowedSkus.add(input.skuQuantity.compositeKey);
    emit(state.copyWith(inputItems: orders, notAllowedSkus: notAllowedSkus));
  }

  void updateItem(ReturnItem input, int i) {
    final list =
        List<ReturnItem>.from(state.inputItems[input.skuQuantity.skuId] ?? []);
    final previousCompositeKey = list[i].skuQuantity.compositeKey;
    list[i] = input;
    final orders = Map.fromEntries(state.inputItems.entries);
    orders[input.skuQuantity.skuId] = list;
    final notAllowedSkus = List<String>.from(state.notAllowedSkus);
    notAllowedSkus.remove(previousCompositeKey);
    notAllowedSkus.add(input.skuQuantity.compositeKey);
    emit(state.copyWith(inputItems: orders, notAllowedSkus: notAllowedSkus));
  }

  void deleteItem(int skuId, int i) {
    final list = List<ReturnItem>.from(state.inputItems[skuId] ?? []);
    final itemRemoved = list.removeAt(i);
    final orders = Map.fromEntries(state.inputItems.entries);
    if (list.isEmpty) {
      orders.remove(skuId);
    } else {
      orders[skuId] = list;
    }
    final notAllowedSkus = List<String>.from(state.notAllowedSkus);
    notAllowedSkus.remove(itemRemoved.skuQuantity.compositeKey);
    emit(state.copyWith(inputItems: orders, notAllowedSkus: notAllowedSkus));
  }

  void submit(int smoId, String terminalId) async {
    emit(state.copyWith(isCtaLoading: true));
    final result = await CreateAndSubmitReturnsRequest(
      smoId: smoId,
      terminalId: terminalId,
      returnItems:
          state.inputItems.values.expand((element) => element).toList(),
    ).execute();
    final newState = result.fold(
      (left) => state.copyWith(message: left.message, isCtaLoading: false),
      (right) => state.copyWith(
        message: right,
        shouldPop: true,
      ),
    );
    emit(newState);
  }
}
