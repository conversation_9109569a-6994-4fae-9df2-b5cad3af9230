import 'package:freezed_annotation/freezed_annotation.dart';

part 'sku_history_dto.freezed.dart';
part 'sku_history_dto.g.dart';

@freezed
class SkuHistoryDto with _$SkuHistoryDto {
  factory SkuHistoryDto({
    required int id,
    required int skuId,
    required String type,
    required String unit,
    required double? lotSize,
    required double? quantity,
    required double? weight,
    required double? amount,
    required double? orderedQuantity,
    required bool? finalSubmit,
    required double? billedCostPrice,
    required double? orderedCostPrice,
  }) = _SkuHistoryDto;

  factory SkuHistoryDto.fromJson(Map<String, dynamic> json) =>
      _$SkuHistoryDtoFromJson(json);
}
