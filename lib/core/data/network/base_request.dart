import 'package:chopper/chopper.dart';
import 'package:either_dart/either.dart';
import 'package:proc2/core/data/network/extensions/chopper_either_extension.dart';
import 'package:proc2/core/data/network/network.dart';
import 'package:proc2/core/data/network/network_service.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/domain/entity/error_result.dart';

abstract class BaseRequest<ErrorType, ResponseType> {
  // Path and Query
  String getPath();
  Map<String, dynamic>? getQuery() => null;

  // Body
  Map<String, dynamic> getBody() => {};

  bool isAuth() => true;

  RequestMethod get method;

  ResponseType mapper(dynamic data);
  ErrorType? errorMapper(dynamic data) => null;
  NetworkService get _networkService => di.get<NetworkService>();

  Future<Either<ErrorResult<ErrorType?>, ResponseType>?> mockData() =>
      Future.value(null);

  Future<Either<ErrorResult<ErrorType?>, ResponseType>> execute(
      {bool mock = false}) async {
    if (mock) {
      final mockResult = await mockData();
      if (mockResult == null) {
        throw Exception(
          'Mock data is null for the mock request. Need mock implementation at $this',
        );
      }
      return mockResult;
    }
    final path = getPath();
    final query = getQuery() ?? {};
    final body = getBody();
    final auth = isAuth();

    late Future<Response<Map<String, dynamic>>> response;

    switch (method) {
      case RequestMethod.GET:
        response = auth
            ? _networkService.get(path, query)
            : _networkService.getNoAuth(path, query);
        break;
      case RequestMethod.POST:
        response = auth
            ? _networkService.post(path, body, query)
            : _networkService.postNoAuth(path, body, query);
        break;
      case RequestMethod.PUT:
        response = auth
            ? _networkService.put(path, body, query)
            : _networkService.putNoAuth(path, body, query);
        break;
      case RequestMethod.DELETE:
        response = auth
            ? _networkService.delete(path, query)
            : _networkService.deleteNoAuth(path, query);
        break;
    }

    try {
      return await _networkService.safeCall(
          apiCall: () => response,
          transform: mapper,
          errorTransform: errorMapper);
    } catch (e) {
      return Left(
        ErrorResult(
          code: '',
          message: e.toString(),
          timestamp: DateTime.now().millisecondsSinceEpoch,
        ),
      );
    }
  }
}

enum RequestMethod { GET, POST, PUT, DELETE }
