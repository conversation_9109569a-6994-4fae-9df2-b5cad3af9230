import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/cubit/field_charge_cubit.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/field_charge/model/proc_field_charge.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class ProcFieldChargesWidget extends StatelessWidget {
  const ProcFieldChargesWidget({
    super.key,
    this.padding = EdgeInsets.zero,
    required this.fieldCharges,
    required this.smoId,
    this.onChanged,
    this.onDelete,
    this.onAdd,
    this.enableEditing = true,
  });
  final bool enableEditing;
  final EdgeInsets padding;
  final List<ProcFieldCharge> fieldCharges;
  final Function(ProcFieldCharge charge, int index)? onChanged;
  final Function(
    int index,
  )? onDelete;
  final Function(ProcFieldCharge procFieldCharge)? onAdd;

  final int smoId;

  @override
  Widget build(BuildContext context) {
    if (fieldCharges.isEmpty && !enableEditing) return SizedBox();

    return BlocProvider(
      create: (context) => di.get<FieldChargeCubit>()..getFieldCharges(),
      child: BlocBuilder<FieldChargeCubit, FieldChargeState>(
        builder: (context, state) {
          return Container(
            color: Colors.white,
            padding: padding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'procFieldCharges.fieldCharges'.tr('Field Charges'),
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline),
                    ),
                    if (state.procFieldCharges != null &&
                        state.procFieldCharges!.isNotEmpty &&
                        enableEditing)
                      OutlinedButton.icon(
                        onPressed: () {
                          onAdd?.call(
                            ProcFieldCharge(
                              type: state.procFieldCharges!.first.value,
                              amount: '',
                              images: [],
                            ),
                          );
                        },
                        icon: Icon(Icons.add),
                        label: LangText(
                          'procFieldCharges.addNew',
                          'Add New',
                        ),
                      ),
                  ],
                ),
                SizedBox(
                  height: 16,
                ),
                state.error != null
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            state.error!,
                            style: TextStyle(color: Colors.red),
                          ),
                          SizedBox(
                            height: 16,
                          ),
                          ElevatedButton(
                            onPressed: () {
                              context
                                  .read<FieldChargeCubit>()
                                  .getFieldCharges(hardRefresh: true);
                            },
                            child: Text('Retry'),
                          ),
                        ],
                      )
                    : SizedBox(),
                if (state.procFieldCharges == null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Center(
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(),
                        ),
                      ),
                    ],
                  ),
                if (state.procFieldCharges != null &&
                    state.procFieldCharges!.isNotEmpty)
                  for (int i = 0; i < fieldCharges.length; i++)
                    _ChargeWidget(
                      charges: state.procFieldCharges!,
                      charge: fieldCharges[i],
                      onChanged: (value) {
                        onChanged?.call(value, i);
                      },
                      onDelete: () {
                        onDelete?.call(i);
                      },
                    ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _ChargeWidget({
    required List<ChargeType> charges,
    required ProcFieldCharge charge,
    required ValueChanged<ProcFieldCharge> onChanged,
    required VoidCallback onDelete,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 8,
      ),
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade100,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: !enableEditing
                    ? Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.grey.shade300,
                        ),
                        padding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 8,
                        ),
                        margin: EdgeInsets.only(top: 8, bottom: 8),
                        child: Text(
                          charge.type,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      )
                    : CustomDropdownV2<ChargeType>(
                        selectedItem: ChargeType(charge.type),
                        items: charges,
                        onItemSelected: (item) {
                          onChanged(charge.copyWith(type: item.value));
                        },
                      ),
              ),
              SizedBox(
                width: 16,
              ),
              SizedBox(
                width: 100,
                child: AmountTextField(
                  enableEditing: enableEditing,
                  value: charge.amount,
                  onChanged: (value) {
                    onChanged(charge.copyWith(amount: value));
                  },
                ),
              ),
              SizedBox(
                width: 16,
              ),
              if (enableEditing)
                InkWell(
                  onTap: onDelete,
                  child: Icon(
                    Icons.delete,
                    color: Colors.red,
                    size: 24,
                  ),
                ),
            ],
          ),
          if (enableEditing || charge.images.isNotEmpty)
            InlineImagePicker(
              files: charge.images,
              allowMultiple: true,
              updateFile: (files) {
                onChanged(charge.copyWith(images: files));
              },
              module: UploadFileModule.smoCharges,
              minFileAllowed: 0,
              maxFileAllowed: 4,
              uploadAlso: true,
              smoId: smoId,
              isEnabled: enableEditing,
            ),
        ],
      ),
    );
  }
}

class AmountTextField extends StatefulWidget {
  const AmountTextField({
    super.key,
    required this.enableEditing,
    required this.value,
    required this.onChanged,
  });
  final bool enableEditing;
  final String value;
  final ValueChanged<String> onChanged;

  @override
  State<AmountTextField> createState() => _AmountTextFieldState();
}

class _AmountTextFieldState extends State<AmountTextField> {
  final TextEditingController controller = TextEditingController();

  @override
  void initState() {
    controller.text = widget.value;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AmountTextField oldWidget) {
    if (oldWidget.value != widget.value) {
      controller.text = widget.value;
      controller.selection = TextSelection.fromPosition(
        TextPosition(offset: controller.text.length),
      );
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      readOnly: !widget.enableEditing,
      decoration: InputDecoration(
        label: Text('amount'.tr('Amount')),
        hintStyle: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        isDense: true,
        contentPadding: EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 8,
        ),
      ),
      controller: controller,
      inputFormatters: Config.numberInputFilters,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      onChanged: widget.onChanged,
    );
  }
}
