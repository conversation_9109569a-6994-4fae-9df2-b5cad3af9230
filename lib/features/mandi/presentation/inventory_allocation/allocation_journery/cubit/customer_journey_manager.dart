import 'package:either_dart/either.dart';
import 'package:flutter/foundation.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_supply_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order_metadata.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/journey_manager.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/process_background.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_configuration.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_error_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_journey_stage.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_terminal.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/filter_data.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/group_info.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/auto_allocate_remaining_sku_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/create_group_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_allocation_configuration_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/get_group_request.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/request/update_allocation_request.dart';

class CustomerJourneyManager extends JourneyManager {
  CustomerJourneyManager({required super.stream, required super.emmiter});

  @override
  Future<void> initialize() async {
    emit(state.copyWith(
      loadingMessage: 'Fetching group info and supply orders...',
      previousStage: null,
    ));
    // Get groupInfo api
    final groupInfoRequest =
        GetGroupRequest(allocationId: state.allocationId).execute();
    // Get supply order api
    final supplyOrderRequest = GetSupplyOrderRequest(
      deliveryEpochSeconds: state.customerGroupData!.deliveryDateEpochSeconds,
      deliverySlot: state.customerGroupData!.deliverySlot,
      customerGroup: state.customerGroupData!.customerGroup,
      allocationId: state.allocationId,
    ).execute();
    final allocationConfigRequest = GetAllocationConfigurationRequest(
      allocationId: state.allocationId,
    ).execute();

    final result = await Future.wait([
      groupInfoRequest,
      supplyOrderRequest,
      allocationConfigRequest,
    ]);
    final groupInfoResponse =
        result[0] as Either<ErrorResult<dynamic>, List<GroupInfo>>;
    final supplyOrderResponse =
        result[1] as Either<ErrorResult<dynamic>, SupplyOrderMetadata>;
    final allocationConfigResponse =
        result[2] as Either<ErrorResult<dynamic>, AllocationConfiguration>;

    if (groupInfoResponse.isLeft ||
        ((supplyOrderResponse.isLeft || allocationConfigResponse.isLeft) &&
            state.isEditingAllowed)) {
      // Throw error
      final error = groupInfoResponse.isLeft
          ? groupInfoResponse.left
          : supplyOrderResponse.isLeft
              ? supplyOrderResponse.left
              : allocationConfigResponse.left;
      emit(
        state.copyWith(
          stage: AllocationJourneyStage.error,
          previousStage: null,
          errorData: AllocationErrorData(
            heading: 'Oops!',
            message: error.message.isEmpty
                ? 'Error fetching group info and supply orders!'
                : error.message,
            functionToRetry: 'initialize',
          ),
          loadingMessage: null,
        ),
      );
      return;
    }

    if (supplyOrderResponse.isRight &&
        supplyOrderResponse.right.supplyOrders.isEmpty &&
        state.isEditingAllowed) {
      // Throw error
      emit(
        state.copyWith(
          stage: AllocationJourneyStage.error,
          previousStage: null,
          errorData: AllocationErrorData(
            heading: 'No supply order found!',
            message: 'Please add supply orders and',
            functionToRetry: 'initialize',
          ),
          loadingMessage: null,
        ),
      );
      return;
    }

    emit(state.copyWith(
      groupInfo: groupInfoResponse.right,
      supplyOrders: supplyOrderResponse.isRight
          ? supplyOrderResponse.right.supplyOrders
          : <SupplyOrder>[],
      allocationConfig: allocationConfigResponse.isRight
          ? allocationConfigResponse.right
          : null,
    ));

    if (state.isEditingAllowed) {
      bool hasPendingGroup = combineGroupInfoAndSupplyOrder(
          groupInfoResponse.right,
          supplyOrderResponse.isRight
              ? supplyOrderResponse.right.supplyOrders
              : <SupplyOrder>[]);
      if ((groupInfoResponse.right.isEmpty || hasPendingGroup)) {
        emit(state.copyWith(
            stage: AllocationJourneyStage.grouping, previousStage: null));
        return;
      }
    }

    await getInventoryAndAllocationData();
  }

  @override
  Future<void> loadSupplyOrders() async {
    emit(state.copyWith(
      loadingMessage: 'Refreshing supply orders...',
      previousStage: null,
      stage: AllocationJourneyStage.loading,
    ));

    final result = await GetSupplyOrderRequest(
      deliveryEpochSeconds: state.customerGroupData!.deliveryDateEpochSeconds,
      deliverySlot: state.customerGroupData!.deliverySlot,
      customerGroup: state.customerGroupData!.customerGroup,
      allocationId: state.allocationId,
    ).execute();

    if (result.isLeft) {
      emit(state.copyWith(
        loadingMessage: null,
        stage: AllocationJourneyStage.error,
        previousStage: null,
        errorData: AllocationErrorData(
          heading: 'Oops!',
          message: result.left.message,
          functionToRetry: 'loadSupplyOrders',
        ),
      ));
      return;
    }

    emit(state.copyWith(
      supplyOrders: result.right.supplyOrders,
      isActionsBlocked: result.right.isActionsBlocked,
      allowOnlyDispatch: result.right.allowOnlyDispatch,
      loadingMessage: null,
    ));

    await getInventoryAndAllocationData(skipInventory: true);
  }

  bool combineGroupInfoAndSupplyOrder(
      List<GroupInfo> groupInfo, List<SupplyOrder> supplyOrder) {
    final allocationTerminal = supplyOrder.map((e) {
      return AllocationTerminal(
          id: e.customer.key,
          groupValue: groupInfo
              .where((g) => g.customerKeys.contains(e.customer.key))
              .firstOrNull
              ?.id,
          serialNumber: 0);
    }).toList();
    allocationTerminal.sort((a, b) {
      if (a.groupValue == null || b.groupValue == null) return 0;
      return a.groupValue!.compareTo(b.groupValue!);
    });

    var groupedTerminal =
        allocationTerminal.where((e) => e.groupValue != null).toList();
    var sno = 1;
    var groupValueString = groupedTerminal.firstOrNull?.groupValueString;
    groupedTerminal = groupedTerminal.map((e) {
      if (e.groupValueString != groupValueString) {
        sno = 1;
        groupValueString = e.groupValueString;
      }
      return e.copyWith(serialNumber: sno++);
    }).toList();
    var pendingTerminal =
        allocationTerminal.where((e) => e.groupValue == null).toList().map((e) {
      if (e.groupValueString != groupValueString) {
        sno = 1;
        groupValueString = e.groupValueString;
      }
      return e.copyWith(serialNumber: sno++);
    }).toList();

    emit(state.copyWith(
      allocationTerminals:
          groupedTerminal.isEmpty ? pendingTerminal : groupedTerminal,
      pendingAllocationTerminals:
          groupedTerminal.isEmpty ? <AllocationTerminal>[] : pendingTerminal,
    ));
    return allocationTerminal.any((e) => e.groupValue == null);
  }

  @override
  void addGroupCount(int groupCount) {
    if (state.allocationTerminals == null) return;

    final allocationTerminals = [...state.allocationTerminals!];
    final groupsCount = _divideEvenly(allocationTerminals.length, groupCount);

    var index = 0;
    for (var i = 0; i < groupsCount.length; i++) {
      final groupCount = groupsCount[i];
      for (var j = 0; j < groupCount; j++) {
        allocationTerminals[index] = allocationTerminals[index].copyWith(
          groupValue: i + 1,
        );
        index++;
      }
    }
    var sno = 1;
    String? groupValueString = null;
    for (int i = 0; i < allocationTerminals.length; i++) {
      final e = allocationTerminals[i];
      if (e.groupValueString != groupValueString) {
        sno = 1;
        groupValueString = e.groupValueString;
      }
      allocationTerminals[i] = e.copyWith(serialNumber: sno++);
    }
    final groupInfo = <GroupInfo>[];
    for (var i = 0; i < groupsCount.length; i++) {
      groupInfo.add(GroupInfo(
        id: i + 1,
        customerKeys: allocationTerminals
            .where((e) => e.groupValue == i + 1)
            .map((e) => e.id)
            .toList(),
      ));
    }
    sno = 1;
    groupValueString = null;
    final updatedPendingTerminal = state.pendingAllocationTerminals
        ?.map((e) => e.copyWith(
            groupValue: e.groupValue == null
                ? null
                : e.groupValue! > groupCount
                    ? null
                    : e.groupValue))
        .toList()
        .map((e) {
      if (e.groupValueString != groupValueString) {
        sno = 1;
        groupValueString = e.groupValueString;
      }
      return e.copyWith(serialNumber: sno++);
    }).toList();
    emit(state.copyWith(
      allocationTerminals: allocationTerminals,
      groupInfo: groupInfo,
      pendingAllocationTerminals: updatedPendingTerminal,
    ));
  }

  List<int> _divideEvenly(int n, int g) {
    var quotient = n ~/ g;
    var remainder = n % g;
    var result = List<int>.filled(g, quotient);
    for (var i = 0; i < remainder; i++) {
      result[i]++;
    }
    return result;
  }

  @override
  void createUpdateGroup() async {
    final groupInfo = [...state.groupInfo ?? <GroupInfo>[]];
    if (groupInfo.isEmpty) {
      return;
    }
    bool isUpdate = false;
    state.pendingAllocationTerminals?.forEach((e) {
      isUpdate = true;
      final index = groupInfo.indexWhere((g) => g.id == e.groupValue);
      if (index == -1) {
        groupInfo.add(GroupInfo(
          id: e.groupValue!,
          customerKeys: [e.id],
        ));
      } else {
        groupInfo[index] = groupInfo[index].copyWith(
          customerKeys: [...groupInfo[index].customerKeys, e.id],
        );
      }
    });

    emit(state.copyWith(
      stage: AllocationJourneyStage.loading,
      loadingMessage: 'Creating groups...',
      previousStage: null,
    ));

    final result = await CreateGroupRequest(
      allocationId: state.allocationId,
      groupInfo: groupInfo,
      isUpdate: isUpdate,
    ).execute();
    final newState = result.fold(
      (left) => state.copyWith(
        stage: AllocationJourneyStage.grouping,
        previousStage: null,
        loadingMessage: null,
        message: left.message,
      ),
      (right) => state.copyWith(
        loadingMessage: null,
        message: right,
        groupInfo: groupInfo,
      ),
    );
    emit(newState);
    if (result.isRight) {
      await getInventoryAndAllocationData();
    }
  }

  @override
  Future<void> reloadAfterAllocationUpdate(
      {AllocationJourneyStage? stage, bool skipInventory = false}) async {
    await getInventoryAndAllocationData(
      skipInventory: skipInventory,
      stage: stage,
    );
  }

  Future<bool> getInventoryAndAllocationData({
    AllocationJourneyStage? stage,
    bool skipInventory = false,
  }) async {
    if (!skipInventory) {
      final inventoryResult = await getInventory(
        functionToRetry: 'getInventoryAndAllocationData',
        showLoadingScreen: true,
      );
      if (inventoryResult == null) return false;
    }
    return await getAllocationData(
      functionToRetry: 'getInventoryAndAllocationData',
      retryParams: {
        'skipInventory': true,
      },
      showLoadingScreen: true,
      stage: stage,
    );
  }

  @override
  void processAllocationData(
    List<AllocationSkuItem> items, {
    AllocationJourneyStage? stage,
  }) async {
    bool isAutoAllocationEnabled = false; // TODO : Remove this
    // state.allocationConfig?.allocation.autoAllocation.enabled ?? false;
    if (items.isEmpty && !isAutoAllocationEnabled) {
      emit(
        state.copyWith(
          stage: AllocationJourneyStage.error,
          errorData: AllocationErrorData(
            heading: 'autoAllocationDisabled'.tr('Auto Allocation Disabled!'),
            message: 'autoAllocationDisabledMessage'.tr(
                'Auto allocation is disabled for this allocation. Please contact your admin to enable it.'),
            functionToRetry: 'initialize',
          ),
        ),
      );
      return;
    }
    // final shouldAutoAllocate = !state.hasAutoAllocationDone &&
    //     !items.any((e) => e.id != -1) &&
    //     isAutoAllocationEnabled;
    final shouldAutoAllocate = false;
    final result = await compute<Map<String, dynamic>, Map<String, dynamic>>(
      processAllocationBackground,
      {
        'items': items,
        'skuMap': state.skuMap,
        'mandiInventory': state.mandiInventoryMap,
        'supplyOrders': state.supplyOrders,
        // 'shouldAutoAllocate': shouldAutoAllocate,
        // 'allocationConfig': state.allocationConfig,
      },
    );
    if (result.containsKey('error')) {
      emit(
        state.copyWith(
          stage: AllocationJourneyStage.error,
          errorData: AllocationErrorData(
            heading: 'Oops!',
            message: result['error'] as String? ??
                'Error while processing allocation data!',
            functionToRetry: 'getInventoryAndAllocationData',
            params: {
              'skipInventory': true,
            },
          ),
        ),
      );
      return;
    }

    final itemsInBoth = result['itemsInBoth'] as List<AllocationSkuItem>;
    final itemsPartial = result['itemsPartial'] as List<AllocationSkuItem>;
    final itemsInSupplyOrder =
        result['itemsInSupplyOrder'] as List<AllocationSkuItem>;
    final isEditingAllowed = state.isEditingAllowed;
    final bool isEveningOrder = state.isEveningOrder && state.isEveningOrder;
    final itemsInMandi = (result['itemsInMandi'] as List<AllocationSkuItem>)
        // .where((e) =>
        //     e.isValidId &&
        //     (e.distributionQuantity != 0 ||
        //         e.backendAllocationQty.totalQty.toDouble() > 0))
        //     .map((e) {
        //   return !isEditingAllowed || (isEveningOrder && e.isReady)
        //       ? e
        //       : e.copyWith(
        //           backendAllocationQty: e.backendAllocationQty.updateErrorMessage(
        //               errorMessage:
        //                   'Please distribute the quantity or make allocation zero!'),
        //         );
        // })
        .toList();

    final hasAnyItemAllocated = items.any((e) => e.id != -1);

    // if (shouldAutoAllocate) {
    //   emit(state.copyWith(hasAutoAllocationDone: true));
    //   await updateAutoAllocation(
    //     items: [...itemsInBoth, ...itemsPartial],
    //     mandiItems: itemsInMandi,
    //     supplyItems: itemsInSupplyOrder,
    //   );
    //   return;
    // }

    final isExpectationEnabled =
        state.allocationConfig?.allocation.updateAllocation.allowExpectation ??
            false;
    final groupAllocationFilters = makeAllocationFilters([
      itemsInBoth,
      itemsPartial,
      itemsInMandi,
      itemsInSupplyOrder,
    ]);

    emit(
      state.copyWith(
        items: itemsInBoth,
        itemsPartial: itemsPartial,
        itemsInMandi: itemsInMandi,
        itemsInSupplyOrder: itemsInSupplyOrder,
        stage: state.allowOnlyDispatch
            ? AllocationJourneyStage.view
            : stage ??
                (hasAnyItemAllocated ||
                        !state.isEditingAllowed ||
                        !isExpectationEnabled
                    ? AllocationJourneyStage.view
                    : AllocationJourneyStage.allocation),
        previousStage: stage != null && state.allowOnlyDispatch
            ? AllocationJourneyStage.view
            : null,
        loadingMessage: null,
        errorData: null,
        hasAutoAllocationDone: true,
        groupAllocationFilters: groupAllocationFilters,
      ),
    );
  }

  List<GroupAllocationFilter> makeAllocationFilters(
      List<List<AllocationSkuItem>> allocationSkuItems) {
    final groupKeys = allocationSkuItems.expand((e) => e).map((e) {
      if (e.id == -1) {
        return '';
      }
      return e.groupKey;
    }).toSet();

    final filters = groupKeys
        .where((e) => e.trim().isNotEmpty)
        .map((e) => GroupAllocationFilter(groupKey: e))
        .toList();

    /// Alphabetically sort the filters
    filters.sort((a, b) => a.groupKey.compareTo(b.groupKey));
    return filters;
  }

  @override
  void autoAllocateRemainingSku() async {
    emit(state.copyWith(
      loadingMessage: 'Creating auto allocation...',
      stage: AllocationJourneyStage.loading,
    ));
    // final strategies =
    //     state.allocationConfig?.allocation.autoAllocation.strategies ?? [];
    // final result = autoAllocate(
    //   strategies: strategies,
    //   items: [...state.items, ...state.itemsPartial],
    //   itemsInSupplyOrder: state.itemsInSupplyOrder,
    //   skuMap: state.skuMap,
    // );

    // final itemsInBoth = result['itemsInBoth'] as List<AllocationSkuItem>;
    // final itemsPartial = result['itemsPartial'] as List<AllocationSkuItem>;
    // await updateAutoAllocation(
    //   items: [...itemsInBoth, ...itemsPartial],
    //   mandiItems: state.itemsInMandi,
    //   supplyItems: state.itemsInSupplyOrder,
    // );
    final result =
        await AutoAllocateRemainingSkuRequest(allocationId: state.allocationId)
            .execute();
    if (result.isLeft) {
      emit(state.copyWith(
        loadingMessage: null,
        hasAutoAllocationDone: false,
        stage: AllocationJourneyStage.view,
        message: result.left.message,
      ));
      return;
    }
    emit(state.copyWith(loadingMessage: 'Auto allocation created!'));
    await initialize();
  }

  Future<bool> updateAutoAllocation({
    required List<AllocationSkuItem> items,
    required List<AllocationSkuItem> mandiItems,
    required List<AllocationSkuItem> supplyItems,
  }) async {
    emit(state.copyWith(loadingMessage: 'Creating auto allocation...'));
    final updateResult = await UpdateAllocationRequest(
      allocationId: state.allocationId,
      items: items,
      mandiItems: mandiItems,
      supplyItems: supplyItems,
    ).execute();
    if (updateResult.isLeft) {
      emit(state.copyWith(
        loadingMessage: null,
        hasAutoAllocationDone: false,
        errorData: AllocationErrorData(
          heading: 'Oops!',
          message: 'Error while creating auto allocation!',
          functionToRetry: 'getInventoryAndAllocationData',
          params: {
            'skipInventory': true,
          },
        ),
      ));
      return Future.value(false);
    } else {
      emit(state.copyWith(loadingMessage: 'Auto allocation created!'));
    }
    return getInventoryAndAllocationData();
  }

  @override
  Future<void> retry() async {
    if (state.errorData?.functionToRetry == 'loadSupplyOrders') {
      await loadSupplyOrders();
    } else if (state.errorData?.functionToRetry == 'initialize') {
      emit(state.copyWith(
        stage: AllocationJourneyStage.loading,
        previousStage: null,
        errorData: null,
      ));
      await initialize();
    } else if (state.errorData?.functionToRetry ==
        'getInventoryAndAllocationData') {
      final skipInventory = state.errorData?.params?['skipInventory'] == true;
      emit(state.copyWith(
        stage: AllocationJourneyStage.loading,
        previousStage: null,
        errorData: null,
      ));
      await getInventoryAndAllocationData(skipInventory: skipInventory);
    } else {
      await super.retry();
    }
  }
}
