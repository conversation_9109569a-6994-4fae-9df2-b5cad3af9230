import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/customer_group_widget.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/manual_distribution_widget.dart';

class DistributionScreen extends StatelessWidget {
  const DistributionScreen({super.key, required this.state});
  final AllocationJourneyState state;

  @override
  Widget build(BuildContext context) {
    double maxAllowedForTerminals = 0;
    double maxAllowedForTerminalsAndWastage = 0;
    final maxExcessPercent =
        state.allocationConfig?.distribution.maxExcessPercent ?? 0;
    bool isTerminalAllocationExceeded = true;
    bool isTotalAllocationExceeded = true;
    final sku = state.terminalDistribution?.skuQuantity;
    final isAnyDistributionEmpty =
        state.terminalDistribution?.isAnyNotDistributed ?? true;

    if (state.terminalDistribution != null) {
      maxAllowedForTerminals = state.terminalDistribution!
          .maxAllowedForAllocatedMandiQty(maxExcessPercent);
      maxAllowedForTerminalsAndWastage = state.terminalDistribution!
          .maxAllowedForTotalMandiQty(maxExcessPercent);

      isTerminalAllocationExceeded =
          state.terminalDistribution!.totalAllocatingNow >
              maxAllowedForTerminals;
      isTotalAllocationExceeded =
          state.terminalDistribution!.wastageAndAllocation >
              maxAllowedForTerminalsAndWastage;
    }
    final isEditOnlyFromWeighingMachine =
        state.allocationConfig?.distribution.isEditOnlyFromWeighingMachine ??
            false;
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          centerTitle: false,
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                state.terminalDistribution?.skuQuantityTitle ?? '',
                style: TextStyle(fontSize: 15),
              ),
              Text(
                'distribution'.tr(
                  'Distribution',
                ),
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
        body: Column(
          children: [
            if (state.customerGroupData != null) ...[
              CustomerGroupWidget(
                data: state.customerGroupData!,
                activeTerminalCount: state.activeTerminalCount,
                supplyOrderCount: state.supplyOrderCount,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              if (sku != null)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.yellow.shade100,
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 16,
                  ),
                  child: Row(
                    children: [
                      Text(
                        state.terminalDistribution?.skuName ?? '',
                        style: TextStyle(
                            fontSize: 15, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        width: 8,
                      ),
                      Text(
                        sku.unit + ' - ' + sku.type,
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              Divider(
                color: Colors.grey.shade600,
                height: 16,
              ),
            ],
            Expanded(
              child: ListView(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                children: [
                  state.terminalDistribution == null
                      ? Container(
                          height: 200,
                          child: SizedBox(
                            height: 24,
                            width: 24,
                            child: CircularProgressIndicator(),
                          ),
                        )
                      : ManualDistributionWidget(
                          distribution: state.terminalDistribution!,
                          padding: EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 16,
                          ),
                          isReadOnly: !state.isEditingAllowed,
                          isManualEdititngAllowed:
                              !isEditOnlyFromWeighingMachine || kIsWeb,
                        ),
                ],
              ),
            ),
            Visibility(
              visible: isTerminalAllocationExceeded && state.isEditingAllowed,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 8,
                ),
                child: Text(
                  'Terminal distribution can not be greater than ${maxAllowedForTerminals.asString()}',
                  style: TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            Visibility(
              visible: isTotalAllocationExceeded && state.isEditingAllowed,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 8,
                ),
                child: Text(
                  'Terminal distribution + wastage, can not be greater than ${maxAllowedForTerminalsAndWastage.asString()}',
                  style: TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            Visibility(
              visible:
                  state.terminalDistribution != null && state.isEditingAllowed,
              child: Card(
                margin: EdgeInsets.zero,
                color: Colors.grey[100],
                child: Padding(
                  padding: const EdgeInsets.only(
                    left: 16,
                    right: 16,
                    top: 16,
                    bottom: 16,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: state.terminalDistribution != null &&
                                  !isTerminalAllocationExceeded &&
                                  !isTotalAllocationExceeded
                              ? () {
                                  context
                                      .read<AllocationJourneyCubit>()
                                      .submitDistribution(isDraft: true);
                                }
                              : null,
                          icon: Icon(Icons.save),
                          label: Text(
                            'draft'.tr('Draft'),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            minimumSize: Size(
                              MediaQuery.of(context).size.width - 32,
                              48,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: state.terminalDistribution != null &&
                                  !isTerminalAllocationExceeded &&
                                  !isTotalAllocationExceeded &&
                                  !isAnyDistributionEmpty
                              ? () {
                                  if (state.terminalDistribution?.skuQuantity
                                          .isBulk ==
                                      true) {
                                    final validate = state.terminalDistribution!
                                        .isValidMinMax();

                                    if (validate != null) {
                                      context
                                          .read<AllocationJourneyCubit>()
                                          .updateDistribution(validate);

                                      showDialog(
                                        context: context,
                                        builder: (_) => Popup(
                                          height: 0.4,
                                          title:
                                              'distribution.validationErrorPopup'
                                                  .tr('Validation Error!'),
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8.0),
                                              child: Text(
                                                'distribution.validationErrorPopupMessage'
                                                    .tr(
                                                  'There are some validation errors in the distribution. Please correct them before submitting.',
                                                ),
                                                style: TextStyle(
                                                  color: Colors.red,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                      return;
                                    }
                                  }
                                  context
                                      .read<AllocationJourneyCubit>()
                                      .submitDistribution(isDraft: false);
                                }
                              : null,
                          icon: Icon(Icons.check),
                          label: Text(
                            'submit'.tr('Submit'),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            minimumSize: Size(
                              MediaQuery.of(context).size.width - 32,
                              48,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
