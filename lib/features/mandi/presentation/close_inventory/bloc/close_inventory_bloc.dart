// ignore_for_file: prefer_final_locals, omit_local_variable_types

import 'package:bloc/bloc.dart';
import 'package:either_dart/either.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/use_case/close_inventory_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_inventory_usecase.dart';
import 'package:proc2/features/mandi/domain/use_case/get_sku_usecase.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';
import 'package:uuid/uuid.dart';

part 'close_inventory_bloc.freezed.dart';
part 'close_inventory_event.dart';
part 'close_inventory_state.dart';

@injectable
class CloseInventoryBloc
    extends Bloc<CloseInventoryEvent, CloseInventoryState> {
  CloseInventoryBloc(this._getInventoryUseCase, this._closeInventoryUseCase,
      this._getSkuUseCase)
      : super(CloseInventoryState.empty) {
    on<CloseInventoryEvent>((event, emit) async {
      await event.when(
        add: (SkuInputWithLoss inputData) async {
          final previousList =
              state.inputSku[inputData.skuInputData.sku.id] ?? [];
          final isSortNeeded = previousList.isEmpty;

          final newList = List<SkuInputWithLoss>.from(previousList)
            ..add(inputData);
          var map = Map.fromEntries(state.inputSku.entries);
          map[inputData.skuInputData.sku.id] = newList;
          if (isSortNeeded) {
            map = getSortedGroupedInputSku(map);
          }
          final newState =
              updateCtaStatus(stateWithUpdatedCounters(map, state));

          emit(newState);
        },
        delete: (SkuInputWithLoss value) async {
          final previousList = state.inputSku[value.skuInputData.sku.id] ?? [];

          final newList = List<SkuInputWithLoss>.from(previousList)
            ..removeWhere((element) =>
                element.skuInputData.uuid == value.skuInputData.uuid);
          final map = Map.fromEntries(state.inputSku.entries);
          if (newList.isEmpty) {
            map.remove(value.skuInputData.sku.id);
          } else {
            map[value.skuInputData.sku.id] = newList;
          }
          final newState = updateCtaStatus(
            stateWithUpdatedCounters(map, state),
          );
          emit(newState);
        },
        update: (SkuInputWithLoss value) async {
          final previousList = state.inputSku[value.skuInputData.sku.id] ?? [];
          final newList = previousList
              .map(
                (e) =>
                    e.skuInputData.uuid == value.skuInputData.uuid ? value : e,
              )
              .toList();
          final map = Map.fromEntries(state.inputSku.entries);
          map[value.skuInputData.sku.id] = newList;
          emit(
            updateCtaStatus(
              state.copyWith(
                inputSku: map,
              ),
            ),
          );
        },
        clearMessage: () async => emit(state.copyWith(message: '')),
        submitCloseInventory: (
          int smoId,
          int mandiId,
        ) async {
          final currentState = state;
          if (currentState is _Initial) {
            emit(currentState.copyWith(isSubmitLoading: true));
            final list = currentState.inputSku.values.expand((e) => e).toList();
            await _closeInventoryUseCase(
                    mandiId: mandiId, smoId: smoId, data: list)
                .then(
              (val) => val.fold(
                (left) => emit(
                  currentState.copyWith(
                    message: left.message,
                    isSubmitLoading: false,
                  ),
                ),
                (right) => emit(
                  currentState.copyWith(
                    isSubmittedSuccess: true,
                  ),
                ),
              ),
            );
          }
        },
        getInventory: (int mandiId, int smoId) async {
          emit(
            state.copyWith(
              isGetInventoryLoading: true,
            ),
          ); // Show loading state

          final futures = <Future>[];
          futures.add(
            _getInventoryUseCase(mandiId),
          );
          futures.add(_getSkuUseCase());

          final result = await Future.wait(futures);

          final inventoryResult =
              result[0] as Either<ErrorResult<dynamic>, Inventory>;
          final skuResult = result[1] as Either<ErrorResult<void>, List<Sku>>;

          if (skuResult.isLeft) {
            emit(state.copyWith(
              message: skuResult.left.message,
              isGetInventoryLoading: false,
            ));
            return;
          }

          if (inventoryResult.isLeft) {
            emit(state.copyWith(
              message: inventoryResult.left.message,
              isGetInventoryLoading: false,
            ));
            return;
          }

          final inventoryData = inventoryResult.right;
          final skuMap = <int, Sku>{};

          for (final sku in skuResult.right) {
            skuMap[sku.id] = sku;
          }

          var inputSkuMap = <int, List<SkuInputWithLoss>>{};

          for (final inventory in inventoryData.skus) {
            if (inventory.quantity <= 0) {
              continue;
            }
            final sku = skuMap[inventory.skuId];
            if (sku == null) {
              continue;
            }

            final inputSku = SkuInputWithLoss(
              skuInputData: SkuInputData(
                sku: sku,
                procurementType: inventory.isBulk()
                    ? ProcurementType.bulk
                    : ProcurementType.lots,
                procurementUnit: inventory.unit,
                lotSize: inventory.isBulk() ? null : inventory.lotSize,
                quantity: sku.shouldAutofillInClosingInventory
                    ? inventory.quantity.asString()
                    : null,
                uuid: const Uuid().v4(),
                weighingSource:
                    sku.shouldAutofillInClosingInventory ? 'auto' : null,
                amount: null,
                weight: null,
              ),
              losses: [],
            );

            inputSkuMap[sku.id] = List<SkuInputWithLoss>.from(
              inputSkuMap[sku.id] ?? [],
            )..add(inputSku);
          }

          inputSkuMap = getSortedGroupedInputSku(inputSkuMap);

          final newState = updateCtaStatus(
            stateWithUpdatedCounters(inputSkuMap, state),
          );
          emit(
            newState.copyWith(
              isGetInventoryLoading: false,
            ),
          );
        },
      );
    });
  }

  CloseInventoryState updateCtaStatus(CloseInventoryState state) {
    final newState = state.copyWith(
      isSubmitCtaActive: state.inputSku.values.expand((e) => e).every(
            (e) => e.skuInputData.quantity != null,
          ),
    );
    return newState;
  }

  Map<int, List<SkuInputWithLoss>> getSortedGroupedInputSku(
    Map<int, List<SkuInputWithLoss>> inputSku,
  ) {
    final sortedInputSku = <int, List<SkuInputWithLoss>>{};
    final sortedItems = inputSku.values.toList();
    sortedItems.sort((a, b) {
      final aTopLevelCategory =
          a.firstOrNull?.skuInputData.sku.topLevelCategory;
      final bTopLevelCategory =
          b.firstOrNull?.skuInputData.sku.topLevelCategory;
      if (aTopLevelCategory == null && bTopLevelCategory == null) {
        return 0;
      }
      if (aTopLevelCategory == bTopLevelCategory) {
        return 0;
      }
      if (aTopLevelCategory == null) {
        return 1;
      }
      if (bTopLevelCategory == null) {
        return -1;
      }
      return aTopLevelCategory.compareTo(bTopLevelCategory);
    });
    for (final item in sortedItems) {
      if (item.isNotEmpty) {
        sortedInputSku[item.first.skuInputData.sku.id] = item;
      }
    }
    return sortedInputSku;
  }

  CloseInventoryState stateWithUpdatedCounters(
    Map<int, List<SkuInputWithLoss>> inputSku,
    CloseInventoryState state,
  ) {
    final counters = <String, int>{};
    counters['all'] = inputSku.values.expand((e) => e).length;
    for (final list in inputSku.values) {
      for (final skuInputWithLoss in list) {
        final topLevelCategory =
            skuInputWithLoss.skuInputData.sku.topLevelCategory?.toLowerCase();
        if (topLevelCategory == null) {
          continue;
        }
        counters[topLevelCategory] = (counters[topLevelCategory] ?? 0) + 1;
      }
    }

    return state.copyWith(
      inputSku: inputSku,
      filterCounts: counters,
    );
  }

  final GetInventoryUseCase _getInventoryUseCase;
  final CloseInventoryUseCase _closeInventoryUseCase;
  final GetSkuUseCase _getSkuUseCase;
}
