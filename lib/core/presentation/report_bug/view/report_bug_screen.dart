import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:path_provider/path_provider.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/report_bug/cubit/report_bug_cubit.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/auth/domain/entity/user.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:talker_flutter/talker_flutter.dart';

class ReportBugScreen extends StatelessWidget {
  const ReportBugScreen({
    super.key,
    required this.talker,
  });
  final Talker talker;

  Future<File> _getTalkerHistoryFile() async {
    final dir = await getApplicationDocumentsDirectory();
    final historyText = talker.history.text;
    final tempFile = File(dir.path +
        'talker_history_${DateTime.now().millisecondsSinceEpoch}.txt');
    await tempFile.writeAsString(historyText);
    return tempFile;
  }

  User? _getAuthUser(BuildContext context) {
    final authState = context.read<AuthBloc>().state;
    if (authState is Authenticated) {
      return authState.user;
    }
    return null;
  }

  Future<void> _submitReport(BuildContext context) async {
    final talkerLogFile = await _getTalkerHistoryFile();
    talker.cleanHistory();
    final user = _getAuthUser(context);
    if (user != null) {
      final reportBugCubit = context.read<ReportBugCubit>();
      reportBugCubit.submit(
        talkerLogFile: talkerLogFile,
        user: user,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text('reportBug.title'.tr('Report a bug')),
          centerTitle: false,
        ),
        body: BlocConsumer<ReportBugCubit, ReportBugState>(
          listener: (context, state) {
            final shouldPop = state.shouldPop;
            if (state.message != null) {
              showSnackBar(state.message!);
              context.read<ReportBugCubit>().clearMessage();
            }
            if (shouldPop) {
              context.pop();
            }
          },
          builder: (context, state) {
            return Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 16),
                    child: Text(
                      'reportBug.description'
                          .tr('Please describe the bug you found'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    child: TextField(
                      maxLines: 5,
                      textAlign: TextAlign.start,
                      textAlignVertical: TextAlignVertical.bottom,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(),
                        labelText: 'reportBug.description'.tr('Description'),
                        floatingLabelAlignment: FloatingLabelAlignment.start,
                      ),
                      onChanged: (value) =>
                          context.read<ReportBugCubit>().updateComment(value),
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            'reportBug.attachments'.tr('Attachments'),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        ElevatedButton.icon(
                          icon: Icon(Icons.add),
                          style: ElevatedButton.styleFrom(
                            maximumSize: Size(
                              200,
                              48,
                            ),
                          ),
                          onPressed: state.files.length >= 5
                              ? null
                              : () async {
                                  FilePickerResult? result =
                                      await FilePicker.platform.pickFiles(
                                    type: FileType.image,
                                    allowMultiple: true,
                                  );
                                  if (result != null) {
                                    context
                                        .read<ReportBugCubit>()
                                        .addFiles(result.files);
                                  }
                                },
                          label: Text(
                              'reportBug.addAttachment'.tr('Add attachment')),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: state.files.length,
                      itemBuilder: (context, index) {
                        final file = state.files[index];
                        return ListTile(
                          title: Text(file.name),
                          trailing: IconButton(
                            icon: Icon(
                              Icons.delete,
                              color: Colors.red,
                            ),
                            onPressed: () =>
                                context.read<ReportBugCubit>().removeFile(file),
                          ),
                        );
                      },
                    ),
                  ),
                  WStickyBottomCta(
                    isEnabled: state.files.isNotEmpty && !state.isCtaLoading,
                    isLoading: state.isCtaLoading,
                    icon: Icons.check,
                    label: Text('submit'.tr('Submit')),
                    onPressed: () async {
                      await _submitReport(context);
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
