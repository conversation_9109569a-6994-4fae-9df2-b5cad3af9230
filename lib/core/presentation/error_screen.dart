import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({
    super.key,
    this.heading = 'Oops!',
    this.message = 'Something went wrong',
    required this.onPressed,
    this.ctaLabel,
  });

  final String heading;
  final String message;
  final VoidCallback? onPressed;
  final String? ctaLabel;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            heading,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Text(
            message,
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
          const SizedBox(
            height: 24,
          ),
          ElevatedButton(
            onPressed: onPressed,
            child: Text(this.ctaLabel ?? 'retry'.tr('Retry')),
          ),
        ],
      ),
    );
  }
}
