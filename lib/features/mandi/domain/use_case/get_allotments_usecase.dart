import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/allotments/allotment.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetAllotmentsUseCase {
  GetAllotmentsUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, List<Allotment>>> call(int smoId) async {
    return _mandiRepository.getAllotments(smoId);
  }
}
