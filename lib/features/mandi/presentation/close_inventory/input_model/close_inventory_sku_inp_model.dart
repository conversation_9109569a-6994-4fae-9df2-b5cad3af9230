// ignore_for_file: omit_local_variable_types

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
part 'close_inventory_sku_inp_model.freezed.dart';

@freezed
class CloseInventorySkuInput with _$CloseInventorySkuInput {
  const CloseInventorySkuInput._();
  const factory CloseInventorySkuInput({
    required InventoryItem inventoryItem,
    @Default('') String quantity,
    @Default([]) List<LossInputModel> losses,
  }) = _CloseInventorySkuInput;

  double totalLoss() {
    return losses.fold(0.0, (previousValue, element) {
      return previousValue + element.lossValue.toDouble();
    });
  }
}
