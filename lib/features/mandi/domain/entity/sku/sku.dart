import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/presentation/widgets/search_suggestion.dart';
import 'package:proc2/core/utils/extensions.dart';

part 'sku.freezed.dart';

@freezed
class Sku with _$Sku implements SearchKey {
  const Sku._();
  const factory Sku({
    required int id,
    required String name,
    required Map<String, dynamic> lotSizes,
    required int parentSkuId,
    @Default('') String image,
    @Default([]) List<String> bulkUnitTypes,
    @Default([]) List<String> alternateNames,
    @Default(true) bool canProcure,
    @Default(null) String? grade,
    @Default(null) String? shelfLifeType,
    @Default(null) String? groupKey,
    @Default(null) String? topLevelCategory,
  }) = _Sku;

  bool get isHard => shelfLifeType?.toLowerCase() == 'hard';
  bool get isSoft => shelfLifeType?.toLowerCase() == 'soft';
  bool get shouldAutofillInClosingInventory =>
      topLevelCategory != null &&
      topLevelCategory!.trim().isNotEmpty &&
      topLevelCategory!.toLowerCase() != 'fnv';

  factory Sku.fromJson(Map<dynamic, dynamic> json) {
    final lotSizes = json['lotSizes'] as Map<dynamic, dynamic>;

    return Sku(
      id: json['id'] as int,
      parentSkuId: json['parentSkuId'] as int,
      name: json['name'] as String,
      lotSizes: lotSizes.map(
        (key, value) => MapEntry(key as String, value),
      ),
      image: json['image'] as String? ?? '',
      bulkUnitTypes: (json['bulkUnitTypes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      alternateNames: (json['alternateNames'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      canProcure: json['canProcure'] as bool? ?? true,
      grade: json['grade'] as String?,
      shelfLifeType: json['shelfLifeType'] as String?,
      groupKey: json['groupKey'] as String?,
      topLevelCategory: json['topLevelCategory'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'lotSizes': lotSizes,
      'parentSkuId': parentSkuId,
      'image': image,
      'bulkUnitTypes': bulkUnitTypes,
      'alternateNames': alternateNames,
      'canProcure': canProcure,
      'grade': grade,
      'shelfLifeType': shelfLifeType,
      'groupKey': groupKey,
      'topLevelCategory': topLevelCategory,
    };
  }

  @override
  String searchKey() {
    return id.toString();
  }

  @override
  String searchTitle() {
    return name.capitalize();
  }

  bool contains(String key) {
    final lower = key.toLowerCase();
    return name.toLowerCase().contains(lower) ||
        alternateNames.any((element) => element.toLowerCase().contains(lower));
  }
}
