import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetLossInitUseCase {
  GetLossInitUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, Map<String, List<LossTypeV2>>>>
      call() async {
    return _mandiRepository.getLossInitV2();
  }
}
