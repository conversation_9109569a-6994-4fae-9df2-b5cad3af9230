part of 'allocation_journey_cubit.dart';

@freezed
class AllocationJourneyState with _$AllocationJourneyState {
  const AllocationJourneyState._();
  const factory AllocationJourneyState.data({
    @Default(null) String? loadingMessage,
    @Default(null) AllocationErrorData? errorData,
    @Default(null) String? message,
    @Default(AllocationJourneyStage.loading) AllocationJourneyStage stage,
    @Default(null) AllocationJourneyStage? previousStage,
    @Default(null) CustomerGroupData? customerGroupData,
    @Default(-1) int mandiId,
    @Default(-1) int smoId,
    @Default(-1) int allocationId,
    @Default({}) Map<int, Sku> skuMap,
    @Default(0) int activeTerminalCount,
    @Default(null) List<SupplyOrder>? supplyOrders,
    @Default(null) List<AllocationTerminal>? allocationTerminals,
    @Default(null) List<AllocationTerminal>? pendingAllocationTerminals,
    @Default(null) List<GroupInfo>? groupInfo,
    @Default(null) Map<String, SkuQuantity>? mandiInventoryMap,
    @Default([]) List<AllocationSkuItem> items,
    @Default([]) List<AllocationSkuItem> itemsPartial,
    @Default([]) List<AllocationSkuItem> itemsInMandi,
    @Default([]) List<AllocationSkuItem> itemsInSupplyOrder,
    @Default(null) TerminalDistribution? terminalDistribution,
    @Default(FilterData()) FilterData viewFilterData,
    @Default(FilterData()) FilterData allocationFilterData,
    @Default([]) List<String> pdfs,
    @Default(false) bool shouldPop,
    @Default(true) bool isInProgress,
    @Default(null) AllocationConfiguration? allocationConfig,
    @Default(false) bool hasAutoAllocationDone,
    @Default(null) String? status,
    @Default(false) bool isActionsBlocked,
    @Default(false) bool allowOnlyDispatch,
    @Default([]) List<GroupAllocationFilter> groupAllocationFilters,
  }) = _Data;

  bool get isCustomerGroup => customerGroupData != null;
  int get supplyOrderCount => supplyOrders?.length ?? 0;
  int? get groupInfoCount {
    if (groupInfo == null || groupInfo!.length == 0) return null;
    return groupInfo!.length;
  }

  bool get isEveningOrder =>
      customerGroupData?.deliverySlot.toLowerCase() == 'evening';

  bool get isDispatched => status == 'DISPATCHED';
  bool get isEditingAllowed => isInProgress;
  bool get canManageOrders =>
      allocationConfig?.allocation.canManageOrders == true &&
      isDispatched &&
      isCustomerGroup;
}
