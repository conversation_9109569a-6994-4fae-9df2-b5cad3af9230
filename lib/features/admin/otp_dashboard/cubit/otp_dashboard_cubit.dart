import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_otp_dashboard_request.dart';

part 'otp_dashboard_state.dart';
part 'otp_dashboard_cubit.freezed.dart';

@injectable
class OtpDashboardCubit extends Cubit<OtpDashboardState> {
  OtpDashboardCubit() : super(OtpDashboardState.initial());

  void setMobile(String value) {
    emit(state.copyWith(mobile: value));
  }

  void clear() {
    emit(state.copyWith(
      message: null,
      otp: null,
      mobile: null,
    ));
  }

  void getOTP() async {
    final mobile = state.mobile;

    emit(state.copyWith(otp: null));

    if (state.isLoading || mobile == null || mobile.trim().length != 10) {
      emit(state.copyWith(message: 'Please Enter mobile'));
      return;
    }

    emit(state.copyWith(isLoading: true, message: null));

    final result = await GetOtpDashboardRequest(
      mobile: "+91$mobile",
    ).execute();

    final newState = result.fold(
        (left) => state.copyWith(isLoading: false, message: left.message),
        (right) => state.copyWith(
              isLoading: false,
              otp: right,
            ));
    emit(newState);
  }
}
