import 'dart:io';

import 'package:file_picker/src/platform_file.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/report_bug/api/bug_reporter_interface.dart';
import 'package:proc2/core/presentation/report_bug/model/bug_report.dart';
import 'package:proc2/core/presentation/report_bug/model/bug_report_log.dart';
import 'package:firebase_database/firebase_database.dart';

@Singleton(as: BugReporter)
class FirebaseRealtimeDbBugReporter implements BugReporter {
  final FirebaseDatabase rtDb;
  final FirebaseStorage storage;

  FirebaseRealtimeDbBugReporter(this.rtDb, this.storage);

  DatabaseReference get _databaseReference => rtDb.ref('bug_reports');
  DatabaseReference get _activeBugReportsReference =>
      _databaseReference.child('active');
  DatabaseReference get _resolvedBugReportsReference =>
      _databaseReference.child('resolved');

  Reference get _storageReference => storage.ref('bug_reports/');

  @override
  Future<List<BugReport>> getBugReports({
    bool isResolved = false,
  }) async {
    final ref =
        isResolved ? _resolvedBugReportsReference : _activeBugReportsReference;
    final snapshot = await ref.get();
    if (snapshot.exists && snapshot.value is Map<dynamic, dynamic>) {
      final bugReportsMap = snapshot.value as Map<dynamic, dynamic>;
      return bugReportsMap.entries
          .map((e) => BugReport.fromJson(e.key, e.value))
          .toList();
    } else {
      return <BugReport>[];
    }
  }

  @override
  Stream<String> reportBug(BugReportLog bugReportLog) async* {
    final ref = _activeBugReportsReference.push();
    final attachments = <String>[];
    for (int i = 0; i < bugReportLog.attachments.length; i++) {
      final file = bugReportLog.attachments[i];
      yield 'uploadingBugReportAttachmentsMessage'
          .tr('Uploading file ##count## of ##total##...', params: {
        'count': (i + 1).toString(),
        'total': bugReportLog.attachments.length.toString(),
      });
      final url = await uploadPlatformFile(file);
      if (url != null) {
        attachments.add(url);
      }
    }
    yield 'uploadingLogFile'.tr('Uploading log file....');
    final logFileUrl = await uploadFile(bugReportLog.logFile);

    yield 'reportingBugMessage'.tr('Reporting bug....');
    final json = {
      ...bugReportLog.toJson(),
      'attachments': attachments,
      'logFile': logFileUrl,
      'createdAt': DateTime.now().toString(),
    };

    await ref.set(json);
    yield 'bugReported'.tr('Bug Reported!');
  }

  @override
  Future<bool> markResolved(BugReport bugReport) async {
    try {
      // Add to resolved
      final resolvedRef = _resolvedBugReportsReference.child(bugReport.id);
      final json = {...bugReport.toJson(), 'hasResolved': true};
      await resolvedRef.set(json);

      // Remove the reference
      final ref = _activeBugReportsReference.child(bugReport.id);
      await ref.remove();
      return true;
    } catch (e, s) {
      talker.handle(e, s);
      return false;
    }
  }

  @override
  Future<String?> uploadFile(File file) async {
    try {
      final fileRef = _storageReference.child(file.path.split('/').last);
      final snapshot = await fileRef.putFile(file);
      return await snapshot.ref.getDownloadURL();
    } catch (e, s) {
      talker.handle(e, s);
      return null;
    }
  }

  @override
  Future<String?> uploadPlatformFile(PlatformFile file) async {
    try {
      return uploadFile(
          file.path != null ? File(file.path!) : File.fromRawPath(file.bytes!));
    } catch (e, s) {
      talker.handle(e, s);
      return null;
    }
  }
}
