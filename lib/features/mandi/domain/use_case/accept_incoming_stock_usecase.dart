import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/input_models/inventory_recieving_input_model.dart';

@injectable
class AcceptIncomingStockUseCase {
  AcceptIncomingStockUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, void>> call(
    int smoId,
    InventoryRecievingInputModel inputModel,
  ) async {
    return _mandiRepository.acceptIncomingStocks(smoId, inputModel);
  }
}
