import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_mandi_status_card.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/incoming_stock/consignment.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/bloc/inventory_recieving_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/view/recieving_inv.dart';
import 'package:proc2/features/mandi/util/get_mandi_name.dart';

class ReceivingHome extends StatefulWidget {
  const ReceivingHome({super.key, required this.mandiId, required this.smoId});
  final int mandiId;
  final int smoId;
  @override
  State<ReceivingHome> createState() => _ReceivingHome();
}

class _ReceivingHome extends State<ReceivingHome> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<InventoryRecievingBloc, InventoryRecievingState>(
      listener: (context, state) {
        state.maybeWhen(
          recievedInventory: () {
            context
                .read<InventoryRecievingBloc>()
                .add(InventoryRecievingEvent.started(widget.mandiId));
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            appBar: appBar(),
            body: Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(left: 8, right: 8, top: 8),
              child:
                  BlocBuilder<InventoryRecievingBloc, InventoryRecievingState>(
                builder: (context, inventoryRecievingState) {
                  return inventoryRecievingState.maybeMap(
                    initial: (_) => loadingView(),
                    data: (dataState) {
                      if (dataState.consignmentList.isEmpty) {
                        return Center(
                          child: Text(
                            LanguageEnum.recieveInventoryEmptyMessage
                                .localized(),
                            style: const TextStyle(color: Colors.red),
                          ),
                        );
                      }
                      return consignmentsList(dataState.consignmentList);
                    },
                    error: (e) => ErrorScreen(
                      onPressed: () {
                        context.read<InventoryRecievingBloc>().add(
                            InventoryRecievingEvent.started(widget.mandiId));
                      },
                      message: e.error.message,
                    ),
                    orElse: () {
                      return Container();
                    },
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget failedView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [Text(LanguageEnum.recieveInventoryErrorMessage.localized())],
    );
  }

  Widget loadingView() {
    return const Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [CircularProgressIndicator()],
    );
  }

  Widget consignmentsList(List<Consignment> consignmentList) {
    return Scrollbar(
      child: ListView(
        children: [
          for (Consignment consignment in consignmentList)
            mandiItem(consignment)
        ],
      ),
    );
  }

  Widget mandiItem(Consignment consignment) {
    final isEditOnlyFromWeighingMachine =
        context.read<SmoBloc>().state.mapOrNull(success: (success) {
              return success.config.allocation.isEditOnlyFromWeighingMachine;
            }) ??
            false;
    final mandiName =
        getMandiName(context, mandiId: consignment.receivedFromMandi);
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          context
              .read<InventoryRecievingBloc>()
              .add(InventoryRecievingEvent.onConsignmentSelect(consignment));
          Navigator.push(
            context,
            // ignore: inference_failure_on_instance_creation
            MaterialPageRoute(
              builder: (_) => BlocProvider.value(
                value: context.read<InventoryRecievingBloc>(),
                child: ReceivingInv(
                  consignment: consignment,
                  mandiId: widget.mandiId,
                  smoId: widget.smoId,
                  isEditOnlyFromWeighingMachine: isEditOnlyFromWeighingMachine,
                ),
              ),
            ),
          );
        },
        child: Card(
          elevation: 4,
          color: Color(0xFFf9fbf7),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 12,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    LangText(
                      'receiveInventory.from',
                      'From',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(
                      width: 6,
                    ),
                    Text(
                      mandiName,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  consignment.consignmentId,
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),
                for (final trip in consignment.tripDetails) ...[
                  SizedBox(
                    height: 4,
                  ),
                  Divider(
                    height: 1,
                    color: Colors.grey[300],
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  if (trip.vehicleNo != null)
                    infoRowText(Icons.time_to_leave, '', trip.vehicleNo!),
                  if (trip.expectedDeliveryTime != null)
                    infoRowText(
                        Icons.timer_rounded,
                        'ETA',
                        trip.expectedDeliveryTime!
                            .toDate('DD MMM, yyyy | hh:mm a'))
                ],
              ],
            ),
          ),
        ),
      ),
    );

    // return InkWell(
    //   onTap: () {
    //     context
    //         .read<InventoryRecievingBloc>()
    //         .add(InventoryRecievingEvent.onConsignmentSelect(consignment));
    //     Navigator.push(
    //       context,
    //       // ignore: inference_failure_on_instance_creation
    //       MaterialPageRoute(
    //         builder: (_) => BlocProvider.value(
    //           value: context.read<InventoryRecievingBloc>(),
    //           child: ReceivingInv(
    //             consignment: consignment,
    //             mandiId: widget.mandiId,
    //             smoId: widget.smoId,
    //           ),
    //         ),
    //       ),
    //     );
    //   },
    //   child: Card(
    //     margin: const EdgeInsets.all(8),
    //     color: Config.primaryColor,
    //     elevation: 6,
    //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    //     child: Container(
    //       width: MediaQuery.of(context).size.width,
    //       padding: const EdgeInsets.all(16),
    //       child: Row(
    //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //         children: [
    //           Column(
    //             crossAxisAlignment: CrossAxisAlignment.start,
    //             children: [
    //               Text(
    //                 LanguageEnum.recieveInventoryFromLabel.localized(
    //                   params: {
    //                     'mandiName': mandiName,
    //                   },
    //                 ),
    //                 style: const TextStyle(
    //                   fontSize: 16,
    //                   fontWeight: FontWeight.w600,
    //                   color: Colors.white,
    //                 ),
    //               ),
    //               Text(
    //                 consignment.consignmentId,
    //                 style: const TextStyle(
    //                   fontSize: 14,
    //                   fontWeight: FontWeight.w400,
    //                   color: Colors.white70,
    //                 ),
    //               )
    //             ],
    //           ),
    //           SizedBox(
    //             child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
    //               Text(
    //                 LanguageEnum.recieveInventoryAcceptLabel.localized(),
    //                 style: const TextStyle(
    //                     color: Colors.white, fontWeight: FontWeight.w600),
    //               ),
    //               const Icon(
    //                 Icons.chevron_right,
    //                 color: Colors.white,
    //               )
    //             ]),
    //           ),
    //         ],
    //       ),
    //     ),
    //   ),
    // );
  }

  AppBar appBar() {
    return WAppBar.getAppBar(
      leading: InkWell(
          onTap: () {
            context.pop();
          },
          child: const Icon(Icons.arrow_back)),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // const SizedBox(height: 4),
          Text(
            LanguageEnum.recieveInventoryTitle.localized(),
            style: const TextStyle(fontSize: 18),
          ),
          // Text(
          //   DateFormat(getMandiName(context, mandiId: widget.mandiId))
          //       .format(DateTime.now()),
          //   style: const TextStyle(fontSize: 14),
          // ),
          // const SizedBox(height: 4),
        ],
      ),
      centerTitle: false,
    );
  }
}
