import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/place_order_popup/cubit/place_order_cubit.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku_types/bloc/sku_types_bloc.dart';

class PlaceOrderPopup extends StatefulWidget {
  const PlaceOrderPopup({
    super.key,
    this.onDelete,
    required this.receiveProcurementConfig,
  });
  final GestureTapCallback? onDelete;
  final ReceiveProcurementConfig? receiveProcurementConfig;

  // final SkuInputData skuInputData;
  @override
  State<PlaceOrderPopup> createState() => _SKUEdit();
}

class _SKUEdit extends State<PlaceOrderPopup> {
  // Initial Selected Value

  final TextEditingController _orderedQtyController = TextEditingController();
  final TextEditingController _receivedQtyController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _skuSearchController = TextEditingController();
  String _receiveQuantityError = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final state = context.read<PlaceOrderCubit>().state;
      if (state.orderedQuantity != null) {
        _orderedQtyController.text = state.orderedQuantity ?? '';
      }
      if (state.receivedQuantity != null) {
        _receivedQtyController.text = state.receivedQuantity ?? '';
      }
      if (state.amount != null) {
        _amountController.text = state.amount ?? '';
      }
      if (state.weight != null) {
        _weightController.text = state.weight ?? '';
      }
      _updateReceiveError(state.orderedQuantity, state.previousReceivedQuantity,
          state.receivedQuantity);
    });
    _receivedQtyController.addListener(() {
      setState(() {
        _updateReceiveError(
          _orderedQtyController.text,
          context.read<PlaceOrderCubit>().state.previousReceivedQuantity,
          _receivedQtyController.text,
        );
      });
    });
    _orderedQtyController.addListener(() {
      setState(() {
        _updateReceiveError(
          _orderedQtyController.text,
          context.read<PlaceOrderCubit>().state.previousReceivedQuantity,
          _receivedQtyController.text,
        );
      });
    });
    _skuSearchController.addListener(_skuSearchListener);
  }

  void _updateReceiveError(
    String? orderedQuantity,
    String? receivedTillNow,
    String? receivedQuantity,
  ) {
    _receiveQuantityError = widget.receiveProcurementConfig?.deviationMessage(
            totalQuantity: orderedQuantity?.toDouble() ?? 0.0,
            receiveQuantity: (receivedTillNow?.toDouble() ?? 0.0) +
                (receivedQuantity?.toDouble() ?? 0.0)) ??
        '';
  }

  void _skuSearchListener() {
    try {
      final skuName = _skuSearchController.text;
      if (skuName == context.read<PlaceOrderCubit>().state.sku?.name) return;
      if (skuName.isNotEmpty) {
        final sku = context.read<SkuBloc>().state.mapOrNull(
              success: (s) => s.skus.firstWhere(
                (element) => element.name == skuName,
              ),
            );
        if (sku != null) {
          context.read<PlaceOrderCubit>().updateSku(sku);
        }
      }
    } catch (e) {}
  }

  @override
  void dispose() {
    super.dispose();
    _skuSearchController.removeListener(_skuSearchListener);
    _orderedQtyController.removeListener(() {});
    _receivedQtyController.removeListener(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SkuTypesBloc, SkuTypesState>(
      builder: (context, skuTypesState) {
        return skuTypesState.map(
          initial: (_) => const Center(
            child: CircularProgressIndicator(),
          ),
          loading: (_) => const Center(
            child: CircularProgressIndicator(),
          ),
          failure: (failure) {
            return ErrorScreen(
              onPressed: () {
                context.read<SkuTypesBloc>().add(SkuTypesEvent.fetch());
              },
              message: failure.message,
            );
          },
          success: (success) {
            return BlocConsumer<PlaceOrderCubit, PlaceOrderState>(
              listener: (context, state) {
                if (state.sku?.name != null) {
                  _skuSearchController.text = state.sku!.name;
                }
                if (state.orderedQuantity != _orderedQtyController.text) {
                  _orderedQtyController.text = state.orderedQuantity ?? '';
                }
                if (state.receivedQuantity != _receivedQtyController.text) {
                  _receivedQtyController.text = state.receivedQuantity ?? '';
                }
                if (state.amount != _amountController.text) {
                  _amountController.text = state.amount ?? '';
                }
                if (state.weight != _weightController.text) {
                  _weightController.text = state.weight ?? '';
                }
              },
              builder: (context, state) {
                return BlocBuilder<SkuBloc, SkuState>(
                  builder: (context, skuState) {
                    return skuState.map(
                      initial: (_) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      success: (skuBlocState) {
                        final skus = skuBlocState.skus
                            .where((e) => e.canProcure)
                            .map((e) => e.name)
                            .toList();
                        return SafeArea(
                          child: Scaffold(
                            backgroundColor: Colors.transparent,
                            body: InkWell(
                              onTap: () {},
                              child: Center(
                                child: Container(
                                  width: MediaQuery.of(context).size.width,
                                  height:
                                      MediaQuery.of(context).size.height * 0.6,
                                  padding: const EdgeInsets.only(
                                    top: 0,
                                    left: 20,
                                    right: 20,
                                  ),
                                  child: Card(
                                    margin: const EdgeInsets.only(top: 0),
                                    child: SizedBox(
                                      width: MediaQuery.of(context).size.width,
                                      child: skus.isEmpty
                                          ? Center(
                                              child: Text(
                                                'There are no skus to select!',
                                                style: TextStyle(
                                                  color: Colors.red,
                                                ),
                                              ),
                                            )
                                          : Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                header(
                                                  isEditing:
                                                      state.isOpenForEdit,
                                                  onTap: () {
                                                    if (widget.onDelete !=
                                                        null) {
                                                      context.pop();
                                                      widget.onDelete!();
                                                    }
                                                  },
                                                  skuName:
                                                      state.sku?.name ?? '',
                                                  newlyAdded: state.newlyAdded,
                                                ),
                                                const SizedBox(height: 12),
                                                if (!state.isOpenForEdit)
                                                  CustomDropdown.search(
                                                    hintText: LanguageEnum
                                                        .addProcSearchBoxHint
                                                        .localized(),
                                                    items: skus,
                                                    controller:
                                                        _skuSearchController,
                                                    listItemBuilder:
                                                        (context, item) => Text(
                                                      item,
                                                      maxLines: 3,
                                                    ),
                                                  ),
                                                Expanded(
                                                  child: Scrollbar(
                                                    child: ListView(
                                                      shrinkWrap: true,
                                                      // crossAxisAlignment: CrossAxisAlignment.start,
                                                      // mainAxisSize: MainAxisSize.min,

                                                      children: [
                                                        if (state.newlyAdded)
                                                          const SizedBox(
                                                              height: 12),
                                                        if (state.sku !=
                                                            null) ...[
                                                          Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .only(
                                                              bottom: 16.0,
                                                            ),
                                                            child: Row(
                                                              children: [
                                                                Expanded(
                                                                  child:
                                                                      procurementTypeField(
                                                                    state
                                                                        .procurementType,
                                                                    state
                                                                        .newlyAdded,
                                                                  ),
                                                                ),
                                                                const SizedBox(
                                                                    height: 12),
                                                                Expanded(
                                                                  child:
                                                                      unitField(
                                                                    unit: state
                                                                        .procurementUnit,
                                                                    isBulk: state
                                                                        .isBulk,
                                                                    bulkUnits: state
                                                                        .sku!
                                                                        .bulkUnitTypes,
                                                                    lotSizesUnits:
                                                                        state
                                                                            .lotsUnit,
                                                                    allowEditing:
                                                                        state
                                                                            .newlyAdded,
                                                                  ),
                                                                ),
                                                                if (!state
                                                                        .isBulk &&
                                                                    state.procurementUnit !=
                                                                        null) ...[
                                                                  Expanded(
                                                                    child:
                                                                        lotSizeField(
                                                                      lotSize: state
                                                                          .lotSize,
                                                                      lotSizes:
                                                                          state
                                                                              .lotSizes,
                                                                      allowEditing:
                                                                          state
                                                                              .newlyAdded,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ],
                                                            ),
                                                          ),
                                                          if (state.isBulk) ...[
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                bottom: 12.0,
                                                              ),
                                                              child: state
                                                                      .newlyAdded
                                                                  ? receiveAndFinalSubmit(
                                                                      state
                                                                          .isBulk,
                                                                      state
                                                                          .isFinal,
                                                                      state
                                                                          .newlyAdded,
                                                                    )
                                                                  : newReceive(
                                                                      orderedQuantity:
                                                                          state
                                                                              .orderedQuantity,
                                                                      previousReceive:
                                                                          state
                                                                              .previousReceivedQuantity,
                                                                      totalReceive:
                                                                          state
                                                                              .totalReceive(),
                                                                      isFinal: state
                                                                          .isFinal,
                                                                      enableEditing:
                                                                          !state
                                                                              .disableReceiveQty,
                                                                      state:
                                                                          state,
                                                                    ),
                                                            ),
                                                            if (!state
                                                                .disableAmountEdit)
                                                              amountField(),
                                                          ],
                                                          if (!state
                                                              .isBulk) ...[
                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                bottom: 16.0,
                                                              ),
                                                              child: state
                                                                      .newlyAdded
                                                                  ? receiveAndFinalSubmit(
                                                                      state
                                                                          .isBulk,
                                                                      state
                                                                          .isFinal,
                                                                      state
                                                                          .newlyAdded,
                                                                    )
                                                                  : newReceive(
                                                                      orderedQuantity:
                                                                          state
                                                                              .orderedQuantity,
                                                                      previousReceive:
                                                                          state
                                                                              .previousReceivedQuantity,
                                                                      totalReceive:
                                                                          state
                                                                              .totalReceive(),
                                                                      isFinal: state
                                                                          .isFinal,
                                                                      enableEditing:
                                                                          !state
                                                                              .disableReceiveQty,
                                                                      state:
                                                                          state,
                                                                    ),
                                                            ),
                                                            // state.newlyAdded
                                                            //     ? receiveAndFinalSubmit(
                                                            //         state.isBulk,
                                                            //         state.isFinal,
                                                            //         state.newlyAdded)

                                                            Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .only(
                                                                bottom: 16.0,
                                                              ),
                                                              child: Row(
                                                                children: [
                                                                  if (!state
                                                                          .isBulk &&
                                                                      state.procurementUnit !=
                                                                          null) ...[
                                                                    Expanded(
                                                                      child:
                                                                          weightField(),
                                                                    ),
                                                                    const SizedBox(
                                                                        height:
                                                                            12),
                                                                  ],
                                                                  if (!state
                                                                      .disableAmountEdit)
                                                                    Expanded(
                                                                      child:
                                                                          amountField(),
                                                                    ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                          const SizedBox(
                                                              height: 20),
                                                        ],
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                if (state.isDuplicate)
                                                  Align(
                                                    alignment: Alignment.center,
                                                    child: Text(
                                                      'Sku is already added!',
                                                      style: TextStyle(
                                                        color: Colors.red,
                                                        fontSize: 15,
                                                      ),
                                                    ),
                                                  ),
                                                if (_receiveQuantityError
                                                    .isNotEmpty)
                                                  Align(
                                                    alignment: Alignment.center,
                                                    child: Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 8,
                                                      ),
                                                      child: Text(
                                                        _receiveQuantityError,
                                                        style: TextStyle(
                                                          color: Colors.red,
                                                          fontSize: 15,
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                    ),
                                                  ),
                                                Container(
                                                  decoration:
                                                      BoxDecoration(boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.grey
                                                          .withOpacity(0.1),
                                                      spreadRadius: 1,
                                                      blurRadius: 1,
                                                      offset: Offset(0, 1),
                                                    ),
                                                  ]),
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 12.0),
                                                    child: Align(
                                                      child: ElevatedButton(
                                                        style: ButtonStyle(
                                                          minimumSize:
                                                              MaterialStateProperty
                                                                  .all(
                                                            Size(
                                                              MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width -
                                                                  80,
                                                              50,
                                                            ),
                                                          ),
                                                        ),
                                                        onPressed: !state
                                                                    .isCtaActive ||
                                                                _receiveQuantityError
                                                                    .isNotEmpty
                                                            ? null
                                                            : () {
                                                                final data = context
                                                                    .read<
                                                                        PlaceOrderCubit>()
                                                                    .getSkuInputData();
                                                                context
                                                                    .pop(data);
                                                              },
                                                        child: Text(
                                                          LanguageEnum
                                                              .addProcSkuSelectCtaTitle
                                                              .localized(),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                      error: (state) => ErrorScreen(
                        onPressed: () {
                          context.read<SkuBloc>().add(const SkuEvent.fetch());
                        },
                        message: state.errorr.message,
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
    //   },
    // ),
    // );
  }

  Widget newReceive({
    required String? orderedQuantity,
    required String? previousReceive,
    required String totalReceive,
    required bool isFinal,
    required bool enableEditing,
    required PlaceOrderState state,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 8,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              LangText(
                'placeOrder.orderedQuantity',
                'Ordered Quantity',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                orderedQuantity?.toString() ?? '-',
                style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          Divider(),
          SizedBox(
            height: 8,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              LangText(
                'placeOrder.previousReceive',
                'Already Received',
                style: TextStyle(
                  fontSize: 15,
                ),
              ),
              Text(
                previousReceive?.toString() ?? '-',
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 4,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              LangText(
                'placeOrder.newReceived',
                'Newly Received',
                style: TextStyle(
                  fontSize: 15,
                ),
              ),
              SizedBox(
                width: 100,
                child: WeighingQuantityButton(
                  isReadOnly: !enableEditing,
                  label: _receivedQtyController.text,
                  popupBuilder: () {
                    return WeightCapturePopupProcurementReceive(
                      isBulkKg: state.isBulkKg,
                      isManualEditAllowed: !(widget.receiveProcurementConfig
                              ?.isEditOnlyFromWeighingMachine ??
                          false),
                      initialWeight: _receivedQtyController.text
                          .toDouble()
                          .asString()
                          .toDouble(),
                      skuName: state.sku?.name ?? '',
                      unitInfo: state.unitInfo,
                    );
                  },
                  onChange: (quantity) {
                    if (quantity != null) {
                      setState(() {
                        _receivedQtyController.text = quantity.value ?? '';
                      });
                      context
                          .read<PlaceOrderCubit>()
                          .updateReceivedQty(quantity.value, quantity.source);
                    }
                  },
                ),
              ),
            ],
          ),
          SizedBox(
            height: 4,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              LangText(
                'placeOrder.totalReceive',
                'Total Received',
                style: TextStyle(
                  fontSize: 15,
                ),
              ),
              Text(
                totalReceive,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 4,
          ),
          CheckboxListTile(
            enabled: enableEditing,
            contentPadding: EdgeInsets.zero,
            value: isFinal,
            onChanged: (value) {
              context.read<PlaceOrderCubit>().updateFinal(value ?? false);
            },
            title: LangText(
              'placeOrder.finalSubmit',
              'Received All?',
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(
            height: 8,
          ),
          if (!enableEditing)
            Text(
              'You can\'t edit received quantity or mark as final',
              style: TextStyle(color: Colors.red),
            ),
          Divider(),
          SizedBox(
            height: 8,
          ),
        ],
      ),
    );
  }

  Widget receiveAndFinalSubmit(bool isBulk, bool isFinal, bool isNewlyAdded) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 12.0,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              orderedQuantityField(isBulk, isNewlyAdded),
              CheckboxListTile(
                visualDensity: VisualDensity.compact,
                contentPadding: EdgeInsets.only(left: 16),
                value: isFinal,
                onChanged: (value) {
                  context.read<PlaceOrderCubit>().updateFinal(value ?? false);
                },
                title: LangText(
                  'placeOrder.finalSubmit',
                  'Received All?',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          )),
          Expanded(
            child: receivedQuantityField(isBulk, !isFinal),
          ),
        ],
      ),
    );
  }

  Widget amountField() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectAmountLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _amountController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
              counter: SizedBox(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.done,
            inputFormatters: Config.numberInputFilters,
            onChanged: (value) {
              context.read<PlaceOrderCubit>().updateAmount(value);
            },
          )
        ],
      ),
    );
  }

  Widget receivedQuantityField(
    bool isBulk,
    bool canEdit,
  ) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              isBulk
                  ? getLangText('addProc.receivedQuantity', 'Received Quantity')
                  : getLangText('addProc.receivedLots', 'Received Lots'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              )),
          const SizedBox(height: 8),
          TextFormField(
            enabled: canEdit,
            controller: _receivedQtyController,
            decoration: InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
              counter: SizedBox(),
              error: _receiveQuantityError.isEmpty
                  ? null
                  : Text(
                      '',
                      style: TextStyle(
                        color: Colors.red,
                      ),
                    ),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.numberInputFilters,
            onChanged: (val) {
              context.read<PlaceOrderCubit>().updateReceivedQty(val, 'manual');
            },
          )
        ],
      ),
    );
  }

  Widget orderedQuantityField(bool isBulk, bool allowEditing) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              isBulk
                  ? getLangText('addProc.orderedQuantity', 'Ordered Quantity')
                  : getLangText('addProc.orderedLots', 'Ordered Lots'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              )),
          const SizedBox(height: 8),
          TextFormField(
            enabled: allowEditing,
            controller: _orderedQtyController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
              counter: SizedBox(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.numberInputFilters,
            onChanged: (val) {
              context.read<PlaceOrderCubit>().updateOrderedQty(val);
            },
          )
        ],
      ),
    );
  }

  Widget weightField() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectWeightLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _weightController,
            decoration: const InputDecoration(
              isDense: false,
              contentPadding: EdgeInsets.only(left: 10, right: 10),
              border: OutlineInputBorder(),
              counter: SizedBox(),
            ),
            keyboardType: TextInputType.number,
            maxLength: 10,
            textInputAction: TextInputAction.next,
            inputFormatters: Config.numberInputFilters,
            onChanged: (value) {
              context.read<PlaceOrderCubit>().updateWeight(value);
            },
          )
        ],
      ),
    );
  }

  Widget unitField({
    required String? unit,
    required bool isBulk,
    required List<String> bulkUnits,
    required List<String> lotSizesUnits,
    required bool allowEditing,
  }) {
    // final skuEditBloc = context.read<SkuEditBloc>();
    // final state = skuEditBloc.state;
    final items = isBulk ? bulkUnits : lotSizesUnits;
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectUnitLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: unit,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((String item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (String? newValue) {
                          context.read<PlaceOrderCubit>().updateUnit(newValue);
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    unit?.capitalize().localized() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget lotSizeField({
    required double? lotSize,
    required dynamic lotSizes,
    required bool allowEditing,
  }) {
    // final selectedLotSize =
    //     state.sku?.lotSizes[state.procurementUnit?.toUpperCase() ?? ''] ??
    //         <String>[];
    final items = <double>[];
    if (lotSizes is List) {
      for (final element in lotSizes) {
        items.add(double.parse(element.toString()));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectLotSizeLabel.localized(),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400),
          ),
          allowEditing
              ? DropdownButton(
                  // Initial Value
                  value: lotSize,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: items.map((double item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.toString()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (double? newValue) {
                          context
                              .read<PlaceOrderCubit>()
                              .updateLotSize(newValue);
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    lotSize.toString() ?? '-',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget procurementTypeField(
      ProcurementType procurementType, bool allowEditing) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectProcTypeLabel.localized(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          allowEditing
              ? DropdownButton<ProcurementType>(
                  // Initial Value
                  value: procurementType,
                  isExpanded: true,

                  // Down Arrow Icon
                  icon: const Icon(Icons.keyboard_arrow_down),

                  // Array list of items
                  items: ProcurementType.values.map((ProcurementType item) {
                    return DropdownMenuItem(
                      value: item,
                      child: Text(item.value.capitalize().localized()),
                    );
                  }).toList(),
                  // After selecting the desired option,it will
                  // change button value to selected value
                  onChanged: !allowEditing
                      ? null
                      : (ProcurementType? newValue) {
                          context
                              .read<PlaceOrderCubit>()
                              .updateType(newValue ?? ProcurementType.bulk);
                        },
                )
              : Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    procurementType.value.capitalize().localized(),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget header({
    required bool isEditing,
    required bool newlyAdded,
    GestureTapCallback? onTap,
    required String skuName,
  }) {
    // final name = context.watch<SkuEditBloc>().state.sku?.name ?? '';
    return Container(
      decoration: BoxDecoration(
        color: Config.primaryColor,
        boxShadow: const <BoxShadow>[
          BoxShadow(
            color: Colors.black54,
            blurRadius: 10,
            offset: Offset(0, 0.75),
          )
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Expanded(
            child: Row(
              children: [
                Text(
                  isEditing
                      ? skuName
                      : LanguageEnum.addProcSkuSelectHeadingAdd.localized(),
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              ],
            ),
          ),
          if (widget.onDelete != null && newlyAdded)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: InkWell(
                onTap: onTap,
                child: const Icon(Icons.delete, color: Colors.white),
              ),
            ),
          InkWell(
            onTap: () => context.pop(),
            child: const Icon(
              Icons.cancel,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
