import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/carry_forward/carry_forward_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/carry_forward/carry_forward_item_dto.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_item.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_loss.dart';

CarryForward toCarryForward(CarryForwardDto dto) {
  return CarryForward(
    id: dto.carryForwardId,
    bulkItems: dto.carryForwardItems
        .where((element) => element.skuQuantity.type.toLowerCase() == 'bulk')
        .map(toCarryForwardItem)
        .toList(),
    lotItems: dto.carryForwardItems
        .where((element) => element.skuQuantity.type.toLowerCase() != 'bulk')
        .map(toCarryForwardItem)
        .toList(),
    invenotryClosedBy: dto.inventoryPreviouslyClosedBy?.name,
  );
}

CarryForwardItem toCarryForwardItem(CarryForwardItemDto dto) {
  return CarryForwardItem(
    id: dto.id,
    skuId: dto.skuQuantity.skuId,
    type: dto.skuQuantity.type,
    unit: dto.skuQuantity.unit,
    lotSize: dto.skuQuantity.lotSize,
    quantity: dto.skuQuantity.quantity,
  );
}

Map<String, dynamic> carryForwardToJsonBody(
    CarryForward carryForward, int smoId) {
  final bulkItemWithLoss = carryForwardItemWithLossJson(carryForward.bulkItems);
  final lotItemWithLoss = carryForwardItemWithLossJson(carryForward.lotItems);
  final itemsWithLoss = [...bulkItemWithLoss, ...lotItemWithLoss].toList();
  return {
    'carryForwardId': carryForward.id,
    'smoId': smoId,
    'comments': carryForward.comments,
    'itemsWithLoss': itemsWithLoss,
  };
}

Iterable<Map<String, dynamic>> carryForwardItemWithLossJson(
  List<CarryForwardItem> items,
) {
  return items
      .where(
    (element) => element.getLoss() > 0.0 || element.getShortOrExcess() > 0.0,
  )
      .map((e) {
    return {
      'itemId': e.id,
      'quantity': e.quantity,
      'losses': lossWithShortageAndExcess(e.loss, e.shortOrExcess),
    };
  });
}

List<Map<String, dynamic>> lossWithShortageAndExcess(
    List<CarryForwardLoss> losses, List<CarryForwardLoss> shortageOrExcess) {
  final cfLoss = <CarryForwardLoss>[...losses, ...shortageOrExcess];

  return cfLoss.where((element) => element.lossValue.toDouble() > 0.0).map(
    (e) {
      var multiplier = 1.0;
      if (e.lossType == 'EXCESS') {
        multiplier = -1.0;
      }
      return {
        'lossType': e.lossType,
        'comment': e.comment,
        'quantity': e.lossValue.toDouble() * multiplier,
        'images': e.files.map((e) => e.uploadKey ?? '').toList(),
      };
    },
  ).toList();
}
