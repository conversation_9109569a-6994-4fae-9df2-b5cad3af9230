import 'package:geolocator/geolocator.dart';
import 'package:proc2/core/app_config.dart';

Future<bool> _isLocationServiceEnabled() async {
  var serviceEnabled = await Geolocator.isLocationServiceEnabled();
  return serviceEnabled;
}

Future<bool> _isLocationPermissionGranted() async {
  var permissionGranted = await Geolocator.checkPermission();

  if (permissionGranted == LocationPermission.denied) {
    permissionGranted = await Geolocator.requestPermission();
  }
  return permissionGranted == LocationPermission.always ||
      permissionGranted == LocationPermission.whileInUse;
}

Future<bool> isAllLocationPermissionGranted() async {
  return await _isLocationServiceEnabled() &&
      await _isLocationPermissionGranted();
}

bool _isLocationRepositoryInitialized = false;

Future<void> initializeLocationRepository() async {
  if (!await isAllLocationPermissionGranted()) {
    return;
  }
  try {
    // await LocationRepository().intialize(enableBackgroundMode: true);
    _isLocationRepositoryInitialized = true;
  } catch (e, s) {
    print('Error initializing location repository');
    print('Error: $e');
    print('Stack: $s');
  }
}

bool _isLocationChangeListenerStarted = false;
Future<void> startLocationChangeListener() async {
  if (!_isLocationRepositoryInitialized) {
    return;
  }
  try {
    // final LocationRepository locationRepository = LocationRepository();
    // await Future.delayed(const Duration(seconds: 2));
    // await locationRepository.start();
    _isLocationChangeListenerStarted = true;
  } catch (e, _) {}
}

Future<Position?> getLocation(Duration validDuration) async {
  if (!_isLocationRepositoryInitialized) {
    await initializeLocationRepository();
  }
  if (!_isLocationRepositoryInitialized) return null;

  if (!_isLocationChangeListenerStarted) {
    await startLocationChangeListener();
  }

  try {
    // final LocationRepository locationRepository = LocationRepository();
    return await Geolocator.getCurrentPosition(
        timeLimit: Duration(seconds: 10));
  } catch (e, s) {
    talker.error(
      'Error getting location',
      e,
      s,
    );
    return null;
  }
}
