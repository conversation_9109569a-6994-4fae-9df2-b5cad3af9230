import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/customer_group_widget.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/terminal_group_widget.dart';

class GroupingScreen extends StatelessWidget {
  const GroupingScreen({super.key, required this.state});
  final AllocationJourneyState state;

  @override
  Widget build(BuildContext context) {
    final bool hasAnyPending = state.pendingAllocationTerminals?.any((e) {
          return e.groupValue == null;
        }) ??
        false;
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: WAppBar.getAppBar(
          title: Text(
            'groupAllocations'.tr('Group Allocations'),
          ),
          centerTitle: false,
        ),
        body: Column(
          children: [
            Expanded(
              child: ListView(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                children: [
                  if (state.customerGroupData != null) ...[
                    SizedBox(
                      height: 8,
                    ),
                    CustomerGroupWidget(
                      data: state.customerGroupData!,
                      activeTerminalCount: state.activeTerminalCount,
                      supplyOrderCount: state.supplyOrderCount,
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    Divider(
                      color: Colors.grey.shade600,
                      height: 32,
                    ),
                  ],
                  state.allocationTerminals != null
                      ? TerminalGroup(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          terminals: state.allocationTerminals!,
                          pendingTerminals:
                              state.pendingAllocationTerminals ?? [],
                          groupCount: state.groupInfoCount,
                          supplyOrderCount: state.allocationTerminals!.length,
                          onGroupCountChanged: (groupCount) {
                            context
                                .read<AllocationJourneyCubit>()
                                .setGroupCount(groupCount);
                          },
                          onPendingTerminalsUpdate: (terminals) {
                            context
                                .read<AllocationJourneyCubit>()
                                .updatePendingTerminals(terminals);
                          },
                        )
                      : Container(
                          height: 200,
                          child: Center(
                            child: SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(),
                            ),
                          ),
                        )
                ],
              ),
            ),
            WStickyBottomCta(
              isEnabled: state.groupInfoCount != null && !hasAnyPending,
              icon: Icons.check,
              label: Text('allocation.startAllocation'.tr('Start Allocation')),
              onPressed: () async {
                final value = await context.showAlertDialog(
                        title: 'groupingPopupSubmitTitle'.tr('Are you sure?'),
                        message: 'groupingPopupSubmitMessage'
                            .tr('Terminals grouping will be updated!')) ??
                    false;
                if (!value) return;
                context.read<AllocationJourneyCubit>().createUpdateGroup();
              },
            ),
          ],
        ),
      ),
    );
  }
}
