import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:proc2/features/auth/presentation/bloc/otp_login_controller.dart';
import 'package:proc2/features/auth/presentation/view/login_phone.dart';
import 'package:proc2/features/auth/presentation/view/wheelocity_login_button.dart';
import 'package:provider/provider.dart';

class LoginForm extends StatelessWidget {
  const LoginForm({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        state.maybeWhen(
          orElse: () {},
          authenticated: (user) {
            context
              ..go('/')
              ..fetchGlobal();
          },
          unauthenticated: (err) {
            if (err != null) {
              showSnackBar(err.message);
            }
          },
        );
      },
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            backgroundColor: Colors.white,
            body: Container(
              padding: const EdgeInsets.all(10),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [Image(image: AssetImage(Config.logoImagePath))],
                  ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.only(left: 15),
                    child: Text(
                      LanguageEnum.appGreetMessage.localized(),
                      style: TextStyle(
                        color: Config.primaryColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Padding(
                    padding: const EdgeInsets.only(left: 15),
                    child: Text(
                      LanguageEnum.appCompanyName.localized(),
                      style: TextStyle(
                        color: Colors.grey.shade400,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.zero,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // InkWell(
                          //   onTap: () async {
                          //     await showDialog(
                          //       context: context,
                          //       builder: (context) {
                          //         return BlocProvider.value(
                          //           value: context.read<LanguageCubit>(),
                          //           child: const LanguageModal(),
                          //         );
                          //       },
                          //     );
                          //   },
                          //   child: Text(
                          //     LanguageEnum.selectLanguageHint.localized(),
                          //     style: const TextStyle(
                          //       fontSize: 16,
                          //       decoration: TextDecoration.underline,
                          //       decorationStyle: TextDecorationStyle.double,
                          //     ),
                          //   ),
                          // ),
                          const SizedBox(
                            height: 16,
                          ),
                          OutlinedButton.icon(
                            onPressed: () {
                              // Open login with phone screen
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) => MultiBlocProvider(
                                    providers: [
                                      BlocProvider.value(
                                        value: context.read<AuthBloc>(),
                                      ),
                                      ChangeNotifierProvider(
                                        create: (context) =>
                                            OTPLoginController(),
                                      ),
                                    ],
                                    child: LoginPhoneScreen(),
                                  ),
                                ),
                              );
                            },
                            icon: Icon(Icons.phone),
                            label: Text(
                              'loginViaPhone'.tr('Login with Phone'),
                            ),
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          const WheelocityLoginButton(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
