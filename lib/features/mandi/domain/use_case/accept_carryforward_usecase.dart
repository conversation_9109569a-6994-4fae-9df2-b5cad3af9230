import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class AcceptCarryForwardUseCase {
  AcceptCarryForwardUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<dynamic>, String>> call({
    required CarryForward carryForward,
    required int smoId,
  }) async {
    return _mandiRepository.acceptCarryForward(
      carryForward: carryForward,
      smoId: smoId,
    );
  }
}
