import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

class LockedSku {
  final SkuQuantity skuQuantity;
  final String? lockedBy;

  LockedSku({
    required this.skuQuantity,
    required this.lockedBy,
  });

  factory LockedSku.fromJson(Map<String, dynamic> json) {
    return LockedSku(
      skuQuantity: SkuQuantity.fromJson(json['skuQuantity']),
      lockedBy: json['lockedBy'],
    );
  }
}
