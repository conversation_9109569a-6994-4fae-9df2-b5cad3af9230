import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/mandi_inventory/mandi_inventory_sku.dart';

part 'mandi_inv_sku_inp.freezed.dart';

@freezed
class MandiInvSkuInp with _$MandiInvSkuInp {
  const factory MandiInvSkuInp({
    required MandiInventorySku sku,
    required double lotSize,
    required double quantity,
  }) = _MandiInvSkuInp;
}
