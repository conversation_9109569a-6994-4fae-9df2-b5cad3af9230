import 'package:proc2/core/data/network/base_request.dart';

class GetLiquidationPresignedUrlRequest
    extends BaseRequest<dynamic, Map<String, dynamic>> {
  final Map<String, dynamic> body;

  GetLiquidationPresignedUrlRequest({required this.body});

  @override
  Map<String, dynamic> getBody() {
    return body;
  }

  @override
  String getPath() => 'liquidation/images/upload';

  @override
  Map<String, dynamic> mapper(data) {
    if (data is Map<String, dynamic>) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
