// ignore_for_file: use_build_context_synchronously

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
//import 'package:proc2/features/mandi/presentation/detail/view/slot_timer.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';

class MandiDetail extends StatelessWidget {
  const MandiDetail({required this.mandiId, required this.smoId, super.key});

  final int? mandiId;
  final int smoId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.get<SmoBloc>()
        ..add(SmoEvent.fetchSmo(mandiId, smoId))
        ..add(SmoEvent.markSelfAttendance(smoId)),
      child: _MandiDetail(
        mandiId: mandiId,
        smoId: smoId,
      ),
    );
  }
}

class _MandiDetail extends StatefulWidget {
  const _MandiDetail({required this.mandiId, required this.smoId});

  final int? mandiId;
  final int smoId;

  @override
  State<_MandiDetail> createState() => __MandiDetail();
}

class __MandiDetail extends State<_MandiDetail> {
  @override
  Widget build(BuildContext context) {
    if (widget.mandiId == null) {
      context.pop();
    }
    return BlocBuilder<MandiBloc, MandiState>(
      builder: (context, state) {
        // This mandiInfo will never be empty because go_router won't navigate
        // to this screen if the mandiId is not valid and we don't have mandiInfo
        // for that mandiId. This has been added to router.dart
        final mandiInfo = state.maybeMap(
          success: (s) => s.allMandis.firstWhere(
            (element) => element.id == widget.mandiId,
            orElse: () => MandiInfo.empty,
          ),
          orElse: () => MandiInfo.empty,
        );

        return SafeArea(
          child: Scaffold(
            appBar: WAppBar.getAppBar(
              centerTitle: false,
              title: Text(mandiInfo.name),
            ),
            body: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              padding: const EdgeInsets.only(
                left: 5,
                right: 5,
                top: 10,
              ),
              child: BlocBuilder<SmoBloc, SmoState>(
                builder: (context, smoState) {
                  return smoState.when(
                    initial: () => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    success: (smo, smoConfig) {
                      if (smo.menuConfig.hasNoAccess())
                        return ErrorScreen(
                          heading: 'noAccess'.tr('No Access'),
                          message: 'noAccessMessage'
                              .tr('You do not have access to any feature!'),
                          onPressed: () {
                            _refreshSmo();
                          },
                          ctaLabel: 'refresh'.tr('Refresh'),
                        );

                      return RefreshIndicator(
                        onRefresh: () async {
                          _refreshSmo();
                        },
                        child: Scrollbar(
                          child: ListView(
                            children: [
                              // SlotTimer(
                              //   endTime: now,
                              // ),
                              //const SizedBox(height: 15),
                              Wrap(
                                alignment: WrapAlignment.spaceBetween,
                                runSpacing: 10,
                                children: [
                                  Visibility(
                                    visible: smo.menuConfig.proc,
                                    child: menuButton(
                                      context: context,
                                      name: getLangText(
                                        'mandiMenu.procurements',
                                        'Procurements',
                                      ),
                                      icon: Icons.my_library_books_rounded,
                                      routeName: 'myProcDetail',
                                    ),
                                  ),

                                  Visibility(
                                    visible: smo.menuConfig.addCarryForward,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.addCarryForward
                                          .localized(),
                                      icon: Icons.add_task,
                                      routeName: 'carryForward',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.attendance,
                                    child: menuButton(
                                      context: context,
                                      name: 'attendance'.tr('Attendance'),
                                      icon: Icons.class_outlined,
                                      routeName: 'attendance',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.addFieldCharges,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.addFieldCharges
                                          .localized(),
                                      icon: Icons.receipt_long,
                                      routeName: 'addCharges',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.recieveInventory,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.recieveInventory
                                          .localized(),
                                      icon: Icons.start,
                                      routeName: 'recieveInventory',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.closingStockPlan,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.closeInventory
                                          .localized(),
                                      icon: Icons.close_fullscreen,
                                      routeName: 'closeInventory',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.mandiInventory,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.mandiInventory
                                          .localized(),
                                      icon: Icons.inventory,
                                      routeName: 'mandiInventory',
                                    ),
                                  ),
                                  Visibility(
                                    visible:
                                        kIsWeb && smo.menuConfig.supplyOrder,
                                    child: menuButton(
                                      context: context,
                                      name: 'Supply Orders',
                                      icon: Icons.fire_truck,
                                      routeName: 'supplyOrder',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.skuAllocation,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.skuAllocation
                                          .localized(),
                                      icon: Icons.bar_chart_outlined,
                                      routeName: 'skuAllocation',
                                    ),
                                  ),
                                  // Visibility(
                                  //   visible: smo.menuConfig.mandiProcSummary,
                                  //   child:menuButton(
                                  //   context: context,
                                  //   name: 'Mandi Procurement\nSummary',
                                  //   icon: Icons.summarize,
                                  //   routeName: 'mandiProcurementSummary',
                                  // ),),
                                  Visibility(
                                    visible: smo.menuConfig.lotting,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.lotting.localized(),
                                      icon: Icons.account_tree,
                                      routeName: 'lotting',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.returns,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.returns.localized(),
                                      icon: Icons.repeat_rounded,
                                      routeName: 'returns',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.returns,
                                    child: menuButton(
                                      context: context,
                                      name: 'wastageReturns'
                                          .tr('Wastage Returns'),
                                      icon: Icons.repeat_rounded,
                                      routeName: 'wastageReturns',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.grading,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.grading.localized(),
                                      icon: Icons.recycling,
                                      routeName: 'grading',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.grading,
                                    child: menuButton(
                                      context: context,
                                      name: 'Combo',
                                      icon: Icons.recycling,
                                      routeName: 'combo',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.liquidation,
                                    child: menuButton(
                                      context: context,
                                      name: getLangText('mandiMenu.liquidation',
                                          'Liquidation'),
                                      icon: Icons.water_drop_outlined,
                                      routeName: 'liquidation',
                                    ),
                                  ),
                                  Visibility(
                                    visible: smo.menuConfig.closeOpsBtn,
                                    child: menuButton(
                                      context: context,
                                      name: LanguageEnum.closeOps.localized(),
                                      icon: Icons.close,
                                      routeName: 'closeOps',
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 40),
                            ],
                          ),
                        ),
                      );
                    },
                    error: (error) {
                      return ErrorScreen(
                        message: error.message,
                        onPressed: _refreshSmo,
                      );
                    },
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  void _refreshSmo() {
    final smoBloc = context.read<SmoBloc>();
    if (smoBloc.isClosed) return;
    smoBloc.add(
      SmoEvent.fetchSmo(widget.mandiId, widget.smoId),
    );
  }

  Widget menuButton({
    required BuildContext context,
    required String name,
    required IconData icon,
    required String routeName,
  }) {
    return InkWell(
      onTap: () async {
        if (routeName == 'closeOps') {
          final shouldClose = await _showCloseOpsPopup(context) ?? false;
          if (!shouldClose) return;
        }
        await context.push(
          context.namedLocation(
            routeName,
            pathParameters: {'mandiId': '${widget.mandiId}'},
          ),
          extra: context,
        );
        _refreshSmo();
      },
      child: Card(
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Container(
          height: 120,
          width: (MediaQuery.of(context).size.width / 2) - 15,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, color: Config.primaryColor, size: 26),
              Text(
                name,
                textAlign: TextAlign.start,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<bool?> _showCloseOpsPopup(BuildContext context) {
  return showDialog<bool>(
    context: context,
    builder: (_) => AlertDialog(
      title: Text(LanguageEnum.closeOps.localized()),
      actions: [
        TextButton(
          onPressed: () {
            context.pop(false);
          },
          child: Text(LanguageEnum.falseButton.localized()),
        ),
        TextButton(
          onPressed: () {
            context.pop(true);
          },
          child: Text(LanguageEnum.trueButton.localized()),
        ),
      ],
    ),
  );
}
