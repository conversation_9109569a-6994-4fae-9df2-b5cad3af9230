import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/inventory/inventory_item.dart';
import 'package:proc2/features/mandi/domain/use_case/get_inventory_usecase.dart';

part 'select_sku_event.dart';
part 'select_sku_state.dart';
part 'select_sku_bloc.freezed.dart';

@injectable
class SelectSkuBloc extends Bloc<SelectSkuEvent, SelectSkuState> {
  SelectSkuBloc(this._getInventoryUseCase)
      : super(const SelectSkuState.initial()) {
    on<SelectSkuEvent>((event, emit) async {
      await event.map(
        started: (started) async {
          await _getInventoryUseCase(started.mandiId).then(
            (value) => value.fold(
              (left) => emit(SelectSkuState.error(left)),
              (right) {
                final bulkItems = right.skus.where((e) => e.isBulk()).toList();
                final lotItems = right.skus.where((e) => !e.isBulk()).toList();
                emit(SelectSkuState.success(
                  bulkItems: bulkItems,
                  lotItems: lotItems,
                ));
              },
            ),
          );
        },
        selectUnSelect: (_SelectSku value) {
          final currentState = state;
          if (currentState is _Success) {
            final newList = List<String>.from(currentState.selectedItems);
            final newListIds = List<int>.from(currentState.selectedSkuIds);
            if (value.isSelected) {
              newList.add(value.skuKey);
              newListIds.add(value.skuId);
            } else {
              newList.remove(value.skuKey);
              newListIds.remove(value.skuId);
            }
            emit(currentState.copyWith(
                selectedItems: newList, selectedSkuIds: newListIds));
          }
        },
      );
    });
  }

  final GetInventoryUseCase _getInventoryUseCase;
}
