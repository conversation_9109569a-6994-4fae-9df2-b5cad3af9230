import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/app/view/splash_screen.dart';
import 'package:proc2/core/app_config.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/presentation/report_bug/cubit/report_bug_cubit.dart';
import 'package:proc2/core/presentation/report_bug/view/report_bug_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/utils/weighing-machine/weighing_machine_screen.dart';
import 'package:proc2/features/admin/bug_report/cubit/bug_report_admin_cubit.dart';
import 'package:proc2/features/admin/bug_report/view/bug_report_admin_screen.dart';
import 'package:proc2/features/admin/menu/admin_menu_screen.dart';
import 'package:proc2/features/admin/otp_dashboard/cubit/otp_dashboard_cubit.dart';
import 'package:proc2/features/admin/otp_dashboard/otp_dashboard_page.dart';
import 'package:proc2/features/admin/procurement_update/cubit/procurement_update_cubit.dart';
import 'package:proc2/features/admin/procurement_update/cubit/procurement_update_edit_cubit.dart';
import 'package:proc2/features/admin/procurement_update/model/procurement_order.dart';
import 'package:proc2/features/admin/procurement_update/view/procurement_update_edit_screen.dart';
import 'package:proc2/features/admin/procurement_update/view/procurement_update_screen.dart';
import 'package:proc2/features/admin/return_download/cubit/return_download_cubit.dart';
import 'package:proc2/features/admin/return_download/return_download.dart';
import 'package:proc2/features/admin/weighing_machine_bypass/cubit/weighing_machine_bypass_cubit.dart';
import 'package:proc2/features/admin/weighing_machine_bypass/view/weighing_machine_bypass_screen.dart';
import 'package:proc2/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:proc2/features/auth/presentation/view/login.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/smo/smo_config.dart';
import 'package:proc2/features/mandi/domain/entity/smo_history/smo_detail.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/add_field_charges/bloc/add_field_charge_bloc.dart';
import 'package:proc2/features/mandi/presentation/add_field_charges/view/add_field_charges.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/cubit/add_parent_proc_cubit.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/add_parent_proc/view/add_parent_proc_screen.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/view/add_procurement.dart';
import 'package:proc2/features/mandi/presentation/attendance/cubit/attendance_cubit.dart';
import 'package:proc2/features/mandi/presentation/attendance/view/attendance_screen.dart';
import 'package:proc2/features/mandi/presentation/carry_forward/bloc/add_carry_forward_bloc.dart';
import 'package:proc2/features/mandi/presentation/carry_forward/view/add_carry_forward.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/view/close_inventory.dart';
import 'package:proc2/features/mandi/presentation/close_ops/bloc/close_ops_bloc.dart';
import 'package:proc2/features/mandi/presentation/close_ops/view/close_ops.dart';
import 'package:proc2/features/mandi/presentation/combo/cubit/combo_conversion_cubit.dart';
import 'package:proc2/features/mandi/presentation/combo/view/combo_conversion_screen.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/detail/view/mandi_detail.dart';
import 'package:proc2/features/mandi/presentation/grading/cubit/grading_conversion_cubit.dart';
import 'package:proc2/features/mandi/presentation/grading/view/grading_conversion_screen.dart';
import 'package:proc2/features/mandi/presentation/grading/view/grading_screen.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/mandi_bloc.dart';
import 'package:proc2/features/mandi/presentation/home/<USER>/home.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocating/bloc/allocating_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocating/view/allocate_inventory.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/view/allocation_journey_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/home/<USER>/inventory_allocation_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/home/<USER>/allocation_home.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/trips/bloc/trip_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/trips/view/trips_home.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/bloc/inventory_recieving_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_recieving/view/recieving_home.dart';
import 'package:proc2/features/mandi/presentation/liquidation/cubit/liquidation_cubit.dart';
import 'package:proc2/features/mandi/presentation/liquidation/cubit/update_liquidation_cubit.dart';
import 'package:proc2/features/mandi/presentation/liquidation/summary/cubit/liquidation_summary_cubit.dart';
import 'package:proc2/features/mandi/presentation/liquidation/view/liquidation_screen.dart';
import 'package:proc2/features/mandi/presentation/liquidation/view/liquidation_summary_screen.dart';
import 'package:proc2/features/mandi/presentation/liquidation/view/update_liquidation_screen.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/bloc/lotting_conversion_bloc.dart';
import 'package:proc2/features/mandi/presentation/lotting/conversion/view/select_lotting_sku.dart';
import 'package:proc2/features/mandi/presentation/lotting/home/<USER>/lotting_home.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/inventory_type.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/bloc/mandi_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/mandi_inventory/view/mandi_inventory.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/detail/cubit/my_proc_detail_cubit.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/detail/view/my_procurement_detail.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/summary/cubit/my_proc_summary_cubit.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/summary/view/my_procurement_summary.dart';
import 'package:proc2/features/mandi/presentation/ops_summary/cubit/ops_summary_cubit.dart';
import 'package:proc2/features/mandi/presentation/ops_summary/view/ops_summary.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/cubit/manage_users_cubit.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/manage_users/view/manage_users_screen.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/search_proc/cubit/search_proc_cubit.dart';
import 'package:proc2/features/mandi/presentation/proc_admin_panel/search_proc/view/search_proc_screen.dart';
import 'package:proc2/features/mandi/presentation/procurement_item/cubit/procurement_item_cubit.dart';
import 'package:proc2/features/mandi/presentation/procurement_item/view/procurement_item.dart';
import 'package:proc2/features/mandi/presentation/returns/create/cubit/create_returns_cubit.dart';
import 'package:proc2/features/mandi/presentation/returns/create/view/create_returns_screen.dart';
import 'package:proc2/features/mandi/presentation/returns/cubit/returns_cubit.dart';
import 'package:proc2/features/mandi/presentation/returns/cubit/returns_submit_cubit.dart';
import 'package:proc2/features/mandi/presentation/returns/view/returns_accept_screen.dart';
import 'package:proc2/features/mandi/presentation/returns/view/returns_list_screen.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/edit/bloc/edit_procurement_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/edit/view/edit_procurement.dart';
import 'package:proc2/features/mandi/presentation/smo_history/home/<USER>/smo_history_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/home/<USER>/smo_history_home.dart';
import 'package:proc2/features/mandi/presentation/smo_history/procurement/bloc/procurement_history_bloc.dart';
import 'package:proc2/features/mandi/presentation/smo_history/procurement/cubit/get_field_charges_cubit.dart';
import 'package:proc2/features/mandi/presentation/smo_history/procurement/view/my_procurement.dart';
import 'package:proc2/features/mandi/presentation/smo_history/procurement/view/procurement_history.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/cubit/edit_supply_order_cubit.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/edit_supply_order.dart';
import 'package:proc2/features/mandi/presentation/supply_order/view_supply_order/cubit/view_supply_order_cubit.dart';
import 'package:proc2/features/mandi/presentation/supply_order/view_supply_order/view_supply_order.dart';
import 'package:talker_flutter/talker_flutter.dart';

final router = GoRouter(
  observers: [TalkerRouteObserver(talker)],
  initialLocation: '/splash',
  routes: [
    GoRoute(
      path: '/splash',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginForm(),
    ),
    GoRoute(
      path: '/',
      builder: (context, state) => const Home(),
      routes: [
        GoRoute(
          path: 'weighing-machine',
          name: 'weighingMachine',
          builder: (context, state) {
            return WeighingMachineScreen();
          },
        ),
        GoRoute(
          path: 'app-logs',
          name: 'appLogs',
          builder: (context, state) {
            final config = di.get<AppConfig>();
            return TalkerScreen(talker: config.talker);
          },
        ),
        GoRoute(
          path: 'report-bug',
          name: 'reportBug',
          builder: (context, state) {
            final config = di.get<AppConfig>();
            return BlocProvider(
              create: (context) => di.get<ReportBugCubit>(),
              child: ReportBugScreen(talker: config.talker),
            );
          },
        ),
        GoRoute(
            path: 'admin-panel',
            name: 'adminPanel',
            builder: (context, state) {
              return const AdminMenuScreen();
            },
            redirect: (context, state) {
              final userRoles = context.read<AuthBloc>().state.maybeMap(
                    orElse: () => <String>[],
                    authenticated: (auth) => auth.user.roles,
                  );
              if (userRoles.contains('ROLE_CENTRAL_PROCUREMENT')) {
                return null;
              }
              return '/';
            },
            routes: [
              GoRoute(
                path: 'admin-bug-reports',
                name: 'adminBugReports',
                builder: (context, state) {
                  return BlocProvider(
                    create: (_) => di.get<BugReportAdminCubit>()
                      ..loadBugReports(isResolved: false),
                    child: BugReportAdminScreen(),
                  );
                },
              ),
              GoRoute(
                path: 'admin-proc-search',
                name: 'procAdminSearch',
                builder: (context, state) {
                  return BlocProvider(
                    create: (_) => di.get<SearchProcCubit>(),
                    child: SearchProcScreen(),
                  );
                },
              ),
              GoRoute(
                path: 'admin-weighing-machine-bypass',
                name: 'weighingMachineBypass',
                builder: (context, state) {
                  return BlocProvider(
                    create: (_) => di.get<WeighingMachineBypassCubit>(),
                    child: WeighingMachineBypassScreen(),
                  );
                },
              ),
              GoRoute(
                path: 'admin-proc-order-update',
                name: 'adminProcOrderUpdate',
                builder: (context, state) {
                  return BlocProvider(
                    create: (_) => di.get<ProcurementUpdateCubit>(),
                    child: ProcurementUpdateScreen(),
                  );
                },
              ),
              GoRoute(
                path: 'admin-proc-order-update-edit',
                name: 'adminProcOrderUpdateEdit',
                builder: (context, state) {
                  final extra = state.extra as Map<String, dynamic>;
                  final mandi = extra['mandi'] as MandiInfo;
                  final smoId = extra['smoId'] as int;
                  final order = extra['order'] as ProcurementOrder;
                  final skuMap = extra['skuMap'] as Map<int, Sku>;
                  final mandis = extra['mandis'] as Map<int, MandiInfo>;

                  return BlocProvider(
                    create: (_) => di.get<ProcurementUpdateEditCubit>()
                      ..init(
                        mandi: mandi,
                        smoId: smoId,
                        order: order,
                        skuMap: skuMap,
                        mandis: mandis,
                      ),
                    child: ProcurementUpdateEditScreen(),
                  );
                },
              ),
              GoRoute(
                  path: 'admin-proc-create',
                  name: 'adminProcOrder',
                  builder: (context, state) {
                    return MultiBlocProvider(
                      providers: [
                        BlocProvider(
                            create: (_) => di.get<AddParentProcCubit>())
                      ],
                      child: AddParentProcScreen(),
                    );
                  }),
              GoRoute(
                  path: 'admin-manage-users',
                  name: 'adminManageUsers',
                  builder: (context, state) {
                    return BlocProvider(
                      create: (_) => di.get<ManageUsersCubit>()..load(),
                      child: ManageUsersScreen(),
                    );
                  }),
              GoRoute(
                path: 'admin-otp-dashboard',
                name: 'adminOtpDashboard',
                builder: (context, state) {
                  return BlocProvider(
                      create: (_) => di.get<OtpDashboardCubit>(),
                      child: const OtpDashboardPage());
                },
              ),
              GoRoute(
                path: 'admin-return-download',
                name: 'adminReturnDownload',
                builder: (context, state) {
                  return BlocProvider(
                      create: (_) => di.get<ReturnDownloadCubit>(),
                      child: const ReturnDownload());
                },
              ),
            ]),
        GoRoute(
          path: 'smo-hisotry',
          name: 'smoHistory',
          builder: (context, state) {
            return BlocProvider(
              create: (_) => di.get<SmoHistoryBloc>()
                ..add(const SmoHistoryEvent.started()),
              child: const SmoHistoryHome(),
            );
          },
          routes: [
            GoRoute(
              name: 'opsSummary',
              path: 'ops-summary/:mandiId/:smoId/:status/:startTime/:endTime',
              builder: (context, state) {
                final smoId = int.parse(state.pathParameters['smoId']!);
                final mandiId = int.parse(state.pathParameters['mandiId']!);
                final status = state.pathParameters['status']!;
                final startTime = int.parse(state.pathParameters['startTime']!);
                final endTime = int.tryParse(state.pathParameters['endTime']!);

                return BlocProvider(
                  create: (_) => di.get<OpsSummaryCubit>()
                    ..init(smoId, mandiId, status, startTime, endTime),
                  child: OpsSummary(),
                );
              },
            ),
            GoRoute(
              path: 'mandi/:mandiId/smo/:smoId/my-procurement',
              name: 'myProcurement',
              builder: (context, state) {
                final smoId =
                    int.tryParse(state.pathParameters['smoId'] ?? '-1') ?? -1;
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                return MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (_) => di.get<ProcurementHistoryBloc>()
                        ..add(ProcurementHistoryEvent.started(smoId)),
                    ),
                  ],
                  child: MyProcurement(
                    smoId: smoId,
                    mandiId: mandiId,
                  ),
                );
              },
            ),
            GoRoute(
              path: 'mandi/:mandiId/smo/:smoId/procurement-history',
              name: 'procurementHistory',
              builder: (context, state) {
                final smoId =
                    int.tryParse(state.pathParameters['smoId'] ?? '-1') ?? -1;
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                return MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (_) => di.get<ProcurementHistoryBloc>()
                        ..add(ProcurementHistoryEvent.started(smoId)),
                    ),
                    BlocProvider(
                      create: (_) => di.get<GetFieldChargesCubit>()
                        ..init(smoId)
                        ..fetch(),
                    ),
                  ],
                  child: ProcurementHistory(
                    smoId: smoId,
                    mandiId: mandiId,
                  ),
                );
              },
              routes: [
                GoRoute(
                  path: 'edit-procurement',
                  name: 'editProcurement',
                  builder: (context, state) {
                    final smoDetail = state.extra! as SmoDetail;
                    final smoId =
                        int.tryParse(state.pathParameters['smoId'] ?? '-1') ??
                            -1;
                    final mandiId =
                        int.tryParse(state.pathParameters['mandiId'] ?? '-1') ??
                            -1;
                    return BlocProvider(
                      create: (_) => di.get<EditProcurementBloc>()
                        ..add(EditProcurementEvent.started(smoDetail)),
                      child: EditProcurement(
                        smoId: smoId,
                        mandiId: mandiId,
                      ),
                    );
                  },
                ),
                GoRoute(
                  path: 'edit-procurement-admin',
                  name: 'editProcurementAdmin',
                  builder: (context, state) {
                    final smoDetail = state.extra! as SmoDetail;
                    final smoId =
                        int.tryParse(state.pathParameters['smoId'] ?? '-1') ??
                            -1;
                    final mandiId =
                        int.tryParse(state.pathParameters['mandiId'] ?? '-1') ??
                            -1;
                    return BlocProvider(
                      create: (_) => di.get<EditProcurementBloc>()
                        ..add(EditProcurementEvent.started(smoDetail)),
                      child: EditProcurement(
                        smoId: smoId,
                        mandiId: mandiId,
                        isAdmin: true,
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
        GoRoute(
          name: 'procItem',
          path: 'procItem',
          builder: (context, state) {
            final procDetail = state.extra as ProcDetail?;
            final cubit = di.get<ProcItemCubit>();
            if (procDetail != null) {
              cubit.loadWithData(procDetail);
            }

            return BlocProvider<ProcItemCubit>(
              create: (context) => cubit,
              child: ProcItem(
                refId: procDetail?.refId,
                refSource: procDetail?.refSource,
              ),
            );
          },
        ),
        GoRoute(
          name: 'mandiDetail',
          path: 'detail/:mandiId',
          builder: (context, state) {
            final int mandiId =
                int.parse(state.pathParameters['mandiId'] ?? '-1');
            final int smoId =
                int.parse(state.uri.queryParameters['smoId'] ?? '-1');
            if (mandiId != -1 && smoId != -1) {
              final mandiInfo = context.read<MandiBloc>().state.maybeMap(
                    orElse: () => MandiInfo.empty,
                    success: (s) => s.allMandis.firstWhere(
                      (element) => element.id == mandiId,
                      orElse: () => MandiInfo.empty,
                    ),
                  );

              WAppBar.instance.setMandiAndSmo(
                mandi: mandiInfo,
                smoId: smoId,
              );
            }
            return MandiDetail(
              mandiId: mandiId,
              smoId: smoId,
            );
          },
          redirect: (context, state) {
            try {
              final mandiId =
                  int.parse(state.pathParameters['mandiId'] ?? '-1');
              if (mandiId == -1) {
                return '/';
              }
              final mandiInfo = context.read<MandiBloc>().state.maybeMap(
                    orElse: () => MandiInfo.empty,
                    success: (s) => s.allMandis.firstWhere(
                      (element) => element.id == mandiId,
                      orElse: () => MandiInfo.empty,
                    ),
                  );
              if (mandiInfo == MandiInfo.empty) {
                return '/';
              }
              return null;
            } catch (e) {
              return '/';
            }
          },
          routes: [
            GoRoute(
              name: 'attendance',
              path: 'attendance',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                return BlocProvider<AttendanceCubit>(
                  create: (context) =>
                      di.get<AttendanceCubit>()..init(smoId: smoId),
                  child: AttendanceScreen(),
                );
              },
            ),
            GoRoute(
              name: 'returns',
              path: 'returns',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;

                return MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => di.get<ReturnsCubit>()
                        ..load(
                          smoId,
                          isWastageReturns: false,
                        ),
                    ),
                    BlocProvider.value(
                      value: smoContext.read<SmoBloc>(),
                    ),
                  ],
                  child: ReturnsListScreen(
                    smoId: smoId,
                    mandiId: mandiId,
                    isWastageReturns: false,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'wastageReturns',
              path: 'wastage-returns',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;

                return MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => di.get<ReturnsCubit>()
                        ..load(smoId, isWastageReturns: true),
                    ),
                    BlocProvider.value(
                      value: smoContext.read<SmoBloc>(),
                    ),
                  ],
                  child: ReturnsListScreen(
                    smoId: smoId,
                    mandiId: mandiId,
                    isWastageReturns: true,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'returnsHistory',
              path: 'returns/history/:smoId',
              builder: (context, state) {
                final mandiId = state.pathParameters['mandiId'] ?? '-1';
                final smoId = state.pathParameters['smoId'] ?? '-1';
                final isWastageReturns =
                    state.uri.queryParameters['isWastageReturns'] == 'true';
                final smoContext = state.extra! as BuildContext;

                return MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => di.get<ReturnsCubit>()
                        ..load(int.parse(smoId),
                            isHistory: true,
                            isWastageReturns: isWastageReturns),
                    ),
                    BlocProvider.value(
                      value: smoContext.read<SmoBloc>(),
                    ),
                  ],
                  child: ReturnsListScreen(
                    smoId: int.parse(smoId),
                    mandiId: int.parse(mandiId),
                    isHistory: true,
                    isWastageReturns: isWastageReturns,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'returnsSubmit',
              path: 'returns-submit/:smoId',
              builder: (context, state) {
                final mandiId = state.pathParameters['mandiId'] ?? '-1';
                final smoId = state.pathParameters['smoId'] ?? '-1';
                final extra = state.extra as Map<String, dynamic>?;
                final smoContext = extra!['smoContext'] as BuildContext;
                final smoBloc = smoContext.read<SmoBloc>();
                final item = extra['item']!;
                final isHistory =
                    state.uri.queryParameters['isHistory'] == 'true';
                final isWastageReturns =
                    state.uri.queryParameters['isWastageReturns'] == 'true';
                final config = smoBloc.state.mapOrNull(success: (success) {
                      return isWastageReturns
                          ? success.config.wastageReturns
                          : success.config.returns;
                    }) ??
                    ReturnsConfig();
                final isEditOnlyFromWeighingMachine = state
                        .uri.queryParameters['isEditOnlyFromWeighingMachine'] ==
                    'true';
                final hideSystemQuantity =
                    state.uri.queryParameters['hideSystemQuantity'] == 'true';

                return BlocProvider(
                  create: (context) => di.get<ReturnsSubmitCubit>()
                    ..updateReturnsOrHistory(
                      item!,
                      int.parse(smoId),
                    ),
                  child: ReturnsAcceptScreen(
                    smoId: int.parse(smoId),
                    mandiId: int.parse(mandiId),
                    isHistory: isHistory,
                    isWastageReturns: isWastageReturns,
                    isEditOnlyFromWeighingMachine:
                        isEditOnlyFromWeighingMachine,
                    hideSystemQuantity: hideSystemQuantity,
                    config: config,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'returnsCreate',
              path: 'returns-create/:smoId',
              builder: (context, state) {
                final mandiId = state.pathParameters['mandiId'] ?? '-1';
                final smoId = state.pathParameters['smoId'] ?? '-1';

                return BlocProvider(
                  create: (context) => di.get<CreateReturnsCubit>(),
                  child: CreateReturnsScreen(
                    smoId: int.parse(smoId),
                    mandiId: int.parse(mandiId),
                  ),
                );
              },
            ),
            GoRoute(
              name: 'grading',
              path: 'grading',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                var smoId = -1;
                ConversionConfig? config;
                smoContext.read<SmoBloc>().state.maybeWhen(
                    orElse: () {},
                    success: (s, c) {
                      smoId = s.smoId;
                      config = c.conversion;
                    });
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;

                return GradingScreen(
                  smoId: smoId,
                  mandiId: mandiId,
                  config: config!,
                );
              },
            ),
            GoRoute(
              name: 'gradingConversion',
              path: 'grading-conversion/:smoId',
              builder: (context, state) {
                final mandiId = state.pathParameters['mandiId'] ?? '-1';
                final smoId = state.pathParameters['smoId'] ?? '-1';
                final title = state.uri.queryParameters['title'];
                final disableGrading =
                    state.uri.queryParameters['allowGrading'] == 'false';
                final extra = state.extra as Map<String, dynamic>;
                final inventoryType = extra['type'] as InventoryType;
                final config = extra['config'] as ConversionInventoryConfig;

                return BlocProvider(
                  create: (context) => di.get<GradingConversionCubit>()
                    ..loadInventory(int.parse(mandiId), inventoryType)
                    ..loadDeviation(),
                  child: GradingConversionScreen(
                    smoId: int.parse(smoId),
                    mandiId: int.parse(mandiId),
                    inventoryType: inventoryType,
                    title: title,
                    allowGrading: !disableGrading,
                    config: config,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'combo',
              path: 'combo',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                var smoId = -1;
                ConversionConfig? config;
                smoContext.read<SmoBloc>().state.maybeWhen(
                    orElse: () {},
                    success: (s, c) {
                      smoId = s.smoId;
                      config = c.conversion;
                    });
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;

                // return ComboScreen(
                //   smoId: smoId,
                //   mandiId: mandiId,
                //   config: config!,
                // );

                return BlocProvider(
                  create: (context) => di.get<ComboConversionCubit>()
                    ..loadInventory(mandiId, InventoryType.primary)
                    ..loadDeviation(),
                  child: ComboConversionScreen(
                    smoId: smoId,
                    mandiId: mandiId,
                    inventoryType: InventoryType.primary,
                    title: 'Combo',
                    allowGrading: true,
                    config: config!.mandiInventory,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'comboConversion',
              path: 'combo-conversion/:smoId',
              builder: (context, state) {
                final mandiId = state.pathParameters['mandiId'] ?? '-1';
                final smoId = state.pathParameters['smoId'] ?? '-1';
                final title = state.uri.queryParameters['title'];
                final disableGrading =
                    state.uri.queryParameters['allowGrading'] == 'false';
                final extra = state.extra as Map<String, dynamic>;
                final inventoryType = extra['type'] as InventoryType;
                final config = extra['config'] as ConversionInventoryConfig;

                return BlocProvider(
                  create: (context) => di.get<ComboConversionCubit>()
                    ..loadInventory(int.parse(mandiId), inventoryType)
                    ..loadDeviation(),
                  child: ComboConversionScreen(
                    smoId: int.parse(smoId),
                    mandiId: int.parse(mandiId),
                    inventoryType: inventoryType,
                    title: title,
                    allowGrading: !disableGrading,
                    config: config,
                  ),
                );
              },
            ),

            GoRoute(
              name: 'addProcurement',
              path: 'add-procurement',
              builder: (context, state) {
                if (state.extra == null || state.extra is! BuildContext) {
                  throw Exception(
                    'add-procurement Extra is not a BuildContext.',
                  );
                }
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                final mandiName = context.read<MandiBloc>().state.maybeMap(
                      orElse: () => '',
                      success: (s) => s.allMandis
                          .firstWhere((element) => element.id == mandiId)
                          .name,
                    );
                return BlocProvider.value(
                  value: smoContext.read<SmoBloc>(),
                  child: AddProcurement(
                    mandiId: mandiId,
                    mandiName: mandiName,
                    smoId: smoId,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'myProc',
              path: 'my-proc',
              builder: (context, state) {
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                final userRoles = context.read<AuthBloc>().state.maybeMap(
                    orElse: () => <String>[],
                    authenticated: (auth) => auth.user.roles);
                final isProcManager = userRoles.contains('ROLE_PITSTOP_HEAD');
                final isFieldOps = userRoles.contains('ROLE_PITSTOP_OPS');
                return BlocProvider<MyProcSummaryCubit>(
                  create: (context) => di.get<MyProcSummaryCubit>()
                    ..changeMandiSummaryOpen(
                        isMandiSummaryOpen: !isProcManager),
                  child: BlocProvider.value(
                    value: smoContext.read<SmoBloc>(),
                    child: MyProcSummary(
                      smoId: smoId,
                      mandiId: mandiId,
                      isFieldOps: isFieldOps,
                      isProcManager: isProcManager,
                    ),
                  ),
                );
              },
            ),
            GoRoute(
                name: 'liquidation',
                path: 'liquidation',
                builder: (context, state) {
                  final mandiId =
                      int.tryParse(state.pathParameters['mandiId'] ?? '-1') ??
                          -1;
                  final smoContext = state.extra! as BuildContext;
                  final smo = smoContext.read<SmoBloc>().state.maybeMap(
                        orElse: () => null,
                        success: (s) => s,
                      );
                  final isEditOnlyFromWeighingMachine =
                      smo!.config.liquidation.isEditOnlyFromWeighingMachine;
                  return BlocProvider<LiquidationSummaryCubit>(
                    create: (context) => di.get<LiquidationSummaryCubit>(),
                    child: LiquidationSummaryScreen(
                      smoId: smo.smo.smoId,
                      mandiId: mandiId,
                      isEditOnlyFromWeighingMachine:
                          isEditOnlyFromWeighingMachine,
                    ),
                  );
                },
                routes: [
                  GoRoute(
                    path: 'liquidation-list/:smoId',
                    name: 'liquidationList',
                    builder: (context, state) {
                      final mandiId = int.tryParse(
                              state.pathParameters['mandiId'] ?? '-1') ??
                          -1;
                      final smoId =
                          int.tryParse(state.pathParameters['smoId'] ?? '-1') ??
                              -1;
                      final map = state.extra as Map<String, dynamic>? ?? {};
                      final isEditOnlyFromWeighingMachine =
                          map['isEditOnlyFromWeighingMachine'] as bool? ??
                              false;

                      return BlocProvider<LiquidationCubit>(
                        create: (context) => di.get<LiquidationCubit>()
                          ..getAllLiquidationOrders(smoId),
                        child: LiquidationScreen(
                          smoId: smoId,
                          mandiId: mandiId,
                          isEditOnlyFromWeighingMachine:
                              isEditOnlyFromWeighingMachine,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    name: 'updateLiquidation',
                    path: 'update-liquidation/:smoId',
                    builder: (context, state) {
                      final mandiId = int.tryParse(
                              state.pathParameters['mandiId'] ?? '-1') ??
                          -1;
                      final smoId =
                          int.tryParse(state.pathParameters['smoId'] ?? '-1') ??
                              -1;

                      final liquidationOrderMap =
                          state.extra as Map<String, dynamic>? ?? {};
                      final liquidationOrder =
                          liquidationOrderMap['order'] as LiquidationOrder?;
                      final itemId = liquidationOrderMap['itemId'] as int?;
                      final isEditOnlyFromWeighingMachine =
                          liquidationOrderMap['isEditOnlyFromWeighingMachine']
                                  as bool? ??
                              false;

                      return BlocProvider<UpdateLiquidationCubit>(
                        create: (context) => di.get<UpdateLiquidationCubit>(),
                        child: UpdateLiquidationScreen(
                          smoId: smoId,
                          mandiId: mandiId,
                          order: liquidationOrder,
                          itemId: itemId,
                          isEditOnlyFromWeighingMachine:
                              isEditOnlyFromWeighingMachine,
                        ),
                      );
                    },
                  ),
                ]),
            GoRoute(
              name: 'myProcDetail',
              path: 'my-proc/detail',
              builder: (context, state) {
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                return BlocProvider<MyProcDetailCubit>(
                  create: (context) =>
                      di.get<MyProcDetailCubit>()..loadDetail(smoId),
                  child: BlocProvider.value(
                    value: smoContext.read<SmoBloc>(),
                    child: MyProcDetail(
                      smoId: smoId,
                      mandiId: mandiId,
                    ),
                  ),
                );
              },
            ),
            GoRoute(
              name: 'recieveInventory',
              path: 'recieve-inventory',
              builder: (context, state) {
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                return MultiBlocProvider(
                  providers: [
                    BlocProvider<InventoryRecievingBloc>(
                      create: (context) => di.get<InventoryRecievingBloc>()
                        ..add(InventoryRecievingEvent.started(mandiId)),
                    ),
                    BlocProvider.value(value: smoContext.read<SmoBloc>()),
                  ],
                  child: ReceivingHome(
                    mandiId: mandiId,
                    smoId: smoId,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'carryForward',
              path: 'carry-forward',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.mapOrNull(
                          success: (s) => s.smo.smoId,
                        ) ??
                    -1;
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                final mandiName = context.read<MandiBloc>().state.maybeMap(
                      orElse: () => '',
                      success: (s) => s.allMandis
                          .firstWhere((element) => element.id == mandiId)
                          .name,
                    );
                return MultiBlocProvider(
                  providers: [
                    BlocProvider.value(value: smoContext.read<SmoBloc>()),
                    BlocProvider<AddCarryForwardBloc>(
                      create: (context) => di.get<AddCarryForwardBloc>()
                        ..add(
                          AddCarryForwardEvent.started(smoId: smoId),
                        ),
                    ),
                  ],
                  child: AddCarryForward(
                    mandiName: mandiName,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'addCharges',
              path: 'add-charges',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.mapOrNull(
                          success: (s) => s.smo.smoId,
                        ) ??
                    -1;
                return MultiBlocProvider(
                  providers: [
                    BlocProvider<AddFieldChargeBloc>(
                      create: (context) => di.get<AddFieldChargeBloc>()
                        ..add(const AddFieldChargeEvent.fetchChargesType()),
                    ),
                    BlocProvider.value(value: smoContext.read<SmoBloc>()),
                  ],
                  child: AddFieldCharges(
                    smoId: smoId,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'mandiInventory',
              path: 'mandi-inventory',
              builder: (context, state) {
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.mapOrNull(
                          success: (s) => s.smo.smoId,
                        ) ??
                    -1;

                return MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => di.get<MandiInventoryBloc>()
                        ..add(MandiInventoryEvent.fetchAll(mandiId)),
                    ),
                    BlocProvider.value(
                      value: smoContext.read<SmoBloc>(),
                    ),
                  ],
                  child: MandiInventoryPage(
                    mandiId: mandiId,
                    smoId: smoId,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'supplyOrder',
              path: 'supply-order',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.mapOrNull(
                          success: (s) => s.smo.smoId,
                        ) ??
                    -1;
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;

                return BlocProvider(
                  create: (_) => di.get<ViewSupplyOrderCubit>(),
                  child: ViewSupplyOrder(
                    mandiId: mandiId,
                    smoId: smoId,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'editSupplyOrder',
              path: 'supply-order/edit',
              builder: (context, state) => BlocProvider(
                  create: (_) => di.get<EditSupplyOrderCubit>()
                    ..preload(
                      state.extra as List<SupplyOrder>,
                      context.read<SkuBloc>().state.mapOrNull(
                                success: (s) => Map.fromEntries(
                                  s.skus.map(
                                    (e) => MapEntry(
                                      e.id,
                                      e,
                                    ),
                                  ),
                                ),
                              ) ??
                          {},
                    ),
                  child: const EditSupplyOrder()),
            ),
            GoRoute(
              name: 'skuAllocation',
              path: 'sku-allocation',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoBloc = smoContext.read<SmoBloc>();
                final smoId = smoBloc.state.mapOrNull(
                      success: (s) => s.smo.smoId,
                    ) ??
                    -1;
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                return MultiBlocProvider(
                  providers: [
                    BlocProvider.value(value: smoBloc),
                    BlocProvider(
                        create: (context) => di.get<InventoryAllocationBloc>()
                          ..add(InventoryAllocationEvent.started(smoId))
                        // ..add(InventoryAllocationEvent.getAllotments(smoId)),
                        ),
                  ],
                  child: AllocationScreen(
                    smoId: smoId,
                    mandiId: mandiId,
                  ),
                );
              },
              routes: [
                GoRoute(
                  path: 'allocate/:smoId/trips/:allotmentId',
                  name: 'trips',
                  builder: (context, state) {
                    final mandiId =
                        int.tryParse(state.pathParameters['mandiId'] ?? '-1') ??
                            -1;
                    final allotmentId = int.tryParse(
                            state.pathParameters['allotmentId'] ?? '-1') ??
                        -1;
                    final smoId =
                        int.tryParse(state.pathParameters['smoId'] ?? '-1') ??
                            -1;

                    return BlocProvider(
                      create: (_) => di.get<TripBloc>()
                        ..add(
                          TripEvent.started(smoId, allotmentId),
                        ),
                      child: TripsHome(
                        smoId: smoId,
                        mandiId: mandiId,
                        allotmentId: allotmentId,
                      ),
                    );
                  },
                ),
                GoRoute(
                  path: 'allocation-journey',
                  name: 'allocationJourney',
                  builder: (context, state) {
                    final extra = state.extra as BuildContext;
                    final mandiId = int.parse(state.pathParameters['mandiId']!);
                    final smoId =
                        int.parse(state.uri.queryParameters['smoId']!);
                    final allocationId =
                        int.parse(state.uri.queryParameters['allocationId']!);
                    final customerGroup =
                        state.uri.queryParameters['customerGroup'];
                    final deliverySlot =
                        state.uri.queryParameters['deliverySlot'];
                    final deliveryDate =
                        state.uri.queryParameters['deliveryDate'];
                    final status = state.uri.queryParameters['status'];
                    final isInProgress =
                        status == 'IN_PROGRESS' || status == 'UPDATED';
                    final canAllocateExcess =
                        state.uri.queryParameters['canAllocateExcess'] ==
                            'true';

                    final cubit = di.get<AllocationJourneyCubit>();
                    if (customerGroup != null &&
                        deliverySlot != null &&
                        deliveryDate != null) {
                      final deliveryDateInt =
                          int.parse(state.uri.queryParameters['deliveryDate']!);
                      cubit.startCustomerGroupJourney(
                        mandiId: mandiId,
                        smoId: smoId,
                        allocationId: allocationId,
                        customerGroup: customerGroup,
                        deliverySlot: deliverySlot,
                        deliveryDate: deliveryDateInt,
                        isInProgress: isInProgress,
                        canAllocateExcess: canAllocateExcess,
                        status: status,
                      );
                    } else {
                      cubit.startMandiJourney(
                        mandiId: mandiId,
                        smoId: smoId,
                        allocationId: allocationId,
                        isInProgress: isInProgress,
                        status: status,
                      );
                    }

                    return MultiBlocProvider(
                      providers: [
                        BlocProvider(
                          create: (_) => cubit,
                        ),
                        BlocProvider.value(
                          value: extra.read<SmoBloc>(),
                        ),
                      ],
                      child: AllocationJourneyScreen(),
                    );
                  },
                ),
                GoRoute(
                  path:
                      'allocate/:allocateId/:destinationName/:canAllocateExcess',
                  name: 'allocate',
                  builder: (context, state) {
                    final customerGroup =
                        state.uri.queryParameters['customerGroup'];
                    final deliverySlot =
                        state.uri.queryParameters['deliverySlot'];
                    final deliveryDate =
                        state.uri.queryParameters['deliveryDate'];
                    final canAllocateExcess =
                        state.pathParameters['canAllocateExcess'] == 'true';
                    final mandiId =
                        int.tryParse(state.pathParameters['mandiId'] ?? '-1') ??
                            -1;
                    final allocateId = int.tryParse(
                            state.pathParameters['allocateId'] ?? '-1') ??
                        -1;
                    final isMandiAllocation = customerGroup == null ||
                        deliverySlot == null ||
                        deliveryDate == null;
                    final skuMap = !isMandiAllocation
                        ? context.read<SkuBloc>().state.mapOrNull(
                                  success: (s) => Map.fromEntries(
                                    s.skus.map(
                                      (e) => MapEntry(
                                        e.id,
                                        e,
                                      ),
                                    ),
                                  ),
                                ) ??
                            <int, Sku>{}
                        : <int, Sku>{};
                    return BlocProvider<AllocatingInventoryBloc>(
                      create: (context) => di.get<AllocatingInventoryBloc>()
                        ..add(AllocatingInventoryEvent.started(
                          mandiId: mandiId,
                          allotmentId: allocateId,
                          customerGroup: customerGroup,
                          deliverySlot: deliverySlot,
                          deliveryDate: deliveryDate,
                          skuMap: skuMap,
                        )),
                      child: AllocateInventory(
                        allocateId: allocateId,
                        mandiId: mandiId,
                        destinationName:
                            state.pathParameters['destinationName']!,
                        canAllocateExcess: canAllocateExcess,
                        isMandiAllocation: isMandiAllocation,
                        customerGroup: customerGroup,
                        deliverySlot: deliverySlot,
                        deliveryDate: deliveryDate,
                        skuMap: skuMap,
                      ),
                    );
                  },
                )
              ],
            ),
            GoRoute(
              name: 'lotting',
              path: 'lotting',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                return BlocProvider.value(
                  value: smoContext.read<SmoBloc>(),
                  child: LottingHome(
                    mandiId: mandiId,
                  ),
                );
              },
              routes: [
                GoRoute(
                  path: 'conversion/lotting',
                  name: 'lottingSku',
                  builder: (context, state) {
                    final mandiId =
                        int.tryParse(state.pathParameters['mandiId'] ?? '-1') ??
                            -1;
                    final smoContext = state.extra! as BuildContext;
                    final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                          orElse: () => -1,
                          success: (s, _) => s.smoId,
                        );
                    return BlocProvider(
                      create: (context) => di.get<LottingConversionBloc>()
                        ..add(
                          LottingConversionEvent.started(
                            isLotting: true,
                            smoId: smoId,
                          ),
                        ),
                      child: SelectLottingSku(
                        mandiId: mandiId,
                        isLotting: true,
                      ),
                    );
                  },
                ),
                GoRoute(
                  path: 'conversion/delloting',
                  name: 'dellotingSku',
                  builder: (context, state) {
                    final mandiId =
                        int.tryParse(state.pathParameters['mandiId'] ?? '-1') ??
                            -1;
                    final smoContext = state.extra! as BuildContext;
                    final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                          orElse: () => -1,
                          success: (s, _) => s.smoId,
                        );
                    return BlocProvider(
                      create: (context) => di.get<LottingConversionBloc>()
                        ..add(
                          LottingConversionEvent.started(
                            isLotting: false,
                            smoId: smoId,
                          ),
                        ),
                      child: SelectLottingSku(
                        mandiId: mandiId,
                        isLotting: false,
                      ),
                    );
                  },
                ),
              ],
            ),
            // GoRoute(
            //   name: 'viewLoadingPlan',
            //   path: 'view-loading-plan',
            //   builder: (context, state) {
            //     return const Lotting();
            //   },
            // ),
            GoRoute(
              name: 'closeInventory',
              path: 'closeInventory',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                final smo = smoContext.read<SmoBloc>().state.maybeMap(
                      orElse: () => null,
                      success: (s) => s,
                    );

                return BlocProvider<CloseInventoryBloc>(
                  create: (context) => di.get<CloseInventoryBloc>(),
                  child: CloseInventory(
                    mandiId: mandiId,
                    smoId: smo!.smo.smoId,
                    isEditOnlyFromWeighingMachine: smo
                        .config.closingInventory.isEditOnlyFromWeighingMachine,
                  ),
                );
              },
            ),
            GoRoute(
              name: 'closeOps',
              path: 'closeOps',
              builder: (context, state) {
                final smoContext = state.extra! as BuildContext;
                final smoId = smoContext.read<SmoBloc>().state.maybeWhen(
                      orElse: () => -1,
                      success: (s, _) => s.smoId,
                    );
                final mandiId =
                    int.tryParse(state.pathParameters['mandiId'] ?? '-1') ?? -1;
                context.read<CloseOpsBloc>().add(
                      CloseOpsEvent.started(smoId),
                    );
                return CloseOps(
                  smoId: smoId,
                  mandiId: mandiId,
                  smoContext: smoContext,
                );
              },
            ),
          ],
        ),
      ],
    ),
  ],
);
