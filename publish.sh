#!/bin/bash

# Get the current version and version code from pubspec.yaml
version=$(grep 'version:' pubspec.yaml | cut -d ' ' -f 2)
versionCode=$(echo $version | cut -d '+' -f 2)
version=$(echo $version | cut -d '+' -f 1)

# Increment the version
newVersion=$(echo $version | awk -F. -v OFS=. '{$3++; if ($3 >= 10) {$2++; $3=0}; if ($2 >= 10) {$1++; $2=0}; print}')

# Increment the version code
newVersionCode=$((versionCode + 1))

# Replace the version in pubspec.yaml
sed -i '' "s/^version: .*/version: $newVersion+$newVersionCode/" pubspec.yaml

# Checkout the branch
git checkout -b release/$newVersion+$newVersionCode

# Run generate.sh command
fvm flutter clean
fvm flutter pub get
fvm dart run build_runner build --delete-conflicting-outputs

# Run release.sh command with argument prod
sh ./release.sh prod --shorebird

sh ./deploy-web.sh prod --no-clean

# Generate the lang file
#python ./lang_gen.py ./lib/

# Run git add . command
git add .

# Run git commit command with message 'Releasing version ${version name}'
git commit -m "Releasing version $newVersion+$newVersionCode"
git tag $newVersion+$newVersionCode
git push # Push the commit
git push --tags  # Push the tag
