import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_variants.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/terminal_distribution.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/row_item_field.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/row_item_value.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/text_styles.dart';

class ManualDistributionWidget extends StatefulWidget {
  const ManualDistributionWidget({
    super.key,
    this.padding,
    this.margin,
    required this.distribution,
    required this.isReadOnly,
    required this.isManualEdititngAllowed,
  });
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final TerminalDistribution distribution;
  final bool isReadOnly;
  final bool isManualEdititngAllowed;

  @override
  State<ManualDistributionWidget> createState() =>
      _ManualDistributionWidgetState();
}

class _ManualDistributionWidgetState extends State<ManualDistributionWidget> {
  bool isWastageReadOnly = true;
  @override
  Widget build(BuildContext context) {
    // double leftToAllocate = distribution.leftToAllocate;
    final isEditOnlyFromWeighingMachine =
        context.read<SmoBloc>().state.mapOrNull(
                  success: (s) =>
                      s.config.allocation.isEditOnlyFromWeighingMachine,
                ) ??
            false;
    return Container(
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 48,
                child: LangText(
                  'allocation.serialNumber',
                  'S.No',
                  textAlign: TextAlign.start,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                flex: 2,
                child: LangText(
                  'allocation.terminal',
                  'Terminal',
                  textAlign: TextAlign.start,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                flex: 1,
                child: LangText(
                  'allocation.allocated',
                  'Allocated',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                flex: 2,
                child: LangText(
                  'allocation.finalAllocation',
                  'Final Allocation',
                  textAlign: TextAlign.end,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: widget.distribution.distributionInfo.length,
            itemBuilder: (context, parentIndex) {
              final listOfItems =
                  widget.distribution.distributionInfo[parentIndex];
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    margin: EdgeInsets.only(bottom: 8, top: 8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      listOfItems.first.priority?.label ?? '-',
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue,
                      ).merge(TextStyles.heading),
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: listOfItems.length,
                    itemBuilder: (context, index) {
                      final info = listOfItems[index];
                      return Opacity(
                        opacity: info.readOnly ? 0.5 : 1.0,
                        child: Container(
                          decoration: BoxDecoration(
                            border: index == listOfItems.length - 1
                                ? null
                                : Border(
                                    bottom: BorderSide(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                            color: info.isAllocatedAndFineTunedDiff
                                ? Colors.amber.shade800.withAlpha(150)
                                : Colors.white,
                          ),
                          padding: EdgeInsets.only(
                            bottom: 8,
                            top: 8,
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 48,
                                    child: Text(
                                      info.orderSlNo?.toString() ?? '-',
                                      textAlign: TextAlign.start,
                                      style: TextStyles.heading,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          info.customerKey ?? '-',
                                          textAlign: TextAlign.start,
                                          style: TextStyles.heading,
                                        ),
                                        if (info.refId != null)
                                          Text(
                                            info.refId ?? '-',
                                            textAlign: TextAlign.start,
                                            style: TextStyles.heading,
                                          ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Text(
                                      info.skuQuantity.quantity?.asString() ??
                                          '-',
                                      textAlign: TextAlign.center,
                                      style: TextStyles.heading,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 16,
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: info.readOnly
                                        ? Text(
                                            '-',
                                            textAlign: TextAlign.right,
                                          )
                                        : Align(
                                            alignment: Alignment.centerRight,
                                            child: SizedBox(
                                              width: 100,
                                              child: WeighingQuantityButton(
                                                isReadOnly: widget.isReadOnly,
                                                popupBuilder: () {
                                                  return WeightCapturePopupAllocationDistribution(
                                                    isBulkKg: widget
                                                            .distribution
                                                            .skuQuantity
                                                            .isBulk &&
                                                        widget
                                                                .distribution
                                                                .skuQuantity
                                                                .unit
                                                                .toLowerCase() ==
                                                            'kg',
                                                    isManualEditAllowed:
                                                        !isEditOnlyFromWeighingMachine,
                                                    initialWeight:
                                                        (info.weighingQuantity
                                                                    ?.value ??
                                                                '')
                                                            .toDouble()
                                                            .asString()
                                                            .toDouble(),
                                                    terminalId:
                                                        info.customerKey ?? '',
                                                    allocatedQuantity: info
                                                            .skuQuantity
                                                            .quantity ??
                                                        0,
                                                  );
                                                },
                                                label: info.weighingQuantity
                                                        ?.value ??
                                                    '',
                                                onChange: (quantity) {
                                                  if (quantity != null) {
                                                    final newList = listOfItems
                                                        .map(
                                                          (e) => e != info
                                                              ? e
                                                              : e.copyWith(
                                                                  weighingQuantity:
                                                                      quantity,
                                                                  errorMessage:
                                                                      null,
                                                                ),
                                                        )
                                                        .toList();
                                                    final listOfList = widget
                                                        .distribution
                                                        .distributionInfo
                                                        .toList();
                                                    listOfList[parentIndex] =
                                                        newList;
                                                    context
                                                        .read<
                                                            AllocationJourneyCubit>()
                                                        .updateDistribution(
                                                          widget.distribution
                                                              .copyWith(
                                                            distributionInfo:
                                                                listOfList,
                                                          ),
                                                        );
                                                  }
                                                },
                                              ),
                                            ),
                                          ),
                                  ),
                                ],
                              ),
                              if (info.errorMessage != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: Text(
                                    info.errorMessage!,
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          ),
          SizedBox(
            height: 16,
          ),
          Container(
            color: Colors.grey.shade300,
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SizedBox(),
                  flex: 2,
                ),
                SizedBox(
                  width: 8,
                ),
                Expanded(
                  child: Text(
                    widget.distribution.totalAllocated.asString(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ).merge(TextStyles.heading),
                    textAlign: TextAlign.center,
                  ),
                  flex: 1,
                ),
                SizedBox(
                  width: 8,
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Text(
                      widget.distribution.totalAllocatingNow.asString(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ).merge(TextStyles.heading),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // if (leftToAllocate != 0)
          //   Align(
          //     alignment: Alignment.centerRight,
          //     child: Text(
          //       leftToAllocate > 0
          //           ? '${(leftToAllocate).asString()} left to allocate.'
          //           : 'Can not allocate more than ${distribution.totalAllocated.asString()}',
          //       style: TextStyle(
          //         color: Colors.red,
          //         fontSize: 12,
          //       ).merge(TextStyles.heading),
          //     ),
          //   ),
          SizedBox(
            height: 16,
          ),
          if (!widget.isReadOnly)
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (isWastageReadOnly)
                  Expanded(
                    child: RowItemValue(
                      label: 'wastage'.tr('Wastage'),
                      value: '',
                      padding: EdgeInsets.symmetric(
                        horizontal: 8,
                      ),
                    ),
                  ),
                if (!isWastageReadOnly)
                  Expanded(
                    child: RowItemField(
                      label: 'wastage'.tr('Wastage'),
                      value: widget.distribution.wastage,
                      inputFormatters: Config.numberInputFilters,
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        context
                            .read<AllocationJourneyCubit>()
                            .updateDistribution(
                              widget.distribution.copyWith(
                                wastage: value,
                              ),
                            );
                      },
                    ),
                  ),
                if (isWastageReadOnly) ...[
                  SizedBox(
                    width: 16,
                  ),
                  InkWell(
                    onTap: () {
                      setState(() {
                        isWastageReadOnly = !isWastageReadOnly;
                      });
                    },
                    child: Icon(
                      Icons.add,
                      size: 24,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                ],
              ],
            ),
        ],
      ),
    );
  }
}
