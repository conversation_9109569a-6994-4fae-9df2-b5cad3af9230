import 'dart:async';

import 'package:flutter/material.dart';

class SlotTimer extends StatefulWidget {
  const SlotTimer({super.key, required this.endTime});
  final DateTime endTime;
  @override
  State<SlotTimer> createState() => _SlotTimer();
}

class _SlotTimer extends State<SlotTimer> {
  late Timer _timer;

  int pendingHours = 0;
  int pendingMinutes = 0;
  int pendingSeconds = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      const oneSec = Duration(seconds: 1);
      _timer = Timer.periodic(
        oneSec,
        (Timer timer) {
          if (mounted) {
            setState(() {
              pendingHours = widget.endTime.difference(DateTime.now()).inHours;
              pendingHours = pendingHours < 0 ? 0 : pendingHours;
              pendingMinutes =
                  60 - (DateTime.now().minute - widget.endTime.minute);
              pendingMinutes = pendingMinutes < 0 ? 0 : pendingMinutes;
              pendingMinutes = pendingMinutes - 1;
              pendingSeconds =
                  60 - (DateTime.now().second - widget.endTime.second);
              pendingSeconds = pendingSeconds < 0 ? 0 : pendingSeconds;
            });
          } else {
            timer.cancel();
          }
        },
      );
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Slot closes in',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade400,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 5),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                showNumber(num: pendingHours.toString(), name: 'Hours'),
                const SizedBox(width: 10),
                const Text(
                  ':',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 10),
                showNumber(num: pendingMinutes.toString(), name: 'Minutes'),
                const SizedBox(width: 10),
                const Text(
                  ':',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 10),
                showNumber(num: pendingSeconds.toString(), name: 'Seconds'),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget showNumber({required String num, required String name}) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          num,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          name,
          style: TextStyle(
            color: Colors.grey.shade400,
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
        )
      ],
    );
  }
}
