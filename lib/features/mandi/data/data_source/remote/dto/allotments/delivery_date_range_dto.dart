import 'package:freezed_annotation/freezed_annotation.dart';

part 'delivery_date_range_dto.freezed.dart';
part 'delivery_date_range_dto.g.dart';

@freezed
class DeliveryDateRangeDto with _$DeliveryDateRangeDto {
  const factory DeliveryDateRangeDto({
    required int past,
    required int future,
  }) = _DeliveryDateRangeDto;

  @override
  factory DeliveryDateRangeDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryDateRangeDtoFromJson(json);
}
