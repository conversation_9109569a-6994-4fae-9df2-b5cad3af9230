import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_return_download_request.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/return_download/return_download_entity.dart';
import 'package:proc2/features/mandi/domain/use_case/get_all_mandis_usecase.dart';

part 'return_download_state.dart';
part 'return_download_cubit.freezed.dart';

class ReturnDownloadCubit extends Cubit<ReturnDownloadState> {
  final GetAllMandisUseCase _getAllMandisUseCase;

  ReturnDownloadCubit(this._getAllMandisUseCase)
      : super(ReturnDownloadState.initial()) {
    getAllMandis();
  }

  Future<void> getAllMandis() async {
    emit(state.copyWith(isLoading: true, message: null));

    final result = await _getAllMandisUseCase();

    result.fold(
      (error) {
        emit(state.copyWith(
          isLoading: false,
          message: error.toString(),
          allMandisList: [],
        ));
      },
      (list) {
        emit(state.copyWith(
          isLoading: false,
          allMandisList: list,
        ));
      },
    );
  }

  void setSelectedMandiId(int val) {
    emit(state.copyWith(selectedMandiId: val));
  }

  void setSelectedStartDateTime(DateTime value) {
    emit(state.copyWith(selectedStartDateTime: value));
  }

  void setSelectedEndDateTime(DateTime value) {
    emit(state.copyWith(selectedEndDateTime: value));
  }

  void setSelectedType(String type) {
    emit(state.copyWith(selectedType: type));
  }

  void clear() {
    emit(state.copyWith(
      message: null,
      selectedStartDateTime: null,
      selectedEndDateTime: null,
      selectedType: null,
      selectedMandiId: null,
    ));
  }

  Future<void> getReturns() async {
    final mandiId = state.selectedMandiId;
    final startDateTime = state.selectedStartDateTime;
    final endDateTime = state.selectedEndDateTime;
    final type = state.selectedType;

    emit(state.copyWith(returnList: []));

    if (mandiId == null) {
      emit(state.copyWith(message: 'Please Select Mandi...'));
      return;
    }
    if (state.isLoading || startDateTime == null || endDateTime == null) {
      emit(state.copyWith(message: 'Please Select Date...'));
      return;
    }
    if (type == null) {
      emit(state.copyWith(message: 'Please Select Type...'));
      return;
    }

    emit(state.copyWith(isLoading: true, message: null));

    final result = await GetReturnDownloadRequest(
      mandiId: mandiId,
      startDateTime: startDateTime.millisecondsSinceEpoch ~/ 1000,
      endDateTime: endDateTime.millisecondsSinceEpoch ~/ 1000,
      type: type,
    ).execute();

    final newState = result.fold(
      (left) => state.copyWith(isLoading: false, message: left.message),
      (right) => state.copyWith(isLoading: false, returnList: right),
    );

    emit(newState);
  }
}
