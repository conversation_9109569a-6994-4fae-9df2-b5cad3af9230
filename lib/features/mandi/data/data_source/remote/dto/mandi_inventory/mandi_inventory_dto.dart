import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/mandi_inventory/mandi_inventory_sku_dto.dart';

part 'mandi_inventory_dto.freezed.dart';
part 'mandi_inventory_dto.g.dart';

@freezed
class MandiInventoryDto with _$MandiInventoryDto {
  const factory MandiInventoryDto({
    required List<MandiInventorySkuDto> skuList,
  }) = _MandiInventoryDto;

  @override
  factory MandiInventoryDto.fromJson(Map<String, dynamic> json) =>
      _$MandiInventoryDtoFromJson(json);
}
