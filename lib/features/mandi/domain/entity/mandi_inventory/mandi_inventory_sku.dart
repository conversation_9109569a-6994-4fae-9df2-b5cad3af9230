import 'package:freezed_annotation/freezed_annotation.dart';

part 'mandi_inventory_sku.freezed.dart';

@freezed
class MandiInventorySku with _$MandiInventorySku {
  const MandiInventorySku._();

  const factory MandiInventorySku({
    required int skuId,
    required String type,
    required String unit,
    required double lotSize,
    required double quantity,
  }) = _MandiInventorySku;

  bool isBulk() => type.toLowerCase() == 'bulk';
}
