import 'package:proc2/core/data/network/base_request.dart';

class GetAllocationPdfRequest extends BaseRequest<dynamic, List<String>> {
  final int allocationId;
  final String customerGroup;
  final String pdfType;
  final List<String> selectedSkus;

  GetAllocationPdfRequest({
    required this.allocationId,
    required this.customerGroup,
    required this.pdfType,
    required this.selectedSkus,
  });

  @override
  String getPath() => 'deliveryMemos/getPdfs';

  @override
  Map<String, dynamic> getBody() {
    final body = {
      'allotmentId': allocationId,
      'pdfType': pdfType,
      'customerGroup': customerGroup,
    };

    if (selectedSkus.isNotEmpty) {
      body['skuQuantityList'] = selectedSkus.map(
        (e) {
          final splits = e.split('-');

          return {
            "skuId": splits[0],
            "type": splits[1],
            "unit": splits[2],
            "lotSize": splits.length == 3 ? null : splits[3],
          };
        },
      ).toList();
    }

    return body;
  }

  @override
  List<String> mapper(data) {
    if (data is Map<String, dynamic>) {
      final List<String> pdfs = [];
      if (pdfType == 'TRIPS') {
        if (data.containsKey('tripsMemoFile')) {
          try {
            final file = data['tripsMemoFile'] as String?;
            if (file != null) pdfs.add(file);
          } catch (_) {
            throw 'No pdfs found!';
          }
        }
      } else {
        if (data.containsKey('memoFiles')) {
          final pdfList = data['memoFiles'] as List<dynamic>;
          pdfs.addAll(pdfList.map((e) => e as String));
        }
      }

      if (pdfs.isEmpty) throw 'No pdfs found!';
      return pdfs;
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
