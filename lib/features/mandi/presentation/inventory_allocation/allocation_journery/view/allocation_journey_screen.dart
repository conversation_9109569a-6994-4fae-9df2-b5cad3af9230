import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_journey_stage.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/allocation_action_blocked_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/allocation_error_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/allocation_management_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/allocation_pdf_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/allocation_update_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/allocation_view_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/distribution_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/grouping_screen.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/screens/loading_screen.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';

class AllocationJourneyScreen extends StatefulWidget {
  const AllocationJourneyScreen({super.key});

  @override
  State<AllocationJourneyScreen> createState() =>
      _AllocationJourneyScreenState();
}

class _AllocationJourneyScreenState extends State<AllocationJourneyScreen> {
  bool canPop = false;
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final allSkus =
            context.read<SkuBloc>().state.mapOrNull(success: (s) => s.skus);
        if (allSkus != null) {
          context.read<AllocationJourneyCubit>().updateSkuMap(skus: allSkus);
        } else {
          context.read<SkuBloc>().add(const SkuEvent.fetch());
        }
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SkuBloc, SkuState>(
      listener: (context, state) {
        state.mapOrNull(
          success: (s) {
            context.read<AllocationJourneyCubit>().updateSkuMap(skus: s.skus);
          },
        );
      },
      child: BlocConsumer<AllocationJourneyCubit, AllocationJourneyState>(
        listener: (context, stage) {
          final message = stage.message;
          final shouldPop = stage.shouldPop;
          if (message != null) {
            showSnackBar(message,
                duration: Duration(
                  seconds: 5,
                ));
            context.read<AllocationJourneyCubit>().clearMessage();
          }
          if (shouldPop) {
            setState(() {
              canPop = true;
            });
            context.pop();
          }
        },
        builder: (context, state) {
          if (state.isActionsBlocked) {
            return const AllocationActionBlockedScreen();
          }
          if (state.stage == AllocationJourneyStage.pdf) {
            return AllocationPdfScreen(state: state);
          }
          final cubit = context.read<AllocationJourneyCubit>();

          return PopScope(
            canPop: state.isEditingAllowed ? canPop : !cubit.canMoveBack(),
            onPopInvoked: (didPop) async {
              if (didPop) return;
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                if (state.stage == AllocationJourneyStage.manualDistribution) {
                  if (!state.isEditingAllowed) {
                    cubit.moveBack();
                    return;
                  }
                  final value = await context.showAlertDialog(
                          title: 'distribution.goBackTitle'.tr('Are you sure?'),
                          message: 'distribution.goBackMessage'.tr(
                              'You have not submitted the distribution. Your data will be lost if you don\'t submit.')) ??
                      false;
                  if (value) {
                    cubit.unlockDistributionSku();
                    cubit.moveBack();
                  }
                  return;
                }
                if (cubit.canMoveBack()) {
                  cubit.moveBack();
                  return;
                }
                final value = await context.showAlertDialog(
                        title: 'exit'.tr('Exit?'),
                        message: 'exitMessage'
                            .tr('Are you sure you want to exit?')) ??
                    false;

                setState(() {
                  canPop = value;
                });
                if (value) {
                  context.pop();
                }
              });
            },
            child: Builder(
              builder: (context) {
                final stage = state.stage;
                if (stage == AllocationJourneyStage.loading) {
                  return LoadingScreen(state: state);
                } else if (stage == AllocationJourneyStage.grouping) {
                  return GroupingScreen(state: state);
                } else if (stage == AllocationJourneyStage.allocation) {
                  return AllocationUpdateScreen(state: state);
                } else if (stage == AllocationJourneyStage.view) {
                  return AllocationViewScreen(state: state);
                } else if (stage == AllocationJourneyStage.manualDistribution) {
                  return DistributionScreen(state: state);
                } else if (stage == AllocationJourneyStage.error) {
                  return AllocationErrorScreen(
                    state: state,
                  );
                } else if (stage == AllocationJourneyStage.management) {
                  return AllocationManagementScreen(
                    state: state,
                  );
                }
                return Container();
              },
            ),
          );
        },
      ),
    );
  }
}
