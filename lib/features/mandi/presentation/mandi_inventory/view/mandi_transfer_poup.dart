import 'package:flutter/material.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/delayed_button.dart';

class MandiTransferPopup extends StatelessWidget {
  const MandiTransferPopup({
    super.key,
    required this.onPressed,
    required this.title,
    required this.body,
    required this.message,
    required this.ctaTitle,
  });
  final VoidCallback onPressed;
  final String title;
  final String body;
  final String message;
  final String ctaTitle;

  @override
  Widget build(BuildContext context) {
    return Popup(
      title: title,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(body),
              Text(
                message,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        Spacer(),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: DelayedButton(
            label: Text(
              ctaTitle,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            delayInSeconds: 5,
            onPressed: onPressed,
          ),
        ),
      ],
      height: 0.4,
    );
  }
}
