import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/charges/charge.dart';

part 'field_charges.freezed.dart';
part 'field_charges.g.dart';

@freezed
class FieldCharges with _$FieldCharges {
  const factory FieldCharges({
    required List<Charge> charges,
    required List<String> files,
  }) = _FieldCharges;

  @override
  factory FieldCharges.fromJson(Map<String, dynamic> json) =>
      _$FieldChargesFromJson(json);
}
