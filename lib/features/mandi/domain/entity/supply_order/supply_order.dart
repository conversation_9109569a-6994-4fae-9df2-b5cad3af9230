import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/sku_quantity_dto.dart';

part 'supply_order.freezed.dart';
part 'supply_order.g.dart';

@freezed
class SupplyOrder with _$SupplyOrder {
  const SupplyOrder._();
  const factory SupplyOrder({
    @Default(false) bool isChecked,
    required String id,
    required int timestamp,
    required Customer customer,
    required List<SupplyOrderItem> items,
    @Default(0) int deliveryDate,
    @Default('') String deliverySlot,
    @Default(null) int? updatedAt,
    @Default('') String createdBy,
    @Default(null) String? updatedBy,
    @Default(null) String? type,
    @Default(null) DistributionVariation? distributions,
    @Default(null) Map<String, dynamic>? metadata,
  }) = _SupplyOrder;

  String get orderDateString =>
      timestamp.toDate('dd MMM, yyyy | hh:mm a', skipTimezone: true);
  String get deliveryDateString => deliveryDate.toDate('dd MMM, yyyy');
  String get updatedAtDateString =>
      updatedAt?.toDate('dd MMM, yyyy | hh:mm a', skipTimezone: true) ?? '';

  bool get isPreOrder => type == 'PREORDER';

  String? get refId => metadata?['shopifyOrderRefId'];

  factory SupplyOrder.fromJson(Map<String, dynamic> json) =>
      _$SupplyOrderFromJson(json);
}

@freezed
class Customer with _$Customer {
  const factory Customer({
    @Default('') String key,
    @Default('') String mobile,
    @Default('') String dropLocationLink,
    @Default(Address()) Address address,
    @Default('') String name,
    @Default('') String dropLocation,
    @Default('') String customerGroup,
    @Default(null) int? orderSlNo,
    required CustomerPriority? priority,
  }) = _Customer;

  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);
}

@freezed
class Address with _$Address {
  const factory Address({
    @Default('') String addressLine,
    @Default('') String village,
    @Default('') String taluka,
    @Default('') String district,
    @Default('') String state,
  }) = _Address;

  factory Address.fromJson(Map<String, dynamic> json) =>
      _$AddressFromJson(json);
}

@freezed
class SupplyOrderItem with _$SupplyOrderItem {
  const factory SupplyOrderItem({
    required SkuQuantityDto skuQuantity,
    required double orderedPrice,
  }) = _SupplyOrderItem;

  factory SupplyOrderItem.fromJson(Map<String, dynamic> json) =>
      _$SupplyOrderItemFromJson(json);
}

@freezed
class CustomerPriority with _$CustomerPriority {
  const factory CustomerPriority({
    required String label,
    required int displayPriority,
  }) = _CustomerPriority;

  factory CustomerPriority.fromJson(Map<String, dynamic> json) =>
      _$CustomerPriorityFromJson(json);
}

@freezed
class DistributionVariation with _$DistributionVariation {
  const DistributionVariation._();
  const factory DistributionVariation({
    required double? minValueVariation,
    required double? maxValueVariation,
  }) = _DistributionVariation;

  factory DistributionVariation.fromJson(Map<String, dynamic> json) =>
      _$DistributionVariationFromJson(json);

  bool validate({
    required double targetValue,
    required double valueToCheck,
  }) {
    if (minValueVariation == null && maxValueVariation == null) {
      return true;
    }

    final minTarget = minValueVariation == null
        ? null
        : targetValue * (1 - minValueVariation!);
    final maxTarget = maxValueVariation == null
        ? null
        : targetValue * (1 + maxValueVariation!);

    if (minTarget != null && valueToCheck < minTarget) {
      return false;
    }

    if (maxTarget != null && valueToCheck > maxTarget) {
      return false;
    }

    return true;
  }

  String? getRangeMessage({required double targetValue}) {
    if (minValueVariation == null && maxValueVariation == null) {
      return null;
    }

    final minTarget = minValueVariation == null
        ? null
        : targetValue * (1 - minValueVariation!);
    final maxTarget = maxValueVariation == null
        ? null
        : targetValue * (1 + maxValueVariation!);

    if (minTarget != null && maxTarget != null) {
      return 'The value should be between ${minTarget.asString()} and ${maxTarget.asString()}';
    }

    if (minTarget != null) {
      return 'The value should be greater than ${minTarget.asString()}';
    }

    if (maxTarget != null) {
      return 'The value should be less than ${maxTarget.asString()}';
    }

    return null;
  }
}
