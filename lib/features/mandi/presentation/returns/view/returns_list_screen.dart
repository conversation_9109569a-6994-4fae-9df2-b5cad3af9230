import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/presentation/detail/bloc/smo_bloc.dart';
import 'package:proc2/features/mandi/presentation/returns/cubit/returns_cubit.dart';
import 'package:proc2/features/mandi/presentation/returns/view/return_qr_code_popup.dart';

class ReturnsListScreen extends StatefulWidget {
  final int smoId;
  final int mandiId;
  final bool isHistory;
  final bool isWastageReturns;
  const ReturnsListScreen({
    super.key,
    required this.smoId,
    required this.mandiId,
    this.isHistory = false,
    required this.isWastageReturns,
  });

  @override
  State<ReturnsListScreen> createState() => _ReturnsListScreenState();
}

class _ReturnsListScreenState extends State<ReturnsListScreen> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          title: Text(
            widget.isHistory
                ? (widget.isWastageReturns
                    ? 'wastageReturnsHistory.title'
                        .tr('Wastage Returns: History')
                    : 'returnsHistoryScreen.title'.tr('Returns: History'))
                : (widget.isWastageReturns
                    ? 'wastageReturnsListScreen.title'.tr('Wastage Returns')
                    : 'returnsListScreen.title'.tr('Returns')),
          ),
          centerTitle: false,
          actions: widget.isHistory
              ? []
              : [
                  if (!widget.isWastageReturns) ...[
                    IconButton(
                      onPressed: () async {
                        await showDialog(
                          context: context,
                          builder: (_) => ReturnQrCodePopup(
                            smoId: widget.smoId,
                          ),
                        );
                      },
                      icon: Icon(Icons.qr_code),
                    ),
                    SizedBox(
                      width: 16,
                    ),
                  ],
                  IconButton(
                    onPressed: () {
                      context.push(
                        context.namedLocation(
                          'returnsHistory',
                          pathParameters: {
                            'mandiId': widget.mandiId.toString(),
                            'smoId': widget.smoId.toString(),
                          },
                          queryParameters: {
                            'isWastageReturns':
                                widget.isWastageReturns.toString(),
                          },
                        ),
                        extra: context,
                      );
                    },
                    icon: Icon(Icons.history),
                  ),
                  SizedBox(
                    width: 16,
                  ),
                ],
        ),
        floatingActionButton: null,
        body: BlocConsumer<ReturnsCubit, ReturnsState>(
          listener: (context, state) {
            if (state.message != null) {
              showSnackBar(state.message!);
              context.read<ReturnsCubit>().clearMessage();
            }
          },
          builder: (context, state) {
            final returns = state.returns;
            final returnsHistory = state.returnsHistory;
            if ((!widget.isHistory && returns == null) ||
                (widget.isHistory && returnsHistory == null))
              return Center(
                child: CircularProgressIndicator(),
              );
            if ((!widget.isHistory && returns!.isEmpty) ||
                (widget.isHistory && returnsHistory!.isEmpty))
              return EmptyScreen(
                message:
                    'returnsListScreen.emptyMessage'.tr('No Returns Found!'),
              );
            return RefreshIndicator(
              onRefresh: () {
                context.read<ReturnsCubit>().load(
                      widget.smoId,
                      isHistory: widget.isHistory,
                      isWastageReturns: widget.isWastageReturns,
                    );
                return Future.value();
              },
              child: Scrollbar(
                child: ListView.builder(
                  padding: EdgeInsets.only(bottom: 60),
                  itemCount: widget.isHistory
                      ? returnsHistory!.length
                      : returns!.length,
                  itemBuilder: (context, index) {
                    if (widget.isHistory) {
                      final item = returnsHistory![index];
                      return InkWell(
                        onTap: () {
                          final config = context
                              .read<SmoBloc>()
                              .state
                              .mapOrNull(success: (success) {
                            return widget.isWastageReturns
                                ? success.config.wastageReturns
                                : success.config.returns;
                          });
                          final isEditOnlyFromWeighingMachine =
                              config?.isEditOnlyFromWeighingMachine ?? false;
                          final hideSystemQuantity =
                              config?.hideSystemQuantity ?? false;
                          // if (item.isAccepted) {
                          context.push(
                            context.namedLocation('returnsSubmit',
                                pathParameters: {
                                  'mandiId': widget.mandiId.toString(),
                                  'smoId': widget.smoId.toString(),
                                },
                                queryParameters: {
                                  'isHistory': widget.isHistory.toString(),
                                  'isWastageReturns':
                                      widget.isWastageReturns.toString(),
                                  'isEditOnlyFromWeighingMachine':
                                      isEditOnlyFromWeighingMachine.toString(),
                                  'hideSystemQuantity':
                                      hideSystemQuantity.toString(),
                                }),
                            extra: {
                              'item': item,
                              'smoContext': context,
                            },
                          );
                          // }
                        },
                        child: Container(
                          margin:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          padding: EdgeInsets.only(
                            left: 8,
                            top: 8,
                            bottom: 8,
                            right: 8,
                          ),
                          decoration: BoxDecoration(
                            color: item.isRejected
                                ? Colors.grey.shade100
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: item.isRejected
                                ? []
                                : [
                                    BoxShadow(
                                      color: Colors.grey.withOpacity(0.2),
                                      blurRadius: 4,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                          ),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Column(
                                    children: [
                                      Icon(
                                        item.isSelfDrop
                                            ? Icons.trolley
                                            : Icons.directions_bus_outlined,
                                        color: item.isSelfDrop
                                            ? Colors.yellow.shade900
                                            : Colors.teal.shade900,
                                      ),
                                      SizedBox(
                                        height: 8,
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          vertical: 2,
                                          horizontal: 16,
                                        ),
                                        child: Text(
                                          item.returnType.capitalize(),
                                          style: TextStyle(
                                            color: item.isSelfDrop
                                                ? Colors.yellow.shade900
                                                : Colors.teal.shade900,
                                            fontSize: 12,
                                          ),
                                        ),
                                        decoration: BoxDecoration(
                                          color: item.isSelfDrop
                                              ? Colors.yellow.shade100
                                              : Colors.teal.shade100,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            color: item.isSelfDrop
                                                ? Colors.yellow.shade400
                                                : Colors.teal.shade400,
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Container(
                                    height: 50,
                                    width: 1,
                                    color: Colors.grey.shade300,
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                item.returnOrderId,
                                                style: TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 16,
                                            ),
                                            Text(
                                              item.returnStatus,
                                              style: TextStyle(
                                                color: item.isAccepted
                                                    ? Colors.green
                                                    : Colors.red,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  item.isSelfDrop
                                      ? SizedBox()
                                      : Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.phone,
                                              size: 18,
                                            ),
                                            SizedBox(
                                              width: 4,
                                            ),
                                            Text(
                                              item.driverPhone ?? '-',
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.calendar_month,
                                        size: 18,
                                      ),
                                      SizedBox(
                                        width: 4,
                                      ),
                                      Text(
                                        item.returnAt.toDate(
                                          'dd MMM | hh:mm a',
                                          skipTimezone: false,
                                        ),
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 4,
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                      );
                    } else {
                      final item = returns![index];

                      return InkWell(
                        onTap: () async {
                          final config = context
                              .read<SmoBloc>()
                              .state
                              .mapOrNull(success: (success) {
                            return widget.isWastageReturns
                                ? success.config.wastageReturns
                                : success.config.returns;
                          });
                          final isEditOnlyFromWeighingMachine =
                              config?.isEditOnlyFromWeighingMachine ?? false;
                          final hideSystemQuantity =
                              config?.hideSystemQuantity ?? false;
                          await context.push(
                            context.namedLocation(
                              'returnsSubmit',
                              pathParameters: {
                                'mandiId': widget.mandiId.toString(),
                                'smoId': widget.smoId.toString(),
                              },
                              queryParameters: {
                                'isHistory': widget.isHistory.toString(),
                                'isWastageReturns':
                                    widget.isWastageReturns.toString(),
                                'isEditOnlyFromWeighingMachine':
                                    isEditOnlyFromWeighingMachine.toString(),
                                'hideSystemQuantity':
                                    hideSystemQuantity.toString(),
                              },
                            ),
                            extra: {
                              'item': item,
                              'smoContext': context,
                            },
                          );
                          context.read<ReturnsCubit>().load(
                                widget.smoId,
                                isHistory: widget.isHistory,
                                isWastageReturns: widget.isWastageReturns,
                              );
                        },
                        child: Container(
                          margin:
                              EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          padding: EdgeInsets.only(
                            left: 8,
                            top: 8,
                            bottom: 8,
                            right: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Column(
                                    children: [
                                      Icon(
                                        item.isSelfDrop
                                            ? Icons.trolley
                                            : Icons.directions_bus_outlined,
                                        color: item.isSelfDrop
                                            ? Colors.yellow.shade900
                                            : Colors.teal.shade900,
                                      ),
                                      SizedBox(
                                        height: 8,
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          vertical: 2,
                                          horizontal: 16,
                                        ),
                                        child: Text(
                                          item.returnType.capitalize(),
                                          style: TextStyle(
                                            color: item.isSelfDrop
                                                ? Colors.yellow.shade900
                                                : Colors.teal.shade900,
                                            fontSize: 12,
                                          ),
                                        ),
                                        decoration: BoxDecoration(
                                          color: item.isSelfDrop
                                              ? Colors.yellow.shade100
                                              : Colors.teal.shade100,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            color: item.isSelfDrop
                                                ? Colors.yellow.shade400
                                                : Colors.teal.shade400,
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Container(
                                    height: 50,
                                    width: 1,
                                    color: Colors.grey.shade300,
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                item.returnOrderId,
                                                style: TextStyle(
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            SizedBox(
                                              width: 8,
                                            ),
                                            if (!widget.isHistory)
                                              Align(
                                                alignment: Alignment.topRight,
                                                child: PopupMenuButton(
                                                  itemBuilder: (context) {
                                                    return [
                                                      PopupMenuItem(
                                                        child: Text(
                                                            'returnsListScreen.rejectCta'
                                                                .tr('Reject')),
                                                        value: 'reject',
                                                      ),
                                                    ];
                                                  },
                                                  onSelected: (value) async {
                                                    if (value == 'reject') {
                                                      final shouldReject =
                                                          await context
                                                                  .showAlertDialog(
                                                                title: 'returnsListScreen.rejectPopupTitle'
                                                                    .tr('Reject?'),
                                                                message:
                                                                    'returnsListScreen.rejectPopupMessage'
                                                                        .tr(
                                                                  'Are you sure you want to reject this return?',
                                                                ),
                                                              ) ??
                                                              false;
                                                      if (shouldReject) {
                                                        context
                                                            .read<
                                                                ReturnsCubit>()
                                                            .reject(
                                                              index,
                                                              widget.smoId,
                                                              item.returnOrderId,
                                                              item.driversDetails,
                                                              item.returnType,
                                                              item.createdAt,
                                                            );
                                                      }
                                                    }
                                                  },
                                                  child: Icon(
                                                    Icons.more_vert_outlined,
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 8,
                                            ),
                                            Wrap(
                                              children: [
                                                for (final terminal
                                                    in item.terminals)
                                                  Container(
                                                    margin: EdgeInsets.only(
                                                      right: 8,
                                                      bottom: 4,
                                                    ),
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                      vertical: 2,
                                                      horizontal: 16,
                                                    ),
                                                    child: Text(
                                                      terminal,
                                                      style: TextStyle(
                                                        color: Colors
                                                            .grey.shade900,
                                                        fontSize: 12,
                                                      ),
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          Colors.grey.shade100,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12),
                                                      border: Border.all(
                                                        color: Colors
                                                            .grey.shade400,
                                                        width: 1,
                                                      ),
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  item.isSelfDrop
                                      ? SizedBox()
                                      : Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.phone,
                                              size: 18,
                                            ),
                                            SizedBox(
                                              width: 4,
                                            ),
                                            Text(
                                              item.driversDetails
                                                      ?.driverMobile ??
                                                  '-',
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.calendar_month,
                                        size: 18,
                                      ),
                                      SizedBox(
                                        width: 4,
                                      ),
                                      Text(
                                        item.createdAt.toDate(
                                          'dd MMM | hh:mm a',
                                          skipTimezone: true,
                                        ),
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 4,
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
