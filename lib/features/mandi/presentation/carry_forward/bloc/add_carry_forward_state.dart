part of 'add_carry_forward_bloc.dart';

@freezed
class AddCarryForwardState with _$AddCarryForwardState {
  const factory AddCarryForwardState.initial() = _Initial;
  const factory AddCarryForwardState.success(
    CarryForward carryForward,
    String? message,
    bool isCtaLoading,
  ) = SuccessCarryForward;
  const factory AddCarryForwardState.error(
    ErrorResult<dynamic> error,
    int smoId,
  ) = _Error;
  const factory AddCarryForwardState.submitted(String message) = _Submitted;
}
