class BugReport {
  final String id;
  final String userId;
  final String name;
  final String? email;
  final List<String> attachments;
  final String comment;
  final String logFile;
  final bool hasResolved;
  final String? createdAt;

  BugReport({
    required this.id,
    required this.userId,
    required this.name,
    this.email,
    required this.attachments,
    required this.comment,
    required this.logFile,
    required this.hasResolved,
    required this.createdAt,
  });

  String get title => '$name ${email == null ? '' : "[$email]"} $userId';
  String get subtitle => 'Created at $createdAt';

  // From Json
  factory BugReport.fromJson(String id, Map<dynamic, dynamic> json) {
    return BugReport(
      id: id,
      userId: json['userId'],
      name: json['name'],
      email: json['email'],
      attachments: List<String>.from(json['attachments'] ?? []),
      comment: json['comment'],
      logFile: json['logFile'] ?? '',
      hasResolved: json['hasResolved'],
      createdAt: json['createdAt'],
      // updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'name': name,
      'email': email,
      'attachments': attachments,
      'comment': comment,
      'logFile': logFile,
      'hasResolved': hasResolved,
    };
  }
}
