import 'dart:collection';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/recieve_inventory/sku_quantity_dto.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/update_supply_order_request.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/entity/supply_order/supply_order.dart';
import 'package:proc2/features/mandi/presentation/supply_order/edit_supply_order/supply_order_input.dart';

part 'edit_supply_order_cubit.freezed.dart';
part 'edit_supply_order_state.dart';

@injectable
class EditSupplyOrderCubit extends Cubit<EditSupplyOrderState> {
  EditSupplyOrderCubit()
      : super(EditSupplyOrderState(allAddedSkusCompositeKeys: HashSet()));

  void preload(List<SupplyOrder> supplyOrders, Map<int, Sku> skuMap) {
    final result = createTerminalListFromSupplyOrder(supplyOrders, skuMap);
    emit(state.copyWith(
      supplyOrders: supplyOrders,
      terminals: result.terminals,
      allAddedSkusCompositeKeys: result.allCompositeKeys,
    ));
  }

  void onCheckboxValueChanged({required bool? value, required int index}) {
    final terminal = state.terminals;
    final terminal0SkuIndexValue = terminal[0].skus[index].quantity;
    final isSelected = value ?? false;
    final newList = isSelected
        ? terminal.indexed
            .map(
              (t) => t.$2.copyWith(
                skus: t.$2.skus.indexed
                    .map((s) => index != s.$1
                        ? s.$2
                        : s.$2.copyWith(
                            isChecked: t.$1 == 0,
                            quantity: terminal0SkuIndexValue,
                          ))
                    .toList(),
              ),
            )
            .toList()
        : [
            terminal[0].copyWith(
              skus: terminal[0]
                  .skus
                  .indexed
                  .map((e) =>
                      e.$1 == index ? e.$2.copyWith(isChecked: false) : e.$2)
                  .toList(),
            ),
            ...terminal.sublist(1),
          ];
    emit(state.copyWith(terminals: newList));
  }

  void onQuantityChanged(
      {required String value,
      required int columnIndex,
      required int rowIndex}) {
    final terminal = state.terminals;
    final shouldCopyAcross =
        columnIndex == 0 && terminal[columnIndex].skus[rowIndex].isChecked;
    final newTerminal = terminal.indexed
        .map(
          (e) => e.$1 == columnIndex || shouldCopyAcross
              ? e.$2.copyWith(
                  skus: e.$2.skus.indexed
                      .map(
                        (r) => r.$1 == rowIndex
                            ? r.$2.copyWith(
                                quantity: value,
                                skuQuantity: r.$2.skuQuantity
                                    .copyWith(quantity: value.toDouble()))
                            : r.$2,
                      )
                      .toList(),
                )
              : e.$2,
        )
        .toList();
    emit(state.copyWith(terminals: newTerminal));
  }

  void addSkuEntry(List<SupplyOrderSku> newSkuEntry) {
    final terminals = state.terminals.indexed.map((e) {
      final index = e.$1;
      final terminal = e.$2;
      return terminal.copyWith(skus: [...terminal.skus, newSkuEntry[index]]);
    }).toList();
    final newCompositeKey = newSkuEntry[0].skuQuantity.compositeKey;

    final newHashSet = HashSet.of(state.allAddedSkusCompositeKeys);
    newHashSet.add(newCompositeKey);
    emit(state.copyWith(
      terminals: terminals,
      allAddedSkusCompositeKeys: newHashSet,
    ));
  }

  void updateSkuEntry(
      {required List<SupplyOrderSku> entry, required int index}) {
    final terminals = state.terminals.indexed.map((e) {
      final terminalIndex = e.$1;
      final terminal = e.$2;
      return terminal.copyWith(skus: [
        ...(terminal.skus.sublist(0, index)),
        entry[terminalIndex],
        ...(terminal.skus.sublist(index + 1))
      ]);
    }).toList();
    final newCompositeKey = entry[0].skuQuantity.compositeKey;

    final newHashSet = HashSet.of(state.allAddedSkusCompositeKeys);
    newHashSet.add(newCompositeKey);
    emit(state.copyWith(
      terminals: terminals,
      allAddedSkusCompositeKeys: newHashSet,
    ));
  }

  Future<void> update() async {
    emit(state.copyWith(isUpdating: true));
    final result = await UpdateSupplyOrderRequest(state.supplyOrders,
            terminals: state.terminals)
        .execute();
    final newState = result.fold(
        (left) => state.copyWith(isUpdating: false, message: left.message),
        (right) =>
            state.copyWith(isUpdating: false, message: right, shouldPop: true));
    emit(newState);
  }

  void clearMessage() {
    emit(state.copyWith(message: null, shouldPop: false));
  }
}

TerminalsWithCompositeKeyResult createTerminalListFromSupplyOrder(
    List<SupplyOrder> supplyOrders, Map<int, Sku> skuMap) {
  final listMap = <Map<String, String>>[];
  for (final order in supplyOrders) {
    final mp = <String, String>{};
    for (final item in order.items) {
      mp[item.skuQuantity.compositeKey] = item.skuQuantity.quantity > 0
          ? item.skuQuantity.quantity.toString()
          : '';
    }
    listMap.add(mp);
  }

  final mapOfAllSkus = <String, SkuQuantityDto>{};
  supplyOrders.forEach((element) {
    element.items.forEach((element) {
      if (skuMap[element.skuQuantity.skuId] != null) {
        mapOfAllSkus[element.skuQuantity.compositeKey] = element.skuQuantity;
      }
    });
  });

  final allCompositeKeys = HashSet.of(mapOfAllSkus.keys);

  final allSkus = mapOfAllSkus.values.toList();
  allSkus.sort((a, b) => a.skuId.compareTo(b.skuId));

  final terminals = supplyOrders.indexed.map((o) {
    final terminalIndex = o.$1;
    final order = o.$2;

    return Terminal(
      terminalName: order.customer.key,
      isEditAllowed: !order.isPreOrder,
      skus: allSkus.map((sku) {
        final wasOrdered = listMap[terminalIndex].containsKey(sku.compositeKey);
        final quantity = listMap[terminalIndex][sku.compositeKey] ?? '';
        final updatedSku = sku.copyWith(quantity: quantity.toDouble());
        return SupplyOrderSku(
          skuQuantity: updatedSku,
          skuName: skuMap[sku.skuId]?.name ?? '',
          quantity: quantity,
          initialValue: quantity,
          wasOrdered: wasOrdered,
        );
      }).toList(),
    );
  }).toList();
  return TerminalsWithCompositeKeyResult(
    terminals: terminals,
    allCompositeKeys: allCompositeKeys,
  );
}

class TerminalsWithCompositeKeyResult {
  final List<Terminal> terminals;
  final HashSet<String> allCompositeKeys;

  TerminalsWithCompositeKeyResult({
    required this.terminals,
    required this.allCompositeKeys,
  });
}
