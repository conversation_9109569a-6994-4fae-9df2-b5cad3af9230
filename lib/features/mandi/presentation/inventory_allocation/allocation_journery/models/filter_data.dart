import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';

part 'filter_data.freezed.dart';

@freezed
class FilterData with _$FilterData {
  const FilterData._();
  const factory FilterData({
    @Default('') String searchQuery,
    @Default(null) AllocationFilter? allocationFilter,
    @Default(null) GroupAllocationFilter? selectedGroupAllocationFilter,
    @Default([]) List<String> selectedSkuIds,
  }) = _FilterData;

  bool shouldShow(AllocationSkuItem item) {
    if (searchQuery.isEmpty &&
        allocationFilter == null &&
        selectedGroupAllocationFilter == null &&
        selectedSkuIds.isEmpty) {
      return true;
    }
    bool shouldShow = true;
    if (searchQuery.isNotEmpty) {
      shouldShow = shouldShow &&
          item.skuName.toLowerCase().contains(searchQuery.toLowerCase());
    }
    if (shouldShow && allocationFilter != null) {
      switch (allocationFilter!) {
        case AllocationFilter.expected:
          shouldShow = shouldShow &&
              item.backendAllocationQty.expectedQty.toDouble() > 0;
          break;
        case AllocationFilter.pending:
          shouldShow = shouldShow && item.isPending;
          break;
        case AllocationFilter.ready:
          shouldShow = shouldShow && item.isReady;
          break;
        // case AllocationFilter.manual:
        //   shouldShow = shouldShow && item.isManuallyEdited;
        //   break;
        default:
          break;
      }
    }

    if (shouldShow && selectedSkuIds.isNotEmpty) {
      shouldShow =
          shouldShow && selectedSkuIds.contains(item.skuQuantity.compositeKey);
    }

    if (shouldShow && selectedGroupAllocationFilter != null) {
      shouldShow = shouldShow &&
          item.groupKey == selectedGroupAllocationFilter!.groupKey;
    }

    return shouldShow;
  }
}

enum AllocationFilter implements DropDownItem {
  expected,
  pending,
  ready;

  @override
  String get displayText {
    switch (this) {
      case AllocationFilter.expected:
        return 'Expected';
      case AllocationFilter.pending:
        return 'Pending';
      case AllocationFilter.ready:
        return 'Ready';
    }
  }
}

class GroupAllocationFilter implements DropDownItem {
  final String groupKey;
  GroupAllocationFilter({required this.groupKey});
  @override
  String get displayText {
    return groupKey;
  }

  @override
  bool operator ==(Object other) {
    if (other is GroupAllocationFilter) {
      return groupKey == other.groupKey;
    }
    return false;
  }

  @override
  int get hashCode => groupKey.hashCode;
}
