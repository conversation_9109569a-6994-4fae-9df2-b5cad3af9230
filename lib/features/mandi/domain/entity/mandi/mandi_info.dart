import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:animated_custom_dropdown_v2/custom_dropdown.dart';

part 'mandi_info.freezed.dart';

@freezed
class MandiInfo with _$MandiInfo implements DropDownItem {
  const MandiInfo._();
  const factory MandiInfo({
    required int id,
    required String name,
    required MandiType type,
    required String key,
  }) = _MandiInfo;

  static const empty = MandiInfo(
    id: -1,
    name: '',
    type: MandiType.mandi,
    key: '',
  );

  @override
  String get displayText => name;
}

enum MandiType {
  @JsonValue('MANDI')
  mandi,
  @JsonValue('CORPORATE')
  corporate,
  @JsonValue('PITSTOP')
  pitstop,
}
