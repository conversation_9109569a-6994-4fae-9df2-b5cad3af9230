part of 'manage_users_cubit.dart';

@freezed
class ManageUsersState with _$ManageUsersState {
  const factory ManageUsersState.initial({
    @Default([]) List<ProcUser> userList,
    @Default(null) ProcUser? selectedUser,
    @Default(null) String? message,
    @Default(false) bool isLoading,
    @Default(null) UserMandi? userMandi,
    @Default([]) List<UserRole> roleList,
    @Default([]) List<UserRole> selectedUserRoles,
    @Default(null) String? userFirstName,
    @Default(null) String? userLastName,
    @Default(null) String? userEmail,
    @Default(null) String? userMobile,
  }) = _Initial;
}
