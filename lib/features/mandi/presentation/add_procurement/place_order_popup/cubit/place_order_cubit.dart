import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';
import 'package:uuid/uuid.dart';

part 'place_order_cubit.freezed.dart';
part 'place_order_state.dart';

@injectable
class PlaceOrderCubit extends Cubit<PlaceOrderState> {
  PlaceOrderCubit() : super(PlaceOrderState.empty);

  void updateSku(Sku? sku) {
    emit(_validate(state.copyWith(sku: sku)));
  }

  void disableAmountEdit(bool shouldDisable) {
    emit(state.copyWith(disableAmountEdit: shouldDisable));
  }

  void disableReceiveQty(bool value) {
    emit(state.copyWith(disableReceiveQty: value));
  }

  void updateType(ProcurementType type) {
    if (state.procurementType == type) return;
    emit(
      _validate(
        state.copyWith(
          procurementType: type,
          procurementUnit: null,
          lotSize: null,
        ),
      ),
    );
  }

  void updateUnit(unit) {
    if (state.procurementUnit == unit) return;
    emit(
      _validate(
        state.copyWith(
          procurementUnit: unit,
          lotSize: null,
        ),
      ),
    );
  }

  void updateLotSize(lotSize) {
    if (lotSize == state.lotSize) return;
    emit(
      _validate(
        state.copyWith(
          lotSize: lotSize,
        ),
      ),
    );
  }

  void updateOrderedQty(String? qty) {
    emit(
      _validate(
        state.copyWith(
          orderedQuantity: qty,
          receivedQuantity: state.isFinal ? qty : state.receivedQuantity,
        ),
      ),
    );
  }

  void updateReceivedQty(String? qty, String? source) {
    emit(
      _validate(
        state.copyWith(
          receivedQuantity: qty,
          weighingSource: source,
        ),
      ),
    );
  }

  void updateFinal(bool isFinal) {
    emit(
      _validate(
        state.copyWith(
          isFinal: isFinal,
          receivedQuantity: isFinal && state.newlyAdded
              ? state.orderedQuantity
              : state.receivedQuantity,
        ),
      ),
    );
  }

  void updateWeight(String? weight) {
    emit(
      _validate(
        state.copyWith(
          weight: weight,
        ),
      ),
    );
  }

  void updateAmount(String? amount) {
    emit(
      _validate(
        state.copyWith(amount: amount),
      ),
    );
  }

  void updateNotAllowedSkus(List<String> notAllowedSkus) {
    emit(
      _validate(
        state.copyWith(
          notAllowedSku: notAllowedSkus,
        ),
      ),
    );
  }

  editOrder(SkuInputData input, List<String> notAllowedSkus) {
    emit(
      _validate(
        state.copyWith(
          notAllowedSku: notAllowedSkus,
          newlyAdded: input.newlyAdded,
          amount: input.amount,
          isFinal: input.isFinal,
          isOpenForEdit: true,
          lotSize: input.lotSize,
          orderedQuantity: input.orderedQuantity,
          previousReceivedQuantity: input.quantity,
          receivedQuantity: input.newReceivedQty,
          procurementType:
              input.isBulk() ? ProcurementType.bulk : ProcurementType.lots,
          uuid: input.uuid,
          weight: input.weight,
          procurementUnit: input.procurementUnit,
          sku: input.sku,
          procItemId: input.procItemId,
        ),
      ),
    );
  }

  SkuInputData getSkuInputData() {
    return SkuInputData(
      sku: state.sku!,
      procurementType: state.procurementType,
      procurementUnit: state.procurementUnit!,
      lotSize: state.lotSize,
      orderedQuantity: state.orderedQuantity,
      newReceivedQty: state.receivedQuantity,
      quantity: state.previousReceivedQuantity,
      amount: state.amount,
      weight: state.weight,
      uuid: state.uuid,
      isFinal: state.isFinal,
      newlyAdded: state.newlyAdded,
      procItemId: state.procItemId,
      weighingSource: state.weighingSource,
    );
  }

  PlaceOrderState _validate(PlaceOrderState state) {
    bool isDuplicate = state.notAllowedSku.contains(getCompositeKey(state));
    bool hasInvalidData = state.sku == null ||
        state.procurementUnit == null ||
        state.procurementUnit!.isEmpty ||
        !state.isBulk && state.lotSize == null ||
        state.orderedQuantity == null ||
        state.orderedQuantity!.isEmpty ||
        isDuplicate;
    return state.copyWith(
      isCtaActive: !hasInvalidData,
      isDuplicate: isDuplicate,
    );
  }

  String getCompositeKey(PlaceOrderState state) {
    final baseKey =
        '${state.sku?.id}-${state.procurementType.value}-${state.procurementUnit}';
    if (state.isBulk) {
      return baseKey;
    } else {
      return '$baseKey-${state.lotSize}';
    }
  }
}
