import 'package:flutter/material.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';

class AllocationActionBlockedScreen extends StatelessWidget {
  const AllocationActionBlockedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          centerTitle: false,
          title:
              Text('allocationActionBlocked'.tr('Allocation Action Blocked')),
        ),
        body: Column(
          children: [
            Expanded(
              child: ErrorScreen(
                heading:
                    'allocationActionBlocked'.tr('Allocation Action Blocked'),
                message: 'allocationActionBlockedMessage'.tr(
                    'Actions are blocked for this allocation. Please contact your admin.'),
                onPressed: null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
