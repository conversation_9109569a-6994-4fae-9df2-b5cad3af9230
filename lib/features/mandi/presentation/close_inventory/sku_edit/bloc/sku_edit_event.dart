part of 'sku_edit_bloc.dart';

@freezed
class SkuEditEvent with _$SkuEditEvent {
  const factory SkuEditEvent.init(
    SkuInputData skuInputData, {
    @Default(false) bool isOpenForEdit,
  }) = _Started;
  const factory SkuEditEvent.updateProcurementType(
      ProcurementType procurementType) = _UpdateProcurementType;
  const factory SkuEditEvent.updateUnit(String? unit) = _UpdateUnit;
  const factory SkuEditEvent.updateLotSize(double? lotSize) = _UpdateLotSize;
  const factory SkuEditEvent.updateQuanity(String? quantity, String? source) =
      _UpdateQuantity;
  const factory SkuEditEvent.updateAmount(String? amount) = _UpdateAmount;
  const factory SkuEditEvent.updateWeight(String? weight) = _UpdateWeight;
}
