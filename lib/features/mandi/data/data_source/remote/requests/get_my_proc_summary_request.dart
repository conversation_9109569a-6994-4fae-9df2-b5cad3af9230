import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/my_proc_summary.dart';

class GetMyProcSummaryRequest
    extends BaseRequest<dynamic, List<MyProcSummary>> {
  final int smoId;

  GetMyProcSummaryRequest(this.smoId);

  @override
  String getPath() => 'procurements/summary/smo/$smoId';

  @override
  List<MyProcSummary> mapper(data) {
    if (data is List<dynamic>) {
      return data
          .map((e) => e as Map<String, dynamic>)
          .map((e) => MyProcSummary.fromJson(e))
          .toList();
    }
    throw Exception('Invalid data type');
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
