import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/data/data_source/remote/dto/allotments/delivery_memo_dto.dart';

part 'allotment_dto.freezed.dart';
part 'allotment_dto.g.dart';

@freezed
class AllotmentDto with _$AllotmentDto {
  const factory AllotmentDto({
    required int id,
    required int smoId,
    required String? destinationType,
    @Default(null) String? customerGroup,
    @Default('') String deliverySlot,
    @Default(0) int deliveryDate,
    required int destinationId,
    required String status,
    @Default([]) List<String> processStages,
    @Default([]) List<String> actions,
    required String consignmentId,
    DeliveryMemoDto? deliveryMemo,
  }) = _AllotmentDto;

  @override
  factory AllotmentDto.fromJson(Map<String, dynamic> json) =>
      _$AllotmentDtoFromJson(json);
}
