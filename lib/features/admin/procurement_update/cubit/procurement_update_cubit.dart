import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/di/di.dart';
import 'package:proc2/core/presentation/vendor/cubit/vendor_cubit.dart';
import 'package:proc2/features/admin/procurement_update/model/procurement_order.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_proc_detail_request.dart';
import 'package:proc2/features/mandi/domain/entity/mandi/mandi_info.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_detail.dart';

part 'procurement_update_state.dart';

@injectable
class ProcurementUpdateCubit extends Cubit<ProcurementUpdateState> {
  ProcurementUpdateCubit() : super(ProcurementUpdateState.initial());

  void updateSelectedMandi({
    required MandiInfo mandi,
    required int smoId,
  }) {
    emit(state.copyWith(selectedMandi: mandi, selectedSmoId: smoId, isSearchLoading: false, procurementOrders: null));
  }

  void updateStatus({required String status}) {
    emit(state.copyWith(selectedStatus: status, isSearchLoading: false, procurementOrders: null));
  }

  Future<void> search({int offset = 0, int limit = 10}) async {
    if (state.selectedMandi == null) return;
    emit(state.copyWith(isSearchLoading: true));
    final result = await GetMyProcDetailRequest(smoId: state.selectedSmoId!, isAll: true, status: state.selectedStatus, limit: limit, offset: offset).execute();
    result.fold(
      (l) => emit(state.copyWith(isSearchLoading: false, message: l.message)),
      (r) => emit(state.copyWith(
        isSearchLoading: false,
        procurementOrders: _toProcurementOrderList(r),
        message: null,
      )),
    );
  }

  List<ProcurementOrder> _toProcurementOrderList(List<ProcDetail> data) {
    final vendor = di.get<VendorCubit>();
    return data
        .where((e) => e.isParentOrder)
        .map((e) => ProcurementOrder(
              createdAt: e.createdAt,
              createdBy: e.procuredBy,
              id: e.id,
              vendor: e.vendorLocationId != null ? vendor.getVendorFromLocation(e.vendorLocationId!) : null,
              pitstops: e.allChildOrders.map((e) => e.mandiId).toList(),
              skus: e.orderedSkuIds ?? <int>[],
              smoId: e.smoId,
              status: e.status,
              items: e.items,
            ))
        .toList();
  }

  void clearMessage() {
    emit(state.copyWith(message: null));
  }
}
