import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/uploader/image_picker_screen.dart';
import 'package:proc2/core/presentation/vendor/view/vendor_drop_down.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/presentation/widgets/weight_summary_card.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/core/utils/ui/vendor_text_field.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order.dart';
import 'package:proc2/features/mandi/domain/entity/vendor/vendor.dart';
import 'package:proc2/features/mandi/presentation/liquidation/cubit/update_liquidation_cubit.dart';
import 'package:proc2/features/mandi/presentation/liquidation/view/update_liquidation_sku_popup.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/util/get_sku_name.dart';

class UpdateLiquidationScreen extends StatefulWidget {
  const UpdateLiquidationScreen({
    super.key,
    required this.smoId,
    required this.mandiId,
    required this.order,
    required this.itemId,
    required this.isEditOnlyFromWeighingMachine,
  });
  final int smoId;
  final int mandiId;
  final LiquidationOrder? order;
  final int? itemId;
  final bool isEditOnlyFromWeighingMachine;

  @override
  State<UpdateLiquidationScreen> createState() =>
      _UpdateLiquidationScreenState();
}

class _UpdateLiquidationScreenState extends State<UpdateLiquidationScreen> {
  final TextEditingController _vendorController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  bool textFieldUpdated = true;

  VendorLocation? _selectedVendorLocation;

  @override
  void dispose() {
    _vendorController.dispose();
    _mobileController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    final skuState = context.read<SkuBloc>().state;
    if (widget.order != null) {
      skuState.maybeMap(
          orElse: () {},
          success: (s) {
            WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
              context
                  .read<UpdateLiquidationCubit>()
                  .init(widget.order!, s.skus);
              _vendorController.text = widget.order!.vendor.name;
              _mobileController.text = widget.order!.vendor.mobile.toString();
            });
          });
    } else if (widget.itemId != null) {
      textFieldUpdated = false;
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        skuState.maybeMap(
            orElse: () {},
            success: (s) {
              WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                context
                    .read<UpdateLiquidationCubit>()
                    .loadByItemId(widget.itemId!, s.skus);
              });
            });
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        color: Colors.white,
        child: BlocConsumer<UpdateLiquidationCubit, UpdateLiquidationState>(
          listener: (context, state) {
            bool shouldPop = state.shouldPop;
            if (state.message != null) {
              showSnackBar(state.message!);
              context.read<UpdateLiquidationCubit>().clearMessage();
            }
            if (shouldPop) {
              context.pop();
            }
            if (!textFieldUpdated && state.order != null) {
              setState(() {
                textFieldUpdated = true;
              });
              _vendorController.text = state.order!.vendor.name;
              _mobileController.text = state.order!.vendor.mobile.toString();
            }
          },
          builder: (context, state) {
            if (state.shouldShowLoading)
              return Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              );
            return Scaffold(
              backgroundColor: Colors.grey.shade100,
              appBar: WAppBar.getAppBar(
                centerTitle: false,
                title: Text(
                  state.isCreateOrder
                      ? 'Create Liquidation'
                      : state.order?.status.toLowerCase() != 'completed'
                          ? 'Update Liquidation'
                          : "Liquidation Order",
                ),
                elevation: 0,
              ),
              body: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Container(
                    color: Colors.green,
                    padding: const EdgeInsets.all(0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if (state.isCreateOrder) ...[
                          SizedBox(
                            width: 16,
                          ),
                          _headerButton('Add Sku ${state.skuCountText}',
                              () async {
                            if (state.isCtaLoading) return;
                            final result = await showDialog(
                              context: context,
                              builder: (ctx) => UpdateLiquidationSkuPopup(
                                showDelete: false,
                                isCreating: true,
                                smoId: widget.smoId,
                                notAllowedSkus: state.notAllowedSkus,
                                isEditOnlyFromWeighingMachine:
                                    widget.isEditOnlyFromWeighingMachine,
                              ),
                            );
                            if (result != null) {
                              context
                                  .read<UpdateLiquidationCubit>()
                                  .addOrder(result);
                            }
                          }),
                        ],
                        SizedBox(
                          width: 8,
                        ),
                        if (state.order?.status.toLowerCase() != 'completed')
                          _headerButton('Upload Slips ${state.imageCountText}',
                              () async {
                            if (state.isCtaLoading) return;

                            final result = await Navigator.push<PickerResult?>(
                              context,
                              // ignore: inference_failure_on_instance_creation
                              MaterialPageRoute(
                                builder: (context) => ImagePickerScreen(
                                  pageTitle: getLangText(
                                      'updateLiquidation.uploadSlipsTitle',
                                      'Upload Slips'),
                                  reqBody: {'smoId': widget.smoId},
                                  module: UploadFileModule.liquidationOrder,
                                  allowMultiple: true,
                                  initialImages: state.files,
                                ),
                              ),
                            );
                            if (result != null) {
                              if (result.message.isNotEmpty) {
                                showSnackBar(result.message);
                              }
                              context
                                  .read<UpdateLiquidationCubit>()
                                  .updateFiles(result.files);
                            }
                          }),
                      ],
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 16,
                      ),
                      child: state.isCreateOrder
                          ? VendorDropDown(
                              isEnabled: true,
                              selectedVendorLocation: _selectedVendorLocation,
                              onChanged: (value) {
                                _selectedVendorLocation = value;
                                _vendorController.text = value.vendorName;
                                _mobileController.text = value.vendorPhone;
                                setState(() {});
                              },
                              enableNewCreation: false,
                            )
                          : Row(
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: VendorTextField(
                                    controller: _vendorController,
                                    isEnabled: state.isCreateOrder,
                                    onChanged: (value) {
                                      _vendorController.text = value;
                                    },
                                    onMobileChanged: (value) {
                                      _mobileController.text = value;
                                    },
                                  ),
                                ),
                                SizedBox(
                                  width: 16,
                                ),
                                Expanded(
                                  flex: 2,
                                  child: TextFormField(
                                    enabled: state.isCreateOrder,
                                    maxLength: 10,
                                    controller: _mobileController,
                                    decoration: InputDecoration(
                                        labelText: getLangText(
                                            'updateLiquidation.vendorMobile',
                                            'Vendor Mobile*'),
                                        border: OutlineInputBorder(),
                                        isDense: true,
                                        contentPadding: EdgeInsets.only(
                                          left: 8,
                                          right: 4,
                                          top: 12,
                                          bottom: 12,
                                        ),
                                        counter: SizedBox.shrink()),
                                    onChanged: (val) {},
                                    keyboardType: TextInputType.number,
                                    textInputAction: TextInputAction.next,
                                    inputFormatters: Config.numberInputFilters,
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                  Container(
                    color: Colors.grey[300],
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 4.0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 3,
                            child: LangText(
                              'unit',
                              'Unit',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'totalQty',
                              'Total Qty',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'amt',
                              'Amt',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            flex: 2,
                            child: LangText(
                              'receivedAmount',
                              'Rec Amt',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ),
                          SizedBox(
                            width: 48,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: state.inputOrders.isEmpty
                        ? EmptyScreen(
                            message: 'Please add skus!',
                          )
                        : Scrollbar(
                            child: ListView(
                              children: [
                                for (final list in state.inputOrders.values)
                                  skuCard(list, state)
                              ],
                            ),
                          ),
                  ),
                  if (state.summaryItems.isNotEmpty)
                    WeightSummaryCard(
                      items: state.summaryItems,
                      padding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                  if (state.inputOrders.isNotEmpty &&
                      state.order?.status.toLowerCase() == 'completed')
                    AmountSummary(
                        state.inputOrders.values.expand((element) => element)),
                  if (state.order?.status.toLowerCase() != 'completed')
                    WStickyBottomCta(
                      isEnabled: state.inputOrders.isNotEmpty &&
                          !state.isCtaLoading &&
                          _vendorController.text.isNotEmpty &&
                          _mobileController.text.length == 10,
                      isLoading: state.isCtaLoading,
                      icon: Icons.check,
                      label: Text('Submit'),
                      onPressed: () async {
                        final result = await context.showAlertDialog(
                                title: 'Are you sure?',
                                message: state.isCreateOrder
                                    ? 'Do you want to create the liquidation order!'
                                    : 'Do you want to update the liquidation Order!') ??
                            false;
                        if (result) {
                          context.read<UpdateLiquidationCubit>().submit(
                              widget.smoId,
                              _vendorController.text,
                              _mobileController.text.toString());
                        }
                      },
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget AmountSummary(Iterable<LiquidationInputModel> input) {
    final amount =
        input.fold(0.0, (p, e) => p + (e.agreedPrice?.toDouble() ?? 0.0));
    final receivedAmount =
        input.fold(0.0, (p, e) => p + e.receiveAmountTillNow.toDouble());

    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey[400]!,
              blurRadius: 30,
              offset: Offset(0, 10),
            ),
          ]),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'liquidation.receivedAmount'.tr('Received Amount'),
                  style: TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  NumberFormat.simpleCurrency(name: 'INR', decimalDigits: 2)
                      .format(
                    receivedAmount,
                  ),
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget skuCard(
      List<LiquidationInputModel> inputModel, UpdateLiquidationState state) {
    final sku = getSKU(context, skuID: inputModel.first.sku.id);
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 4,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Image.network(
                  sku.image,
                  width: 48,
                  height: 48,
                ),
                SizedBox(
                  width: 16,
                ),
                Text(
                  sku.name,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 4,
            ),
            Divider(
              color: Colors.grey[300],
              thickness: 1,
            ),
            SizedBox(
              height: 4,
            ),
            for (int i = 0; i < inputModel.length; i++)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        // 'Lots - 2.5kg',
                        inputModel[i].getUnitString(),
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        inputModel[i].quantity,
                        style: TextStyle(
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Text(
                            // input.amount ?? '-',
                            inputModel[i].agreedPrice?.toString() ?? '-',
                            style: TextStyle(
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Text(
                            inputModel[i].receiveAmountTillNow,
                            style: TextStyle(
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ),
                      ),
                    ),
                    // SizedBox(width: 4),

                    inputModel[i].item?.isFinalized == true
                        ? SizedBox(
                            width: 44,
                          )
                        : InkWell(
                            onTap: () async {
                              if (state.isCtaLoading) return;
                              final notAllowedSkus =
                                  List<String>.from(state.notAllowedSkus);
                              notAllowedSkus
                                  .remove(inputModel[i].getCompositeKey());
                              final result = await showDialog(
                                context: context,
                                builder: (ctx) => UpdateLiquidationSkuPopup(
                                  showDelete: inputModel[0].item == null,
                                  smoId: widget.smoId,
                                  isCreating: state.isCreateOrder,
                                  notAllowedSkus: notAllowedSkus,
                                  inputModel: inputModel[i],
                                  isEditOnlyFromWeighingMachine:
                                      widget.isEditOnlyFromWeighingMachine,
                                  onDelete: () {
                                    context
                                        .read<UpdateLiquidationCubit>()
                                        .deleteOrder(inputModel[i].sku.id, i);
                                  },
                                  // inputModel: inputModel[i],
                                ),
                              );
                              if (result != null) {
                                context
                                    .read<UpdateLiquidationCubit>()
                                    .updateOrder(result, i);
                              }
                            },
                            child: Container(
                              width: 44,
                              child: const Icon(
                                Icons.edit,
                                color: Colors.green,
                              ),
                            ),
                          ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _headerButton(String title, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        side: BorderSide(
          color: Colors.white,
        ),
        elevation: 0,
      ),
      child: Text(title),
    );
  }
}
