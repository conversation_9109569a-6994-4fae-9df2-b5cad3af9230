import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';
import 'package:proc2/features/mandi/presentation/my_procurement/detail/cubit/my_proc_detail_state.dart';

@injectable
class MyProcDetailCubit extends Cubit<MyProcDetailState> {
  final MandiRepository _mandiRepository;
  bool loading = false;
  String selectedStatus = 'PLACED';

  MyProcDetailCubit({required MandiRepository mandiRepository})
      : _mandiRepository = mandiRepository,
        super(MyProcDetailInitial());

  void updateStatus(String status) {
    selectedStatus = status;
  }

  void loadDetail(int smoId, {int offset = 0, int limit = 10}) async {
    emit(MyProcDetailLoading());
    // print("status ---> $selectedStatus limit--->$limit  offset--->$offset");
    final myProcDetailList = await _mandiRepository.getAllProcDetail(smoId, selectedStatus, limit, offset);
    final newState = myProcDetailList.fold((left) => MyProcDetailError(left.message), (right) => MyProcDetailLoaded(right));
    emit(newState);
  }
}
