import json
import os

def get_flutter_version():
    # Define the path to the .fvmrc file
    fvmrc_path = '.fvmrc'

    # Check if the file exists
    if not os.path.exists(fvmrc_path):
        print("Error: .fvmrc file not found")
        return

    # Read the contents of the .fvmrc file
    with open(fvmrc_path, 'r') as file:
        data = json.load(file)

    # Extract the "flutter" value
    flutter_version = data.get("flutter")

    # Print the value
    if flutter_version:
        print(flutter_version)
    else:
        print("Error: 'flutter' key not found in .fvmrc file")

if __name__ == "__main__":
    get_flutter_version()