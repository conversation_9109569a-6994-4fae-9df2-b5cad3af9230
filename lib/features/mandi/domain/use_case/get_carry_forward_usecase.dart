import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetCarryForwardUseCase {
  GetCarryForwardUseCase(this._mandiRepository);

  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, CarryForward>> call(int smoId) async {
    return _mandiRepository.getCarryForward(smoId: smoId);
  }
}
