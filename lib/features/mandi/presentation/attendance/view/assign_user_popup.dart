import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/widgets/popup.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/features/mandi/presentation/attendance/request/get_user_name_by_phone_request.dart';

class AssignUserPopup extends StatefulWidget {
  const AssignUserPopup({
    super.key,
    required this.suggestionsCallback,
    required this.onAssignUser,
  });
  final Future<List<String>?> Function(String) suggestionsCallback;
  final Future<bool> Function(String phoneNumber, String? name) onAssignUser;

  @override
  State<AssignUserPopup> createState() => _AssignUserPopupState();
}

class _AssignUserPopupState extends State<AssignUserPopup> {
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final SuggestionsController<String> _suggestionsController =
      SuggestionsController<String>();

  bool isPhoneSearchLoading = false;
  bool isNameFieldHidden = true;
  bool isNameFieldEnabled = false;
  bool isUserPreviouslyCreated = false;

  bool isCtaLoading = false;

  @override
  void initState() {
    super.initState();
  }

  void searchUser() async {
    if (isPhoneSearchLoading) return;

    final number = _phoneNumberController.text.trim();
    if (number.length != 10) {
      isNameFieldEnabled = false;
      isNameFieldHidden = true;
      _nameController.clear();
      isUserPreviouslyCreated = false;
      setState(() {});
      return;
    }

    setState(() {
      isPhoneSearchLoading = true;
    });

    final result =
        await GetUserNameByPhoneRequest(phoneNumber: number).execute();
    result.fold(
      (error) {
        setState(() {
          isPhoneSearchLoading = false;
          isUserPreviouslyCreated = false;
          isNameFieldEnabled = true;
          isNameFieldHidden = false;
          _nameController.text = '';
        });
      },
      (data) {
        setState(() {
          isPhoneSearchLoading = false;
          isUserPreviouslyCreated = true;
          _nameController.text = data;
          isNameFieldEnabled = false;
          isNameFieldHidden = false;
        });
      },
    );
  }

  @override
  void dispose() {
    _phoneNumberController.dispose();
    _suggestionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Popup(
      title: 'assignUser'.tr('Assign User'),
      height: 0.6,
      children: [
        Expanded(
            child: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          shrinkWrap: true,
          children: [
            TypeAheadField<String>(
              controller: _phoneNumberController,
              suggestionsController: _suggestionsController,
              suggestionsCallback: widget.suggestionsCallback,
              emptyBuilder: (context) => SizedBox(),
              offset: Offset(0, -20),
              builder: (context, controller, focusNode) {
                return TextFormField(
                  enabled: !isPhoneSearchLoading,
                  controller: controller,
                  focusNode: focusNode,
                  autofocus: true,
                  maxLength: 10,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(),
                    labelText: 'Phone Number',
                    counter: Text(
                      '',
                      style: TextStyle(fontSize: 0),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: Config.numberInputFiltersInt,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter phone number';
                    }
                    if (value.length < 10) {
                      return 'Please enter valid phone number';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    setState(() {
                      if (value.length != 10) {
                        isNameFieldEnabled = false;
                        isNameFieldHidden = true;
                        _nameController.clear();
                        isUserPreviouslyCreated = false;
                      }
                    });
                  },
                );
              },
              itemBuilder: (context, phoneNumber) {
                return ListTile(
                  title: Text(phoneNumber),
                );
              },
              onSelected: (phoneNumber) {
                _phoneNumberController.text = phoneNumber;
                _suggestionsController.close(
                  retainFocus: false,
                );
                searchUser();
              },
            ),
            SizedBox(
              height: 8,
            ),
            ElevatedButton.icon(
              onPressed: _phoneNumberController.text.length != 10 ||
                      isPhoneSearchLoading
                  ? null
                  : () {
                      searchUser();
                    },
              icon: Icon(Icons.search),
              label: isPhoneSearchLoading
                  ? SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(),
                    )
                  : Text('Search User'),
            ),
            Divider(
              height: 32,
            ),
            if (!isNameFieldHidden)
              Text(
                isUserPreviouslyCreated
                    ? 'User found!'
                    : 'User not found! Please create user.',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isUserPreviouslyCreated ? Colors.green : Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
            if (!isNameFieldHidden)
              SizedBox(
                height: 16,
              ),
            if (!isNameFieldHidden)
              TextFormField(
                enabled: isNameFieldEnabled,
                controller: _nameController,
                decoration: InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Name',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter name';
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {});
                },
              )
          ],
        )),
        WStickyBottomCta(
          isLoading: isCtaLoading,
          isEnabled: _phoneNumberController.text.length == 10 &&
              _nameController.text.trim().isNotEmpty,
          icon: Icons.check,
          label: LangText('submit', 'Submit'),
          onPressed: () async {
            setState(() {
              isCtaLoading = true;
            });
            final result = await widget.onAssignUser(
              _phoneNumberController.text,
              isUserPreviouslyCreated ? null : _nameController.text,
            );
            if (result) {
              Navigator.of(context).pop();
            }
            setState(() {
              isCtaLoading = false;
            });
          },
        )
      ],
    );
  }
}
