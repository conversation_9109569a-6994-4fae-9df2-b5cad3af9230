import 'dart:io';

import 'package:either_dart/either.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:proc2/core/data/network/extensions/chopper_either_extension.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/utils/file_upload/model/aws_file_url.dart';
import 'package:proc2/core/utils/file_upload/s3_uploader_base.dart';
import 'package:proc2/core/utils/file_upload/upload_file.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_grading_presigned_url_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/requests/get_liquidation_presigned_url_request.dart';
import 'package:proc2/features/mandi/data/data_source/remote/service/mandi_service.dart';
import 'package:http/http.dart' as http;

class S3Uploader extends S3BaseUploader {
  static final S3Uploader _instance = S3Uploader._internal();

  S3Uploader._internal();

  factory S3Uploader() {
    return _instance;
  }

  static void initialize(
      {required Map<String, dynamic> entityMap,
      required String appName,
      required String host}) {
    _instance.updateData(
        entityMap: entityMap, appName: appName, hostData: host);
  }

  @override
  Future<List<AWSFileUrl>> getUploadUrl(
    UploadFileModule module,
    Map<String, dynamic> body,
    List<PlatformFile> files,
    MandiService mandiService,
  ) async {
    List<String> fileExtensions = [];

    for (final file in files) {
      fileExtensions.add(file.extension ?? '');
    }

    Map<String, dynamic> reqBody = body;

    reqBody['fileExtensions'] = fileExtensions.map((e) => e).toList();

    late Either<ErrorResult<dynamic>, Map<String, dynamic>> result;
    if (module == UploadFileModule.smoCharges) {
      result = await mandiService.safeCall(
        apiCall: () => mandiService.getSmoChargesPresignedUrl(reqBody),
        transform: (d) {
          if (d is Map<String, dynamic>) {
            return d;
          }
          throw Exception('Invalid Response');
        },
        errorTransform: (e) {
          throw Exception(e);
        },
      );
    } else if (module == UploadFileModule.procurementsInvoice) {
      result = await mandiService.safeCall(
        apiCall: () => mandiService.getProcurementsInvoicePresignedUrl(reqBody),
        transform: (d) {
          if (d is Map<String, dynamic>) {
            return d;
          }
          throw Exception('Invalid Response');
        },
        errorTransform: (e) {
          throw Exception(e);
        },
      );
    } else if (module == UploadFileModule.liquidationOrder) {
      result = await GetLiquidationPresignedUrlRequest(body: reqBody).execute();
    } else if (module == UploadFileModule.grading) {
      result = await GetGradingPresignedUrlRequest(body: reqBody).execute();
    } else {
      throw Exception('Invalid module');
    }

    final data = result.fold(
      (l) => throw Exception(l.message),
      (r) => r,
    );

    final presignedUrls = Map<String, String>.from(data).values.toList();
    final keys = Map<String, String>.from(data).keys.toList();

    final awsFileUrls = <AWSFileUrl>[];

    for (var i = 0; i < presignedUrls.length; i++) {
      awsFileUrls.add(
        AWSFileUrl(
          presignedUrl: presignedUrls[i],
          key: keys[i],
        ),
      );
    }

    return awsFileUrls;
  }

  @override
  Future<bool> uploadFileToAws(String preSignedUrl, PlatformFile file) async {
    final fileBytes = kIsWeb
        ? file.bytes!
        : file.path == null
            ? file.bytes
            : File(file.path!).readAsBytesSync();

    final response = await http.put(Uri.parse(preSignedUrl), body: fileBytes);

    // Check the response status code.
    if (response.statusCode == 200) {
      return true;
    } else {
      // The file was not uploaded successfully.
      return false;
    }
  }
}
