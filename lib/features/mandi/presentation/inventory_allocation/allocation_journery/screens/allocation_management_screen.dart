import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/presentation/empty_screen.dart';
import 'package:proc2/core/presentation/widgets/w_app_bar.dart';
import 'package:proc2/core/presentation/widgets/w_cancel_dialog.dart';
import 'package:proc2/core/presentation/widgets/w_sticky_bottom_cta.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';

class AllocationManagementScreen extends StatefulWidget {
  const AllocationManagementScreen({
    super.key,
    required this.state,
  });
  final AllocationJourneyState state;

  @override
  State<AllocationManagementScreen> createState() =>
      _AllocationManagementScreenState();
}

class _AllocationManagementScreenState
    extends State<AllocationManagementScreen> {
  final List<String> orders = [];

  @override
  Widget build(BuildContext context) {
    final supplyOrders = widget.state.supplyOrders;
    return SafeArea(
      child: Scaffold(
        appBar: WAppBar.getAppBar(
          title: Text('allocationManagement.ttile'.tr('Allocation Management')),
        ),
        body: Container(
          child: supplyOrders == null || supplyOrders.isEmpty
              ? EmptyScreen(
                  message: 'allocationManagement.noSupplyOrderFound'
                      .tr('No supply order found to cancel'),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Text(
                        'allocationManagement.selectOrders'
                            .tr('Select Orders to Cancel'),
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        shrinkWrap: true,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        children: [
                          ...supplyOrders.map(
                            (order) => CheckboxListTile(
                              title: Text(order.id),
                              subtitle: Text(order.customer.key),
                              value: orders.contains(order.id),
                              onChanged: (value) {
                                setState(
                                  () {
                                    if (value == true) {
                                      orders.add(order.id);
                                    } else {
                                      orders.remove(order.id);
                                    }
                                  },
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    WStickyBottomCta(
                      isEnabled: orders.isNotEmpty,
                      icon: Icons.check,
                      label: Text('allocationManagement.cancelSelectedOrder'
                          .tr('Cancel Selected Orders')),
                      onPressed: () async {
                        final shouldCancel = await context.showAlertDialog(
                                title: 'allocationManagement.cancelOrderTitle'
                                    .tr('Cancel Order?'),
                                message:
                                    'allocationManagement.cancelOrderMessage'.tr(
                                        'Are you sure you want to cancel the selected orders?')) ??
                            false;
                        if (!shouldCancel) return;

                        await context
                            .read<AllocationJourneyCubit>()
                            .cancelSelectedOrders(orders: orders);
                      },
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}
