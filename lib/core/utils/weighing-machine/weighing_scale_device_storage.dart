import 'dart:convert';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:weighing_machine/weighing_machine.dart';

class WeighingScaleDeviceStorage extends IWeighingScaleDeviceStorageInterface {
  @override
  Future<BluetoothDeviceCache?> readDevice() {
    return Hive.openBox('weighingMachine').then((weighingMachineBox) {
      final json = weighingMachineBox.get('lastDevice');
      if (json != null) {
        return BluetoothDeviceCache.fromMap(jsonDecode(json));
      }
      return null;
    });
  }

  @override
  Future<void> writeDevice(BluetoothDeviceCache cache) async {
    final json = jsonEncode(cache.toMap());
    final weighingMachineBox = await Hive.openBox('weighingMachine');
    await weighingMachineBox.put('lastDevice', json);
  }
}
