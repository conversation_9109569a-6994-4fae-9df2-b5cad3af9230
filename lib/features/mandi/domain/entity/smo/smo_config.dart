import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/core/utils/extensions.dart';

part 'smo_config.freezed.dart';
part 'smo_config.g.dart';

@freezed
class SmoConfig with _$SmoConfig {
  const factory SmoConfig({
    required ProcurementConfig procurement,
    required InventoryConfig inventory,
    required ConversionConfig conversion,
    @Default(ReturnsConfig(isEditOnlyFromWeighingMachine: false))
    ReturnsConfig returns,
    @Default(ReturnsConfig(isEditOnlyFromWeighingMachine: false))
    ReturnsConfig wastageReturns,
    @Default(ClosingInventoryConfig(isEditOnlyFromWeighingMachine: false))
    ClosingInventoryConfig closingInventory,
    @Default(LiquidationConfig(isEditOnlyFromWeighingMachine: false))
    LiquidationConfig liquidation,
    @Default(AllocationSmoConfig(isEditOnlyFromWeighingMachine: false))
    AllocationSmoConfig allocation,
  }) = _SmoConfig;

  @override
  factory SmoConfig.fromJson(Map<String, dynamic> json) =>
      _$SmoConfigFromJson(json);
}

@freezed
class LiquidationConfig with _$LiquidationConfig {
  const factory LiquidationConfig({
    @Default(false)
    @JsonKey(name: 'editOnlyFromWeighingMachine')
    bool isEditOnlyFromWeighingMachine,
  }) = _LiquidationConfig;

  @override
  factory LiquidationConfig.fromJson(Map<String, dynamic> json) =>
      _$LiquidationConfigFromJson(json);
}

@freezed
class ClosingInventoryConfig with _$ClosingInventoryConfig {
  const factory ClosingInventoryConfig({
    @Default(false)
    @JsonKey(name: 'editOnlyFromWeighingMachine')
    bool isEditOnlyFromWeighingMachine,
  }) = _ClosingInventoryConfig;

  @override
  factory ClosingInventoryConfig.fromJson(Map<String, dynamic> json) =>
      _$ClosingInventoryConfigFromJson(json);
}

@freezed
class AllocationSmoConfig with _$AllocationSmoConfig {
  const factory AllocationSmoConfig({
    @Default(false)
    @JsonKey(name: 'editOnlyFromWeighingMachine')
    bool isEditOnlyFromWeighingMachine,
  }) = _AllocationSmoConfig;

  @override
  factory AllocationSmoConfig.fromJson(Map<String, dynamic> json) =>
      _$AllocationSmoConfigFromJson(json);
}

@freezed
class ReturnsConfig with _$ReturnsConfig {
  const factory ReturnsConfig({
    @Default(false)
    @JsonKey(name: 'editOnlyFromWeighingMachine')
    bool isEditOnlyFromWeighingMachine,
    @Default(false) bool hideSystemQuantity,
    @Default(ValidationVariation()) ValidationVariation validationVariation,
  }) = _ReturnsConfig;

  @override
  factory ReturnsConfig.fromJson(Map<String, dynamic> json) =>
      _$ReturnsConfigFromJson(json);
}

@freezed
class ValidationVariation with _$ValidationVariation {
  const ValidationVariation._();
  const factory ValidationVariation({
    @Default(ValidationVariationValue(minValue: 0.05, maxValue: 0.05))
    ValidationVariationValue bulkKg,
    @Default(ValidationVariationValue(minValue: 1, maxValue: 1))
    ValidationVariationValue bulkBunch,
    @Default(ValidationVariationValue(minValue: 1, maxValue: 1))
    ValidationVariationValue bulkPiece,
  }) = _ValidationVariation;

  ValidationVariationValue? getVariationValue(
      {required String type, required String unit}) {
    if (type.toLowerCase() == 'bulk') {
      switch (unit.toLowerCase()) {
        case 'kg':
          return bulkKg;
        case 'bunch':
          return bulkBunch;
        case 'piece':
          return bulkPiece;
      }
    }
    return null;
  }

  @override
  factory ValidationVariation.fromJson(Map<String, dynamic> json) =>
      _$ValidationVariationFromJson(json);
}

@freezed
class ValidationVariationValue with _$ValidationVariationValue {
  const factory ValidationVariationValue({
    required double minValue,
    required double maxValue,
  }) = _ValidationVariationValue;

  @override
  factory ValidationVariationValue.fromJson(Map<String, dynamic> json) =>
      _$ValidationVariationValueFromJson(json);
}

@freezed
class ConversionConfig with _$ConversionConfig {
  const factory ConversionConfig({
    required ConversionInventoryConfig mandiInventory,
    required ConversionInventoryConfig returnsInventory,
    required ConversionInventoryConfig wastageReturnsInventory,
  }) = _ConversionConfig;

  @override
  factory ConversionConfig.fromJson(Map<String, dynamic> json) =>
      _$ConversionConfigFromJson(json);
}

@freezed
class ConversionInventoryConfig with _$ConversionInventoryConfig {
  const factory ConversionInventoryConfig({
    required bool enabled,
    @Default(false)
    @JsonKey(name: 'editOnlyFromWeighingMachine')
    bool isEditOnlyFromWeighingMachine,
  }) = _ConversionInventoryConfig;

  @override
  factory ConversionInventoryConfig.fromJson(Map<String, dynamic> json) =>
      _$ConversionInventoryConfigFromJson(json);
}

@freezed
class TransferTiming with _$TransferTiming {
  const TransferTiming._();
  const factory TransferTiming({
    required InventoryTransferConfig gradeD1,
    required InventoryTransferConfig gradeC,
    required InventoryTransferConfig gradeB,
    required InventoryTransferConfig gradeA,
  }) = _TransferTiming;

  @override
  factory TransferTiming.fromJson(Map<String, dynamic> json) =>
      _$TransferTimingFromJson(json);
}

@freezed
class InventoryConfig with _$InventoryConfig {
  const factory InventoryConfig({
    required TransferTiming transferTiming,
  }) = _InventoryConfig;

  @override
  factory InventoryConfig.fromJson(Map<String, dynamic> json) =>
      _$InventoryConfigFromJson(json);
}

@freezed
class InventoryTransferConfig with _$InventoryTransferConfig {
  const InventoryTransferConfig._();
  const factory InventoryTransferConfig({
    required List<AllowedTime> allowedTime,
  }) = _InventoryTransferConfig;

  bool isAllowed(DateTime time) {
    final seconds = time.hour * 3600 + time.minute * 60 + time.second;
    for (final allowedTime in allowedTime) {
      if (seconds >= allowedTime.getStartSeconds() &&
          seconds <= allowedTime.getEndSeconds()) {
        return true;
      }
    }
    return false;
  }

  AllowedTime? getNearestAllowedTime(DateTime time) {
    final seconds = time.hour * 3600 + time.minute * 60 + time.second;
    AllowedTime? nearestAllowedTime;
    for (final allowedTime in allowedTime) {
      if (seconds >= allowedTime.getStartSeconds() &&
          seconds <= allowedTime.getEndSeconds()) {
        return allowedTime;
      }
      if (nearestAllowedTime == null) {
        nearestAllowedTime = allowedTime;
      } else {
        if ((seconds - allowedTime.getStartSeconds()).abs() <
            (seconds - nearestAllowedTime.getStartSeconds()).abs()) {
          nearestAllowedTime = allowedTime;
        }
      }
    }
    return nearestAllowedTime;
  }

  @override
  factory InventoryTransferConfig.fromJson(Map<String, dynamic> json) =>
      _$InventoryTransferConfigFromJson(json);
}

@freezed
class AllowedTime with _$AllowedTime {
  const AllowedTime._();
  const factory AllowedTime({
    required String startTime,
    required String endTime,
  }) = _AllowedTime;

  int getSeconds(String timeString) {
    final time = timeString.split(':');
    int seconds = 0;
    if (time.length == 3) {
      seconds = int.parse(time[0]) * 3600 +
          int.parse(time[1]) * 60 +
          int.parse(time[2]);
    } else if (time.length == 2) {
      seconds = int.parse(time[0]) * 3600 + int.parse(time[1]) * 60;
    } else if (time.length == 1) {
      seconds = int.parse(time[0]) * 3600;
    }
    return seconds;
  }

  int getStartSeconds() => getSeconds(startTime);
  int getEndSeconds() => getSeconds(endTime);

  @override
  factory AllowedTime.fromJson(Map<String, dynamic> json) =>
      _$AllowedTimeFromJson(json);
}

@freezed
class ProcurementConfig with _$ProcurementConfig {
  const factory ProcurementConfig({
    required ReceiveProcurementConfig receiveProcurement,
  }) = _ProcurementConfig;

  @override
  factory ProcurementConfig.fromJson(Map<String, dynamic> json) =>
      _$ProcurementConfigFromJson(json);
}

@freezed
class ReceiveProcurementConfig with _$ReceiveProcurementConfig {
  const ReceiveProcurementConfig._();
  const factory ReceiveProcurementConfig({
    required bool allowed,
    required bool canAddSku,
    required double deviationPercentage,
    @Default(false)
    @JsonKey(name: 'editOnlyFromWeighingMachine')
    bool isEditOnlyFromWeighingMachine,
  }) = _ReceiveProcurementConfig;

  bool get isAllowed => allowed;

  String deviationMessage({
    required double totalQuantity,
    required double receiveQuantity,
  }) {
    final maxValue = totalQuantity * (1 + deviationPercentage / 100);
    if (receiveQuantity > maxValue) {
      return 'Receive quantity should not be greater than ${maxValue.asString()}';
    }
    return '';
  }

  @override
  factory ReceiveProcurementConfig.fromJson(Map<String, dynamic> json) =>
      _$ReceiveProcurementConfigFromJson(json);
}
