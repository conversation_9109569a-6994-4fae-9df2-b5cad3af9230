part of 'attendance_cubit.dart';

class AttendanceState {
  final int smoId;
  final bool isLoading;
  final String? message;
  final List<AssignedUser> assignedUsers;

  AttendanceState({
    required this.smoId,
    required this.isLoading,
    required this.message,
    required this.assignedUsers,
  });

  factory AttendanceState.initial() {
    return AttendanceState(
      smoId: -1,
      isLoading: true,
      message: null,
      assignedUsers: [],
    );
  }

  AttendanceState copyWith({
    int? smoId,
    bool? isLoading,
    String? message,
    List<AssignedUser>? assignedUsers,
  }) {
    return AttendanceState(
      smoId: smoId ?? this.smoId,
      isLoading: isLoading ?? this.isLoading,
      message: message,
      assignedUsers: assignedUsers ?? this.assignedUsers,
    );
  }
}
