part of 'combo_conversion_cubit.dart';

@freezed
class ComboConversionState with _$ComboConversionState {
  const factory ComboConversionState.initial({
    @Default(null) List<InventoryItem>? allInventory,
    @Default([]) List<InventoryItem>? fromInventory,
    @Default([]) List<PickedFile> dumpFiles,
    @Default([]) List<GradingInput> toInventory,
    @Default(null) String? message,
    @Default(false) bool isCtaLoading,
    @Default(false) bool shouldPop,
    @Default(null) double? gradingInventoryDeviation,
    @Default(null) double? gradingConversionDeviation,
  }) = _Initial;
}
