import 'package:freezed_annotation/freezed_annotation.dart';

part 'mandi_menu_config.freezed.dart';

@freezed
class MandiMenuConfig with _$MandiMenuConfig {
  const MandiMenuConfig._();
  const factory MandiMenuConfig({
    required bool proc,
    required bool addCarryForward,
    required bool addFieldCharges,
    required bool recieveInventory,
    required bool mandiInventory,
    required bool skuAllocation,
    required bool mandiProcSummary,
    required bool viewLoadingPlan,
    required bool closingStockPlan,
    required bool closeOpsBtn,
    required bool lotting,
    required bool supplyOrder,
    required bool liquidation,
    required bool grading,
    required bool returns,
    required bool procAdmin,
    required bool attendance,
    // Add in below function as well
  }) = _MandiMenuConfig;

  bool hasNoAccess() {
    return !proc &&
        !addCarryForward &&
        !addFieldCharges &&
        !recieveInventory &&
        !mandiInventory &&
        !skuAllocation &&
        !mandiProcSummary &&
        !viewLoadingPlan &&
        !closingStockPlan &&
        !closeOpsBtn &&
        !lotting &&
        !supplyOrder &&
        !liquidation &&
        !grading &&
        !returns &&
        !procAdmin &&
        !attendance;
  }
}
