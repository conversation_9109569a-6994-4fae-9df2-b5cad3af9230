import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:proc2/features/mandi/domain/entity/carry_forward/carry_forward_item.dart';

part 'carry_forward.freezed.dart';

@freezed
class CarryForward with _$CarryForward {
  const CarryForward._();

  const factory CarryForward({
    required int id,
    required List<CarryForwardItem> bulkItems,
    required List<CarryForwardItem> lotItems,
    @Default('') String comments,
    @Default(null) String? invenotryClosedBy,
  }) = _CarryForward;

  bool isNotEmpty() {
    return bulkItems.isNotEmpty || lotItems.isNotEmpty;
  }

  bool isEmpty() {
    return !isNotEmpty();
  }
}
