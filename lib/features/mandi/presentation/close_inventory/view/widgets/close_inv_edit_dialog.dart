import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:proc2/core/lang/lang_text.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/core/lang/language_enum.dart';
import 'package:proc2/core/presentation/error_screen.dart';
import 'package:proc2/core/presentation/uploader/picked_file.dart';
import 'package:proc2/core/utils/config.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weighing_quantity_button.dart';
import 'package:proc2/core/utils/weighing-machine/ui/weight_capture_popup_base.dart';
import 'package:proc2/features/mandi/domain/entity/losses/loss_type_v2.dart';
import 'package:proc2/features/mandi/presentation/add_losses/input_model/loss_input_model.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/procurement_type.dart';
import 'package:proc2/features/mandi/presentation/add_procurement/input_models/sku_input_data.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/bloc/close_inventory_bloc.dart';
import 'package:proc2/features/mandi/presentation/close_inventory/sku_edit/bloc/sku_edit_bloc.dart';
import 'package:proc2/features/mandi/presentation/loss_types/bloc/loss_types_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku/bloc/sku_bloc.dart';
import 'package:proc2/features/mandi/presentation/sku_types/bloc/sku_types_bloc.dart';
import 'package:proc2/features/mandi/util/loss_upload/loss_picker.dart';

class CloseInvEditDialog extends StatefulWidget {
  const CloseInvEditDialog({
    super.key,
    required this.losses,
    this.onDelete,
    required this.pickedFiles,
    required this.isEditOnlyFromWeighingMachine,
  });
  final GestureTapCallback? onDelete;
  final List<LossInputModel> losses;
  final List<PickedFile> pickedFiles;
  final bool isEditOnlyFromWeighingMachine;

  // final SkuInputData skuInputData;
  @override
  State<CloseInvEditDialog> createState() => _CloseInvEditDialog();
}

class _CloseInvEditDialog extends State<CloseInvEditDialog> {
  // Initial Selected Value

  // final bulkUnits = ['Kg', 'Pieces', 'Bunch'];
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _skuSearchController = TextEditingController();
  List<LossInputModel> _losses = [];
  List<LossTypeV2> lossTypes = [];
  List<PickedFile> pickedFiles = [];
  String? weighingSource;

  @override
  void initState() {
    super.initState();
    _skuSearchController.addListener(_skuSearchListener);
    _losses = widget.losses;
    pickedFiles = widget.pickedFiles;
    final lossState = context.read<LossTypesBloc>().state;
    lossState.maybeMap(
      orElse: () {},
      success: (s) {
        final list = s.losses['CLOSING_STOCK'];
        if (list != null) {
          lossTypes = list;
        }
      },
    );
  }

  void _skuSearchListener() {
    try {
      final skuName = _skuSearchController.text;
      if (skuName == context.read<SkuEditBloc>().state.sku?.name) return;
      if (skuName.isNotEmpty) {
        final sku = context.read<SkuBloc>().state.mapOrNull(
              success: (s) => s.skus.firstWhere(
                (element) => element.name == skuName,
              ),
            );
        if (sku != null) {
          context.read<SkuEditBloc>().add(
                SkuEditEvent.init(
                  SkuInputData.withSku(sku),
                ),
              );
        }
      }
    } catch (e) {}
  }

  @override
  void dispose() {
    super.dispose();
    _skuSearchController.removeListener(_skuSearchListener);
  }

  final int minFileUploadCount = 0;

  @override
  Widget build(BuildContext context) {
    final isLossInvalid = _losses
        .where((e) =>
            e.lossValue.isNotEmpty && e.files.length < e.minFileUploadCount)
        .isNotEmpty;

    final isNotMinImageUploaded = pickedFiles.length < minFileUploadCount;

    // return BlocProvider(
    //   create: (context) =>
    //       di.get<SkuEditBloc>()..add(SkuEditEvent.init(widget.skuInputData)),
    //   child:
    return BlocBuilder<SkuTypesBloc, SkuTypesState>(
      builder: (context, skuTypesState) {
        return skuTypesState.map(
          initial: (_) => const Center(
            child: CircularProgressIndicator(),
          ),
          loading: (_) => const Center(
            child: CircularProgressIndicator(),
          ),
          failure: (failure) {
            return ErrorScreen(
              onPressed: () {
                context.read<SkuTypesBloc>().add(SkuTypesEvent.fetch());
              },
              message: failure.message,
            );
          },
          success: (success) {
            return BlocConsumer<SkuEditBloc, SkuEditState>(
              listener: (context, state) {
                if (state.sku?.name != null) {
                  _skuSearchController.text = state.sku!.name;
                }
                if (state.quantity != _quantityController.text) {
                  _quantityController.text = state.quantity ?? '';
                }
                if (state.amount != _amountController.text) {
                  _amountController.text = state.amount ?? '';
                }
                if (state.weight != _weightController.text) {
                  _weightController.text = state.weight ?? '';
                }
              },
              builder: (context, state) {
                double lossValue =
                    _losses.fold(0.0, (p, e) => p + e.lossValue.toDouble());
                double totalQtyWithLoss =
                    (state.quantity?.toDouble() ?? 0) + lossValue;
                final isCtaActive = state.isCtaActive && totalQtyWithLoss > 0;

                final skuEditBloc = context.read<SkuEditBloc>();
                return BlocBuilder<SkuBloc, SkuState>(
                  builder: (context, skuState) {
                    return skuState.map(
                      initial: (_) => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      success: (skuBlocState) {
                        return SafeArea(
                          child: Scaffold(
                            backgroundColor: Colors.transparent,
                            body: InkWell(
                              onTap: () {
                                context.pop();
                              },
                              child: Center(
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    left: 20,
                                    right: 20,
                                  ),
                                  child: InkWell(
                                    onTap: () {},
                                    child: Container(
                                      width: MediaQuery.of(context).size.width,
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.7,
                                      color: Colors.white,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          header(state.isOpenForEdit,
                                              onTap: () {
                                            if (widget.onDelete != null) {
                                              widget.onDelete!();
                                              context.pop();
                                            }
                                          }),
                                          Expanded(
                                            child: Scrollbar(
                                              child: ListView(
                                                // crossAxisAlignment: CrossAxisAlignment.start,
                                                // mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  const SizedBox(height: 12),
                                                  // Container(
                                                  //   padding: const EdgeInsets.all(12),
                                                  //   width: MediaQuery.of(context).size.width,
                                                  //   height: 430,
                                                  //   child: ListView(
                                                  //     children: [
                                                  if (state.isOpenForEdit)
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                        left: 16,
                                                        right: 16,
                                                      ),
                                                      child: Text(
                                                        state.sku?.name ?? '',
                                                        style: const TextStyle(
                                                            fontSize: 18),
                                                      ),
                                                    ),
                                                  if (!state.isOpenForEdit)
                                                    CustomDropdown.search(
                                                      hintText: LanguageEnum
                                                          .addProcSearchBoxHint
                                                          .localized(),
                                                      items: skuBlocState.skus
                                                          .map((e) => e.name)
                                                          .toList(),
                                                      controller:
                                                          _skuSearchController,
                                                      listItemBuilder:
                                                          (context, item) =>
                                                              Text(
                                                        item,
                                                        maxLines: 3,
                                                      ),
                                                    ),
                                                  const SizedBox(height: 12),

                                                  if (state.sku != null) ...[
                                                    procurementTypeField(),
                                                    const SizedBox(height: 12),
                                                    unitField(state
                                                        .sku!.bulkUnitTypes),
                                                    if (state.showLotSizeField)
                                                      const SizedBox(
                                                          height: 12),
                                                    if (state.showLotSizeField)
                                                      lotSizeField(),
                                                    const SizedBox(height: 12),
                                                    quantityField(state
                                                            .procurementType ==
                                                        ProcurementType.bulk),
                                                    const SizedBox(height: 12),
                                                    Divider(
                                                      height: 2,
                                                      color: Colors.grey[300],
                                                    ),
                                                    damageWidget(
                                                      'Kg',
                                                      state.procurementType ==
                                                          ProcurementType.bulk,
                                                    ),
                                                    Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 16),
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            'Inventory Images',
                                                            style: TextStyle(
                                                              fontSize: 16,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            height: 8,
                                                          ),
                                                          if (pickedFiles
                                                                  .length <
                                                              minFileUploadCount)
                                                            Text(
                                                              'Please upload at least ${minFileUploadCount} file.',
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .red),
                                                            ),
                                                          InlineImagePicker(
                                                              files:
                                                                  pickedFiles,
                                                              minFileAllowed:
                                                                  minFileUploadCount,
                                                              maxFileAllowed: 4,
                                                              allowMultiple:
                                                                  true,
                                                              updateFile:
                                                                  (files) {
                                                                setState(() {
                                                                  pickedFiles =
                                                                      files;
                                                                });
                                                              }),
                                                          Divider(
                                                            thickness: 2,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                  //       Row(
                                                  //         mainAxisAlignment:
                                                  //             MainAxisAlignment.center,
                                                  //         children: [],
                                                  //       )
                                                  //     ],
                                                  //   ),
                                                  // ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          Align(
                                            alignment: Alignment.bottomCenter,
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.only(top: 8),
                                              child: ElevatedButton(
                                                style: ButtonStyle(
                                                  minimumSize:
                                                      MaterialStateProperty.all(
                                                    Size(
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width -
                                                          80,
                                                      50,
                                                    ),
                                                  ),
                                                ),
                                                onPressed: !isCtaActive ||
                                                        isLossInvalid ||
                                                        isNotMinImageUploaded
                                                    ? null
                                                    : () {
                                                        final data = skuEditBloc
                                                            .getSkuInputData();
                                                        if (data != null) {
                                                          context.pop(
                                                            SkuInputWithLoss(
                                                              files:
                                                                  pickedFiles,
                                                              skuInputData:
                                                                  data,
                                                              losses: _losses
                                                                  .map((e) => e
                                                                      .copyWith(
                                                                          unit:
                                                                              data.procurementUnit!))
                                                                  .toList(),
                                                            ),
                                                          );
                                                        }
                                                      },
                                                child: Text(
                                                  LanguageEnum
                                                      .addProcSkuSelectCtaTitle
                                                      .localized(),
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 12),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                      error: (state) => ErrorScreen(
                        onPressed: () {
                          context.read<SkuBloc>().add(const SkuEvent.fetch());
                        },
                        message: state.errorr.message,
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
    //   },
    // ),
    // );
  }

  Widget damageWidget(
    String unit,
    bool isBulk,
  ) {
    return BlocConsumer<LossTypesBloc, LossTypesState>(
      listener: (context, state) {
        state.maybeWhen(
          orElse: () {},
          success: (mp) {
            final list = mp['CLOSING_STOCK'];
            if (list != null) {
              setState(() {
                lossTypes = list;
              });
            }
          },
        );
      },
      builder: (context, state) {
        return state.maybeMap(
            orElse: () => Container(),
            success: (success) {
              return Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          isBulk
                              ? getLangText(
                                  'receiveInventory.damaged',
                                  'Damaged',
                                )
                              : getLangText(
                                  'receiveInventory.damagedLot',
                                  'Damaged Lots',
                                ),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (lossTypes.isNotEmpty)
                          OutlinedButton.icon(
                            icon: Icon(Icons.add),
                            label: LangText(
                              'receiveInventory.addDamages',
                              'Add',
                            ),
                            onPressed: () {
                              setState(
                                () {
                                  _losses = [
                                    ..._losses,
                                    LossInputModel(
                                      lossType: lossTypes[0].lossType,
                                      lossValue: '',
                                      unit: unit,
                                      comment: '',
                                      maxFileUploadCount:
                                          lossTypes[0].maxImageUpload,
                                      minFileUploadCount:
                                          lossTypes[0].minImageUpload,
                                    )
                                  ];
                                },
                              );
                            },
                          ),
                      ],
                    ),
                    for (int i = 0; i < _losses.length; i++)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              Container(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: DropdownButton<String>(
                                    value: _losses[i].lossType,
                                    underline: Container(),
                                    items: lossTypes.map(
                                      (LossTypeV2 value) {
                                        return DropdownMenuItem<String>(
                                          value: value.lossType,
                                          child: Text(
                                            value.lossType.localized(),
                                            style: TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                        );
                                      },
                                    ).toList(),
                                    onChanged: (newValue) {
                                      final selectedNewLoss =
                                          lossTypes.firstWhere((element) =>
                                              element.lossType == newValue);
                                      setState(() {
                                        _losses = [
                                          ..._losses.sublist(0, i),
                                          _losses[i].copyWith(
                                            lossType: newValue!,
                                            minFileUploadCount:
                                                selectedNewLoss.minImageUpload,
                                            maxFileUploadCount:
                                                selectedNewLoss.maxImageUpload,
                                          ),
                                          ..._losses.sublist(i + 1),
                                        ];
                                      });
                                    },
                                  ),
                                ),
                              ),
                              Row(
                                children: [
                                  SizedBox(
                                    width: 70,
                                    child: TextFormField(
                                      initialValue:
                                          _losses[i].lossValue == '0.0'
                                              ? ''
                                              : _losses[i].lossValue,
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      textAlign: TextAlign.end,
                                      decoration: const InputDecoration(
                                        contentPadding:
                                            EdgeInsets.symmetric(horizontal: 8),
                                        border: OutlineInputBorder(),
                                        hintText: '0',
                                        counterStyle: TextStyle(
                                          height: double.minPositive,
                                        ),
                                        counterText: '',
                                      ),
                                      keyboardType: TextInputType.number,
                                      maxLength: 10,
                                      inputFormatters:
                                          Config.numberInputFilters,
                                      onChanged: (String value) {
                                        setState(() {
                                          _losses = [
                                            ..._losses.sublist(0, i),
                                            _losses[i]
                                                .copyWith(lossValue: value),
                                            ..._losses.sublist(i + 1),
                                          ];
                                        });
                                      },
                                    ),
                                  ),
                                  SizedBox(
                                    width: 16,
                                  ),
                                  InkWell(
                                      onTap: () {
                                        setState(() {
                                          _losses = [
                                            ..._losses.sublist(0, i),
                                            ..._losses.sublist(i + 1),
                                          ];
                                        });
                                      },
                                      child: Icon(
                                        Icons.delete,
                                        size: 24,
                                        color: Colors.red,
                                      )),
                                ],
                              ),
                            ],
                          ),
                          if (_losses[i].lossValue.isNotEmpty &&
                              _losses[i].files.length <
                                  _losses[i].minFileUploadCount)
                            Text(
                              'Please upload at least ${_losses[i].minFileUploadCount} file.',
                              style: TextStyle(color: Colors.red),
                            ),
                          InlineImagePicker(
                              files: _losses[i].files,
                              minFileAllowed: _losses[i].minFileUploadCount,
                              maxFileAllowed: _losses[i].maxFileUploadCount,
                              allowMultiple:
                                  widget.isEditOnlyFromWeighingMachine,
                              updateFile: (files) {
                                setState(() {
                                  _losses = [
                                    ..._losses.sublist(0, i),
                                    _losses[i].copyWith(files: files),
                                    ..._losses.sublist(i + 1),
                                  ];
                                });
                              }),
                          Divider(),
                        ],
                      ),
                    SizedBox(
                      height: 16,
                    ),
                  ],
                ),
              );
            });
      },
    );
  }

  Widget quantityField(bool isBulk) {
    final skuEditBloc = context.read<SkuEditBloc>();
    final state = skuEditBloc.state;
    final isBulkKg = isBulk && state.procurementUnit?.toLowerCase() == 'kg';
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
              isBulk
                  ? 'closeInv.quantity'.tr('Quantity')
                  : 'closeInv.numberOfLots'.tr('Number of Lots'),
              style: const TextStyle(fontSize: 12)),
          const SizedBox(height: 8),
          SizedBox(
            width: 120,
            child: WeighingQuantityButton(
              isReadOnly: false,
              label: _quantityController.text,
              popupBuilder: () {
                return WeighingCapturePopupBase(
                  title: 'captureWeight'.tr('Capture Weight'),
                  initalWeight: _quantityController.text.toDouble(),
                  allowManualEdit:
                      isBulkKg ? !widget.isEditOnlyFromWeighingMachine : true,
                  inputFormatters: isBulkKg
                      ? Config.numberInputFilters
                      : Config.numberInputFiltersInt,
                  enableWeighingMachine: isBulkKg,
                );
              },
              onChange: (quantity) {
                if (quantity == null) return;
                context.read<SkuEditBloc>().add(
                      SkuEditEvent.updateQuanity(
                        quantity.value,
                        quantity.source,
                      ),
                    );
                _quantityController.text = quantity.value ?? '';
                weighingSource = quantity.source;
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget unitField(List<String> bulkUnits) {
    final skuEditBloc = context.read<SkuEditBloc>();
    final state = skuEditBloc.state;
    final items = state.procurementType == ProcurementType.bulk
        ? bulkUnits
        : state.sku?.lotSizes.keys.toList() ?? [];
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectUnitLabel.localized(),
            style: const TextStyle(
              fontSize: 12,
            ),
          ),
          DropdownButton(
            // Initial Value
            value: state.procurementUnit,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: items.map((String item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.capitalize().localized()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (String? newValue) {
              skuEditBloc.add(SkuEditEvent.updateUnit(newValue));
            },
          )
        ],
      ),
    );
  }

  Widget lotSizeField() {
    final skuEditBloc = context.read<SkuEditBloc>();
    final state = skuEditBloc.state;
    final selectedLotSize =
        state.sku?.lotSizes[state.procurementUnit?.toUpperCase() ?? ''] ??
            <String>[];
    final items = <double>[];
    if (selectedLotSize is List) {
      for (final element in selectedLotSize) {
        items.add(double.parse(element.toString()));
      }
    }
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectLotSizeLabel.localized(),
            style: const TextStyle(
              fontSize: 12,
            ),
          ),
          DropdownButton(
            // Initial Value
            value: state.lotSize,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: items.map((double item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.toString()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (double? newValue) {
              skuEditBloc.add(SkuEditEvent.updateLotSize(newValue));
            },
          )
        ],
      ),
    );
  }

  Widget procurementTypeField() {
    final skuEditBloc = context.read<SkuEditBloc>();
    final state = skuEditBloc.state;
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LanguageEnum.addProcSkuSelectProcTypeLabel.localized(),
            style: const TextStyle(
              fontSize: 12,
            ),
          ),
          DropdownButton<ProcurementType>(
            // Initial Value
            value: state.procurementType,
            isExpanded: true,

            // Down Arrow Icon
            icon: const Icon(Icons.keyboard_arrow_down),

            // Array list of items
            items: ProcurementType.values.map((ProcurementType item) {
              return DropdownMenuItem(
                value: item,
                child: Text(item.value.capitalize().localized()),
              );
            }).toList(),
            // After selecting the desired option,it will
            // change button value to selected value
            onChanged: (ProcurementType? newValue) {
              skuEditBloc.add(
                SkuEditEvent.updateProcurementType(
                  newValue ?? ProcurementType.bulk,
                ),
              );
            },
          )
        ],
      ),
    );
  }

  Widget header(bool isEditing, {GestureTapCallback? onTap}) {
    // final name = context.watch<SkuEditBloc>().state.sku?.name ?? '';
    return Container(
      decoration: BoxDecoration(
        color: Config.primaryColor,
        boxShadow: const <BoxShadow>[
          BoxShadow(
            color: Colors.black54,
            blurRadius: 10,
            offset: Offset(0, 0.75),
          )
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Expanded(
            child: Row(
              children: [
                Text(
                  isEditing
                      ? LanguageEnum.addProcSkuSelectHeadingEdit.localized()
                      : LanguageEnum.addProcSkuSelectHeadingAdd.localized(),
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              ],
            ),
          ),
          if (widget.onDelete != null)
            InkWell(
              onTap: onTap,
              child: const Icon(Icons.delete, color: Colors.white),
            ),
          SizedBox(width: 24),
          InkWell(
            onTap: () {
              context.pop();
            },
            child: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }
}
