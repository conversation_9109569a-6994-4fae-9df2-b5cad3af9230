import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/procurement/proc_item.dart';

class TransferMandiInventoryRequest extends BaseRequest<dynamic, String> {
  final int smoId;
  final String gradeTo;
  final List<SkuQuantity> skus;

  TransferMandiInventoryRequest._(
      {required this.smoId, required this.gradeTo, required this.skus});

  factory TransferMandiInventoryRequest.toGradeB({
    required int smoId,
    required List<SkuQuantity> skus,
  }) {
    return TransferMandiInventoryRequest._(
      smoId: smoId,
      gradeTo: 'B',
      skus: skus,
    );
  }

  factory TransferMandiInventoryRequest.toGradeD1({
    required int smoId,
    required List<SkuQuantity> skus,
  }) {
    return TransferMandiInventoryRequest._(
      smoId: smoId,
      gradeTo: 'D1',
      skus: skus,
    );
  }

  factory TransferMandiInventoryRequest.toGradeC({
    required int smoId,
    required List<SkuQuantity> skus,
  }) {
    return TransferMandiInventoryRequest._(
      smoId: smoId,
      gradeTo: 'C',
      skus: skus,
    );
  }

  factory TransferMandiInventoryRequest.toDump({
    required int smoId,
    required List<SkuQuantity> skus,
  }) {
    return TransferMandiInventoryRequest._(
      smoId: smoId,
      gradeTo: 'dump',
      skus: skus,
    );
  }

  @override
  String getPath() => 'mandis/gradeInventory';

  @override
  Map<String, dynamic> getBody() {
    return {
      "smoId": smoId,
      "toGrade": gradeTo,
      "skus": skus
          .map((e) => {
                "skuId": e.skuId,
                "type": e.type,
                "unit": e.unit,
                "lotSize": e.lotSize,
                "quantity": e.quantity,
              })
          .toList(),
    };
  }

  @override
  String mapper(data) {
    if (data is String) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
