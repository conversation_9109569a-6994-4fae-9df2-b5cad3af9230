import 'package:either_dart/either.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/domain/entity/error_result.dart';
import 'package:proc2/core/lang/language.dart';
import 'package:proc2/features/mandi/domain/entity/sku/sku.dart';
import 'package:proc2/features/mandi/domain/repository/mandi_repository.dart';

@injectable
class GetSkuUseCase {
  GetSkuUseCase(this._language, this._mandiRepository);

  final Language _language;
  final MandiRepository _mandiRepository;

  Future<Either<ErrorResult<void>, List<Sku>>> call({
    String? lang,
  }) async {
    final language = lang ?? await _language.currentLanguage() ?? 'en';
    return _mandiRepository.getSku(language);
  }
}
