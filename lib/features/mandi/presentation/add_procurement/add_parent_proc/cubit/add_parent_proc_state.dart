part of 'add_parent_proc_cubit.dart';

@freezed
class AddParentProcState with _$AddParentProcState {
  factory AddParentProcState.initial({
    @Default(null) VendorLocation? selectedVendorLocation,
    @Default([]) List<String> headers,
    @Default(null) MandiInfo? selectedMandi,
    @Default([]) List<List<dynamic>> values,
    @Default(false) bool isCtaLoading,
    @Default(null) String? message,
    @Default(null) DateTime? selectedSupplyDate,
  }) = _Initial;
}
