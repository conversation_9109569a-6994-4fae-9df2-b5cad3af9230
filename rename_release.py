import argparse
import os
import shutil
from datetime import datetime

def usage():
    print("Usage: script.py -f <flavor> [-t <type>]")
    exit(1)

# Parse input arguments
parser = argparse.ArgumentParser(description='Rename release script.')
parser.add_argument('-f', '--flavor', required=True, help='Flavor of the build')
parser.add_argument('-t', '--type', default='aab', help='Type of the build (default: aab)')
args = parser.parse_args()

flavor = args.flavor
type = args.type

# Get current date and time
current_date = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
output_file = f"Flow-{current_date}-{flavor}-release.{type}"
current_file = f"app-{flavor}-release.{type}"

if type == "apk":
    output_dir = f"./build/app/outputs/apk/{flavor}Release"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    shutil.move(f"./build/app/outputs/flutter-apk/{current_file}", f"{output_dir}/{output_file}")
else:
    output_dir = f"./build/app/outputs/bundle/{flavor}Release"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    shutil.move(f"./build/app/outputs/bundle/{flavor}Release/{current_file}", f"{output_dir}/{output_file}")