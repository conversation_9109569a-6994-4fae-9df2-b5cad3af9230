import 'package:freezed_annotation/freezed_annotation.dart';

part 'carry_forward_user_dto.freezed.dart';
part 'carry_forward_user_dto.g.dart';

@freezed
class CarryForwardUserDto with _$CarryForwardUserDto {
  const factory CarryForwardUserDto({
    @Default(null) int? id,
    @Default(null) String? name,
    @Default(null) String? email,
  }) = _CarryForwardUserDto;

  @override
  factory CarryForwardUserDto.fromJson(Map<String, dynamic> json) =>
      _$CarryForwardUserDtoFromJson(json);
}
