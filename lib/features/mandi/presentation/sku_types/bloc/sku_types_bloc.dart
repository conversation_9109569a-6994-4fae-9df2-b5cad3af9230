import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:proc2/core/utils/show_snackbar.dart';
import 'package:proc2/features/mandi/domain/use_case/get_sku_types_usecase.dart';

part 'sku_types_event.dart';
part 'sku_types_state.dart';
part 'sku_types_bloc.freezed.dart';

@injectable
class SkuTypesBloc extends Bloc<SkuTypesEvent, SkuTypesState> {
  SkuTypesBloc(this._getSkuTypesUseCase) : super(const _Initial()) {
    on<SkuTypesEvent>((event, emit) async {
      await event.map(
        fetch: (e) async {
          emit(const SkuTypesState.loading());
          await _getSkuTypesUseCase().then(
            (value) => value.fold(
              (left) {
                emit(const SkuTypesState.failure('Failed to fetch'));
                showRetrySnackBar(
                    'Failed to Fetch SKU Types. Error : ${left.message}', () {
                  add(const SkuTypesEvent.fetch());
                });
              },
              (right) => emit(
                SkuTypesState.success(
                  right.quantityTypes,
                  right.unitTypes,
                ),
              ),
            ),
          );
        },
      );
    });
  }

  final GetSkuTypesUseCase _getSkuTypesUseCase;
}
