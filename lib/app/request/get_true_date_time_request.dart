import 'package:proc2/core/data/network/base_request.dart';

class GetTrueDateTimeRequest extends BaseRequest<dynamic, int> {
  @override
  String getPath() => 'init/true-time';

  @override
  int mapper(data) {
    if (data is Map<String, dynamic>) {
      final epochSeconds = data['epochSeconds'] ?? (DateTime.now().millisecondsSinceEpoch ~/ 1000);
      return epochSeconds;
    }
    throw Exception('Invalid data');
  }

  @override
  isAuth () => false;

  @override
  RequestMethod get method => RequestMethod.GET;
}
