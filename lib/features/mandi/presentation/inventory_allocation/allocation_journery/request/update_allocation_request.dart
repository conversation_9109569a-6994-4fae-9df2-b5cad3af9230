import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/core/utils/extensions.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_change_response.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/models/allocation_sku_item.dart';

class UpdateAllocationRequest
    extends BaseRequest<dynamic, AllocationChangeResponse> {
  final int allocationId;
  final List<AllocationSkuItem> items;
  final List<AllocationSkuItem> mandiItems;
  final List<AllocationSkuItem> supplyItems;

  UpdateAllocationRequest({
    required this.allocationId,
    required this.items,
    required this.mandiItems,
    required this.supplyItems,
  });

  Map<String, dynamic> itemToJson(AllocationSkuItem e, bool shouldUpdate) {
    return shouldUpdate
        ? {
            "id": e.id,
            "quantity": e.totalQuantityUpdate,
            "expectedQuantity": e.updateAllocationQty.expectedQty.toDouble(),
            "weighingSource": e.updateAllocationQty.weighingSource,
          }
        : {
            "skuId": e.skuQuantity.skuId,
            "type": e.skuQuantity.type,
            "unit": e.skuQuantity.unit,
            "lotSize": e.skuQuantity.lotSize,
            "quantity": e.totalQuantityUpdate,
            "expectedQuantity": e.updateAllocationQty.expectedQty.toDouble(),
            "weighingSource": e.updateAllocationQty.weighingSource,
          };
  }

  void forEachItem(
    AllocationSkuItem e,
    void Function(Map<String, dynamic>) onNewItem,
    void Function(Map<String, dynamic>) onUpdateItem,
  ) {
    if (e.id == -1) {
      // New Item
      final totalQty = e.totalQuantityUpdate;
      if (totalQty > 0) {
        onNewItem(itemToJson(e, false));
      }
    } else {
      // Update Item
      onUpdateItem(itemToJson(e, true));
    }
  }

  @override
  Map<String, dynamic> getBody() {
    final itemToBeAdded = <Map<String, dynamic>>[];
    final itemsToBeUpdated = <Map<String, dynamic>>[];
    final itemAddedToOrder = <Map<String, dynamic>>[];

    items.forEach((e) {
      forEachItem(
        e,
        (newItem) {
          itemToBeAdded.add(newItem);
        },
        (updateItem) {
          itemsToBeUpdated.add(updateItem);
        },
      );
    });

    supplyItems.forEach((e) {
      forEachItem(
        e,
        (newItem) {
          itemToBeAdded.add(newItem);
        },
        (updateItem) {
          itemsToBeUpdated.add(updateItem);
        },
      );
    });

    mandiItems.forEach((e) {
      forEachItem(
        e,
        (newItem) {
          itemToBeAdded.add(newItem);
          itemAddedToOrder.add(newItem);
        },
        (updateItem) {
          itemsToBeUpdated.add(updateItem);
        },
      );
    });

    return {
      'allotmentId': allocationId,
      'forSubmit': true,
      'itemToBeAdded': itemToBeAdded,
      'itemsToBeUpdated': itemsToBeUpdated,
      'itemAddedToOrder': itemAddedToOrder,
    };
  }

  @override
  String getPath() => 'allotments/submit';

  @override
  AllocationChangeResponse mapper(data) {
    if (data is Map<String, dynamic>)
      return AllocationChangeResponse.fromJson(data);
    throw 'Invalid Json!';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
