import 'package:freezed_annotation/freezed_annotation.dart';

part 'mandi_inventory_sku_dto.freezed.dart';
part 'mandi_inventory_sku_dto.g.dart';

@freezed
class MandiInventorySkuDto with _$MandiInventorySkuDto {
  const factory MandiInventorySkuDto({
    required int skuId,
    required String type,
    required String unit,
    required double lotSize,
    required double quantity,
  }) = _MandiInventorySkuDto;

@override
factory MandiInventorySkuDto.fromJson(Map<String, dynamic> json) =>_$MandiInventorySkuDtoFromJson(json);
}
