{"retry": "Retry", "uploadingLogFile": "Uploading log file....", "reportingBugMessage": "Reporting bug....", "bugReported": "Bug Reported!", "commentCopied": "Comment copied!", "reportBug": {"title": "Report a bug", "description": "Description", "attachments": "Attachments", "addAttachment": "Add attachment"}, "submit": "Submit", "upload": {"deleteImageMessage": "Are you sure you want to delete it?", "updatedMessage": "Updated the files", "clearedMessage": "Cleared the uploaded files", "exitPopupTitle": "Go back?", "exitPopupMessage": "Images which are not uploaded will be removed. Are you sure?", "maxSelectionMessage": "You can select maximum of ##maxSelection## images", "deleteImageTitle": "Delete?", "ctaTitle": "Upload", "erorUploadingFiles": "Error while uploading files!", "chooseOption": "Choose option", "chooseOptionGallery": "Gallery", "chooseOptionCamera": "Camera"}, "vendorManagement": {"create": "Create", "createLocation": "Create Location", "createVendor": "Create <PERSON><PERSON><PERSON>", "vendorName": "Vendor Name*", "vendorPhone": "Vendor Phone*", "pocName": "POC Name", "pocPhone": "POC Phone", "addNewVendor": "Add New Vendor", "addNewLocation": "Add New Location", "noAreaMapped": "No Area Mapped"}, "vendorName": "Vendor Name", "vendorLocation": "Vendor Location*", "searchSku": {"selectSku": "Select Sku"}, "unit": "Unit", "count": "Count", "qty": "Qty", "startedAt": "Started at", "closedAt": "Closed at", "reportBugMessage": "Found a bug? Report it!", "reportNow": "Report Now", "info": "Info", "searchingIfDeviceNearbyMessage": "Searching if device is nearby...", "error": "Error", "weighingMachineNotFoundMessage": "Failed to find weighing machine!", "success": "Success", "weighingMachineSucessMessage": "Weighing machine connected successfully!", "weighingMachineFailedMessage": "Failed to connect weighing machine!", "bluetoothPermissionMessage": "Bluetooth permission is required!", "weighingMachine": "Weighing Machine", "cancel": "Cancel", "update": "Update", "emptyQuantityMessage": "Please fill the previous quantity!", "weighingMachineStatus": "Weighing Scale Status", "weights": "Weights", "total": "Total", "captureWeight": "Capture Weight", "updateWeight": "Update Weight", "terminalId": "Terminal Id", "reportBugAdmin": {"title": "Active Bug Reports"}, "noBugReportFound": "No active bug reports", "comment": "Comment", "attachments": "Attachments", "downloadLogFile": "Download Log File", "resolveABug": "Resolve Bug", "resolveABugMessage": "Are you sure you want to resolve this bug?", "markResolve": "<PERSON>", "adminMenu": {"adminMenu": "Admin Panel", "procAdmin": "Proc Admin", "procOrder": "Proc Order", "procOrderUpdate": "Proc Order Update", "manageUsers": "Manage Users", "weighingMachine": "Weighing Machine", "bugReports": "Bug Reports"}, "procurementUpdate": {"deleteSkuMessage": "Are you sure you want to delete this SKU? It can not be reverted!", "title": "Procurement Update", "noOrderMessage": "No procurement order found for this date!"}, "updateProcurement": {"title": "Update Procurement", "errorLoadingChildOrders": "Error while loading child orders. Please try again."}, "pitstops": "Pitstops", "serialNumber": "S.No.", "createdAt": "Created At", "createdBy": "Created By", "skus": "Skus", "status": "Status", "weighingMachineBypass": {"submit": "Submit"}, "searchUsers": "Search Users", "name": "Name", "emailorPhone": "Email/Phone", "invalidUser": "Invalid User", "loginFailed": "Login Failed", "wheelocityFailedLogin": "Login Failed by Wheelocity", "waitingForOTPToBeSent": "Waiting For OTP To be sent", "failedOTPVerification": "Failed OTP Verification", "enter6DigitsOTP": "Enter Six Digits OTP", "invalidOTP": "Invalid OTP", "pleaseEnter10Digit": "Please Enter 10 Digits Number", "loginViaPhone": "Login with Phone", "phoneNumber": "Phone number", "otp": "OTP", "changePhoneNumber": "Change Number", "verifyOTP": "Verify OTP", "sendOTP": "Get OTP", "amount": "Amount", "addProc": {"placeProcOrder": "Place Procurement Order", "placeProcOrderSubmit": "Submit", "receivedQuantity": "Received Quantity", "receivedLots": "Received Lots", "orderedQuantity": "Ordered Quantity", "orderedLots": "Ordered Lots", "submitPopupTitle": "Are you sure?", "submitPopupMessage": "The procurements will submit.", "deletePopupTitle": "Are you sure?", "deletePopupMessage": "The procurement will be deleted.", "productCost": "Product Cost", "fieldCharges": "Field Charges", "addComment": "Add Comment", "editComment": "Edit Comment"}, "addProcAdmin": {"selectPitStops": "Select Pit Stops"}, "procFieldCharges": {"addNew": "Add New", "fieldCharges": "Field Charges"}, "placeOrder": {"orderedQuantity": "Ordered Quantity", "previousReceive": "Already Received", "newReceived": "<PERSON>ly Received", "totalReceive": "Total Received", "finalSubmit": "Received All?"}, "orderedQty": "<PERSON><PERSON>", "received": "Received", "childOpenOrders": "Child Open Orders", "assignUser": "Assign User", "attendance": {"noUserAssigned": "No User Assigned"}, "phone": "Phone", "refresh": "Refresh", "lotSize": "Lot Size", "noOfLots": "# of Lots", "losses": "Losses", "closeInventory": {"noSkuMessage": "Please add SKUs for closing inventory.", "successMessage": "Inventory closed successfully"}, "receiveInventory": {"damaged": "Damaged", "damagedLot": "Damaged Lots", "addDamages": "Add", "expectedQty": "Expected", "expectedLots": "Expected Lots", "received": "Received", "receivedLots": "Received Lots", "damagedLots": "Damaged Lots", "totalDamages": "Total Damage", "goodQty": "Good Qty", "goodLots": "Good Lots", "editUpdateCta": "Update", "infoTitle": "Trip Info", "from": "From", "submit": "Submit", "damages": "Damages", "missing": "Missing"}, "closeInv": {"quantity": "Quantity", "numberOfLots": "Number of Lots"}, "mandiMenu": {"procurements": "Procurements", "liquidation": "Liquidation", "addProcurement": "Add Procurement"}, "noAccess": "No Access", "gradingConversion": {"selectSku": "Select Sku", "qtyLabel": "Qty", "title": "Grading Conversion", "configLoadError": "Error while loading configuration!", "clearSkuPopupTitle": "Clear Select Sku?", "clearSkuPopupMessage": "Are you sure you want to clear selected Sku? All data will be cleared!", "availableQty": "Available Qty", "availableLot": "Available Lot", "qtyToGrade": "Qty to Grade*", "lotsToGrade": "No. of Lots to Grade*", "addSku": "Add Sku", "submitCta": "Submit", "dumpQty": "Dump Quantity($unit)"}, "gradingPopup": {"submitCta": "Submit"}, "quantity": "Quantity", "grading": {"title": "Grading", "selectSourceInventory": "Select Source Inventory", "mandiInventory": "Mandi Inventory", "returnInventory": "Return Inventory", "wastageReturnsInventory": "Wastage Returns Inventory"}, "smoHistory": "SMO History", "home": {"noOpsFound": "No operations found!"}, "adminPanel": "Admin Panel", "allocateInventory": {"customerAllocationEmpty": "No Supply Orders Found!"}, "allocationInventory": {"excessMessage": "Excess inventory allocation is allowed for this customer group", "noExcessMessage": "Allocation can be done upto the supply order quantity for this customer group", "excessSubmitTitle": "Allow Excess?", "excessSubmitMessage": {"": "Any excess inventory quantity would be allocated based on proportion of product in the supply orders"}, "noExcessSubmitMessage": {"": "Allocation would be done based on the available inventory in the mandi."}, "excessCopyTitle": "Allow Excess?", "excessCopyMessage": {"": "Allow all available mandi inventory to be distributed for allocation"}, "noExcessCopyMessage": {"": "Allow all available mandi inventory to be distributed for allocation"}, "excessCopySupplyMessage": {"": "Allow supply order quantity to be distributed for allocation"}, "noExcessCopySupplyMessage": {"": "Allow supply order quantity to be distributed for allocation"}}, "mandiQty": "Mandi\\nQty", "allocatedQty": "Allocated\\nQty", "allocation": {"supplyOrders": "SupplyOrders: ", "serialNumber": "S.No", "terminal": "Terminal", "allocated": "Allocated", "finalAllocation": "Final Allocation", "sno": "S.No", "terminals": "Terminals", "mandi": "Mandi", "customer": "Customers", "allocateCta": "Allocate", "noAllocationFoundTitle": "No Allocation Found!", "noAllocationrDescription": "Please create new allocation!"}, "autoAllocationDisabled": "Auto Allocation Disabled!", "autoAllocationDisabledMessage": "Auto allocation is disabled for this allocation. Please contact your admin to enable it.", "allocationError": "Allocation Error", "allocationManagement": {"ttile": "Allocation Management", "cancelOrderMessage": "Are you sure you want to cancel the selected orders?"}, "updateAllocation": "Update Allocation", "viewAllocation": "View Allocation", "allocationSummary": "Allocation Summary", "distribution": {"goBackTitle": "Are you sure?", "goBackMessage": "You have not submitted the distribution. Your data will be lost if you don\\"}, "draft": "Draft", "groupAllocations": "Group Allocations", "groupingPopupSubmitTitle": "Are you sure?", "allocationLoading": "Allocation Loading...", "exit": "Exit?", "exitMessage": "Are you sure you want to exit?", "sku": "S<PERSON>", "ordered": "Ordered", "mandi": "Mandi", "copyFrom": "Copy From", "mandiInventory": "Mandi Inventory", "supplyOrders": "Supply Orders", "copy": "Copy", "customerGroup": "Customer Group:", "delivery": "Delivery:", "lockedSkuPopup": {"title": "Locked <PERSON>", "noLockedSkus": "No locked skus found"}, "lockedBy": "Locked By", "wastage": "Wastage", "allocationQtyLessThanDistribution": "Allocation Qty can not be less than ##qty##. To change please update distribution first", "expected": "Expected", "Allocated": "Total", "mandiQtyError": "Qty can not be greater than mandi qty", "deliverySlot": "Delivery Slot:", "deliveryDate": "Delivery Date", "driverName": "Driver Name", "driverPhone": "Driver Phone", "vehicleNumber": "Vehicle Number", "eta": "ETA", "liquidation": {"title": "Liquidation", "activeTabKey": "Active", "pastTabKey": "Past", "orderedDate": "Ordered Date", "skuAge": "Age(Days)", "orderBy": "By", "vendor": "<PERSON><PERSON><PERSON>", "receivedAmount": "Received Amount"}, "liqQty": "<PERSON><PERSON>", "pending": "Pending", "vendor": "<PERSON><PERSON><PERSON>", "updateLiquidation": {"uploadSlipsTitle": "Upload Slips", "vendorMobile": "Vendor Mobile*"}, "totalQty": "Total Qty", "amt": "Amt", "receivedAmount": "<PERSON><PERSON>", "liquidationPopup": {"searchSku": "Search Sku", "submitCta": "Submit", "agreedAmount": "Agreed Amount", "liquidationQty": "Liquidation Qty", "previousLiquidationQty": "Previous Liquidation Qty", "previousDumpedQty": "Previous Dumped <PERSON><PERSON>", "availableForLiquidateQty": "Available for Liquidation Qty", "liquidatingQtyNow": "Liquidating Qty Now", "dumpingQtyNot": "Dumping Qty Now", "previousReceivedAmount": "Already Received Amount", "amount": "Amount", "totalAmount": "Total Amount"}, "all": "All", "gradeA": "Grade A", "gradeD1": "Grade D1", "gradeC": "Grade C", "transferToC": "Transfer to C", "transferToDump": "Transfer to Dump", "transferTitle": "Are you sure", "transferBody": "Do you want to transfer the inventory?", "transferCtaTitle": "Transfer", "returns": {"copyAll": "Copy All"}, "wastageReturns": "Wastage Returns", "ungraded": "Ungraded", "myProcDetail": {"title": "My Procurements", "noData": "No Procurements Found!"}, "dateAndTime": "Date & Time", "numberOfItems": "# of Items", "myProcDetails": {"updateOrder": "Update Order", "receiveOrder": "Receive Order", "updatePrice": "Update Price", "view": "View"}, "myProc": {"vendorSummary": "<PERSON><PERSON><PERSON> Summary: "}, "detail": "Details", "myProcSummary": {"noData": "No Procurements Found!", "noDataFilter": "No procurements for this search!"}, "pendingQty": "Pending", "closedOpsAt": "Ops Closed at", "closingInventoryClosedAt": "Inventory Closed at", "endAt": "Ended at", "opsSummary": {"myOpenProc": "My Open Procurements", "otherOpenProc": "Other Open Procurements", "operationsClosed": "Operations Closed!"}, "procurementSummary": {"managerTitle": "Manager", "pendingTitle": "Pending Count", "detailCta": "Details"}, "procItem": {"title": "Procurement Order"}, "receivedQty": "<PERSON><PERSON>", "createUser": {"firstName": "First Name*", "lastName": "Last Name", "email": "Email*", "phone": "Phone*", "create": "Create"}, "userCreationSuccess": "User created successfully!", "search": "Search", "clearAll": "Clear All", "mandiList": "Mandi List", "selectRoles": "Select Roles", "userMandiRoleUpdateSuccess": "User mandi role updated successfully!", "selectProc": {"title": "Select Procurement"}, "selectMandi": "Select Mandi", "selectDate": "Select Date", "apply": "Apply", "receiveQty": "<PERSON><PERSON>", "createReturnsScreen": {"submitPopupMessage": "These inventory will be added to the returns inventory!"}, "returnsPopup": {"receiveQty": "Receive Qty"}, "returnedQty": "Returned <PERSON><PERSON>", "availableQty": "Available Qty", "acceptedQty": "Accepted <PERSON>ty", "recQty": "<PERSON><PERSON>", "returnsAcceptScreen": {"ctaLabel": "Submit", "title": "Accept Returns", "noItemsFoundMessage": "No Return Items Found!", "submitPopupMessage": "Are you sure you want to submit?\\nYou can\\"}, "returnsAccept": {"validationVariationPopup": "Are you sure?"}, "returnsHistoryScreen": {"title": "Returns: History"}, "wastageReturnsListScreen": {"title": "Wastage Returns"}, "returnsListScreen": {"title": "Returns", "emptyMessage": "No Returns Found!"}, "editProc": {"editPrice": "Edit Price", "addImage": "Add Image${fileCountText}", "deleteImagePoolMessage": "Are you sure you want to delete it? This will also delete from any sku if you have selected it."}, "updatePrice": {"ctaTitle": "Update", "update": "Update", "submit": "Submit"}, "editPrice": {"orderedLots": "Ord Lots", "receivedLots": "Rec Lots"}, "procurementHistory": {"procuredBy": "ProcuredBy"}, "noOfItems": "# of Items", "editSupplyOrder": {"addSkuDialogEnableQuantityCheckboxTitle": "Enable quantity update?", "addSkuDialogEnableQuantityCheckboxSubTitle": "Updating the quantity will update the quantity for the entire row for this sku!", "addSkuDialogCtaGoBack": "Go Back", "addSkuDialogCtaStage0": "Next", "addSkuDialogCtaStage1": "Add to Supply Order", "title": "Edit Supply Order", "productLabel": "Product", "copyAllDialogTitle": "Are you sure?"}, "viewSupplyOrder": {"title": "View Supply Order", "deliveryDateLabel": "Delivery Date", "deliverySlotLabel": "Delivery Slot", "customerGroupsLabel": "Customers Group", "searchCta": "Search", "editCta": "Edit Supply Order", "terminal": "Terminal", "orderDate": "Order Date", "orderNumber": "Order Number", "user": "User", "deliveryDate": "Delivery Date", "deliverySlot": "Delivery Slot", "customerGroup": "Customer Group"}, "ViewSupplyOrderDetail": {"title": "View Supply Order"}, "lossPick": {"addCta": "Add Image", "deleteTitle": "Delete?", "deleteMessage": "Are you sure to delete the file?"}}