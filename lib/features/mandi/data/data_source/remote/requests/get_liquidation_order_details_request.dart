import 'package:proc2/core/data/network/base_request.dart';
import 'package:proc2/features/mandi/domain/entity/liquidation/liquidation_order_details.dart';

class GetLiquidationOrderDetailRequest
    extends BaseRequest<dynamic, LiquidationOrderDetails> {
  final int smoId;

  GetLiquidationOrderDetailRequest({required this.smoId});

  @override
  Map<String, dynamic>? getQuery() {
    return {
      'smoId': smoId,
    };
  }

  @override
  String getPath() => 'liquidation/order-details';

  @override
  LiquidationOrderDetails mapper(data) {
    if (data is Map<String, dynamic>) {
      return LiquidationOrderDetails.fromJson(data);
    }
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.GET;
}
