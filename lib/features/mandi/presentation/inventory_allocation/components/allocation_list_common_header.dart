import 'package:flutter/material.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/allocation_journery/cubit/allocation_journey_cubit.dart';
import 'package:proc2/features/mandi/presentation/inventory_allocation/components/customer_group_widget.dart';

class AllocationListCommonHeader extends StatelessWidget {
  const AllocationListCommonHeader({
    super.key,
    required this.state,
    this.showCopy = true,
  });
  final AllocationJourneyState state;
  final bool showCopy;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (state.customerGroupData != null) ...[
          SizedBox(
            height: 8,
          ),
          CustomerGroupWidget(
            data: state.customerGroupData!,
            activeTerminalCount: state.activeTerminalCount,
            supplyOrderCount: state.supplyOrderCount,
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          ),
          Divider(
            color: Colors.grey.shade600,
            height: 16,
          ),
        ],
        // Visibility(
        //   visible: showCopy,
        //   child: Padding(
        //     padding: const EdgeInsets.only(bottom: 16.0),
        //     child: CopyInventoryWidget(
        //       state: state,
        //       padding: EdgeInsets.symmetric(
        //         horizontal: 8,
        //         vertical: 4,
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
