import 'package:proc2/core/data/network/base_request.dart';

class DownloadLiquidationImageRequest
    extends BaseRequest<dynamic, Map<String, dynamic>> {
  final int smoId;
  final List<String> imageKeys;

  DownloadLiquidationImageRequest(
      {required this.smoId, required this.imageKeys});

  @override
  Map<String, dynamic> getBody() {
    return {"smoId": smoId, "imagePaths": imageKeys};
  }

  @override
  String getPath() => 'liquidation/images/download';

  @override
  Map<String, dynamic> mapper(data) {
    if (data is Map<String, dynamic>) return data;
    throw 'Invalid Json';
  }

  @override
  RequestMethod get method => RequestMethod.POST;
}
