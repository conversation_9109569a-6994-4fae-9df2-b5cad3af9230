part of 'procurement_update_cubit.dart';

class ProcurementUpdateState {
  final MandiInfo? selectedMandi;
  final int? selectedSmoId;
  final String selectedStatus;
  final bool isSearchLoading;
  final List<ProcurementOrder>? procurementOrders;
  final String? message;

  const ProcurementUpdateState._({
    required this.selectedMandi,
    required this.selectedSmoId,
    required this.selectedStatus,
    required this.isSearchLoading,
    required this.procurementOrders,
    required this.message,
  });
  factory ProcurementUpdateState.initial() => ProcurementUpdateState._(
        selectedMandi: null,
        selectedSmoId: null,
        selectedStatus: "PLACED",
        isSearchLoading: false,
        procurementOrders: null,
        message: null,
      );

  ProcurementUpdateState copyWith({
    MandiInfo? selectedMandi,
    int? selectedSmoId,
    String? selectedStatus,
    bool? isSearchLoading,
    List<ProcurementOrder>? procurementOrders,
    String? message,
  }) {
    return ProcurementUpdateState._(
      selectedMandi: selectedMandi ?? this.selectedMandi,
      selectedSmoId: selectedSmoId ?? this.selectedSmoId,
      selectedStatus: selectedStatus ?? this.selectedStatus,
      isSearchLoading: isSearchLoading ?? this.isSearchLoading,
      procurementOrders: procurementOrders ?? this.procurementOrders,
      message: message ?? this.message,
    );
  }
}
