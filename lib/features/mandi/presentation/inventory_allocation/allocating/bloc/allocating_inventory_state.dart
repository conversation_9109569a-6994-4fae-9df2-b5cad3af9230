part of 'allocating_inventory_bloc.dart';

@freezed
class AllocatingInventoryState with _$AllocatingInventoryState {
  const factory AllocatingInventoryState.initial() = _Initial;
  const factory AllocatingInventoryState.error(ErrorResult<dynamic> error) =
      _Error;
  const factory AllocatingInventoryState.input({
    required List<List<AllocateItemInputModel>> items,
    @Default([]) List<List<AllocateItemInputModel>> onlyMandiItems,
    @Default([]) List<List<AllocateItemInputModel>> onlySupplyItems,
    required List<AllotedItem> allotedItems,
    String? message,
    @Default(false) bool isDraftLoading,
    @Default(false) bool isSubmitLoading,
    @Default(false) bool shouldPopBack,
    @Default(false) bool isValidData,
  }) = AllocatingInput;
}
